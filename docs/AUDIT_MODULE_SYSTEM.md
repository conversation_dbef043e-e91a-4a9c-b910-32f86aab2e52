# Module-Based Audit System

## Overview

The enhanced audit system provides module-based categorization of audit logs, making it easier to track and analyze user activities by functional areas. Each `FormName` enum is now mapped to a specific `Module`, allowing for better organization and reporting of audit data.

## Module Categories

The system organizes activities into the following modules:

### 1. User Management
- **Forms**: USER, ROLE, DEACTIVATE_USER, EXCEMPT_USER, CHAN<PERSON>PHONENUMBER
- **Purpose**: Track user account management, role assignments, and user-related operations

### 2. Master Records  
- **Forms**: BENEFICIARYTYPE, COUNTY, DISTRICT, FACILITY, FINANCIER, ORDERTYPE, PROVINCE, BUDGET, PERDIEM, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>IE<PERSON>, OR<PERSON>NI<PERSON>TION, SELECT_COUNTIES
- **Purpose**: Track master data management and configuration

### 3. Finance
- **Forms**: ORDER, ORG_PAYMENT_TYPES, LINEITEM, PAYMENTSYNC, PAYMENTS_INBUILD_PAYMENT, FUNDALLOCATION, TRANS_ADD, RESUBMIT, APPROVALTOP, APPROVAL, STAGEAPPROVAL, REVERSEAPPROVAL, REVERSEREJECTION
- **Purpose**: Track financial operations, payments, and approvals

### 4. Activities
- **Forms**: MEETING, ATTENDANCEPROCESSINGWITHEXCEL, ATTENDANCEAPPROVAL, ATTENDANCEPROCESSING, ATTENDANCEAPPROVALEXCEL, CONFIRMRECIPIENT
- **Purpose**: Track meeting management and attendance processing

### 5. SMS
- **Forms**: BULKSMS, SMSTEMPLATES, CONTACTUPLOAD, ADDRESSBK
- **Purpose**: Track SMS and communication activities

### 6. Configuration
- **Forms**: B2CACCOUNTSETTINGS, PARAMETER, ORGTYPEFILTER
- **Purpose**: Track system configuration changes

## Database Schema Changes

The `activity` table has been enhanced with the following new columns:

```sql
-- Module category for the activity
module VARCHAR(50) NULL

-- Specific form name from FormName enum  
form_name VARCHAR(100) NULL

-- Additional metadata or context as JSON
metadata TEXT NULL
```

### Indexes Added
- `idx_activity_module` - For module-based queries
- `idx_activity_form_name` - For form-specific queries
- `idx_activity_module_org` - For module + organization queries
- `idx_activity_user_module` - For user + module queries

## Usage Examples

### 1. Basic Module-Aware Auditing

```java
// Using AuditService directly
auditService.auditActionWithModuleNative(
    "CREATE", 
    "Created new admin role", 
    sourceIp, 
    postData, 
    null, 
    userId, 
    orgId, 
    FormName.ROLE
);
```

### 2. Using SharedFunctions Convenience Methods

```java
// Role-specific auditing (automatically uses ROLE FormName)
sharedFunctions.auditRoleAction(
    "UPDATE", 
    "Updated role permissions", 
    sourceIp, 
    postData, 
    null, 
    userId, 
    orgId
);

// User-specific auditing (automatically uses USER FormName)
sharedFunctions.auditUserAction(
    "CREATE", 
    "Created new user: john.doe", 
    sourceIp, 
    postData, 
    null, 
    userId, 
    orgId
);

// Finance-specific auditing with custom FormName
sharedFunctions.auditFinanceAction(
    "CREATE", 
    "Created payment order", 
    sourceIp, 
    postData, 
    null, 
    userId, 
    orgId, 
    FormName.ORDER
);
```

### 3. Generic Module-Aware Auditing

```java
// For any FormName with automatic module detection
sharedFunctions.auditActionWithModule(
    "DELETE", 
    "Deleted county record", 
    sourceIp, 
    postData, 
    null, 
    userId, 
    orgId, 
    FormName.COUNTY  // Will be categorized under Master Records module
);
```

## Querying Audit Data by Module

### Using the Repository

```java
// Get all activities for a specific module
Page<Activity> userManagementActivities = activityRepository.findByModule(
    Module.UserManagement, 
    pageable
);

// Get activities by module and organization
Page<Activity> financeActivitiesForOrg = activityRepository.findByModuleAndOrgId(
    Module.Finance, 
    orgId, 
    pageable
);

// Complex filtering
Page<Activity> filteredActivities = activityRepository.findActivitiesWithFilters(
    Module.Finance,     // module
    orgId,             // organization
    userId,            // user
    "CREATE",          // activity type
    "ORDER",           // form name
    pageable
);
```

### Using Native Queries

```sql
-- Get activity count by module
SELECT module, COUNT(*) as activity_count 
FROM activity 
GROUP BY module;

-- Get recent activities for a specific module
SELECT * FROM activity 
WHERE module = 'User Management' 
ORDER BY creationTime DESC 
LIMIT 10;

-- Get activities by module and time range
SELECT * FROM activity 
WHERE module = 'Finance' 
  AND creationTime BETWEEN :startTime AND :endTime
ORDER BY creationTime DESC;
```

## Migration Guide

### For Existing Code

1. **Replace existing audit calls** with module-aware versions:
   ```java
   // Old way
   sharedFunctions.auditAction("CREATE", "Created role", sourceIp, postData, null, userId, orgId);
   
   // New way
   sharedFunctions.auditRoleAction("CREATE", "Created role", sourceIp, postData, null, userId, orgId);
   ```

2. **Run the migration script** to add new columns and populate existing data:
   ```sql
   -- Execute: src/main/resources/db/migration/add_module_columns_to_activity.sql
   ```

### Benefits

1. **Better Organization**: Activities are automatically categorized by functional area
2. **Improved Reporting**: Generate module-specific audit reports
3. **Enhanced Filtering**: Query activities by module, form, or combination
4. **Backward Compatibility**: Existing audit calls continue to work
5. **Performance**: Indexed columns for faster queries

## Best Practices

1. **Use specific convenience methods** when available (e.g., `auditRoleAction`, `auditUserAction`)
2. **Always specify the FormName** for new audit calls to ensure proper module categorization
3. **Include meaningful descriptions** that clearly explain what action was performed
4. **Use consistent activity types** (CREATE, UPDATE, DELETE, etc.)
5. **Consider adding metadata** for complex operations that need additional context

## Reporting Examples

### Module Activity Summary
```java
List<Object[]> moduleStats = activityRepository.getActivityCountByModule();
// Returns: [["User Management", 150], ["Finance", 300], ["Activities", 75], ...]
```

### Recent Activities by Module
```java
Page<Activity> recentFinanceActivities = activityRepository.findRecentActivitiesByModule(
    Module.Finance, 
    PageRequest.of(0, 20)
);
```

This enhanced audit system provides comprehensive tracking and categorization of all system activities, making it easier to monitor, analyze, and report on user actions across different functional areas of the application.
