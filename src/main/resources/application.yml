server:
  port: 9095
  tomcat:
    relaxed-path-chars:
      - "["
      - "]"
      - "{"
      - "}"
      - "|"
      - "\\"
      - "^"
      - "`"
      - "\""
      - "<"
      - ">"
    relaxed-query-chars:
      - "["
      - "]"
      - "{"
      - "}"
      - "|"
      - "\\"
      - "^"
      - "`"
      - "\""
      - "<"
      - ">"
  servlet:
    context-path: /tibuPortalEngine
spring:
#  application.name: tibuPortalEngine
  jpa:
    hibernate:
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
  datasource:
    url: *********************************************************************************
    username: root
    password: mysql
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 10
      idle-timeout: 30000
      minimum-idle: 5
      max-lifetime: 600000
      connection-timeout: 30000
      pool-name: MyHikariCP

encryption:
  key: a9d4f82b5c6e8f7d2a9b4c3d6e7f8a9c
  expirationMs: 30000000
  refreshExpirationMs: 30000

allowedFileTypes: application/vnd.ms-excel,application/ms-excel,application/wps-office.xls,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,image/jpeg,image/png,image/gif,application/pdf,text/plain,text/csv,text/comma-separated-values,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/octet-stream,application/wps-office.xls
recaptcha:
  secret: 6Len92MrAAAAAIX6ndt4oPYX_FY4mH9rAjzLC5DO
dataMs:
  BaseUrl: http://***********:9099/dataMs/dataApi/
  User: TibuUser
  Password: TibuUser

receipts:
  uploadPath:  /home/<USER>/Downloads/uploads/
sms:
  userId: 61475
  passkey: 60DY7RTS7J
  service: 1
  apiUrl: https://api.prsp.tangazoletu.com/
pms:
  apiKey: 89e46ea19372ab9f6cce869a5004b2cd,
  authKey: nmioJK90
