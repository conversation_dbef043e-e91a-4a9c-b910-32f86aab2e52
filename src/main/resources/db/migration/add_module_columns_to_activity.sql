-- Migration script to add module-related columns to the activity table
-- This script adds the new columns needed for module-based audit categorization

-- Add module column to store the module information
ALTER TABLE activity 
ADD COLUMN module VARCHAR(50) NULL COMMENT 'Module category for the activity (User Management, Finance, etc.)';

-- Add form_name column to store the specific form name
ALTER TABLE activity 
ADD COLUMN form_name VARCHAR(100) NULL COMMENT 'Specific form name from FormName enum';

-- Add metadata column for additional context
ALTER TABLE activity 
ADD COLUMN metadata TEXT NULL COMMENT 'Additional metadata or context as JSON';

-- Add index on module column for better query performance
CREATE INDEX idx_activity_module ON activity(module);

-- Add index on form_name column for better query performance  
CREATE INDEX idx_activity_form_name ON activity(form_name);

-- Add composite index for common queries
CREATE INDEX idx_activity_module_org ON activity(module, org_id);

-- Add composite index for user and module queries
CREATE INDEX idx_activity_user_module ON activity(user, module);

-- Update existing records to set module based on resource field (optional)
-- This is a best-effort mapping for existing data

UPDATE activity SET module = 'User Management' 
WHERE resource IN ('user', 'role', 'deactivate_user', 'excempt_user', 'changephonenumber');

UPDATE activity SET module = 'Master Records' 
WHERE resource IN ('beneficiarytype', 'county', 'district', 'facility', 'financier', 'ordertype', 
                   'province', 'budget', 'perdiem', 'jobgroup', 'recipient', 'organisation', 'select_counties');

UPDATE activity SET module = 'Finance' 
WHERE resource IN ('order', 'org_payment_types', 'lineitem', 'paymentsync', 'payments_inbuild_payment', 
                   'fundallocation', 'trans_add', 'resubmit', 'approvaltop', 'approval', 'stageapproval', 
                   'reverseapproval', 'reverserejection');

UPDATE activity SET module = 'Activities' 
WHERE resource IN ('meeting', 'attendanceprocessingwithexcel', 'attendanceapproval', 'attendanceprocessing', 
                   'attendanceapprovalexcel', 'confirmrecipient');

UPDATE activity SET module = 'Sms' 
WHERE resource IN ('bulksms', 'smstemplates', 'contactupload', 'addressbk');

UPDATE activity SET module = 'Configuration' 
WHERE resource IN ('b2caccountsettings', 'parameter', 'orgtypefilter');

-- Set default module for records that don't match any category
UPDATE activity SET module = 'System' WHERE module IS NULL;

-- Update form_name based on resource field (convert to uppercase to match enum)
UPDATE activity SET form_name = UPPER(resource) WHERE resource IS NOT NULL;

-- Add comment to the table
ALTER TABLE activity COMMENT = 'Audit table for tracking user activities with module categorization';
