package com.tangazoletu.tibuPortalEngine.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to define Excel column properties
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcelColumn {

    /**
     * Display name for the column header
     */
    String value() default "";

    /**
     * Order of the column (lower numbers appear first)
     */
    int order() default Integer.MAX_VALUE;

    /**
     * Whether to exclude this field from export
     */
    boolean exclude() default false;

    /**
     * Date format for date/time fields
     */
    String dateFormat() default "dd/MM/yyyy HH:mm";
}