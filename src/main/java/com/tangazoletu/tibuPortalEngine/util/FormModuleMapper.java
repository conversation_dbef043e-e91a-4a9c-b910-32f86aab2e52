package com.tangazoletu.tibuPortalEngine.util;

import com.tangazoletu.tibuPortalEngine.enums.FormName;
import com.tangazoletu.tibuPortalEngine.enums.Module;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Utility class to map FormName enums to their corresponding Module categories
 * for audit logging and module-based grouping
 */
@Component
public class FormModuleMapper {

    private static final Map<FormName, Module> FORM_MODULE_MAP = new HashMap<>();

    static {
        // User Management Module
        FORM_MODULE_MAP.put(FormName.USER, Module.UserManagement);
        FORM_MODULE_MAP.put(FormName.ROLE, Module.UserManagement);
        FORM_MODULE_MAP.put(FormName.DEACTIVATE_USER, Module.UserManagement);
        FORM_MODULE_MAP.put(FormName.EXCEMPT_USER, Module.UserManagement);
        FORM_MODULE_MAP.put(FormName.CHANGEPHONENUMBER, Module.UserManagement);

        // Master Records Module
        FORM_MODULE_MAP.put(FormName.BENEFICIARYTYPE, Module.MasterRecords);
        FORM_MODULE_MAP.put(FormName.COUNTY, Module.MasterRecords);
        FORM_MODULE_MAP.put(FormName.DISTRICT, Module.MasterRecords);
        FORM_MODULE_MAP.put(FormName.FACILITY, Module.MasterRecords);
        FORM_MODULE_MAP.put(FormName.FINANCIER, Module.MasterRecords);
        FORM_MODULE_MAP.put(FormName.ORDERTYPE, Module.MasterRecords);
        FORM_MODULE_MAP.put(FormName.PROVINCE, Module.MasterRecords);
        FORM_MODULE_MAP.put(FormName.BUDGET, Module.MasterRecords);
        FORM_MODULE_MAP.put(FormName.PERDIEM, Module.MasterRecords);
        FORM_MODULE_MAP.put(FormName.JOBGROUP, Module.MasterRecords);
        FORM_MODULE_MAP.put(FormName.RECIPIENT, Module.MasterRecords);
        FORM_MODULE_MAP.put(FormName.ORGANISATION, Module.MasterRecords);
        FORM_MODULE_MAP.put(FormName.SELECT_COUNTIES, Module.MasterRecords);

        // Finance Module
        FORM_MODULE_MAP.put(FormName.ORDER, Module.Finance);
        FORM_MODULE_MAP.put(FormName.ORG_PAYMENT_TYPES, Module.Finance);
        FORM_MODULE_MAP.put(FormName.LINEITEM, Module.Finance);
        FORM_MODULE_MAP.put(FormName.PAYMENTSYNC, Module.Finance);
        FORM_MODULE_MAP.put(FormName.PAYMENTS_INBUILD_PAYMENT, Module.Finance);
        FORM_MODULE_MAP.put(FormName.FUNDALLOCATION, Module.Finance);
        FORM_MODULE_MAP.put(FormName.TRANS_ADD, Module.Finance);
        FORM_MODULE_MAP.put(FormName.RESUBMIT, Module.Finance);

        // Activities Module
        FORM_MODULE_MAP.put(FormName.MEETING, Module.Activities);
        FORM_MODULE_MAP.put(FormName.ATTENDANCEPROCESSINGWITHEXCEL, Module.Activities);
        FORM_MODULE_MAP.put(FormName.ATTENDANCEAPPROVAL, Module.Activities);
        FORM_MODULE_MAP.put(FormName.ATTENDANCEPROCESSING, Module.Activities);
        FORM_MODULE_MAP.put(FormName.ATTENDANCEAPPROVALEXCEL, Module.Activities);
        FORM_MODULE_MAP.put(FormName.CONFIRMRECIPIENT, Module.Activities);

        // SMS Module
        FORM_MODULE_MAP.put(FormName.BULKSMS, Module.Sms);
        FORM_MODULE_MAP.put(FormName.SMSTEMPLATES, Module.Sms);
        FORM_MODULE_MAP.put(FormName.CONTACTUPLOAD, Module.Sms);
        FORM_MODULE_MAP.put(FormName.ADDRESSBK, Module.Sms);

        // Configuration Module
        FORM_MODULE_MAP.put(FormName.B2CACCOUNTSETTINGS, Module.Configuration);
        FORM_MODULE_MAP.put(FormName.PARAMETER, Module.Configuration);
        FORM_MODULE_MAP.put(FormName.ORGTYPEFILTER, Module.Configuration);

        // Finance/Approval Module
        FORM_MODULE_MAP.put(FormName.APPROVALTOP, Module.Finance);
        FORM_MODULE_MAP.put(FormName.APPROVAL, Module.Finance);
        FORM_MODULE_MAP.put(FormName.STAGEAPPROVAL, Module.Finance);
        FORM_MODULE_MAP.put(FormName.REVERSEAPPROVAL, Module.Finance);
        FORM_MODULE_MAP.put(FormName.REVERSEREJECTION, Module.Finance);
    }

    /**
     * Get the module for a given FormName
     * @param formName The FormName enum
     * @return The corresponding Module enum
     */
    public Module getModuleForForm(FormName formName) {
        return FORM_MODULE_MAP.getOrDefault(formName, Module.System);
    }

    /**
     * Get the module name as string for a given FormName
     * @param formName The FormName enum
     * @return The module name as string
     */
    public String getModuleNameForForm(FormName formName) {
        Module module = getModuleForForm(formName);
        return module.getDbValue();
    }

    /**
     * Check if a FormName belongs to a specific module
     * @param formName The FormName enum
     * @param module The Module enum to check against
     * @return true if the form belongs to the module, false otherwise
     */
    public boolean isFormInModule(FormName formName, Module module) {
        return getModuleForForm(formName) == module;
    }

    /**
     * Get all FormNames that belong to a specific module
     * @param module The Module enum
     * @return Map of FormName to Module for the specified module
     */
    public Map<FormName, Module> getFormsInModule(Module module) {
        Map<FormName, Module> result = new HashMap<>();
        FORM_MODULE_MAP.entrySet().stream()
                .filter(entry -> entry.getValue() == module)
                .forEach(entry -> result.put(entry.getKey(), entry.getValue()));
        return result;
    }
}
