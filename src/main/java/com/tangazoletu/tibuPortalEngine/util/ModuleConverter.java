package com.tangazoletu.tibuPortalEngine.util;

import com.tangazoletu.tibuPortalEngine.enums.Module;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter(autoApply = true)
public class ModuleConverter implements AttributeConverter<com.tangazoletu.tibuPortalEngine.enums.Module, String> {

    @Override
    public String convertToDatabaseColumn(com.tangazoletu.tibuPortalEngine.enums.Module module) {
        return module != null ? module.getDbValue() : null;
    }

    @Override
    public com.tangazoletu.tibuPortalEngine.enums.Module convertToEntityAttribute(String dbValue) {
        return dbValue != null ? Module.fromDbValue(dbValue) : null;
    }
}
