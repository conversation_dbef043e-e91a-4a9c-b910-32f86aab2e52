package com.tangazoletu.tibuPortalEngine.util;

import com.tangazoletu.tibuPortalEngine.configurations.SecurityUser;
import com.tangazoletu.tibuPortalEngine.entities.User;
import com.tangazoletu.tibuPortalEngine.enums.FormName;
import com.tangazoletu.tibuPortalEngine.repositories.CrudService;
import com.tangazoletu.tibuPortalEngine.service.AuditService;
import com.tangazoletu.tibuPortalEngine.service.UserDetailsImpl;
import jakarta.mail.*;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.math.BigInteger;
import java.net.URL;
import java.net.URLConnection;
import java.security.MessageDigest;
import java.time.Instant;
import java.util.*;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;

@Service
@Slf4j
@RequiredArgsConstructor
public class SharedFunctions {

    private final CrudService crudService;
    private final AuditService auditService;


    public void auditAction(String activity, String description, String sourceIp, String postData, String getData, Integer userId, Integer orgId) {

        if (userId != null) {
            UserDetailsImpl userDetails = (UserDetailsImpl) SecurityContextHolder.getContext()
                    .getAuthentication()
                    .getPrincipal();
             userId = Math.toIntExact(userDetails.getId());
        }
        if (orgId == null) {
            orgId = 0;
        }

        if (postData == null) {
            postData = "";
        }
        if (getData == null) {
            getData = "";
        }
        int createdTime1 = (int) (Instant.now().toEpochMilli() / 1000);
        String query = "INSERT INTO activity (user, activityType,description,sourceip,postData,getData,creationTime,org_id) VALUES (:user,:activitype,:description,:sourceip,:postdata,:getdata, :creationtime,:orgId)";
        HashMap<String, Object> params = new HashMap<>();
        params.put("user", userId);
        params.put("activitype", activity);
        params.put("description", description);
        params.put("sourceip", sourceIp);
        params.put("postdata", postData);
        params.put("getdata", getData);
        params.put("creationtime", createdTime1);
        params.put("orgId", orgId);

        crudService.executeNativeQuery(query, params);

    }

    /**
     * Enhanced audit method that includes module information based on FormName
     * This method delegates to the AuditService for better organization
     */
    public void auditActionWithModule(String activity, String description, String sourceIp,
                                    String postData, String getData, Integer userId, Integer orgId,
                                    FormName formName) {
        auditService.auditActionWithModuleNative(activity, description, sourceIp, postData, getData, userId, orgId, formName);
    }

    /**
     * Convenience method for role-related audit actions
     */
    public void auditRoleAction(String activity, String description, String sourceIp,
                              String postData, String getData, Integer userId, Integer orgId) {
        auditActionWithModule(activity, description, sourceIp, postData, getData, userId, orgId, FormName.ROLE);
    }

    /**
     * Convenience method for user-related audit actions
     */
    public void auditUserAction(String activity, String description, String sourceIp,
                              String postData, String getData, Integer userId, Integer orgId) {
        auditActionWithModule(activity, description, sourceIp, postData, getData, userId, orgId, FormName.USER);
    }

    /**
     * Convenience method for finance-related audit actions
     */
    public void auditFinanceAction(String activity, String description, String sourceIp,
                                 String postData, String getData, Integer userId, Integer orgId,
                                 FormName formName) {
        auditActionWithModule(activity, description, sourceIp, postData, getData, userId, orgId, formName);
    }

    public String getSourceIp(HttpServletRequest request) {

        if (request != null) {
            String ip = request.getHeader("x-forwarded-for");
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("WL-Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_CLIENT_IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_X_FORWARDED_FOR");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
            }
            return ip;

        }
        return null;

    }
    public boolean isEmailValid(String email){
        String emailRegex = "^(?=.{1,64}@)[A-Za-z0-9_-]+(\\\\.[A-Za-z0-9_-]+)*@[^-][A-Za-z0-9-]+(\\\\.[A-Za-z0-9-]+)*(\\\\.[A-Za-z]{2,})$\n" +
                "\n";
        return Pattern.compile(emailRegex)
                .matcher(email)
                .matches();
    }
    public boolean hasPermission(String item, int action, int userId){
        String query = null;
        HashMap<String, Object> params = new HashMap<>();
        switch (action) {
            case 1://select
            {
                query = "SELECT id FROM permission WHERE id IN (" +
                        "SELECT permission FROM permissionmap WHERE role IN (" +
                        "SELECT role FROM rolemap WHERE user = :userId)) AND title =:title";
                params.put("title", item + ".select.permission");
                break;
            }
                case 2://insert
                {
                    query = "SELECT id FROM permission WHERE id IN (" +
                            "SELECT permission FROM permissionmap WHERE role IN ( " +
                            "SELECT role FROM rolemap WHERE user = :userId)) AND title =:title";
                    params.put("title", item + ".insert.permission");
                    break;
                }
                    case 3://update
                    {
                        query = "SELECT id FROM permission WHERE id IN (" +
                                "SELECT permission FROM permissionmap WHERE role IN (" +
                                "SELECT role FROM rolemap WHERE user = :userId)) AND title =:title";
                        params.put("title", item + ".update.permission");
                        break;
                    }
                        case 4://delete
                        {
                            query = "SELECT id FROM permission WHERE id IN (" +
                                    "SELECT permission FROM permissionmap WHERE role IN (" +
                                    "SELECT role FROM rolemap WHERE user = :userId)) AND title =:title";
                            params.put("title", item + ".delete.permission");
                            break;
                        }
                            case 5://dashboard
                            {
                                query = "SELECT id FROM permission WHERE id IN (" +
                                        "SELECT permission FROM permissionmap WHERE role IN (" +
                                        "SELECT role FROM rolemap WHERE user = :userId)) AND title =:title";
                                params.put("title", item + ".permission");
                                break;
                            }
                                case 6: {


                                    query = "SELECT id FROM permission WHERE id IN (" +
                                            "SELECT permission FROM permissionmap WHERE role IN (" +
                                            "SELECT role FROM rolemap WHERE user = :userId)) AND url =:url";
                                    params.put("url", item);
                                    break;
                                }
            default:
        }
        params.put("userId", userId);
        List<?> result = crudService.fetchWithNativeQuery(query, params, 0, 1);
        return result != null && result.size() > 0;
    }
    public String getRecordId(String tablename, String id){
        try {
            String query = "SELECT * FROM " + tablename + " WHERE id = '"+id+" LIMIT 1'";
            List<?> result =  crudService.fetchWithNativeQuery(query, new HashMap<>(), 0, 1);
            return result.isEmpty() ? null : String.valueOf(result.get(0));
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }
    public String returnLogString(String oldRecord, String newRecord){
        if (oldRecord == null || newRecord == null) {
            return "Error: One or more record is null";
        }
        Map<String, String> newMap = parseStringToMap(newRecord);
        Map<String, String> oldMap = parseStringToMap(oldRecord);
        StringBuilder sb = new StringBuilder("Changed: ");
        for (String key : oldMap.keySet()) {
            Object value = oldMap.get(key);
            Object newValue = newMap.get(key);
            if(!Objects.equals(value,newValue)){
                sb.append(key)
                        .append(": ")
                        .append(value != null ? value.toString() : "")
                        .append(" to ")
                        .append(newValue != null ? newValue.toString() : "")
                        .append(", ");
            }
        }
        return sb.toString();

    }

    private Map<String, String> parseStringToMap(String record) {
        Map<String, String> map = new HashMap<>();
        String[] fields = record.split(",");
        for (String field : fields) {
            String[] keyValue = field.split("=", 2);
            if (fields.length ==2){
                map.put(keyValue[0].trim(), keyValue[1].trim());
            } else if (fields.length ==1) {
                map.put(keyValue[0].trim(),"");
                
            }
        }
        return map;
    }
    public Object getItems(List<String> colums, String table, String join, String where,String order, boolean resultType){
        String selectedColumns = String.join(",", colums);
        String query = "SELECT " + selectedColumns + " FROM " + table + " " +
                (join != null ? join : "") + " " +
                (where != null ? where : "") + " " +
                (order != null ? order : "");
        query = query.trim().replaceAll("\\s+", " ");
        if (resultType){
            List<?> resultList = crudService.fetchWithNativeQuery(query, new HashMap<>(), 0, Integer.MAX_VALUE);
            List<Object> flatList = new ArrayList<>();
            for (Object row : resultList){
                if (row instanceof Object[]){
                    flatList.add(((Object[]) row)[0]);
                }else {
                    flatList.add(row);
                }
            }
            return flatList;
        }else {
            List<?> resultList = crudService.fetchWithNativeQuery(query, new HashMap<>(), 0, Integer.MAX_VALUE);
            if (!resultList.isEmpty()){
                return resultList.get(0);
            }else {
                return null;
            }
        }
    }

    public String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }
    public String hashSha512(String input) throws Exception {
        MessageDigest md = MessageDigest.getInstance("SHA-512");
        byte[] inputBytes = input.getBytes();
        BigInteger hash = new BigInteger(1, md.digest(inputBytes));
        StringBuilder hashString = new StringBuilder(hash.toString(16));
        while (hashString.length() < 128) {
            hashString.insert(0, '0');

        }
        return hashString.toString();
    }
    public boolean sendEmail(String firstName, String lastName, String username, String password, String email, String resetLink) {
        boolean emailSent = false;

        try {
            String subject = "Notification";
            String fromName = "TIBU";

            // Prepare the HTML email content
            String msg = String.format("""
            Dear %s %s,<br><br>
            Your account has been created successfully. Below are your login credentials:<br><br>
            <b>Username:</b> %s<br>
            <b>Password:</b> %s<br><br>
            Please change your password upon first login.<br><br>
            <a href='" %s "'>Reset Password</a>
            Best regards,<br>Admin Team
        """, firstName, lastName, username, password, resetLink);

            // Fetch MAIL_SERVER_USERNAME from DB
            String mailUserQuery = "SELECT VALUE from PARAM WHERE PARAMETER = :parameter";
            Map<String, Object> paramUsername = new HashMap<>();
            paramUsername.put("parameter", "MAIL_SERVER_USERNAME");
            List<String> mailUsernames = crudService.fetchWithNativeQuery(mailUserQuery, paramUsername, 0, 1);

            // Fetch MAIL_SERVER_PASSWORD from DB
            Map<String, Object> paramPassword = new HashMap<>();
            paramPassword.put("parameter", "MAIL_SERVER_PASSWORD");
            List<String> mailPasswords = crudService.fetchWithNativeQuery(mailUserQuery, paramPassword, 0, 1);

            if (mailUsernames.isEmpty() || mailPasswords.isEmpty()) {
                log.info("Email credentials not found in database.");
                return false;
            }

            String smtpUser = mailUsernames.get(0);
            String smtpPass = mailPasswords.get(0);

            // SMTP Configuration
            Properties props = new Properties();
            props.put("mail.smtp.host", "smtp.gmail.com");
            props.put("mail.smtp.port", "587");
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.starttls.enable", "true");

            Session session = Session.getInstance(props, new Authenticator() {
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(smtpUser, smtpPass);
                }
            });

            // Compose and send the message
            MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress(smtpUser, fromName));
            message.setRecipient(Message.RecipientType.TO, new InternetAddress(email));
            message.setSubject(subject);
            message.setContent(msg, "text/html; charset=utf-8");

            Transport.send(message);
            emailSent = true;

        } catch (Exception e) {
            log.error("Error: ", e);
            e.printStackTrace();
        }

        // Log result regardless of success/failure
        try {
            String sqlLog = "INSERT INTO MAIL_DELIVERY_LOG (status, feature, recipient) VALUES (:status, :feature, :recipient)";
            Map<String, Object> logParams = new HashMap<>();
            logParams.put("status", emailSent ? 1 : 0);
            logParams.put("feature", "New User Registration");
            logParams.put("recipient", email);

            crudService.executeNativeQuery(sqlLog, logParams);
        } catch (Exception logEx) {
            log.error("Error: ", logEx);
            logEx.printStackTrace(); // If logging fails, it shouldn't crash the app
        }

        return emailSent;
    }
    public String getRecordById(String tableName, String whereClause, Map<String, Object> params) {
        String sql = String.format("SELECT * FROM %s WHERE %s", tableName, whereClause);
        List<?> result = crudService.fetchWithNativeQuery(sql, params, 0, 1);
        if (result != null && !result.isEmpty()) {
            return result.isEmpty() ? null : String.valueOf(result.get(0));
        }
        return Collections.emptyMap().toString();
    }


    public String generateRandomNumber(int length) {
        Random rand = new Random();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(rand.nextInt(10));
        }
        return sb.toString();
    }
    public void insertEventCodesView(String meetingCode, String col1, String col2, String col3, String col4, int day) {
        String insertSql = """
        INSERT INTO event_codes_view(meetingCode, columnOne, columnTwo, columnThree, columnFour, day)
        VALUES (:meetingCode, :col1, :col2, :col3, :col4, :day)
    """;

        Map<String, Object> insertParams = new HashMap<>();
        insertParams.put("meetingCode", meetingCode);
        insertParams.put("col1", col1);
        insertParams.put("col2", col2);
        insertParams.put("col3", col3);
        insertParams.put("col4", col4);
        insertParams.put("day", day);

        crudService.executeNativeQuery(insertSql, insertParams);
    }


    // Helper method
    public void insertIntoLatest(String meetingCode, String col1, String col2, String col3, String col4, int day) {
        String insertSql = """
        INSERT INTO event_codes_latest(meetingCode, columnOne, columnTwo, columnThree, columnFour, day)
        VALUES(:meetingCode, :col1, :col2, :col3, :col4, :day)
    """;
        HashMap<String, Object> params = new HashMap<>();
        params.put("meetingCode", meetingCode);
        params.put("col1", col1);
        params.put("col2", col2);
        params.put("col3", col3);
        params.put("col4", col4);
        params.put("day", day);

        crudService.executeNativeQuery(insertSql, params);
    }


    public void insertIntoEventCodesView(String meetingCode, String col1, String col2, String col3, String col4, String day) {
        String insertViewSql = "INSERT INTO event_codes_view (meetingCode, columnOne, columnTwo, columnThree, columnFour, day) "
                + "VALUES (:meetingCode, :col1, :col2, :col3, :col4, :day)";
        Map<String, Object> params = new HashMap<>();
        params.put("meetingCode", meetingCode);
        params.put("col1", col1);
        params.put("col2", col2);
        params.put("col3", col3);
        params.put("col4", col4);
        params.put("day", day);
        crudService.executeNativeQuery(insertViewSql, params);
    }
    public String updateChainTrx(String chain, HashMap<String, String> maps, String enckey1, String encKey2) {
        try {
            String chainKey = TrxVal.pushKey(formatKeyLength(enckey1+"-"+encKey2));
            String decodedChain = "";

            JSONObject jsonChain;

            // Parse decodedChain into a JSONObject
            if (chain!=null) {
                decodedChain = TrxVal.undoval(chain, chainKey);
                jsonChain = new JSONObject(decodedChain);
            }
            else{
                jsonChain = new JSONObject();
            }
            // Update JSON with values from maps
            for (Map.Entry<String, String> entry : maps.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();

                if (jsonChain.has(key)){
                    jsonChain.put(key+"1", value);
                }
                else{
                    jsonChain.put(key, value);
                }
            }

            String updatedChain = jsonChain.toString();
            //LOGGER.info("Updated chain is "+updatedChain);
            String encryptedUpdatedChain = TrxVal.doval(updatedChain,chainKey);
            return encryptedUpdatedChain;
        } catch (Exception e) {
            e.printStackTrace();
            log.info("Error occurred while updating chain: " + e.getMessage());
            return null;
        }
    }
    public String formatKeyLength(String key){
        String originalString = key;
        int desiredLength = 32;
        String paddedString = "";
// Pad the string with "0" characters to the left
        if(key.length()<=desiredLength) {
            paddedString= StringUtils.leftPad(originalString, desiredLength, '-');
        }
        else{
            paddedString = key.substring(0, desiredLength);
        }
        return paddedString;
    }

    public  String getCellValue(Cell cell){
        if (cell == null) return "";
        switch (cell.getCellType()){
            case STRING -> {
                return  cell.getStringCellValue();
            }
            case NUMERIC ->
            {
                if (DateUtil.isCellDateFormatted(cell)){
                    return cell.getDateCellValue().toString();
                }else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            }
            case BOOLEAN -> {
                return String.valueOf(cell.getBooleanCellValue());
            }
            case BLANK -> {
                return "";
            }
            case FORMULA -> {
                return cell.getCellFormula();
            }
            default -> {
                return "";
            }
        }
    }

    public  String mtgrandomNumber(int numberOfDigits) {
        if (numberOfDigits <= 0) {
            throw new IllegalArgumentException("Number of digits must be at least 1");
        }

        Random random = new Random();
        StringBuilder result = new StringBuilder();

        // First digit: must be between 1 and 9 (non-zero)
        result.append(random.nextInt(9) + 1);

        // Remaining digits: can be 0 to 9
        for (int i = 1; i < numberOfDigits; i++) {
            result.append(random.nextInt(10));
        }

        return result.toString();
    }
    public List<List<Object>> parseJsonArray(JSONObject jsonData) {
        // Assuming jsonData is an array of arrays in JSON string form
        List<List<Object>> result = new ArrayList<>();
        JSONArray arr = new JSONArray(jsonData.toString());
        for (int i = 0; i < arr.length(); i++) {
            JSONArray inner = arr.getJSONArray(i);
            List<Object> row = new ArrayList<>();
            for (int j = 0; j < inner.length(); j++) {
                row.add(inner.get(j));
            }
            result.add(row);
        }
        return result;
    }

    public double parseDouble(Object value) {
        try {
            return Double.parseDouble(String.valueOf(value));
        } catch (Exception e) {
            return 0.0;
        }
    }

    public void insertTempExpense(String attendanceId, String title, double amount) {
        String sql = """
        INSERT INTO temp_supervision_details(apiorder_id, expense_id, title, credit, original_amount, url)
        VALUES(:attendanceId, :expenseId, :title, :amount, :amount, '')
    """;
        Map<String, Object> params = Map.of(
                "attendanceId", attendanceId,
                "expenseId", UUID.randomUUID().toString(),
                "title", title,
                "amount", amount
        );
        crudService.executeNativeQuery(sql, params);
    }
    public String validatePhoneNumbers(String phone) {
        if (phone == null || phone.trim().isEmpty()) return "Invalid";
        String normalized = phone.replaceAll("[^\\d]", "");
        if (normalized.length() == 10 || normalized.length() == 12 || normalized.length() == 13) {
            return normalized;
        }
        return "Invalid";
    }
    public boolean validatePhoneNumber(String phone) {
        return phone != null && phone.matches("^\\d{10,13}$");
    }
    public User getLoggedInUser(){
        try {
            SecurityUser securityUser = (SecurityUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            return securityUser.getUser();
        }catch (Exception ex){
            ex.printStackTrace();
        }
        return null;
    }
    public String validatePaymentsPhoneNumber(String msisdn){
        if (msisdn == null || msisdn.trim().isEmpty()) return "Invalid";
        String normalized = msisdn.trim().replaceAll("[^\\d]", "");
        int msisdnLength = normalized.length();
        if (msisdnLength == 10 || msisdnLength == 12 && (normalized.startsWith("2547") || normalized.startsWith("2541"))) {
            return "OK";
        } else if (msisdnLength == 13 && normalized.startsWith("25407")) {
            return "Replace";
            
        } else if (msisdnLength == 13 && normalized.startsWith("25401")) {
            return "Replace1";
            
        }
        return "Invalid";
    }

    public Object[] validateMandatorySUPItems(List<Map<String, Object>> itemsPre) {
        int scount = 0;
        String[] string = new String[5];

        for (Map<String, Object> supItemsPre : itemsPre) {
            Object nameObj = supItemsPre.get("Name");
            if (nameObj == null) continue;

            String name = nameObj.toString().trim();

            switch (name) {
                case "Expense Sheet/Work Ticket":
                case "Work Ticket":
                case "Expense Sheet":
                    string[0] = "Expense Sheet";
                    scount++;
                    break;
                case "Lunch":
                    string[1] = "Lunch";
                    scount++;
                    break;
                case "Per Diem":
                    string[2] = "Per Diem";
                    scount++;
                    break;
                case "Stationery":
                case "Stationary":
                    string[3] = "Stationery";
                    scount++;
                    break;
                case "Fuel Amount":
                case "Transport/Fuel":
                case "Public Means":
                case "Transport":
                    string[4] = "Transport";
                    scount++;
                    break;
            }
        }

        // Required items
        List<String> requiredItems = Arrays.asList("Expense Sheet", "Lunch", "Per Diem", "Stationery", "Transport");

        // Collected items (remove nulls)
        Set<String> collected = new HashSet<>();
        for (String s : string) {
            if (s != null) {
                collected.add(s);
            }
        }

        // Find missing items
        List<String> missing = new ArrayList<>();
        for (String required : requiredItems) {
            if (!collected.contains(required)) {
                missing.add(required);
            }
        }

        String result = String.join(", ", missing);
        Object[] resp = new Object[2];
        resp[0] = scount;
        resp[1] = "(" + result + ")";
        return resp;
    }

    public String checkIfIpIsWhitelisted(HttpServletRequest request, String postData) {
        String ip;

        String forwarded = request.getHeader("X-Forwarded-For");
        if (forwarded == null || forwarded.length() == 0 || forwarded.equals("0")) {
            ip = request.getRemoteAddr();
        }else {
            ip = forwarded;
        }
        //fetch allowed ips
        String sql = "SELECT `value` FROM PARAM WHERE parameter='SOURCE_ITEMS'";
        List<String> results = crudService.fetchWithNativeQuery(sql, new HashMap<>(), 0, 1);
        if (results != null && !results.isEmpty()) {
            String sourceItems = results.get(0);
            if (sourceItems != null && sourceItems.contains(ip)) {
                return "OK";
            }
        }
        auditAction("AUDIT", "A request received from invalid" +ip, ip, postData, null, null, null);
        return "Failed";
    }
    public boolean startsWithAny(String value, String[] prefixes) {
        if (value == null || prefixes == null) return false;
        for (String prefix : prefixes) {
            if (value.startsWith(prefix)) return true;
        }
        return false;
    }
    public String makeGetRequest(String endpoint) {
        StringBuilder response = new StringBuilder();
        log.info("endpoint: {}", endpoint);
        try {
            URL url = new URL(endpoint);
            URLConnection urlConnection = url.openConnection();
            try (BufferedReader in = new BufferedReader(new InputStreamReader(urlConnection.getInputStream()))) {
                String inputLine;
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
            }
            log.info("Response: {}", response);
            return response.toString();
        } catch (Exception ex) {
            log.error("Error making GET request to [{}]: {}", endpoint, ex.getMessage(), ex);
            return null;
        }
    }











}
