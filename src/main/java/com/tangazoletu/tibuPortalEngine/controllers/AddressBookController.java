package com.tangazoletu.tibuPortalEngine.controllers;

import com.tangazoletu.tibuPortalEngine.dto.*;
import com.tangazoletu.tibuPortalEngine.repositories.AddressBookRepository;
import com.tangazoletu.tibuPortalEngine.repositories.AddressContactRepository;
import com.tangazoletu.tibuPortalEngine.service.AddressBookService;
import com.tangazoletu.tibuPortalEngine.service.AddressContactService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/test/address")
@Slf4j
@RequiredArgsConstructor
public class AddressBookController {
    @Autowired
    private AddressBookRepository addressBookRepository;
    @Autowired
    private AddressContactService addressContactService;
    @Autowired
    AddressBookService addressBookService;

    @PostMapping(value = "/books", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<AddressBookResponseResponse> listAddressBooks(
            @RequestBody(required = false) AddressBookRequest request
    ) {
        try {
            Page<AddressBookDTO> page = addressBookService.getFilteredBooks(
                    request != null ? request : new AddressBookRequest()
            );

            List<AddressBookDTO> content = page.getContent();

            // Use LinkedHashMap to maintain insertion order
            Map<String, Object> data = new LinkedHashMap<>();
            data.put("content", content);

            Map<String, Object> pageInfo = new LinkedHashMap<>();
            pageInfo.put("size", page.getSize());
            pageInfo.put("number", page.getNumber());
            pageInfo.put("totalElements", page.getTotalElements());
            pageInfo.put("totalPages", page.getTotalPages());

            data.put("page", pageInfo);

            AddressBookResponseResponse response = new AddressBookResponseResponse();
            response.setResponseCode("00");
            response.setResponseMessage("Success");
            response.setData(data);
            response.setAddressBook(null); // Optional

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            e.printStackTrace();

            AddressBookResponseResponse errorResponse = new AddressBookResponseResponse();
            errorResponse.setResponseCode("01");
            errorResponse.setResponseMessage("Internal Server Error");
            errorResponse.setData(null);
            errorResponse.setAddressBook(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }


    @Autowired
    private AddressContactRepository addressContactRepository;
    @PostMapping(value = "/contacts", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<AddressBookResponseResponse> listAddressContacts(
            @RequestBody(required = false) AddressContactRequest request,
            Pageable pageable
    ) {
        try {
            Page<AddressContactDTO> page = addressContactService.getFilteredContacts(
                    request != null ? request : new AddressContactRequest(), pageable
            );

            List<AddressContactDTO> content = page.getContent();

            // Use LinkedHashMap to preserve insertion order: content first, then page
            Map<String, Object> data = new LinkedHashMap<>();
            data.put("content", content);

            Map<String, Object> pageInfo = new LinkedHashMap<>();
            pageInfo.put("size", page.getSize());
            pageInfo.put("number", page.getNumber());
            pageInfo.put("totalElements", page.getTotalElements());
            pageInfo.put("totalPages", page.getTotalPages());

            data.put("page", pageInfo);

            // Construct response
            AddressBookResponseResponse response = new AddressBookResponseResponse();
            response.setResponseCode("00");
            response.setResponseMessage("Success");
            response.setData(data);
            response.setAddressBook(null); // Optional

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            e.printStackTrace();

            AddressBookResponseResponse errorResponse = new AddressBookResponseResponse();
            errorResponse.setResponseCode("01");
            errorResponse.setResponseMessage("Internal Server Error");
            errorResponse.setData(null);
            errorResponse.setAddressBook(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }


}
