package com.tangazoletu.tibuPortalEngine.controllers;

import com.tangazoletu.tibuPortalEngine.dto.*;
import com.tangazoletu.tibuPortalEngine.dto.batchdetailsdto.BatchDetailsResponse;
import com.tangazoletu.tibuPortalEngine.dto.paymentapidto.PaymentTimelineResponse;
import com.tangazoletu.tibuPortalEngine.dto.paymentapidto.PendingWebInitiatedPaymentsFilterDTO;
import com.tangazoletu.tibuPortalEngine.entities.ApiOrder;
import com.tangazoletu.tibuPortalEngine.entities.ApiOrderApprovalSummaryView;
import com.tangazoletu.tibuPortalEngine.service.ApiOrderService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/test/approval")
@Slf4j
@RequiredArgsConstructor
public class ApiOderApprovalController {
    private final ApiOrderService apiOrderService;
    @PostMapping(value = "/ctcl")
    public FinanceApprovalResponse getApprovals(@RequestBody ApiOrderApprovalRequest approvalRequest, HttpServletResponse response, HttpServletRequest request) {
        try {
            return apiOrderService.getApiOrderApproval(approvalRequest,response,request);

        }catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    @PostMapping(value = "batch", produces = MediaType.APPLICATION_JSON_VALUE)
    public Page<Map<String, Object>> getPendingApiOrderApproval(@RequestBody ApiOrderApprovalRequest approvalRequest, HttpServletResponse response, HttpServletRequest request) {
        try {
            log.info("getPendingApiOrderApproval {}",  request.toString());
            return apiOrderService.getPendingApiOrders(approvalRequest,response,request);

        }catch (Exception e) {
            e.printStackTrace();
            log.error("Failed to fetch Pending API Order Approvals: {}", e.getMessage(), e);
            return null;
        }
    }
    @PostMapping(value = "/batchdetails", produces = MediaType.APPLICATION_JSON_VALUE)
    public BatchDetailsResponse getBatchDetails(@RequestBody ViewBatchDetailsRequest viewBatchDetailsRequest, HttpServletResponse response, HttpServletRequest request) {
        try {
            log.info("getBatchDetails {}",  viewBatchDetailsRequest.toString());

            return apiOrderService.getBatchDetails(viewBatchDetailsRequest,response,request);

        }catch (Exception e) {
            log.error("Failed to fetch batch details/items API Order Approvals: {}", e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            e.printStackTrace();
            return null;

        }
    }
    @PostMapping(value = "/approval", produces = MediaType.APPLICATION_JSON_VALUE)
    public BatchResponse getApproval(@RequestBody CountyBatchRequest countyBatchRequest, HttpServletResponse response, HttpServletRequest request) {
        try {
            log.info("getBatchDetails {}",  countyBatchRequest.toString());

            return apiOrderService.getBatchApprovalPerLevelAndType(countyBatchRequest,response,request);

        }catch (Exception e) {
            log.error("Failed to fetch batch details/items API Order Approvals: {}", e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            e.printStackTrace();
            return null;

        }
    }
    @PostMapping(value = "/lineitems", produces = MediaType.APPLICATION_JSON_VALUE)
    public LineItemsResponse getLineItems(@RequestBody ReportRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getLineItems {}", request);
            return apiOrderService.getLineItems(request,httpServletRequest,httpServletResponse);

        }catch (Exception e){
            httpServletResponse.setStatus(500);
            log.error("An error occurred {}",e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    @PostMapping(value = "/fundallocation", produces = MediaType.APPLICATION_JSON_VALUE)
    public FundAllocationResponse getFundsAllocation(@RequestBody FundAllocationRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getFundsAllocation {}", request);
            return apiOrderService.fundAllocationResponse(request,httpServletRequest,httpServletResponse);

        }catch (Exception e){
            httpServletResponse.setStatus(500);
            log.error("An error occurred {}",e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    @PostMapping(value = "/web_initiate", produces = MediaType.APPLICATION_JSON_VALUE)
    public FinanceApprovalResponse getPendingWebInitiated(@RequestBody PendingWebInitiatedPaymentsFilterDTO request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getWebInitiated {}", request);
            return apiOrderService.getWebInitiatePendingPayments(request,httpServletRequest,httpServletResponse);

        }catch (Exception e){
            httpServletResponse.setStatus(500);
            log.error("An error occurred {}",e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    @PostMapping(value = "/payment_timeline", produces = MediaType.APPLICATION_JSON_VALUE)
    public PaymentTimelineResponse getPaymentTimelines(@RequestBody PaymentTimelineRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getPaymentTimelines {}", request);
            return apiOrderService.getPaymentTimelines(request,httpServletRequest,httpServletResponse);

        }catch (Exception e){
            httpServletResponse.setStatus(500);
            log.error("An error occurred {}",e.getMessage());
            e.printStackTrace();
            return null;
        }
    }


}
