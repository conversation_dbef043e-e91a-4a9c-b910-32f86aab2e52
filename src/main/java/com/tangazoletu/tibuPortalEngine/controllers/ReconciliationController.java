package com.tangazoletu.tibuPortalEngine.controllers;

import com.tangazoletu.tibuPortalEngine.dto.ReconciliationRequest;
import com.tangazoletu.tibuPortalEngine.dto.ReconciliationResponse;
import com.tangazoletu.tibuPortalEngine.dto.UserReportsRequest;
import com.tangazoletu.tibuPortalEngine.dto.UserResponse;
import com.tangazoletu.tibuPortalEngine.service.PaymentsService;
import com.tangazoletu.tibuPortalEngine.service.ReconciliationService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/test/reconciliation")
@RequiredArgsConstructor
@Slf4j
public class ReconciliationController {

    private final ReconciliationService reconciliationService;

    @PostMapping(value = "/gateway", produces = MediaType.APPLICATION_JSON_VALUE)
    public ReconciliationResponse getGateway(@RequestBody ReconciliationRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        try {
            log.info("getGateway request {}", request);

            return reconciliationService.getGateway(request, response, httpServletRequest);

        }catch (Exception e){
            log.error("getGateway error {}, request {}", e, request);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            return null;


        }

    }
    @PostMapping(value = "/gatewaynotmpesa", produces = MediaType.APPLICATION_JSON_VALUE)
    public ReconciliationResponse getGatewayNotMpesa(@RequestBody ReconciliationRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        try {
            log.info("getGatewayNotMpesa request {}", request);

            return reconciliationService.getGatewayNotMpesa(request, response, httpServletRequest);

        }catch (Exception e){
            log.error("getGatewayNotMpesa error {}, request {}", e, request);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            return null;


        }

    }
    @PostMapping(value = "/mpesanotgateway", produces = MediaType.APPLICATION_JSON_VALUE)
    public ReconciliationResponse getMpesaNotGateway(@RequestBody ReconciliationRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        try {
            log.info("getMpesaNotGateway request {}", request);

            return reconciliationService.getMpesaNotGateway(request, response, httpServletRequest);

        }catch (Exception e){
            log.error("gatewayMpesaNotGateway error {}, request {}", e, request);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            return null;


        }

    }
    @PostMapping(value = "/supervisionexpenses", produces = MediaType.APPLICATION_JSON_VALUE)
    public ReconciliationResponse getSupervisionExpenses(@RequestBody ReconciliationRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        try {
            log.info("getSupervisionExpenses request {}", request);

            return reconciliationService.getSupervisionExpenses(request, response, httpServletRequest);

        }catch (Exception e){
            log.error("getSupervisionExpenses error {}, request {}", e, request);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            return null;


        }

    }
    @PostMapping(value = "/approvalturnaround", produces = MediaType.APPLICATION_JSON_VALUE)
    public ReconciliationResponse getApprovalTurnAroundTime(@RequestBody ReconciliationRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        try {
            log.info("getApprovalTurnAroundTime request {}", request);

            return reconciliationService.getApprovalTurnroundTime(request, response, httpServletRequest);

        }catch (Exception e){
            log.error("getApprovalTurnAroundTime error {}, request {}", e, request);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            return null;


        }

    }
    @PostMapping(value = "/mpesauploads", produces = MediaType.APPLICATION_JSON_VALUE)
    private ReconciliationResponse getMpesaUploads(@RequestBody ReconciliationRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getMpesaUploads request: {}", request);
            return reconciliationService.getMpesaUploads(request, httpServletResponse, httpServletRequest);

        }catch (Exception e) {
            log.error("An error occurred {} request {}",e.getMessage(), request.toString());
            e.printStackTrace();
            httpServletResponse.setStatus(500);
            return null;
        }
    }
    @PostMapping(value = "/allmpesarecords", produces = MediaType.APPLICATION_JSON_VALUE)
    private ReconciliationResponse getAllMpesaRecords(@RequestBody ReconciliationRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getAllMpesaRecords request: {}", request);
            return reconciliationService.getMpesaActualPayments(request, httpServletResponse, httpServletRequest);

        }catch (Exception e) {
            log.error("An error occurred {} request {}",e.getMessage(), request.toString());
            e.printStackTrace();
            httpServletResponse.setStatus(500);
            return null;
        }
    }
}
