package com.tangazoletu.tibuPortalEngine.controllers;

import com.tangazoletu.tibuPortalEngine.dto.FinancierRequest;
import com.tangazoletu.tibuPortalEngine.dto.FinancierResponse;
import com.tangazoletu.tibuPortalEngine.service.FinancierService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@RestController
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/test/financers")
@Slf4j
@RequiredArgsConstructor
public class FinanciersController {
    private final FinancierService financierService;

    @PostMapping(value = "/financers", produces = MediaType.APPLICATION_JSON_VALUE)
    public FinancierResponse fetch(@Valid @RequestBody FinancierRequest financierRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {

        try {
            log.info("fetch new financier {}"  ,  financierRequest);

            return financierService.findAll(financierRequest,  httpServletRequest, httpServletResponse);

        }catch (Exception e){
            httpServletResponse.setStatus(500);
            log.error("An error occurred {} request {}",e.getMessage(), financierRequest);
            e.printStackTrace();
            return null;
        }


    }
}
