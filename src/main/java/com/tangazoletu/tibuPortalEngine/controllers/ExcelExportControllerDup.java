package com.tangazoletu.tibuPortalEngine.controllers;

import com.tangazoletu.tibuPortalEngine.dto.AddressBookDTO;
import com.tangazoletu.tibuPortalEngine.dto.AddressBookRequest;
import com.tangazoletu.tibuPortalEngine.dto.ExcelExportRequest;
import com.tangazoletu.tibuPortalEngine.service.AddressBookService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@RestController
@RequestMapping("/api/exportsdup")
public class ExcelExportControllerDup {
    @Autowired
    private AddressBookService addressBookService; // Inject your service

    // Request DTO class - now includes both fileName and filter parameters

    public static class FileNameRequest {
        private String fileName;

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }
    }

    @PostMapping("/export-excel")
    public ResponseEntity<byte[]> exportToExcel(@RequestBody ExcelExportRequest request) throws Exception {

        // Get filename from request payload
        String fileName = request.getFileName();
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("fileName is required");
        }

        // Get data using your existing service method
        AddressBookRequest filterRequest = request.getFilterRequest() != null ?
                request.getFilterRequest() : new AddressBookRequest();

        Page<AddressBookDTO> page = addressBookService.getFilteredBooks(filterRequest);
        List<AddressBookDTO> content = page.getContent();

        if (content.isEmpty()) {
            throw new RuntimeException("No data found to export");
        }

        // Create filename with date
        String fileNameWithDate = fileName + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        // Create workbook
        XSSFWorkbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet(fileName);

        try {
            // Get field names from the DTO for headers
            List<String> fieldNames = getFieldNames(content.get(0));
            List<String> displayNames = getDisplayNames(fieldNames);

            // Create headers (row 7, 0-indexed = row 8 in Excel)
            Row headerRow = sheet.createRow(7);
            int headerIndex = 0;

            for (String displayName : displayNames) {
                headerRow.createCell(headerIndex++).setCellValue(displayName);
            }

            // Style headers
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);

            for (Cell cell : headerRow) {
                cell.setCellStyle(headerStyle);
            }

            // Add data rows
            int rowIndex = 8; // Start from row 9 (0-indexed = 8)
            for (AddressBookDTO dto : content) {
                Row dataRow = sheet.createRow(rowIndex++);
                int cellIndex = 0;

                for (String fieldName : fieldNames) {
                    Cell cell = dataRow.createCell(cellIndex++);
                    Object value = getFieldValue(dto, fieldName);

                    if (value == null) {
                        cell.setCellValue("");
                    } else if (value instanceof LocalDateTime) {
                        // Format LocalDateTime
                        LocalDateTime dateTime = (LocalDateTime) value;
                        String formattedDate = dateTime.format(DateTimeFormatter.ofPattern("d'%s' MMM yyyy h:mm"));
                        cell.setCellValue(formatOrdinalDate(formattedDate, dateTime.getDayOfMonth()));
                    } else if (value instanceof java.sql.Timestamp) {
                        // Format Timestamp
                        java.sql.Timestamp timestamp = (java.sql.Timestamp) value;
                        String formattedDate = timestamp.toLocalDateTime()
                                .format(DateTimeFormatter.ofPattern("d'%s' MMM yyyy h:mm"));
                        cell.setCellValue(formatOrdinalDate(formattedDate, timestamp.toLocalDateTime().getDayOfMonth()));
                    } else if (value instanceof Number) {
                        // Handle numeric values
                        cell.setCellValue(((Number) value).doubleValue());
                    } else {
                        cell.setCellValue(value.toString());
                    }
                }
            }

            // Add image if exists
            addImageToSheet(workbook, sheet, 0, 0);

            // Auto-size columns
            for (int i = 0; i < fieldNames.size(); i++) {
                sheet.autoSizeColumn(i);
            }

            // Set workbook properties
            workbook.getProperties().getCoreProperties().setCreator("Tangazo Letu");
            workbook.getProperties().getCoreProperties().setTitle(fileName);
            workbook.getProperties().getCoreProperties().setDescription("Tangazoletu Bulk SMS Service Reports.");

            // Convert to byte array - using try-with-resources for proper cleanup
            byte[] excelBytes;
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                workbook.write(outputStream);
                outputStream.flush();
                excelBytes = outputStream.toByteArray();
            }

            // Return response as downloadable Excel file
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));
            headers.setContentDispositionFormData("attachment", fileNameWithDate + ".xlsx");
            headers.setContentLength(excelBytes.length);
            headers.setCacheControl("no-cache, no-store, must-revalidate");
            headers.setPragma("no-cache");
            headers.setExpires(0);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(excelBytes);

        } finally {
            workbook.close();
        }
    }

    /**
     * Get field names from the DTO using reflection
     * Skips fields containing "id", "Id", or "ID"
     */
    private List<String> getFieldNames(AddressBookDTO dto) {
        List<String> fieldNames = new ArrayList<>();
        Field[] fields = dto.getClass().getDeclaredFields();

        for (Field field : fields) {
            String fieldName = field.getName();

            // Skip fields you don't want to export
            if (!fieldName.equals("primarykey") &&
                    !fieldName.equals("serialVersionUID") &&
                    !fieldName.startsWith("$") &&
                    !fieldName.toLowerCase().contains("id")) { // Skip any field containing "id" (case-insensitive)
                fieldNames.add(fieldName);
            }
        }

        return fieldNames;
    }

    /**
     * Convert field names to display names
     */
    private List<String> getDisplayNames(List<String> fieldNames) {
        List<String> displayNames = new ArrayList<>();

        for (String fieldName : fieldNames) {
            // Convert camelCase to Display Name
            String displayName = convertCamelCaseToDisplayName(fieldName);
            displayNames.add(displayName);
        }

        return displayNames;
    }

    /**
     * Convert camelCase field names to readable display names
     */
    private String convertCamelCaseToDisplayName(String fieldName) {
        // Handle specific mappings first
        Map<String, String> fieldMappings = new HashMap<>();
        fieldMappings.put("addressBook", "AddressBook");
        fieldMappings.put("organization", "Organization");
        fieldMappings.put("totalContacts", "Total Contacts");
        fieldMappings.put("addressType", "Address Type");
        fieldMappings.put("timeCreated", "Time Created");
        fieldMappings.put("orgName", "Organization");
        fieldMappings.put("title", "AddressBook");

        if (fieldMappings.containsKey(fieldName)) {
            return fieldMappings.get(fieldName);
        }

        // Generic camelCase to Display Name conversion
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < fieldName.length(); i++) {
            char c = fieldName.charAt(i);
            if (i > 0 && Character.isUpperCase(c)) {
                result.append(" ");
            }
            if (i == 0) {
                result.append(Character.toUpperCase(c));
            } else {
                result.append(c);
            }
        }

        return result.toString();
    }

    /**
     * Get field value from DTO using reflection
     */
    private Object getFieldValue(AddressBookDTO dto, String fieldName) {
        try {
            Field field = dto.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(dto);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            // Try getter method as fallback
            try {
                String getterName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                return dto.getClass().getMethod(getterName).invoke(dto);
            } catch (Exception ex) {
                System.err.println("Could not get value for field: " + fieldName);
                return null;
            }
        }
    }

    private String formatColumnName(String columnName) {
        return capitalizeWords(columnName.replace("_", " ").toLowerCase());
    }

    private String capitalizeWords(String text) {
        String[] words = text.split(" ");
        StringBuilder result = new StringBuilder();

        for (int i = 0; i < words.length; i++) {
            if (i > 0) result.append(" ");
            if (words[i].length() > 0) {
                result.append(Character.toUpperCase(words[i].charAt(0)));
                if (words[i].length() > 1) {
                    result.append(words[i].substring(1));
                }
            }
        }

        return result.toString();
    }

    private String formatOrdinalDate(String template, int day) {
        String suffix;
        if (day >= 11 && day <= 13) {
            suffix = "th";
        } else {
            switch (day % 10) {
                case 1:
                    suffix = "st";
                    break;
                case 2:
                    suffix = "nd";
                    break;
                case 3:
                    suffix = "rd";
                    break;
                default:
                    suffix = "th";
                    break;
            }
        }
        return template.replace("%s", suffix);
    }

    private void addImageToSheet(XSSFWorkbook workbook, Sheet sheet, int row, int col) {
        try (InputStream imageStream = getClass().getResourceAsStream("/TIBU.png")) {
            if (imageStream == null) {
                System.err.println("Image not found: /TIBU.png");
                return;
            }

            byte[] imageBytes = IOUtils.toByteArray(imageStream);
            int pictureIndex = workbook.addPicture(imageBytes, Workbook.PICTURE_TYPE_JPEG);

            // Read image dimensions
            BufferedImage bufferedImage = ImageIO.read(new ByteArrayInputStream(imageBytes));
            int imageWidth = bufferedImage.getWidth();
            int imageHeight = bufferedImage.getHeight();

            // Estimate Excel column/row sizes in pixels (these are rough but better than earlier)
            double excelColWidthPx = 64.0;  // each column is roughly 64px wide
            double excelRowHeightPx = 20.0; // each row is roughly 20px tall

            // Target image size in Excel cells
            int colSpan = (int) Math.round(imageWidth / excelColWidthPx);
            int rowSpan = (int) Math.round(imageHeight / excelRowHeightPx);

            // Anchor to set the image on the sheet
            XSSFDrawing drawing = (XSSFDrawing) sheet.createDrawingPatriarch();
            XSSFClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, col, row, col + colSpan, row + rowSpan);

            drawing.createPicture(anchor, pictureIndex);

            // Optional: Autosize affected columns for better layout
            for (int i = col; i < col + colSpan; i++) {
                sheet.autoSizeColumn(i);
            }

        } catch (IOException e) {
            System.err.println("Could not add image to Excel: " + e.getMessage());
        }
    }
}
