package com.tangazoletu.tibuPortalEngine.controllers;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tangazoletu.tibuPortalEngine.dto.PaymentAPiResponse;
import com.tangazoletu.tibuPortalEngine.dto.paymentapidto.NonSupervisionPaymentRequestDTO;
import com.tangazoletu.tibuPortalEngine.dto.paymentapidto.PaymentRequestDTO;
import com.tangazoletu.tibuPortalEngine.dto.paymentapidto.PaymentRequestResponse;
import com.tangazoletu.tibuPortalEngine.dto.paymentapidto.SupervisionRequestDto;
import com.tangazoletu.tibuPortalEngine.service.PaymentApiService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/test")
@RequiredArgsConstructor
@Slf4j
public class PaymentApiController {


    private final PaymentApiService paymentApiService;

    @PostMapping("/supervision")
    public PaymentAPiResponse postSupervision(
            @ModelAttribute SupervisionRequestDto supervisionRequestDto,
            HttpServletRequest request,
            HttpServletResponse response
    ) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();

            List<Map<String, Object>> driverDetails = objectMapper.readValue(
                    supervisionRequestDto.getDriver_details(),
                    new TypeReference<List<Map<String, Object>>>() {}
            );
            supervisionRequestDto.setSup_driver_details(driverDetails);

            List<Map<String, Object>> items = objectMapper.readValue(
                    supervisionRequestDto.getItems(),
                    new TypeReference<List<Map<String, Object>>>() {}
            );
            supervisionRequestDto.setSupItems(items);

            // Now you can use these parsed lists
            log.info("Parsed driverDetails: {}", driverDetails);
            log.info("Parsed items: {}", items);

            // You can also set them back to a custom DTO if needed

            return paymentApiService.processSupervision(supervisionRequestDto, request, response);

        } catch (Exception e) {
            log.error("Error parsing supervision data", e);
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            return new PaymentAPiResponse("0", "400", "Failed to process supervision data.");
        }
    }


    @GetMapping(value = "/supervision")
    public PaymentAPiResponse getSupervision(@ModelAttribute SupervisionRequestDto supervisionRequestDto, HttpServletRequest request, HttpServletResponse httpServletResponse) {
        try {

            log.info("getsupervision",  supervisionRequestDto.toString());
            ObjectMapper objectMapper = new ObjectMapper();

            List<Map<String, Object>> driverDetails = objectMapper.readValue(
                    supervisionRequestDto.getDriver_details(),
                    new TypeReference<List<Map<String, Object>>>() {}
            );
            supervisionRequestDto.setSup_driver_details(driverDetails);


            List<Map<String, Object>> items = objectMapper.readValue(
                    supervisionRequestDto.getItems(),
                    new TypeReference<List<Map<String, Object>>>() {}
            );
            supervisionRequestDto.setSupItems(items);

            // Now you can use these parsed lists
            log.info("Parsed driverDetails: {}", driverDetails);
            log.info("Parsed items: {}", items);
            return paymentApiService.processSupervision(supervisionRequestDto,request,httpServletResponse);


        }catch (Exception e){
            log.error("An error occurred on supervison api",e.getMessage());
            httpServletResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            return new  PaymentAPiResponse(
                    "0",
                    "500",
                    "An Error Occured try again"
            );
        }
    }
    @GetMapping(value = "/payment")
    public PaymentRequestResponse getPaymentMdr(@ModelAttribute PaymentRequestDTO paymentRequestDTO, HttpServletRequest request, HttpServletResponse httpServletResponse) {
        try {

            log.info("getPaymentMdr post",  paymentRequestDTO.toString());
            ObjectMapper objectMapper = new ObjectMapper();

            List<Map<String, Object>> notes = objectMapper.readValue(
                    paymentRequestDTO.getNotes(),
                    new TypeReference<List<Map<String, Object>>>() {}
            );


            List<Map<String, Object>> items = objectMapper.readValue(
                    paymentRequestDTO.getItems(),
                    new TypeReference<List<Map<String, Object>>>() {}
            );
            paymentRequestDTO.setSupItems(items);

            // Now you can use these parsed lists
            log.info("Parsed notes: {}", notes);
            log.info("Parsed items: {}", items);
            return paymentApiService.getPaymentMdr(paymentRequestDTO,request,httpServletResponse);


        }catch (Exception e){
            log.error("An error occurred on payment api",e.getMessage());
            httpServletResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            return new  PaymentRequestResponse(
                    "0",
                    "500",
                    "An Error Occured try again",
                    "Fail",
                    ""
            );
        }
    }
    @PostMapping(value = "/payment")
    public PaymentRequestResponse postPaymentMdr(@ModelAttribute PaymentRequestDTO paymentRequestDTO, HttpServletRequest request, HttpServletResponse httpServletResponse) {
        try {

            log.info("getPaymentMdr post",  paymentRequestDTO.toString());
            ObjectMapper objectMapper = new ObjectMapper();


            List<Map<String, Object>> items = objectMapper.readValue(
                    paymentRequestDTO.getItems(),
                    new TypeReference<List<Map<String, Object>>>() {}
            );
            paymentRequestDTO.setSupItems(items);

            // Now you can use these parsed lists
            log.info("Parsed notes: {}", paymentRequestDTO.getNotes());
            log.info("Parsed items: {}", items);
            return paymentApiService.getPaymentMdr(paymentRequestDTO,request,httpServletResponse);


        }catch (Exception e){
            log.error("An error occurred on payment api",e.getMessage());
            httpServletResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            e.printStackTrace();
            return new  PaymentRequestResponse(
                    "0",
                    "500",
                    "An Error Occured try again",
                    "Fail",
                    ""
            );

        }
    }
    @GetMapping(value = "/nonsupervision")
    public PaymentAPiResponse getNonSupervision(
            @ModelAttribute NonSupervisionPaymentRequestDTO paymentRequestDTO,
            HttpServletRequest request,
            HttpServletResponse httpServletResponse) {

        try {
            log.info("Received NonSupervision payment request: {}", paymentRequestDTO.toString());

            ObjectMapper objectMapper = new ObjectMapper();

            // Parse the `items` JSON string
            List<Map<String, Object>> items = objectMapper.readValue(
                    paymentRequestDTO.getItems(),
                    new TypeReference<List<Map<String, Object>>>() {}
            );

            paymentRequestDTO.setSupItems(items);

            log.info("Parsed supItems: {}", items);

            return paymentApiService.getNonSupervisionPayments(paymentRequestDTO, request, httpServletResponse);

        } catch (Exception e) {
            log.error("An error occurred in nonsupervision API", e);

            httpServletResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            e.printStackTrace();
            return new PaymentAPiResponse(
                    "0",
                    "500",
                    "An Error Occurred. Try again"
            );
        }
    }
    @PostMapping(value = "/nonsupervision")
    public PaymentAPiResponse getNonSupervisionPayments(
            @ModelAttribute NonSupervisionPaymentRequestDTO paymentRequestDTO,
            HttpServletRequest request,
            HttpServletResponse httpServletResponse) {

        try {
            log.info("Received NonSupervision payment request post: {}", paymentRequestDTO.toString());

            ObjectMapper objectMapper = new ObjectMapper();

            // Parse the `items` JSON string
            List<Map<String, Object>> items = objectMapper.readValue(
                    paymentRequestDTO.getItems(),
                    new TypeReference<List<Map<String, Object>>>() {}
            );

            paymentRequestDTO.setSupItems(items);

            log.info("Parsed supItems: {}", items);

            return paymentApiService.getNonSupervisionPayments(paymentRequestDTO, request, httpServletResponse);

        } catch (Exception e) {
            log.error("An error occurred in nonsupervision API", e);

            httpServletResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);

            return new PaymentAPiResponse(
                    "0",
                    "500",
                    "An Error Occurred. Try again"
            );
        }
    }

}
