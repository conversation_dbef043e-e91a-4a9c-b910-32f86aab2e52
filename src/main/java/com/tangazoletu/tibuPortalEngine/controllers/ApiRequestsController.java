package com.tangazoletu.tibuPortalEngine.controllers;

import com.tangazoletu.tibuPortalEngine.dto.ApiDto;
import com.tangazoletu.tibuPortalEngine.dto.ApiResponse;
import com.tangazoletu.tibuPortalEngine.enums.ApiResponseCode;
import com.tangazoletu.tibuPortalEngine.exceptions.EncryptionException;
import com.tangazoletu.tibuPortalEngine.service.ApiService;
import com.tangazoletu.tibuPortalEngine.service.UserDetailsImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;


@RestController
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api")
@Slf4j
@RequiredArgsConstructor
public class ApiRequestsController {

    @Autowired
    ApiService apiService;
    @PostMapping(value = "/process", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse processRequest(
            @RequestBody ApiDto requestData,
            @AuthenticationPrincipal UserDetails userDetails,
            HttpServletRequest servletRequest,
            HttpServletResponse servletResponse
    ) throws EncryptionException {
        log.info("Received requestData: {}", requestData);
        // File data is inside requestData.getFile() as string
        return apiService.processRequest(requestData, ((UserDetailsImpl) userDetails).getApiUser(), servletRequest, servletResponse);
    }
}
