package com.tangazoletu.tibuPortalEngine.controllers;

import com.tangazoletu.tibuPortalEngine.dto.*;
import com.tangazoletu.tibuPortalEngine.entities.User;
import com.tangazoletu.tibuPortalEngine.entities.UserView;
import com.tangazoletu.tibuPortalEngine.enums.ApiResponseCode;
import com.tangazoletu.tibuPortalEngine.exceptions.EncryptionException;
import com.tangazoletu.tibuPortalEngine.exceptions.ResourceNotFoundException;
import com.tangazoletu.tibuPortalEngine.repositories.UserRepository;
import com.tangazoletu.tibuPortalEngine.repositories.UserViewRepository;
import com.tangazoletu.tibuPortalEngine.service.UserService;
import com.tangazoletu.tibuPortalEngine.service.RoleService;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import org.springframework.web.bind.annotation.*;

@RestController
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/test/users")
@Slf4j
@RequiredArgsConstructor
public class UserController {

    private final RoleService roleService;

    @Autowired
    private UserViewRepository userRepository;
    @Autowired
    private UserService userService;

    @Autowired
    private HttpServletRequest httpServletRequest;
    @Autowired
    private HttpServletResponse httpServletResponse;

    private UserDTO convertToDTO(UserView user) {
        UserDTO dto = new UserDTO();
        dto.setId(user.getId());
        dto.setUsername(user.getUsername());
        dto.setFirstname(user.getFirstname());
        dto.setLastname(user.getLastname());
        dto.setInTrash(user.getInTrash());
        dto.setRoles(user.getRoles());
        dto.setOrganisation(user.getOrganisation());
        dto.setStatus(user.getStatus());
        // Set other fields
        return dto;
    }

    @GetMapping(value = "")
    public Page<UserDTO> listUsers(
            Pageable pageable,
            @RequestParam(required = false) String user,
            @RequestParam(required=false) String in_trash
    ) throws EncryptionException {
        if (user != null) {
            return userRepository.findByEmailOrLoginNonExactMatch(user, pageable)
                    .map(UserDTO::new);
        } if (in_trash != null) {
            return userRepository.findInTrash(in_trash, pageable).map(UserDTO::new);
        } else {
            in_trash = "No";
            return userRepository.findInTrash(in_trash, pageable).map(UserDTO::new);
        }
    }
    @PostMapping("/deactivated")
    public ResponseEntity<deactuvatedUserResponse<Page<UserDTO>>> listDeactivatedUsers(Pageable pageable) {
        Page<UserDTO> deactivatedUsers = userRepository.findeactivated(pageable)
                .map(UserDTO::new);

        deactuvatedUserResponse<Page<UserDTO>> response = new deactuvatedUserResponse<>(
                "00",
                "Success",
                deactivatedUsers
        );

        return ResponseEntity.ok(response);
    }


    @GetMapping("/{id}")
    public ResponseEntity<UserDTO> getUserById(@PathVariable Long id) {
        return userRepository.findById(id)
                .map(user -> ResponseEntity.ok(new UserDTO(user)))
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
    }

    @PostMapping("/view")
    public UserResponse getUsers(@RequestBody UserRequest request) {
        return userService.getUser(request);
    }
    @PostMapping(value = "/role", produces = MediaType.APPLICATION_JSON_VALUE)
    private RoleResponse getRoles(@RequestBody RolePermissionRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getRoles request: {}", request);
            return roleService.getRoles(request, httpServletRequest, httpServletResponse);

        }catch (Exception e) {
            log.error("An error occurred {} request {}",e.getMessage(), request.toString());
            e.printStackTrace();
            httpServletResponse.setStatus(500);
            return null;
        }
    }
    @PostMapping(value = "/auditTrail", produces = MediaType.APPLICATION_JSON_VALUE)
    private UserResponse getAuditTrail(@RequestBody UserReportsRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getAuditTrail request: {}", request);
            return userService.getAuditTrail(request, httpServletResponse, httpServletRequest);

        }catch (Exception e) {
            log.error("An error occurred {} request {}",e.getMessage(), request.toString());
            e.printStackTrace();
            httpServletResponse.setStatus(500);
            return null;
        }
    }
    @PostMapping(value = "/systemparams", produces = MediaType.APPLICATION_JSON_VALUE)
    private UserResponse getSystemParams(@RequestBody UserReportsRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getSystemParams request: {}", request);
            return userService.getApiParam(request, httpServletResponse, httpServletRequest);

        }catch (Exception e) {
            log.error("An error occurred {} request {}",e.getMessage(), request.toString());
            e.printStackTrace();
            httpServletResponse.setStatus(500);
            return null;
        }
    }
    @PostMapping(value = "/b2accountsettings", produces = MediaType.APPLICATION_JSON_VALUE)
    private UserResponse getB2AccountSettings(@RequestBody UserReportsRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getB2CAccountSettings request: {}", request);
            return userService.getB2AccountSettings(request, httpServletResponse, httpServletRequest);

        }catch (Exception e) {
            log.error("An error occurred {} request {}",e.getMessage(), request.toString());
            e.printStackTrace();
            httpServletResponse.setStatus(500);
            return null;
        }
    }
    @PostMapping(value = "/permissions", produces = MediaType.APPLICATION_JSON_VALUE)
    private UserResponse getPermissions(@RequestBody UserReportsRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getpermissions request: {}", request);
            return userService.getPermissions(request, httpServletResponse, httpServletRequest);

        }catch (Exception e) {
            log.error("An error occurred {} request {}",e.getMessage(), request.toString());
            e.printStackTrace();
            httpServletResponse.setStatus(500);
            return null;
        }
    }
    @PostMapping(value = "/mpesauploads", produces = MediaType.APPLICATION_JSON_VALUE)
    private UserResponse getMpesaUploads(@RequestBody UserReportsRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getMpesaUploads request: {}", request);
            return userService.getMpesaUploads(request, httpServletResponse, httpServletRequest);

        }catch (Exception e) {
            log.error("An error occurred {} request {}",e.getMessage(), request.toString());
            e.printStackTrace();
            httpServletResponse.setStatus(500);
            return null;
        }
    }

}
