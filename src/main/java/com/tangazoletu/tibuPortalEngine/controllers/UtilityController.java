package com.tangazoletu.tibuPortalEngine.controllers;

import com.tangazoletu.tibuPortalEngine.dto.*;
import com.tangazoletu.tibuPortalEngine.dto.StatusRequest;
import com.tangazoletu.tibuPortalEngine.entities.PaymentReportEntity;
import com.tangazoletu.tibuPortalEngine.enums.ApiResponseCode;
import com.tangazoletu.tibuPortalEngine.repositories.MpesaUploadsRepository;
import com.tangazoletu.tibuPortalEngine.service.PaymentReportService;
import com.tangazoletu.tibuPortalEngine.service.UtilityService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.juli.logging.Log;
import org.slf4j.Logger;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


@RequiredArgsConstructor
@Slf4j
@Data
@RestController
@RequestMapping(value = "/api/test/utility")
public class UtilityController {

    private final UtilityService utilityService;

    @PostMapping(value = "/budget", produces = MediaType.APPLICATION_JSON_VALUE)
    public UtilityResponse getBudget(UtilityRequest utilityRequest, HttpServletResponse response, HttpServletRequest request) {
        try {

            log.info("UtilityController.getBudget", utilityRequest);
            return utilityService.getBudget(utilityRequest,response,request);

        }catch (Exception e){
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            log.error("An error occurred trying to fetch budget {}", utilityRequest);
            e.printStackTrace();
            return null;
        }
    }
    @PostMapping(value = "/financier", produces = MediaType.APPLICATION_JSON_VALUE)
    public UtilityResponse getFinancier(UtilityRequest utilityRequest, HttpServletResponse response, HttpServletRequest request) {
        try {

            log.info("UtilityController.getFinancier", utilityRequest);
            return utilityService.getFinancier(utilityRequest,response,request);

        }catch (Exception e){
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            log.error("An error occurred trying to fetch budget {}", utilityRequest);
            e.printStackTrace();
            return null;
        }
    }
    @PostMapping(value = "/sendsms", produces = MediaType.APPLICATION_JSON_VALUE)
    public UtilityResponse sendSms( HttpServletResponse response, HttpServletRequest request) {
        try {

            log.info("send sms endpoint");
            return utilityService.sendSms(request,response);

        }catch (Exception e){
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            log.error("An error occurred sending sms {}");
            e.printStackTrace();
            return null;
        }
    }
    @GetMapping(value = "/sendsms", produces = MediaType.APPLICATION_JSON_VALUE)
    public UtilityResponse sendSmsGet( HttpServletResponse response, HttpServletRequest request) {
        try {

            log.info("send sms endpoint");
            return utilityService.sendSms(request,response);

        }catch (Exception e){
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            log.error("An error occurred sending sms {}");
            e.printStackTrace();
            return null;
        }
    }
}
