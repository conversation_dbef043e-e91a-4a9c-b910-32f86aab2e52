package com.tangazoletu.tibuPortalEngine.controllers;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tangazoletu.tibuPortalEngine.dto.DashboardDataResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.*;

@RestController
@RequestMapping("/api/test/dataApi")
@Slf4j
public class DashboardController {

    @Value("${dataMs.BaseUrl}")
    private String baseUrl;

    @Value("${dataMs.User}")
    private String user;

    @Value("${dataMs.Password}")
    private String password;

    private final RestTemplate restTemplate = new RestTemplate();

    @PostMapping("/{endpoint}")
    public DashboardDataResponse forwardToDataMs(
            @PathVariable String endpoint,
            @RequestBody Map<String, Object> requestBody) {

        String targetUrl = baseUrl + "/" + endpoint;
        log.info("Forwarding dashboard request to endpoint: {}", targetUrl);
        log.info("Request payload: {}", requestBody);

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            String auth = Base64.getEncoder().encodeToString((user + ":" + password).getBytes());
            headers.set("Authorization", "Basic " + auth);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<Map> response = restTemplate.exchange(
                    targetUrl,
                    HttpMethod.POST,
                    entity,
                    Map.class
            );

            Map<String, Object> responseBody = response.getBody();
            log.info("Received response from {}: {}", endpoint, responseBody);

            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> dataWrapper = (Map<String, Object>) responseBody.get("data");

            // Convert each entry in dataWrapper to a Map with its section name
            List<Map<String, Object>> structuredSections = new ArrayList<>();
            for (Map.Entry<String, Object> entry : dataWrapper.entrySet()) {
                Map<String, Object> section = new LinkedHashMap<>();
                section.put("section", entry.getKey());
                section.put("items", entry.getValue()); // Keep list/array as-is
                structuredSections.add(section);
            }

            Page<Map<String, Object>> page = new PageImpl<>(
                    structuredSections,
                    PageRequest.of(0, structuredSections.size()),
                    structuredSections.size()
            );

            log.info("Returning structured dashboard data with {} sections", structuredSections.size());

            return DashboardDataResponse.builder()
                    .responseCode((String) responseBody.get("code"))
                    .responseMessage((String) responseBody.get("description"))
                    .data(page)
                    .build();

        } catch (Exception e) {
            log.error("Failed to forward request to {}: {}", endpoint, e.getMessage(), e);
            return DashboardDataResponse.builder()
                    .responseCode("01")
                    .responseMessage("Failed to fetch dashboard data: " + e.getMessage())
                    .data(Page.empty())
                    .build();
        }
    }



}
