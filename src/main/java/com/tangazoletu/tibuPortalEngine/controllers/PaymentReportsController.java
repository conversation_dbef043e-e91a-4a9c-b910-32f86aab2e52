package com.tangazoletu.tibuPortalEngine.controllers;

import com.tangazoletu.tibuPortalEngine.dto.*;
import com.tangazoletu.tibuPortalEngine.dto.StatusRequest;
import com.tangazoletu.tibuPortalEngine.entities.PaymentReportEntity;
import com.tangazoletu.tibuPortalEngine.enums.ApiResponseCode;
import com.tangazoletu.tibuPortalEngine.repositories.MpesaUploadsRepository;
import com.tangazoletu.tibuPortalEngine.service.PaymentReportService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping(value = "/api/test/reports")
public class PaymentReportsController {
    private final PaymentReportService paymentReportService;

    @PostMapping("/view")
    public ResponseEntity<?> getReports(@RequestBody ReportRequest request) {
        ReportResponseView response = paymentReportService.getReport(request);
        return ResponseEntity.ok(response);
    }
    @PostMapping("/viewById")
    public ResponseEntity<ApiResponse> getLatestApproved(@RequestBody ApiOrderIdRequest request) {
        ApiResponse dto = paymentReportService.getLatestApproved(request.getApiorderId());
        return dto != null ? ResponseEntity.ok(dto) : ResponseEntity.notFound().build();
    }
    @PostMapping(value = "/mpesatransactions", produces = MediaType.APPLICATION_JSON_VALUE)
    public ReportResponse getMpesaRecords(@RequestBody ReportRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getMpesaRecords {}", request);
            return paymentReportService.getMpesaRecords(request,httpServletRequest,httpServletResponse);

        }catch (Exception e){
            httpServletResponse.setStatus(500);
            log.error("An error occurred {}",e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    @PostMapping(value = "/pendingmpesarecords", produces = MediaType.APPLICATION_JSON_VALUE)
    public ReportResponse getPendingMpesa(@RequestBody ReportRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getPendingMpesa {}", request);
            return paymentReportService.getPendingMpesa(request,httpServletRequest,httpServletResponse);

        }catch (Exception e){
            httpServletResponse.setStatus(500);
            log.error("An error occurred {}",e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    @PostMapping(value = "/completedmpesa", produces = MediaType.APPLICATION_JSON_VALUE)
    public ReportResponse getCompletedMpesaRecords(@RequestBody ReportRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getCompletedMpesaRecords {}", request);
            return paymentReportService.getCompletedMpesaRecords(request,httpServletRequest,httpServletResponse);

        }catch (Exception e){
            httpServletResponse.setStatus(500);
            log.error("An error occurred {}",e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    @PostMapping("/view-status")
    public ResponseEntity<ApprovalReportResponse> getReports(@RequestBody StatusRequest statusRequest) {
        ApprovalReportResponse response = paymentReportService.fetchReports(statusRequest);
        return ResponseEntity.ok(response);

    }
    @PostMapping("/api-bulk")
    public ResponseEntity<ReportResponse> getReports(@RequestBody ProcessRequest processRequest, @RequestParam(required = false) Integer orgId) {
        ReportResponse response = paymentReportService.getBulkApprovals(processRequest);
        return ResponseEntity.ok(response);

    }
    @PostMapping("/reversed")
    public ReportResponse getReversedApprovals(@RequestParam(required = false) Integer countyId,
                                            @RequestParam(defaultValue = "0") int page,
                                            @RequestParam(defaultValue = "10") int size) {
        return paymentReportService.getReversedApprovals(countyId, page, size);
    }
    @PostMapping("/resubmissions-pms")
    public ReportResponse getResubmissions(
            @RequestParam(required = false) Integer countyId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        return paymentReportService.getResubmissions(countyId, page, size);
    }



          @PostMapping(value = "/failedmpesa", produces = MediaType.APPLICATION_JSON_VALUE)
    public ReportResponse getFailedMpesaRecords(@RequestBody ReportRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getFailedMpesaRecords {}", request);
            return paymentReportService.getFailedMpesaRecords(request,httpServletRequest,httpServletResponse);

        }catch (Exception e){
            httpServletResponse.setStatus(500);
            log.error("An error occurred {}",e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    @PostMapping(value = "/reversedpayments", produces = MediaType.APPLICATION_JSON_VALUE)
    public ReportResponse getReversedPayments(@RequestBody ReportRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getReversedPayments {}", request);
            return paymentReportService.getReversalMpesaRecords(request,httpServletRequest,httpServletResponse);

        }catch (Exception e){
            httpServletResponse.setStatus(500);
            log.error("An error occurred {}",e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    @PostMapping(value = "/allapipayments", produces = MediaType.APPLICATION_JSON_VALUE)
    public ReportResponse getAllApiPayments(@RequestBody ReportRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getAllApiPayments {}", request);
            return paymentReportService.getAllApiPayments(request,httpServletRequest,httpServletResponse);

        }catch (Exception e){
            httpServletResponse.setStatus(500);
            log.error("An error occurred {}",e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    @PostMapping(value = "/intratransfers", produces = MediaType.APPLICATION_JSON_VALUE)
    public ReportResponse getIntraTransfers(@RequestBody ReportRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getIntraTransfers {}", request);
            return paymentReportService.getIntraTransfers(request,httpServletRequest,httpServletResponse);

        }catch (Exception e){
            httpServletResponse.setStatus(500);
            log.error("An error occurred {}",e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    @PostMapping(value = "/mpesaresubmission", produces = MediaType.APPLICATION_JSON_VALUE)
    public ReportResponse getMpesaResubmission(@RequestBody ReportRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getMpesaResubmission {}", request);
            return paymentReportService.getResubmissionMpesaRecords(request,httpServletRequest,httpServletResponse);

        }catch (Exception e){
            httpServletResponse.setStatus(500);
            log.error("An error occurred {}",e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    @PostMapping(value = "/batch", produces = MediaType.APPLICATION_JSON_VALUE)
    public ReportResponse getBatch(@RequestBody ReportRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getBatch {}", request);
            return paymentReportService.getBatch(request,httpServletRequest,httpServletResponse);

        }catch (Exception e){
            httpServletResponse.setStatus(500);
            log.error("An error occurred {}",e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    @PostMapping(value = "/totalexpenditurepercounty", produces = MediaType.APPLICATION_JSON_VALUE)
    public ReportResponse getTotalExpenditurePerCounty(@RequestBody ReportRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getTotalExpenditurePerCounty {}", request);
            return paymentReportService.getTotalexpenditurecounyperiod(request,httpServletRequest,httpServletResponse);

        }catch (Exception e){
            httpServletResponse.setStatus(500);
            log.error("An error occurred {}",e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    @PostMapping(value = "/countybatch", produces = MediaType.APPLICATION_JSON_VALUE)
    public ReportResponse getCountyBatch(@RequestBody ReportRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getCountyBatch {}", request);
            return paymentReportService.getCountyBatch(request,httpServletRequest,httpServletResponse);

        }catch (Exception e){
            httpServletResponse.setStatus(500);
            log.error("An error occurred {}",e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    @PostMapping(value = "/hcwpayments", produces = MediaType.APPLICATION_JSON_VALUE)
    public ReportResponse getHCWPayments(@RequestBody ReportRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getHCWPayments {}", request);
            return paymentReportService.getHcwPayments(request,httpServletRequest,httpServletResponse);

        }catch (Exception e){
            httpServletResponse.setStatus(500);
            log.error("An error occurred {}",e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    @PostMapping(value = "/reverse_rejected", produces = MediaType.APPLICATION_JSON_VALUE)
    public ReverseRejectedReportRepose getReversalRejected(@RequestBody ReportRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getReversalRejected {}", request);
            return paymentReportService.getReverseRejected(request,httpServletRequest,httpServletResponse);

        }catch (Exception e){
            httpServletResponse.setStatus(500);
            log.error("An error occurred {}",e.getMessage());
            e.printStackTrace();
            return null;
        }
    }




}
