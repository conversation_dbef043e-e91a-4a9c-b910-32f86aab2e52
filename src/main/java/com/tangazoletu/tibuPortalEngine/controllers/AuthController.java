package com.tangazoletu.tibuPortalEngine.controllers;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tangazoletu.tibuPortalEngine.configurations.JwtUtils;
import com.tangazoletu.tibuPortalEngine.dto.*;
import com.tangazoletu.tibuPortalEngine.entities.RefreshToken;
import com.tangazoletu.tibuPortalEngine.enums.ApiResponseCode;
import com.tangazoletu.tibuPortalEngine.exceptions.EncryptionException;
import com.tangazoletu.tibuPortalEngine.exceptions.TokenRefreshException;
import com.tangazoletu.tibuPortalEngine.service.AesEncryptionService;
import com.tangazoletu.tibuPortalEngine.service.AuthService;
import com.tangazoletu.tibuPortalEngine.service.RefreshTokenService;
import com.tangazoletu.tibuPortalEngine.service.UserDetailsImpl;
import jakarta.servlet.http.HttpSession;
import jakarta.validation.Valid;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;


@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/auth")
public class AuthController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AuthController.class);
    @Autowired
    private AuthService authService;

    @Autowired
    private RefreshTokenService refreshTokenService;

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    AesEncryptionService encryptionService;

    @PostMapping("/login")
    public ResponseEntity<?> authenticateUser(
            @Valid
            @RequestBody ApiDto request,
            @RequestHeader(required = false) String Key
    ) {
        try {
            String decryptedJson = encryptionService.decrypt(request.getPayload(), Key);
            ObjectMapper mapper = new ObjectMapper();
            LoginRequest loginRequest = mapper.readValue(decryptedJson, LoginRequest.class);

            //Process the login
            LoginResponse response = authService.processLogin(loginRequest);

            //LoginResponse response = new LoginResponse("00", "Success. OTP has been sent to your email");
            String encryptedResponse = encryptionService.encrypt(new ObjectMapper().writeValueAsString(response), Key);
            return ResponseEntity.ok(new ApiResponse(encryptedResponse));

        } catch (AuthService.PasswordExpiredException e) {
            //  Password expired response
            LoginResponse response = new LoginResponse("03", e.getMessage()); // 03 = expired password
            String encryptedResponse = null;
            try {
                encryptedResponse = encryptionService.encrypt(new ObjectMapper().writeValueAsString(response), Key);
            } catch (EncryptionException ex) {
                throw new RuntimeException(ex);
            } catch (JsonProcessingException ex) {
                throw new RuntimeException(ex);
            }
            return ResponseEntity.ok(new ApiResponse(encryptedResponse));

        } catch (Exception ex) {
            LOGGER.error("An error occurred during login: {}", ex.getMessage());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Unauthorized access");
        }
    }

    @PostMapping("/forgot-password")
    public ResponseEntity<ForgotPasswordResponse> handleForgotPassword(@RequestBody ApiDto request,
                                                                       @RequestHeader(required = false) String Key) {
        try {
            String decryptedJson = encryptionService.decrypt(request.getPayload(), Key);
            ObjectMapper mapper = new ObjectMapper();
            ForgotPasswordRequest forgotPassword = mapper.readValue(decryptedJson, ForgotPasswordRequest.class);

            if (forgotPassword.getUsername() == null || forgotPassword.getUsername().isEmpty()
                    || forgotPassword.getOrganization() == null || forgotPassword.getOrganization().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(new ForgotPasswordResponse("01", "Username and Organization are required", null));
            }

            String result = authService.processForgotPassword(
                    forgotPassword.getUsername(),
                    forgotPassword.getOrganization(),
                    forgotPassword.getCaptchaInput()
            );

            return ResponseEntity.ok(new ForgotPasswordResponse("00", result, null));

        } catch (Exception e) {
            e.printStackTrace(); // Consider using logger
            return ResponseEntity.internalServerError()
                    .body(new ForgotPasswordResponse("99", "An error occurred: " + e.getMessage(), null));
        }
    }


    @PostMapping("/verify-otp")
    public ResponseEntity<?> verifyOtp(
            @Valid @RequestBody ApiDto otpRequest,
            @RequestHeader(required = false) String Key
    ) {
        try {
            String decryptedJson = encryptionService.decrypt(otpRequest.getPayload(), Key);
            ObjectMapper mapper = new ObjectMapper();
            OtpVerificationRequest otp = mapper.readValue(decryptedJson, OtpVerificationRequest.class);
            JwtResponse jwtResponse = authService.verifyOtp(otp);
            // encrypt the response here
            String encryptedResponse = encryptionService.encrypt(new ObjectMapper().writeValueAsString(jwtResponse), Key);
            return ResponseEntity.ok(new ApiResponse(encryptedResponse));
        } catch(Exception ex) {
            LOGGER.error("An error occurred verifying otp: {}", ex.getMessage());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Unauthorized access");
        }
    }

    @PostMapping("/refreshtoken")
    public ResponseEntity<?> refreshToken(@RequestBody TokenRefreshRequest request) {
        String requestRefreshToken = request.getRefreshToken();

        return refreshTokenService.findByToken(requestRefreshToken)
                .map(refreshTokenService::verifyExpiration)
                .map(RefreshToken::getUser)
                .map(user -> {
                    String token = jwtUtils.generateJwtToken(
                            user.getLogin(),
                            user.getOrganization().getName(),
                            user.getSecretKey()
                    );

                    return ResponseEntity.ok(new TokenRefreshResponse(token, requestRefreshToken));
                })
                .orElseThrow(() -> new TokenRefreshException(requestRefreshToken,
                        "Refresh token is not in database!"));
    }

    @PostMapping("/reset-password")
    public ResponseEntity<ApiWrapper<ResetPasswordResponse<String>>> resetPassword(@RequestBody ApiDto request,
                                                                                   @RequestHeader(required = false) String Key) {
        try {
            String decryptedJson = encryptionService.decrypt(request.getPayload(), Key);
            ObjectMapper mapper = new ObjectMapper();
            ResetPasswordRequest resetPassword = mapper.readValue(decryptedJson, ResetPasswordRequest.class);

            if (resetPassword.getUsername() == null || resetPassword.getUsername().isEmpty()
                    || resetPassword.getOrganization() == null || resetPassword.getOrganization().isEmpty()) {
                ResetPasswordResponse<String> resp = new ResetPasswordResponse<>("01", "Username and Organization are required", null);
                return ResponseEntity.badRequest().body(new ApiWrapper<>(resp));
            }

            //  Main logic
            ResetPasswordResponse<String> result = authService.resetPassword(resetPassword);

            return ResponseEntity.ok(new ApiWrapper<>(result));

        } catch (Exception e) {
            e.printStackTrace();
            ResetPasswordResponse<String> resp = new ResetPasswordResponse<>("99", "An error occurred: " + e.getMessage(), null);
            return ResponseEntity.internalServerError().body(new ApiWrapper<>(resp));
        }
    }


    @PostMapping("/change-password")
    public ResponseEntity<ChangePasswordResponse> changePasswords(@RequestBody ApiDto request, @RequestHeader(required = false) String Key) {
        try {
            String decryptedJson = encryptionService.decrypt(request.getPayload(), Key);
            ObjectMapper mapper = new ObjectMapper();
            ChangePasswordRequest changePassword = mapper.readValue(decryptedJson, ChangePasswordRequest.class);

            if (changePassword.getUsername() == null || changePassword.getUsername().isEmpty()
                    || changePassword.getOrganization() == null || changePassword.getOrganization().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(new ChangePasswordResponse("01", "Username and Organization are required", null));
            }

            ChangePasswordResponse response = authService.changePassword(changePassword);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            e.printStackTrace(); // Consider using logger
            return ResponseEntity.internalServerError()
                    .body(new ChangePasswordResponse("99", "An error occurred: " + e.getMessage(), null));
        }
    }

    @PostMapping("/logout")
    public ResponseEntity<?> logout(
            @AuthenticationPrincipal UserDetailsImpl userDetails,
            @RequestBody(required = false) ApiDto request,
            @RequestHeader(required = false) String Key
    ) {
        try {
            LogoutResponse logoutResponse;

            if (userDetails != null) {
                Long userId = userDetails.getId();
                String username = userDetails.getUsername();

                // Invalidate all refresh tokens for the user
                int deletedTokens = refreshTokenService.deleteByUserId(userId);

                // Clear the security context
                SecurityContextHolder.clearContext();

                LOGGER.info("User {} logged out successfully. {} refresh tokens invalidated.", username, deletedTokens);

                logoutResponse = new LogoutResponse("00", "Logout successful. All sessions terminated.");
            } else {
                LOGGER.warn("Logout attempt with no authenticated user");
                logoutResponse = new LogoutResponse("01", "No authenticated user found");
            }

            // If encryption is requested, encrypt the response
            if (request != null && Key != null) {
                try {
                    String encryptedResponse = encryptionService.encrypt(
                            new ObjectMapper().writeValueAsString(logoutResponse), Key);
                    return ResponseEntity.ok(new ApiResponse(encryptedResponse));
                } catch (EncryptionException | JsonProcessingException e) {
                    LOGGER.error("Error encrypting logout response: {}", e.getMessage());
                    // Fall back to unencrypted response
                }
            }

            // Return unencrypted response
            return ResponseEntity.ok(logoutResponse);

        } catch (Exception e) {
            LOGGER.error("Error during logout: {}", e.getMessage(), e);
            LogoutResponse errorResponse = new LogoutResponse("99", "An error occurred during logout: " + e.getMessage());

            // Try to encrypt error response if requested
            if (request != null && Key != null) {
                try {
                    String encryptedResponse = encryptionService.encrypt(
                            new ObjectMapper().writeValueAsString(errorResponse), Key);
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                            .body(new ApiResponse(encryptedResponse));
                } catch (Exception encryptionError) {
                    LOGGER.error("Error encrypting error response: {}", encryptionError.getMessage());
                }
            }

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
}