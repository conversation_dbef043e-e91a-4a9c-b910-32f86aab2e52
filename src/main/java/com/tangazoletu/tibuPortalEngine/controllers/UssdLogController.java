package com.tangazoletu.tibuPortalEngine.controllers;

import com.tangazoletu.tibuPortalEngine.dto.UssdLogsRequest;
import com.tangazoletu.tibuPortalEngine.dto.UssdLogsResponse;
import com.tangazoletu.tibuPortalEngine.service.UssdService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/test/ussd")
@Slf4j
@RequiredArgsConstructor
public class UssdLogController {

    private final UssdService ussdService;

    @PostMapping(value = "/ussd_logs", produces = MediaType.APPLICATION_JSON_VALUE)
    public UssdLogsResponse ussdLogs(@Valid @RequestBody UssdLogsRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        try {
            log.info("ussdLogs request: {}", request);

            return  ussdService.getUssdLogs(request, response, httpServletRequest);

        }catch (Exception e) {
            response.setStatus(500);
            log.error("error occurred {} request {}",e.getMessage(), request.toString());
            return null;
        }
    }
}
