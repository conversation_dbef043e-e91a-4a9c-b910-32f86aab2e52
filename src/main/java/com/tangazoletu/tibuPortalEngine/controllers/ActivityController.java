package com.tangazoletu.tibuPortalEngine.controllers;

import com.tangazoletu.tibuPortalEngine.dto.ActivityRequest;
import com.tangazoletu.tibuPortalEngine.dto.ApiResponse;
import com.tangazoletu.tibuPortalEngine.dto.UserRequest;
import com.tangazoletu.tibuPortalEngine.service.ActivityService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RequiredArgsConstructor
@Data
@RestController
@RequestMapping(value="/api/activity")
public class ActivityController {

    private  final ActivityService activityService;

    @GetMapping("/view")
    public ResponseEntity<ApiResponse> getActivities(@RequestBody ActivityRequest request) {
        ApiResponse response = activityService.getActivity(request);
        return ResponseEntity.ok(response);
    }
}
