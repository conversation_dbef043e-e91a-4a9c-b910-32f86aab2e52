package com.tangazoletu.tibuPortalEngine.controllers;

import com.tangazoletu.tibuPortalEngine.dto.*;
import com.tangazoletu.tibuPortalEngine.service.MeetingService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/test/activities")
@Slf4j
public class ActivitiesController {

    private final MeetingService meetingService;

    @PostMapping(value = "/events", produces = MediaType.APPLICATION_JSON_VALUE)
    public MeetingResponse getEvents(@RequestBody EventRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {

        try {
            log.info("getEvents called {}", request);

            return  meetingService.getMeeting(request, response, httpServletRequest);

        }catch (Exception e){
            log.error("An error Occured {} request {}",e.getMessage(), request.toString());
            response.setStatus(500);
            e.printStackTrace();
            return null;
        }
    }
    @PostMapping(value = "/meetingcodes", produces = MediaType.APPLICATION_JSON_VALUE)
    public EventCodesResponse getMeetingCodes(@RequestBody MeetingCodesRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {

        try {
            log.info("getmettingCodes called {}", request);

            return  meetingService.getMeeetingCode(request, httpServletRequest,  response);

        }catch (Exception e){
            log.error("An error Occured {} request {}",e.getMessage(), request.toString());
            response.setStatus(500);
            e.printStackTrace();
            return null;
        }
    }
    @PostMapping(value = "/attendance_processing", produces = MediaType.APPLICATION_JSON_VALUE)
    public MeetingResponse getCodeVerification(@RequestBody CodeVerificationRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {

        try {
            log.info("codeverification called {}", request);

            return  meetingService.getCodeVerification(request, httpServletRequest,  response);

        }catch (Exception e){
            log.error("An error Occured {} request {}",e.getMessage(), request.toString());
            response.setStatus(500);
            e.printStackTrace();
            return null;
        }
    }
    @PostMapping(value = "/meeting_details", produces = MediaType.APPLICATION_JSON_VALUE)
    public ColumRisizeDataResponse getActivitiesDetails(@RequestBody MeetingCodesRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {

        try {
            log.info("meetings details called {}", request);

            return  meetingService.getColumRisizeData(request, httpServletRequest,  response);

        }catch (Exception e){
            log.error("An error Occured {} request {}",e.getMessage(), request.toString());
            response.setStatus(500);
            e.printStackTrace();
            return null;
        }
    }
}
