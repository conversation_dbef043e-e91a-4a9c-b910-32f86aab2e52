package com.tangazoletu.tibuPortalEngine.controllers;

import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import com.tangazoletu.tibuPortalEngine.annotations.ExcelColumn;
import com.tangazoletu.tibuPortalEngine.dto.ExportRequest;
import com.tangazoletu.tibuPortalEngine.enums.PortalReports;
import com.tangazoletu.tibuPortalEngine.service.ReportService;
import com.tangazoletu.tibuPortalEngine.util.FieldMeta;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.*;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import java.lang.reflect.Field;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/test/exports")
public class PdfExportController {

    @Autowired
    private ReportService reportService;

    @PostMapping("/export-pdf")
    public ResponseEntity<byte[]> exportToPdf(@RequestBody ExportRequest request) {
        try {

            // checking the report name
            String reportNamePayload = request.getReportName();

            if (!PortalReports.isValid(request.getReportName())) {
                return ResponseEntity.badRequest()
                        .body(("Invalid report name: " + reportNamePayload).getBytes());
            }
            // Load image from resources
            ClassPathResource imageResource = new ClassPathResource("/TIBU.png");
            byte[] imageBytes = StreamUtils.copyToByteArray(imageResource.getInputStream());

            BufferedImage bufferedImage = ImageIO.read(new ByteArrayInputStream(imageBytes));
            int imageWidth = bufferedImage.getWidth();
            int imageHeight = bufferedImage.getHeight();

            String base64Image = Base64.getEncoder().encodeToString(imageBytes);

            // Simulate table data (replace this with actual DB query or service)
            List<?> tableData = reportService.getReportData(reportNamePayload, request.getFilters());

            // Create filename with date
            String fileNameWithDate = request.getReportName() + "_Report " +
                    LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy MMM dd"));

            String html = buildHtml(fileNameWithDate, base64Image, imageWidth, imageHeight, tableData);

            ByteArrayOutputStream out = new ByteArrayOutputStream();

            PdfRendererBuilder builder = new PdfRendererBuilder();
            builder.useFastMode();
            builder.withHtmlContent(html, null);
            builder.toStream(out);

            // Set A4 portrait manually (210mm x 297mm)
            builder.useDefaultPageSize(210, 297, PdfRendererBuilder.PageSizeUnits.MM);

            builder.run();

            // Set PDF as HTTP response0
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDisposition(ContentDisposition.attachment()
                    .filename(request.getReportName() + ".pdf")
                    .build());


            return new ResponseEntity<>(out.toByteArray(), headers, HttpStatus.OK);

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("Error generating PDF: " + e.getMessage()).getBytes());
        }
    }


    private String buildHtml(String reportName, String base64Image, int imageWidth, int imageHeight, List<?> dataList) {
        StringBuilder html = new StringBuilder();
        html.append("""
        <html>
        <head>
            <style>
                                    @page {
                                        size: A4;
                                        margin: 0.3cm; /* Very tight page margin */
                                    }
                
                                    body {
                                        font-family: Arial, sans-serif;
                                        font-size: 3pt; /* Reduced font size */
                                        margin: 0;
                                        padding: 0;
                                    }
                
                                    h2 {
                                        text-align: center;
                                        margin-bottom: 5px;
                                        font-size: 8pt;
                                        font-weight: normal;
                                    }
                
                                    img {
                                        display: block;
                                        margin: 0 auto 5px auto;
                                        max-width: 100%;
                                        height: auto;
                                    }
                
                                    table {
                                        width: 100%;
                                        border-collapse: collapse;
                                        page-break-inside: auto;
                                        table-layout: fixed;
                                        word-wrap: break-word;
                                    }
                
                                    thead {
                                        font-weight: normal; /* Remove bold */
                                    }
                
                                    tr {
                                        page-break-inside: avoid;
                                        page-break-after: auto;
                                    }
                
                                    th, td {
                                        padding: 2px; /* Tighter padding */
                                        text-align: left;
                                        font-size: 6.5pt;
                                        border: none;
                                        word-wrap: break-word;
                                        max-width: 100px; /* Adjust to limit column width */
                                    }
                
                                    .footer {
                                        margin-top: 5px;
                                        font-size: 7pt;
                                    }
                                </style>
        </head>
        <body>
    """);

        html.append("<h2>").append(reportName).append("</h2>");
        html.append("<img src='data:image/png;base64,")
                .append(base64Image)
                .append("' style='max-width:100%; height:auto;' />");

        if (dataList == null || dataList.isEmpty()) {
            html.append("<p>No data found.</p>");
        } else {
            Object firstRow = dataList.get(0);
            List<FieldMeta> headersMeta = extractFieldMeta(firstRow);
            List<String> headers = headersMeta.stream().map(FieldMeta::getDisplayName).toList();

            html.append("<table>");
            html.append("<thead><tr>");
            for (String header : headers) {
                html.append("<th>").append(header).append("</th>");
            }
            html.append("</tr></thead><tbody>");

            for (Object rowObj : dataList) {
                html.append("<tr>");
                Map<String, Object> rowMap = extractFieldValues(rowObj, headersMeta);
                for (String header : headers) {
                    Object value = rowMap.getOrDefault(header, "");
                    html.append("<td>").append(escapeHtml(value != null ? value.toString() : "")).append("</td>");
                }
                html.append("</tr>");
            }

            html.append("</tbody></table>");

            html.append("<div class='footer'><p><strong>Date:</strong> ")
                    .append(new java.text.SimpleDateFormat("dd-MMM-yyyy h:mm a").format(new Date()))
                    .append("</p><p><strong>Signed by Name:</strong> _____________</p><p><strong>Signature:</strong> _____________</p></div>");
        }

        html.append("</body></html>");
        return html.toString();
    }




    private List<String> extractFieldNames(Object obj) {
        List<Field> allFields = Arrays.asList(obj.getClass().getDeclaredFields());
        return allFields.stream()
                .filter(f -> {
                    ExcelColumn annotation = f.getAnnotation(ExcelColumn.class);
                    return (annotation == null || !annotation.exclude())
                            && !"id".equalsIgnoreCase(f.getName());
                })
                .sorted(Comparator.comparingInt(f -> {
                    ExcelColumn annotation = f.getAnnotation(ExcelColumn.class);
                    return annotation != null ? annotation.order() : Integer.MAX_VALUE;
                }))
                .map(f -> {
                    ExcelColumn annotation = f.getAnnotation(ExcelColumn.class);
                    if (annotation != null && !annotation.value().isEmpty()) {
                        return annotation.value();
                    }

                    // Convert camelCase to "Camel Case"
                    String fieldName = f.getName();
                    String spaced = fieldName.replaceAll("([a-z])([A-Z])", "$1 $2");
                    return capitalizeEachWord(spaced);
                })

                .toList();
    }



    private Map<String, Object> extractFieldValues(Object obj, List<FieldMeta> fieldMetaList) {
        Map<String, Object> result = new LinkedHashMap<>();
        for (FieldMeta meta : fieldMetaList) {
            try {
                Field field = obj.getClass().getDeclaredField(meta.getFieldName());
                field.setAccessible(true);
                Object value = field.get(obj);

                ExcelColumn annotation = field.getAnnotation(ExcelColumn.class);
                if (annotation != null && value instanceof Date) {
                    String pattern = annotation.dateFormat();
                    value = new java.text.SimpleDateFormat(pattern).format((Date) value);
                }

                result.put(meta.getDisplayName(), value);
            } catch (Exception e) {
                result.put(meta.getDisplayName(), "ERR");
            }
        }
        return result;
    }

    private String capitalizeEachWord(String input) {
        return Arrays.stream(input.split(" "))
                .map(word -> word.substring(0, 1).toUpperCase() + word.substring(1))
                .collect(Collectors.joining(" "));
    }
    private List<FieldMeta> extractFieldMeta(Object obj) {
        return Arrays.stream(obj.getClass().getDeclaredFields())
                .filter(f -> {
                    ExcelColumn annotation = f.getAnnotation(ExcelColumn.class);
                    return (annotation == null || !annotation.exclude()) && !"id".equalsIgnoreCase(f.getName());
                })
                .sorted(Comparator.comparingInt(f -> {
                    ExcelColumn annotation = f.getAnnotation(ExcelColumn.class);
                    return annotation != null ? annotation.order() : Integer.MAX_VALUE;
                }))
                .map(f -> {
                    ExcelColumn annotation = f.getAnnotation(ExcelColumn.class);
                    String fieldName = f.getName();
                    String displayName;
                    if (annotation != null && !annotation.value().isEmpty()) {
                        displayName = annotation.value();
                    } else {
                        displayName = capitalizeEachWord(fieldName.replaceAll("([a-z])([A-Z])", "$1 $2"));
                    }
                    return new FieldMeta(fieldName, displayName);
                })
                .toList();
    }
    private String escapeHtml(String input) {
        if (input == null) return "";
        return input.replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&apos;");
    }



}

