package com.tangazoletu.tibuPortalEngine.controllers;

import com.tangazoletu.tibuPortalEngine.dto.*;
import com.tangazoletu.tibuPortalEngine.entities.Payment;
import com.tangazoletu.tibuPortalEngine.entities.PaymentRequestDetails;
import com.tangazoletu.tibuPortalEngine.service.ApiOrderService;
import com.tangazoletu.tibuPortalEngine.service.PaymentsService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/test")
@Slf4j
@RequiredArgsConstructor
public class ProgramIntelligenceReportController {
    private final PaymentsService paymentsService;
    @PostMapping(value = "/transactioncharges", produces = MediaType.APPLICATION_JSON_VALUE)
    public Page<PaymentDTO> getTransactionCharges(
            @RequestBody TransactionChargesRequest transactionChargesRequest,
            HttpServletResponse response,
            HttpServletRequest request) {
        try {
            return paymentsService.getFilteredPayments(transactionChargesRequest);
        } catch (Exception e) {
            e.printStackTrace();
            response.setStatus(500);
            return Page.empty();
        }
    }


    @PostMapping(value = "/fundstransfer", produces  = MediaType.APPLICATION_JSON_VALUE)
    public Page<?> getFundsTransfer(@RequestBody FundsTransferRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        log.info("getFundsTransfer {}",  request.toString());
        try {
            return  paymentsService.getFilteredFundsTransfer(request,response,httpServletRequest);

        }catch (Exception e) {
            e.printStackTrace();
            log.error("getFundsTransfer {}",  request.toString());
            response.setStatus(500);
            return null;
        }
    }
    @PostMapping(value = "/viewpaymentrequestdetails", produces = MediaType.APPLICATION_JSON_VALUE)
    public Page<PaymentRequestDetails> viewPaymentRequestDetails(@RequestBody ApiRequestDto apiRequestDto, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        try {
            log.info("viewPaymentRequestDetails {}",  apiRequestDto.toString());

            return paymentsService.getPaymentRequestDetailById(apiRequestDto,response,httpServletRequest);

        }catch (Exception e) {
            e.printStackTrace();
            response.setStatus(500);
            log.error("viewPaymentRequestDetails {}",  apiRequestDto.toString());
            return null;
        }
    }
    @PostMapping(value = "/paymentsummary", produces = MediaType.APPLICATION_JSON_VALUE)
    public Page<Map<String, Object>> getPaymentSummary(@RequestBody PaymentRequest paymentRequestDetails, HttpServletResponse response, HttpServletRequest request) {
        try {
            log.info("getPaymentSummary {}",  paymentRequestDetails.toString());
            return paymentsService.getPaymentSummary(paymentRequestDetails, response,request);

        }catch (Exception e) {
            e.printStackTrace();
            response.setStatus(500);
            log.error("getPaymentSummary {}",  paymentRequestDetails.toString());
            return null;
        }
    }
    @PostMapping(value = "/detailedsummary", produces = MediaType.APPLICATION_JSON_VALUE)
    public ProgramIntelligenceResponse getDetailedPaymentSummary(@RequestBody DetailedSummaryRequest detailedSummaryRequest, HttpServletResponse response, HttpServletRequest request) {
        try {
            log.info("getDetailedPaymentSummary {}",  detailedSummaryRequest.toString());
            return paymentsService.getDetailedSummary(detailedSummaryRequest, response,request);
        }catch (Exception e) {
            e.printStackTrace();
            log.error("error getDetailedPaymentSummary {}",  detailedSummaryRequest.toString());
            response.setStatus(500);
            return null;
        }
    }
    @PostMapping(value = "/budgetary", produces = MediaType.APPLICATION_JSON_VALUE)
    public ProgramIntelligenceResponse getBudgetary(@RequestBody BudgetaryRequest budgetaryRequest, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        try {
            log.info("getBudgetary {}",  budgetaryRequest.toString());
            return paymentsService.getBudgetary(budgetaryRequest, response,httpServletRequest);
        }catch (Exception e) {
            log.error("An error occurred", e.getMessage());
            e.printStackTrace();
            response.setStatus(500);
            return null;
        }
    }
    @PostMapping(value = "/expected_vs_actual", produces = MediaType.APPLICATION_JSON_VALUE)
    public ProgramIntelligenceResponse getExpectedVsActual(@RequestBody PaymentRequest paymentRequest, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        try {
            log.info("getBudgetary {}",  paymentRequest.toString());
            return paymentsService.getExpectedVsActual(paymentRequest, response,httpServletRequest);
        }catch (Exception e) {
            log.error("An error occurred", e.getMessage());
            e.printStackTrace();
            response.setStatus(500);
            return null;
        }
    }
    @PostMapping(value = "/supervision_payment", produces = MediaType.APPLICATION_JSON_VALUE)
    public ProgramIntelligenceResponse getSupervisionPayment(@RequestBody PaymentRequest paymentRequest, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        try {
            log.info("supervision {}",  paymentRequest.toString());
            return paymentsService.getSupervisionPayments(paymentRequest, response,httpServletRequest);
        }catch (Exception e) {
            log.error("An error occurred", e.getMessage());
            e.printStackTrace();
            response.setStatus(500);
            return null;
        }
    }
    @PostMapping(value = "/mdrsurveillancepayments", produces =MediaType.APPLICATION_JSON_VALUE)
    public ProgramIntelligenceResponse getMdrSurveillancePayments(@RequestBody PaymentRequest paymentRequest, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        try {
            log.info("mdrsurveillance {}",  paymentRequest.toString());
            return paymentsService.getMdrSurveillancePayments(paymentRequest, response,httpServletRequest);
        }catch (Exception e) {
            log.error("An error occurred", e.getMessage());
            e.printStackTrace();
            response.setStatus(500);
            return null;
        }
    }
    @PostMapping(value = "/mdrsupportpercounty", produces =MediaType.APPLICATION_JSON_VALUE)
    public ProgramIntelligenceResponse getMdrSupportPerCounty(@RequestBody PaymentRequest paymentRequest, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        try {
            log.info("mdrsupportpercounty {}",  paymentRequest.toString());
            return paymentsService.getMdrSupportPerCounty(paymentRequest, response,httpServletRequest);
        }catch (Exception e) {
            log.error("An error occurred", e.getMessage());
            e.printStackTrace();
            response.setStatus(500);
            return null;
        }
    }
    @PostMapping(value = "/getmdrdotpayments", produces =MediaType.APPLICATION_JSON_VALUE)
    public ProgramIntelligenceResponse getMdrDotPayments(@RequestBody MdrDotPaymetsRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        try {
            log.info("getmdrdotpayments {}",  request.toString());
            return paymentsService.getMdrDotPayments(request, response,httpServletRequest);
        }catch (Exception e) {
            log.error("An error occurred", e.getMessage());
            e.printStackTrace();
            response.setStatus(500);
            return null;
        }
    }
    @PostMapping(value = "/getmdrpatientandcostpercounty", produces =MediaType.APPLICATION_JSON_VALUE)
    public ProgramIntelligenceResponse getMdrPatientsAndCostPerCounty(@RequestBody MDRpatientsandcostpercountyRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        try {
            log.info("getMdrPatientsAndCostPerCounty {}",  request.toString());
            return paymentsService.getMdrPatientsAndCostPerCounty(request, response,httpServletRequest);
        }catch (Exception e) {
            log.error("An error occurred", e.getMessage());
            e.printStackTrace();
            response.setStatus(500);
            return null;
        }
    }
    @PostMapping(value = "/getpatientpaymentovertime", produces =MediaType.APPLICATION_JSON_VALUE)
    public ProgramIntelligenceResponse getPatientPaymentOverTime(@RequestBody PatientPaymentOverTimeRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        try {
            log.info("getpatientpaymentovertime {}",  request.toString());
            return paymentsService.getPatientPaymentOverTime(request, response,httpServletRequest);
        }catch (Exception e) {
            log.error("An error occurred", e.getMessage());
            e.printStackTrace();
            response.setStatus(500);
            return null;
        }
    }
    @PostMapping(value = "/getpatientpaymentovertimeby_id", produces =MediaType.APPLICATION_JSON_VALUE)
    public ProgramIntelligenceResponse getPatientPaymentOverTimeById(@RequestBody PatientPaymentOverTimeRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        try {
            log.info("getpatientpaymentovertime {}",  request.toString());
            return paymentsService.getPatientPaymentOverTimeById(request, response,httpServletRequest);
        }catch (Exception e) {
            log.error("An error occurred", e.getMessage());
            e.printStackTrace();
            response.setStatus(500);
            return null;
        }
    }
    @PostMapping(value = "/getCompletedTreatmentCount", produces =MediaType.APPLICATION_JSON_VALUE)
    public ProgramIntelligenceResponse getCompletedTreatmentCount(@RequestBody PaymentRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        try {
            log.info("getCompleteTreatment {}",  request.toString());
            return paymentsService.getCompletedTreatment(request,httpServletRequest, response);
        }catch (Exception e) {
            log.error("An error occurred", e.getMessage());
            e.printStackTrace();
            response.setStatus(500);
            return null;
        }
    }
    @PostMapping(value = "/getCompletedTreamentById", produces = MediaType.APPLICATION_JSON_VALUE)
    public ProgramIntelligenceResponse getCompletedTreamentById(@RequestBody PaymentRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        try {
            log.info("getCompletedTreament {}",  request.toString());
            return paymentsService.getCompletedTreatmentById(request,response,httpServletRequest);

        }catch (Exception e) {
            log.error("An error occurred", e.getMessage());
            response.setStatus(500);
            return null;
        }
    }
    @PostMapping(value = "/getCompletedTreatmentPayments", produces = MediaType.APPLICATION_JSON_VALUE)
    public ProgramIntelligenceResponse getPatientCompletedTreatmentPayments(@RequestBody PatientCompletedTreatmentPayments request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        try {
            log.info("getCompletedTreatmentPayments {}",  request.toString());
            return paymentsService.getPatientCompletedTreatmentPayments(request,response,httpServletRequest);

        }catch (Exception e) {
            log.error("An error occurred", e.getMessage());
            response.setStatus(500);
            return null;
        }
    }
    @PostMapping(value = "/getDotNurseMonthlySummary", produces = MediaType.APPLICATION_JSON_VALUE)
    public ProgramIntelligenceResponse getDotNurseMonthlySummary(@RequestBody DotNurseSummaryRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        try {
            log.info("getDotNurseMonthlySummary {}",  request.toString());
            return paymentsService.getDotNurseMonthlySummary(request,httpServletRequest, response);

        }catch (Exception e) {
            log.error("An error occurred", e.getMessage());
            response.setStatus(500);
            return null;
        }
    }
    @PostMapping(value = "/approval_levels", produces = MediaType.APPLICATION_JSON_VALUE)
    public ProgramIntelligenceResponse getApprovalLevels( HttpServletResponse response, HttpServletRequest httpServletRequest) {
        try {
            log.info("getDotNurseMonthlySummary {}" );
            return paymentsService.getApprovalLevels(httpServletRequest, response);

        }catch (Exception e) {
            log.error("An error occurred", e.getMessage());
            response.setStatus(500);
            return null;
        }
    }

}
