package com.tangazoletu.tibuPortalEngine.controllers;

import com.tangazoletu.tibuPortalEngine.dto.*;
import com.tangazoletu.tibuPortalEngine.service.FinancierService;
import com.tangazoletu.tibuPortalEngine.service.MasterRecordsService;
import com.tangazoletu.tibuPortalEngine.service.MeetingService;
import com.tangazoletu.tibuPortalEngine.service.PaymentReportService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/test/master_records")
@Slf4j
@RequiredArgsConstructor
public class MasterRecordsController {

    private final MasterRecordsService masterRecordsService;
    private final FinancierService financierService;
    private final MeetingService meetingService;
    private final PaymentReportService paymentReportService;


    @PostMapping(value = "/ordertype", produces = MediaType.APPLICATION_JSON_VALUE)
    public MasterRecordsResponse getOrderType(@RequestBody @Valid OrderTypeRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getOrderType {}", request.toString());
            return masterRecordsService.getMasterRecords(request, httpServletRequest, httpServletResponse);
        }catch (Exception e){
            log.error("Error occured getting orderType {} request {}", e.getMessage(), request.toString());
            e.printStackTrace();
            httpServletResponse.setStatus(500);
            return null;
        }

    }
    @PostMapping(value = "/provinces", produces = MediaType.APPLICATION_JSON_VALUE)
    public MasterRecordsResponse fetProvinces(@RequestBody @Valid MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getProvinces {}", request.toString());
            return masterRecordsService.getProvince(request, httpServletRequest, httpServletResponse);
        }catch (Exception e){
            log.error("Error occured getting provinces {} request {}", e.getMessage(), request.toString());
            e.printStackTrace();
            httpServletResponse.setStatus(500);
            return null;
        }

    }
    @PostMapping(value = "/beneficiarytype", produces = MediaType.APPLICATION_JSON_VALUE)
    public MasterRecordsResponse getBeneficiaryType(@RequestBody @Valid MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getbeneficiary {}", request.toString());
            return masterRecordsService.getBeneficiaryType(request, httpServletRequest, httpServletResponse);
        }catch (Exception e){
            log.error("Error occured getting beneficiaries {} request {}", e.getMessage(), request.toString());
            e.printStackTrace();
            httpServletResponse.setStatus(500);
            return null;
        }

    }
    @PostMapping(value = "/county", produces = MediaType.APPLICATION_JSON_VALUE)
    public MasterRecordsResponse getCounty(@RequestBody @Valid MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getCounty {}", request.toString());
            return masterRecordsService.getCounty(request, httpServletRequest, httpServletResponse);
        }catch (Exception e){
            log.error("Error occurred getting counties {} request {}", e.getMessage(), request.toString());
            e.printStackTrace();
            httpServletResponse.setStatus(500);
            return null;
        }

    }
    @PostMapping(value = "/perdiem", produces = MediaType.APPLICATION_JSON_VALUE)
    public MasterRecordsResponse getPerDiem(@RequestBody @Valid PerDiemRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getPerDiem {}", request.toString());
            return masterRecordsService.getPerDiem(request, httpServletRequest, httpServletResponse);
        }catch (Exception e){
            log.error("Error occurred getting perdiem {} request {}", e.getMessage(), request.toString());
            e.printStackTrace();
            httpServletResponse.setStatus(500);
            return null;
        }

    }
    @PostMapping(value = "/jobGroup", produces = MediaType.APPLICATION_JSON_VALUE)
    public MasterRecordsResponse getJobGroup(@RequestBody @Valid MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getJobGroup {}", request.toString());
            return masterRecordsService.getJobGroup(request, httpServletRequest, httpServletResponse);
        }catch (Exception e){
            log.error("Error occurred getting getJobGroup {} request {}", e.getMessage(), request.toString());
            e.printStackTrace();
            httpServletResponse.setStatus(500);
            return null;
        }

    }
    @PostMapping(value = "/recipient", produces = MediaType.APPLICATION_JSON_VALUE)
    public MasterRecordsResponse getRecipient(@RequestBody @Valid MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getRecipient {}", request.toString());
            return masterRecordsService.getRecipient(request, httpServletRequest, httpServletResponse);
        }catch (Exception e){
            log.error("Error occurred getting getRecipient {} request {}", e.getMessage(), request.toString());
            e.printStackTrace();
            httpServletResponse.setStatus(500);
            return null;
        }

    }
    @PostMapping(value = "/organisation", produces = MediaType.APPLICATION_JSON_VALUE)
    public MasterRecordsResponse getOrganization(@RequestBody @Valid MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getOrganization {}", request.toString());
            return masterRecordsService.getOrganization(request, httpServletRequest, httpServletResponse);
        }catch (Exception e){
            log.error("Error occurred getting getOrganization {} request {}", e.getMessage(), request.toString());
            e.printStackTrace();
            httpServletResponse.setStatus(500);
            return null;
        }

    }
    @PostMapping(value = "/organisation_ordertypes", produces = MediaType.APPLICATION_JSON_VALUE)
    public MasterRecordsResponse getOrganizationOrderType(@RequestBody @Valid MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getOrganizationOrderType {}", request.toString());
            return masterRecordsService.getOrganizationOrderType(request, httpServletRequest, httpServletResponse);
        }catch (Exception e){
            log.error("Error occurred getting getOrganizationOrderType {} request {}", e.getMessage(), request.toString());
            e.printStackTrace();
            httpServletResponse.setStatus(500);
            return null;
        }

    }
    @PostMapping(value = "/lineitems", produces = MediaType.APPLICATION_JSON_VALUE)
    public MasterRecordsResponse getLineItems(@RequestBody @Valid MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getLineItems {}", request.toString());
            return masterRecordsService.getLineItem(request, httpServletRequest, httpServletResponse);
        }catch (Exception e){
            log.error("Error occurred getting getLineItems {} request {}", e.getMessage(), request.toString());
            e.printStackTrace();
            httpServletResponse.setStatus(500);
            return null;
        }

    }
    @PostMapping(value = "/district", produces = MediaType.APPLICATION_JSON_VALUE)
    public MasterRecordsResponse getDistricts(@RequestBody @Valid MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getDistricts {}", request.toString());
            return masterRecordsService.getDistrict(request, httpServletRequest, httpServletResponse);
        }catch (Exception e){
            log.error("Error occurred getting getdistricts {} request {}", e.getMessage(), request.toString());
            e.printStackTrace();
            httpServletResponse.setStatus(500);
            return null;
        }

    }
    @PostMapping(value = "/facility", produces = MediaType.APPLICATION_JSON_VALUE)
    public MasterRecordsResponse getFacility(@RequestBody @Valid MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            log.info("getFacilities {}", request.toString());
            return masterRecordsService.getFacility(request, httpServletRequest, httpServletResponse);
        }catch (Exception e){
            log.error("Error occurred getting getFacilities {} request {}", e.getMessage(), request.toString());
            e.printStackTrace();
            httpServletResponse.setStatus(500);
            return null;
        }

    }

    @PostMapping(value = "/financers", produces = MediaType.APPLICATION_JSON_VALUE)
    public FinancierResponse fetch(@jakarta.validation.Valid @RequestBody FinancierRequest financierRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {

        try {
            log.info("fetch new financier {}"  ,  financierRequest);

            return financierService.findAll(financierRequest,  httpServletRequest, httpServletResponse);

        }catch (Exception e){
            httpServletResponse.setStatus(500);
            log.error("An error occurred {} request {}",e.getMessage(), financierRequest);
            e.printStackTrace();
            return null;
        }


    }
    @PostMapping(value = "/transactionType", produces = "application/json")
    public TransactionResponse<FundAllocation> fetchAll() {
        return financierService.findAllTransactions();
    }

    @PostMapping(value = "/budget", produces = MediaType.APPLICATION_JSON_VALUE)
    public MasterRecordsResponse getBudget(@RequestBody MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {

        try {
            log.info("fetch new budget {}"  ,  request);

            return masterRecordsService.getBudget(request,httpServletRequest,httpServletResponse);

        }catch (Exception e){
            httpServletResponse.setStatus(500);
            log.error("An error occurred {} request {}",e.getMessage(), request);
            e.printStackTrace();
            return null;
        }
    }
    @PostMapping(value = "/mdrpatient", produces = MediaType.APPLICATION_JSON_VALUE)
    public MasterRecordsResponse getMdrPatient(@RequestBody MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {

        try {
            log.info("fetch mdr patient {}"  ,  request);

            return masterRecordsService.getMdrPatient(request,httpServletRequest,httpServletResponse);

        }catch (Exception e){
            httpServletResponse.setStatus(500);
            log.error("An error occurred {} request {}",e.getMessage(), request);
            e.printStackTrace();
            return null;
        }
    }
    @PostMapping(value = "/beneficiary", produces = MediaType.APPLICATION_JSON_VALUE)
    public MasterRecordsResponse getBeneficiary(@RequestBody MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {

        try {
            log.info("fetch beneficiary  {}"  ,  request);

            return masterRecordsService.getBeneficiary(request,httpServletRequest,httpServletResponse);

        }catch (Exception e){
            httpServletResponse.setStatus(500);
            log.error("An error occurred {} request {}",e.getMessage(), request);
            e.printStackTrace();
            return null;
        }
    }


}
