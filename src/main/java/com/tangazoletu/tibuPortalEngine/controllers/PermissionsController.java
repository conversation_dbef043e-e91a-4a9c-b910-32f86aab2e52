package com.tangazoletu.tibuPortalEngine.controllers;

import com.tangazoletu.tibuPortalEngine.dto.*;
import com.tangazoletu.tibuPortalEngine.service.PermissionService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping("/api/test/permissions")
@CrossOrigin(origins = "*")
public class PermissionsController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PermissionsController.class);

    @Autowired
    private PermissionService menuPermissionService;

//    @PostMapping("/permissions")
//    public ResponseEntity<List<MenuPermissionDto>> getMenuPermissions(HttpServletRequest request) {
//        try {
//            // Get user from Spring Security context
//            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
//            String userId = authentication.getName(); // or however you get the user ID
//
//            // Alternative: If you're using session-based authentication
//            // String userId = (String) request.getSession().getAttribute("user");
//
//            if (userId == null || userId.isEmpty()) {
//                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
//            }
//
//            List<MenuPermissionDto> menuPermissions = menuPermissionService.getMenuPermissions(userId);
//            return ResponseEntity.ok(menuPermissions);
//
//        } catch (Exception e) {
//            e.printStackTrace();
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
//        }
//    }

    @PostMapping(value= "/permissions", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<PermissionsResponse> getMenuPermissionsForUser(@RequestBody PermissionRequest permRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            // This endpoint allows getting permissions for a specific user
            // Add authorization checks as needed
            List<MenuPermissionDto> menuPermissions = menuPermissionService.getMenuPermissions(permRequest.getUserId());
            PermissionsResponse response= PermissionsResponse.builder()
                    .responseCode("00")
                    .responseMessage("Fetched menu permission succesfully.")
                    .data(PermissionsData.builder().content(menuPermissions).build())
                    .build();
            LOGGER.info("All menu permissions {}",response);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            LOGGER.info("An error occurred fetching permissions > {}",e.getMessage());
            PermissionsResponse errorResponse = PermissionsResponse.builder()
                    .responseCode("01")
                    .responseMessage("Unable to fetch menu permissions")
                    .data(PermissionsData.builder().content(List.of()).build())
                    .build();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    @PostMapping("/child")
    public ResponseEntity<PermissionResponse> getChildPermissions(
            @RequestBody ChildPermissionRequest request) {
        try {
            List<ChildPermissionDto> permissions = menuPermissionService.getChildPermissions(request);
            PermissionResponse permissionResponse = PermissionResponse.builder()
                    .responseCode("00")
                    .responseMessage("Child permissions fetched succesfully")
                    .content(permissions)
                    .build();
            return ResponseEntity.ok(permissionResponse);
        } catch (Exception e) {
            PermissionResponse errprResponse = PermissionResponse.builder()
                    .responseCode("01")
                    .responseMessage("Error fetching child permissions")
                    .content(null)
                    .build();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(errprResponse);
        }
    }

}
