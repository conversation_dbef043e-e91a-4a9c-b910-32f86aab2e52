package com.tangazoletu.tibuPortalEngine.controllers;

import com.tangazoletu.tibuPortalEngine.dto.*;
import com.tangazoletu.tibuPortalEngine.exceptions.ResourceNotFoundException;
import com.tangazoletu.tibuPortalEngine.repositories.MessageOutboxRepository;
import com.tangazoletu.tibuPortalEngine.repositories.MessageTemplateRepository;
import com.tangazoletu.tibuPortalEngine.service.MeetingService;
import com.tangazoletu.tibuPortalEngine.service.MessageService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;

@RestController
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/test/messages")
@Slf4j
@RequiredArgsConstructor
public class MessagesController {

    @Autowired
    private MessageOutboxRepository messageOutboxRepository;
    @Autowired
    private MessageService messageService;
    @PostMapping(value = "/sms", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<MessageResponse> listMessages(
            @RequestBody(required = false) MessageFilterRequest filter,
            HttpServletResponse servletResponse,
            HttpServletRequest request,
            Pageable pageable
    ) {
        try {
            Page<MessageOutboxDTO> page = messageService.getFilteredMessages(filter, pageable);
            List<MessageOutboxDTO> content = page.getContent();

            // Use LinkedHashMap to preserve insertion order (content first, then page)
            Map<String, Object> data = new LinkedHashMap<>();
            data.put("content", content);

            Map<String, Object> pageInfo = new LinkedHashMap<>();
            pageInfo.put("size", page.getSize());
            pageInfo.put("number", page.getNumber());
            pageInfo.put("totalElements", page.getTotalElements());
            pageInfo.put("totalPages", page.getTotalPages());

            data.put("page", pageInfo);

            // Build response
            MessageResponse response = new MessageResponse();
            response.setResponseCode("00");
            response.setResponseMessage("Success");
            response.setData(data);
            response.setMessage(null); // Optional

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            e.printStackTrace();

            MessageResponse errorResponse = new MessageResponse();
            errorResponse.setResponseCode("01");
            errorResponse.setResponseMessage("Internal Server Error");
            errorResponse.setData(null);
            errorResponse.setMessage(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }





    @Autowired
    private MessageTemplateRepository messageTemplateRepository;

    @PostMapping(value = "/templates", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<MessageResponse> listMessageTemplate(
            @RequestBody(required = false) MessageTemplateRequest request,
            Pageable pageable,
            HttpServletResponse httpServletResponse
    ) {
        try {
            Page<MessageTemplateDTO> page = messageService.getMessageTemplates(request, pageable);
            List<MessageTemplateDTO> content = page.getContent();

            // Use LinkedHashMap to maintain content before page
            Map<String, Object> data = new LinkedHashMap<>();
            data.put("content", content);

            Map<String, Object> pageInfo = new LinkedHashMap<>();
            pageInfo.put("size", page.getSize());
            pageInfo.put("number", page.getNumber());
            pageInfo.put("totalElements", page.getTotalElements());
            pageInfo.put("totalPages", page.getTotalPages());

            data.put("page", pageInfo);

            MessageResponse response = new MessageResponse();
            response.setResponseCode("00");
            response.setResponseMessage("Success");
            response.setData(data);
            response.setMessage(null); // Optional

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            e.printStackTrace();

            MessageResponse errorResponse = new MessageResponse();
            errorResponse.setResponseCode("01");
            errorResponse.setResponseMessage("Internal Server Error");
            errorResponse.setData(null);
            errorResponse.setMessage(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }




}
