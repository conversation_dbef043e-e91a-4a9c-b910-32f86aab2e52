package com.tangazoletu.tibuPortalEngine.controllers;

import com.tangazoletu.tibuPortalEngine.dto.*;
import com.tangazoletu.tibuPortalEngine.repositories.MessageOutboxRepository;
import com.tangazoletu.tibuPortalEngine.repositories.MessageTemplateRepository;
import com.tangazoletu.tibuPortalEngine.service.MessageService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@RestController
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/test/messages")
@Slf4j
@RequiredArgsConstructor
public class MessagesController {

    @Autowired
    private MessageOutboxRepository messageOutboxRepository;
    @Autowired
    private MessageService messageService;
    @PostMapping(value = "/sms", produces = MediaType.APPLICATION_JSON_VALUE)
    public MessageResponse listMessages(
            @RequestBody(required = false) MessageFilterRequest filter,
            HttpServletResponse servletResponse,
            HttpServletRequest request
    ) {
        try {
            return messageService.getMessages(filter, request, servletResponse);

        } catch (Exception e) {
            e.printStackTrace();
            servletResponse.setStatus(500);
            return null;
        }
    }





    @Autowired
    private MessageTemplateRepository messageTemplateRepository;

    @PostMapping(value = "/templates", produces = MediaType.APPLICATION_JSON_VALUE)
    public MessageResponse listMessageTemplate(
            @RequestBody(required = false) MessageTemplateRequest request,
            HttpServletResponse httpServletResponse,
            HttpServletRequest httpServletRequest
    ) {
        try {
            return  messageService.getMessageTemplates(request, httpServletRequest,httpServletResponse);

        } catch (Exception e) {
            e.printStackTrace();
            httpServletResponse.setStatus(500);

            return null;
        }
    }
}
