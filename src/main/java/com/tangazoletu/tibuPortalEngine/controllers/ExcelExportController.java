package com.tangazoletu.tibuPortalEngine.controllers;

import com.tangazoletu.tibuPortalEngine.annotations.ExcelColumn;
import com.tangazoletu.tibuPortalEngine.dto.EventCodesExportRequest;
import com.tangazoletu.tibuPortalEngine.dto.ExportRequest;
import com.tangazoletu.tibuPortalEngine.dto.MeetingCodesRequest;
import com.tangazoletu.tibuPortalEngine.enums.PortalReports;
import com.tangazoletu.tibuPortalEngine.service.EventCodesExcelExportService;
import com.tangazoletu.tibuPortalEngine.service.MeetingExportService;
import com.tangazoletu.tibuPortalEngine.service.ReportService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/test/exports")
public class ExcelExportController {

    @Autowired
    private ReportService reportService;

    @Autowired
    EventCodesExcelExportService exportEventCodesToExcel;

    @Autowired
    private MeetingExportService meetingExportService;

    // Generic request DTO

    // Inner class to hold field metadata
    private static class FieldMetadata {
        private String fieldName;
        private String displayName;
        private int order;
        private String dateFormat;
        private boolean exclude;

        public FieldMetadata(String fieldName, String displayName, int order, String dateFormat, boolean exclude) {
            this.fieldName = fieldName;
            this.displayName = displayName;
            this.order = order;
            this.dateFormat = dateFormat;
            this.exclude = exclude;
        }

        // Getters
        public String getFieldName() { return fieldName; }
        public String getDisplayName() { return displayName; }
        public int getOrder() { return order; }
        public String getDateFormat() { return dateFormat; }
        public boolean isExclude() { return exclude; }
    }

    @PostMapping("/export-excel")
    public ResponseEntity<byte[]> exportToExcelGeneric(@RequestBody ExportRequest request) throws Exception {

        if (request.getReportName() == null || request.getReportName().trim().isEmpty()) {
            throw new IllegalArgumentException("reportName is required");
        }

        // Get data using the generic report service
        List<?> reportData = reportService.getReportData(request.getReportName(), request.getFilters());

        if (reportData.isEmpty()) {
            throw new RuntimeException("No data found to export for report: " + request.getReportName());
        }

        // checking the report name
        String reportNamePayload = request.getReportName();

        if (!PortalReports.isValid(request.getReportName())) {
            return ResponseEntity.badRequest()
                    .body(("Invalid report name: " + reportNamePayload).getBytes());
        }

        // Create filename with date
        String fileNameWithDate = request.getReportName() + "_Report " +
                LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy MMM dd"));

        // Create Excel file
        byte[] excelBytes = createExcelFile(reportData, fileNameWithDate);

        // Return response
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));
        headers.setContentDispositionFormData("attachment", fileNameWithDate + ".xlsx");
        headers.setContentLength(excelBytes.length);
        headers.setCacheControl("no-cache, no-store, must-revalidate");
        headers.setPragma("no-cache");
        headers.setExpires(0);

        return ResponseEntity.ok()
                .headers(headers)
                .body(excelBytes);
    }

    /**
     * Create Excel file from generic data using annotations
     */
    private byte[] createExcelFile(List<?> data, String sheetName) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook();

        try {
            Sheet sheet = workbook.createSheet(sheetName);

            // Get the first object to determine structure
            Object firstObject = data.get(0);
            List<FieldMetadata> fieldMetadata = getFieldMetadata(firstObject.getClass());

            // Create headers (row 7, 0-indexed = row 8 in Excel)
            Row headerRow = sheet.createRow(7);
            int headerIndex = 0;

            for (FieldMetadata metadata : fieldMetadata) {
                headerRow.createCell(headerIndex++).setCellValue(metadata.getDisplayName());
            }

            // Style headers
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);

            for (Cell cell : headerRow) {
                cell.setCellStyle(headerStyle);
            }

            // Add data rows
            int rowIndex = 8; // Start from row 9 (0-indexed = 8)
            for (Object dto : data) {
                Row dataRow = sheet.createRow(rowIndex++);
                int cellIndex = 0;

                for (FieldMetadata metadata : fieldMetadata) {
                    Cell cell = dataRow.createCell(cellIndex++);
                    Object value = getFieldValue(dto, metadata.getFieldName());

                    setCellValue(cell, value, metadata.getDateFormat());
                }
            }

            // ⬇ Add signature block after data
            Row dateRow = sheet.createRow(rowIndex++);
            dateRow.createCell(0).setCellValue("Date: " +
                    LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd-MMM-yyyy h:mm a")));

            Row signedByRow = sheet.createRow(rowIndex++);
            signedByRow.createCell(0).setCellValue("Signed by Name:");

            Row signatureRow = sheet.createRow(rowIndex++);
            signatureRow.createCell(0).setCellValue("Signature:");

            // Add image if exists
            addImageToSheet(workbook, sheet, 0, 0);

            // Auto-size columns
            for (int i = 0; i < fieldMetadata.size(); i++) {
                sheet.autoSizeColumn(i);
            }

            // Set workbook properties
            workbook.getProperties().getCoreProperties().setCreator("Tangazo Letu");
            workbook.getProperties().getCoreProperties().setTitle(sheetName);
            workbook.getProperties().getCoreProperties().setDescription("Tangazoletu Bulk SMS Service Reports.");

            // Convert to byte array
            byte[] excelBytes;
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                workbook.write(outputStream);
                outputStream.flush();
                excelBytes = outputStream.toByteArray();
            }

            return excelBytes;

        } finally {
            workbook.close();
        }
    }

    @PostMapping("/event-codes-excel")
    public ResponseEntity<byte[]> exportEventCodesToExcel(@RequestBody EventCodesExportRequest request) throws Exception {

        // Validate required parameters
        if (request.getMeetingCode() == null || request.getMeetingCode().trim().isEmpty()) {
            throw new IllegalArgumentException("meetingCode is required");
        }

        if (request.getReportName() == null || request.getReportName().trim().isEmpty()) {
            throw new IllegalArgumentException("fileName is required");
        }

        String fileNameWithDate = request.getReportName() + "_Report_" +
                LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy_MMM_dd"));

        // Export to Excel
        byte[] excelBytes = exportEventCodesToExcel.exportEventCodesToExcel(
                request.getMeetingCode(),
                fileNameWithDate,
                request.isUseLatest()
        );

        // Return response as downloadable Excel file
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));
        headers.setContentDispositionFormData("attachment", fileNameWithDate);
        headers.setContentLength(excelBytes.length);
        headers.setCacheControl("no-cache, no-store, must-revalidate");
        headers.setPragma("no-cache");
        headers.setExpires(0);

        return ResponseEntity.ok()
                .headers(headers)
                .body(excelBytes);
    }

    /**
     * Get field metadata from class using annotations
     */
    private List<FieldMetadata> getFieldMetadata(Class<?> clazz) {
        List<FieldMetadata> fieldMetadataList = new ArrayList<>();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            String fieldName = field.getName();

            // Skip basic unwanted fields
            if (shouldSkipBasicField(fieldName)) {
                continue;
            }

            ExcelColumn annotation = field.getAnnotation(ExcelColumn.class);

            if (annotation != null) {
                // Field has annotation
                if (annotation.exclude()) {
                    continue; // Skip excluded fields
                }

                String displayName = annotation.value().isEmpty() ?
                        convertCamelCaseToDisplayName(fieldName) : annotation.value();

                fieldMetadataList.add(new FieldMetadata(
                        fieldName,
                        displayName,
                        annotation.order(),
                        annotation.dateFormat(),
                        false
                ));
            } else {
                // Field doesn't have annotation - apply default rules
                if (!containsIdPattern(fieldName)) {
                    fieldMetadataList.add(new FieldMetadata(
                            fieldName,
                            convertCamelCaseToDisplayName(fieldName),
                            Integer.MAX_VALUE, // Default order
                            "dd/MM/yyyy HH:mm", // Default date format
                            false
                    ));
                }
            }
        }

        // Sort by order
        return fieldMetadataList.stream()
                .sorted(Comparator.comparingInt(FieldMetadata::getOrder)
                        .thenComparing(FieldMetadata::getDisplayName))
                .collect(Collectors.toList());
    }

    /**
     * Determine if a field should be skipped (basic checks)
     */
    private boolean shouldSkipBasicField(String fieldName) {
        return fieldName.equals("primarykey") ||
                fieldName.equals("serialVersionUID") ||
                fieldName.startsWith("$");
    }

    /**
     * Check if field name contains ID patterns that should be skipped
     */
    private boolean containsIdPattern(String fieldName) {
        String lowerFieldName = fieldName.toLowerCase();

        return lowerFieldName.endsWith("id") ||
                lowerFieldName.startsWith("id") ||
                fieldName.contains("Id") ||
                lowerFieldName.equals("id");
    }

    /**
     * Convert camelCase field names to readable display names
     */
    private String convertCamelCaseToDisplayName(String fieldName) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < fieldName.length(); i++) {
            char c = fieldName.charAt(i);
            if (i > 0 && Character.isUpperCase(c)) {
                result.append(" ");
            }
            if (i == 0) {
                result.append(Character.toUpperCase(c));
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }

    /**
     * Get field value from any object using reflection
     */
    private Object getFieldValue(Object obj, String fieldName) {
        try {
            Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(obj);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            // Try getter method as fallback
            try {
                String getterName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                return obj.getClass().getMethod(getterName).invoke(obj);
            } catch (Exception ex) {
                System.err.println("Could not get value for field: " + fieldName + " in class: " + obj.getClass().getSimpleName());
                return null;
            }
        }
    }

    /**
     * Set cell value based on object type with custom date formatting
     */
    private void setCellValue(Cell cell, Object value, String dateFormat) {
        if (value == null) {
            cell.setCellValue("");
        } else if (value instanceof LocalDateTime) {
            LocalDateTime dateTime = (LocalDateTime) value;
            if (dateFormat.contains("%s")) {
                // Handle ordinal date format
                String formattedDate = dateTime.format(DateTimeFormatter.ofPattern(dateFormat.replace("%s", "'")));
                cell.setCellValue(formatOrdinalDate(formattedDate, dateTime.getDayOfMonth()));
            } else {
                cell.setCellValue(dateTime.format(DateTimeFormatter.ofPattern(dateFormat)));
            }
        } else if (value instanceof java.sql.Timestamp) {
            java.sql.Timestamp timestamp = (java.sql.Timestamp) value;
            LocalDateTime dateTime = timestamp.toLocalDateTime();
            if (dateFormat.contains("%s")) {
                String formattedDate = dateTime.format(DateTimeFormatter.ofPattern(dateFormat.replace("%s", "'")));
                cell.setCellValue(formatOrdinalDate(formattedDate, dateTime.getDayOfMonth()));
            } else {
                cell.setCellValue(dateTime.format(DateTimeFormatter.ofPattern(dateFormat)));
            }
        } else if (value instanceof java.util.Date) {
            // Handle java.util.Date
            java.util.Date date = (java.util.Date) value;
            cell.setCellValue(date);
        } else if (value instanceof Number) {
            cell.setCellValue(((Number) value).doubleValue());
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else {
            cell.setCellValue(value.toString());
        }
    }

    private String formatOrdinalDate(String template, int day) {
        String suffix;
        if (day >= 11 && day <= 13) {
            suffix = "th";
        } else {
            switch (day % 10) {
                case 1: suffix = "st"; break;
                case 2: suffix = "nd"; break;
                case 3: suffix = "rd"; break;
                default: suffix = "th"; break;
            }
        }
        return template.replace("%s", suffix);
    }

    private void addImageToSheet(XSSFWorkbook workbook, Sheet sheet, int row, int col) {
        try (InputStream imageStream = getClass().getResourceAsStream("/TIBU.png")) {
            if (imageStream == null) {
                System.err.println("Image not found: /TIBU.png");
                return;
            }

            byte[] imageBytes = IOUtils.toByteArray(imageStream);
            int pictureIndex = workbook.addPicture(imageBytes, Workbook.PICTURE_TYPE_JPEG);

            BufferedImage bufferedImage = ImageIO.read(new ByteArrayInputStream(imageBytes));
            int imageWidth = bufferedImage.getWidth();
            int imageHeight = bufferedImage.getHeight();

            double excelColWidthPx = 64.0;
            double excelRowHeightPx = 20.0;

            int colSpan = (int) Math.round(imageWidth / excelColWidthPx);
            int rowSpan = (int) Math.round(imageHeight / excelRowHeightPx);

            XSSFDrawing drawing = (XSSFDrawing) sheet.createDrawingPatriarch();
            XSSFClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, col, row, col + colSpan, row + rowSpan);

            drawing.createPicture(anchor, pictureIndex);

            for (int i = col; i < col + colSpan; i++) {
                sheet.autoSizeColumn(i);
            }

        } catch (IOException e) {
            System.err.println("Could not add image to Excel: " + e.getMessage());
        }
    }

    /**
     * Export meeting data to Excel format
     */
    @PostMapping("/meeting-excel")
    public ResponseEntity<byte[]> exportMeetingToExcel(
            @RequestBody MeetingCodesRequest request,
            jakarta.servlet.http.HttpServletRequest httpServletRequest,
            jakarta.servlet.http.HttpServletResponse httpServletResponse) {

        try {
            // Generate Excel file
            byte[] excelBytes = meetingExportService.exportToExcel(request, httpServletRequest, httpServletResponse);

            // Create filename
            String fileName = "Meeting_" + request.getMeetingcode() + "_Mode_" + request.getMode() +
                            "_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy_MM_dd")) + ".xlsx";

            // Set response headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(excelBytes.length);
            headers.setCacheControl("no-cache, no-store, must-revalidate");
            headers.setPragma("no-cache");
            headers.setExpires(0);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(excelBytes);

        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(("Error generating Excel file: " + e.getMessage()).getBytes());
        }
    }

    /**
     * Export meeting data to CSV format
     */
    @PostMapping("/meeting-csv")
    public ResponseEntity<byte[]> exportMeetingToCsv(
            @RequestBody MeetingCodesRequest request,
            jakarta.servlet.http.HttpServletRequest httpServletRequest,
            jakarta.servlet.http.HttpServletResponse httpServletResponse) {

        try {
            // Generate CSV file
            byte[] csvBytes = meetingExportService.exportToCsv(request, httpServletRequest, httpServletResponse);

            // Create filename
            String fileName = "Meeting_" + request.getMeetingcode() + "_Mode_" + request.getMode() +
                            "_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy_MM_dd")) + ".csv";

            // Set response headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("text/csv"));
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(csvBytes.length);
            headers.setCacheControl("no-cache, no-store, must-revalidate");
            headers.setPragma("no-cache");
            headers.setExpires(0);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(csvBytes);

        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(("Error generating CSV file: " + e.getMessage()).getBytes());
        }
    }
}