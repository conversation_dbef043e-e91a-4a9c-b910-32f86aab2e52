package com.tangazoletu.tibuPortalEngine.repositories;
import com.tangazoletu.tibuPortalEngine.entities.ExpectedVsActualView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ExpectedVsActualViewRepository extends JpaRepository<ExpectedVsActualView, Long>, JpaSpecificationExecutor<ExpectedVsActualView> {

    @Query(value = """
        SELECT e FROM ExpectedVsActualView e
        WHERE (:orgFilter IS NULL OR e.orgId IN :orgFilter)
          AND (
              :keyword IS NULL OR :keyword = '' OR
              LOWER(e.trxType) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
              LOWER(e.recipientName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
              LOWER(e.msisdn) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
              CAST(e.creditAmount AS string) LIKE CONCAT('%', :keyword, '%') OR
              CAST(e.trxDate AS string) LIKE CONCAT('%', :keyword, '%')
          )
    """)
    Page<ExpectedVsActualView> searchCompletedTransactions(
            @Param("keyword") String keyword,
            @Param("orgFilter") java.util.List<Integer> orgIds,
            Pageable pageable
    );
}
