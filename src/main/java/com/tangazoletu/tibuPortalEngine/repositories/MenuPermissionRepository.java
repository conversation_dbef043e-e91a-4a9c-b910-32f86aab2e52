package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.Permission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface MenuPermissionRepository extends JpaRepository<Permission, Integer> {
    @Query("SELECT DISTINCT p.module FROM Permission p WHERE p.module != 'System' ORDER BY p.module")
    List<String> findDistinctModules();

    @Query(value = """
        SELECT p.menu_name, p.url, p.id, p.menu_level, p.menu_pos, p.child_of 
        FROM permission p 
        WHERE p.id IN (
            SELECT pm.permission 
            FROM permissionmap pm 
            WHERE pm.role IN (
                SELECT rm.role 
                FROM rolemap rm 
                WHERE rm.user = :userId
            )
        ) 
        AND (p.excempted_users IS NULL OR p.excempted_users = '' OR FIND_IN_SET(:userId, p.excempted_users) = 0)
        AND p.menu_level = :menuLevel 
        AND p.is_menu = 1 
        AND p.module = :module 
        AND (:parentId IS NULL OR p.child_of = :parentId)
        ORDER BY p.menu_pos ASC
    """, nativeQuery = true)
    List<Object[]> findMenuItemsByUserAndModule(
            @Param("userId") String userId,
            @Param("module") String module,
            @Param("menuLevel") Integer menuLevel,
            @Param("parentId") Long parentId
    );

    @Query(value = """
        SELECT p.id, p.title, p.description 
        FROM permission p 
        WHERE p.id IN (
            SELECT pm.permission 
            FROM permissionmap pm 
            WHERE pm.role IN (
                SELECT rm.role 
                FROM rolemap rm 
                WHERE rm.user = :userId
            )
        ) 
        AND (p.excempted_users IS NULL 
             OR p.excempted_users = '' 
             OR FIND_IN_SET(:userId, p.excempted_users) = 0) 
        AND p.is_menu = 0 
        AND p.module = :module
        """, nativeQuery = true)
    List<Object[]> findChildPermissionsByModule(
            @Param("userId") String userId,
            @Param("module") String module
    );
}
