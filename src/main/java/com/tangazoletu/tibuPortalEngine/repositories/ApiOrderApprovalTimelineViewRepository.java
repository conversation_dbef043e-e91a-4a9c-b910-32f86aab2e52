package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.ApiOrderApprovalTimelineView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

@Repository
public interface ApiOrderApprovalTimelineViewRepository extends JpaRepository<ApiOrderApprovalTimelineView, Integer>, JpaSpecificationExecutor<ApiOrderApprovalTimelineView> {

    @Query("""
        SELECT a FROM ApiOrderApprovalTimelineView a
        WHERE (:province IS NULL OR a.province = :province)
          AND (:county IS NULL OR a.county = :county)
          AND (:keyword IS NULL OR :keyword = '' OR
               LOWER(a.ordertype) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
               LOWER(a.county) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
               LOWER(a.province) LIKE LOWER(CONCAT('%', :keyword, '%'))
          )
          AND (
            (:dateFrom IS NULL AND MONTH(a.requestTime) = MONTH(CURRENT_DATE) AND YEAR(a.requestTime) = YEAR(CURRENT_DATE))
            OR (:dateFrom IS NOT NULL AND a.requestTime >= :dateFrom)
        )
        AND (:dateTo IS NULL OR a.requestTime <= :dateTo)
        ORDER BY a.id DESC
    """)
    Page<ApiOrderApprovalTimelineView> searchWithFilters(
            @Param("province") String province,
            @Param("county") String county,
            @Param("keyword") String keyword,
            @Param("dateFrom") LocalDateTime dateFrom,
            @Param("dateTo") LocalDateTime dateTo,
            Pageable pageable
    );
}
