package com.tangazoletu.tibuPortalEngine.repositories;


import com.tangazoletu.tibuPortalEngine.entities.BatchFileProcess;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.time.LocalDateTime;

@Repository
public interface BatchFileProcessRepository extends JpaRepository<BatchFileProcess, Integer>, JpaSpecificationExecutor<BatchFileProcess> {

    @Query("""
        SELECT b FROM BatchFileProcess b 
        WHERE (:orgId IS NULL OR b.orgId = :orgId)
        AND (
            :keyword IS NULL OR :keyword = '' OR
            LOWER(b.fileLogName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(b.statusDescription) LIKE LOWER(CONCAT('%', :keyword, '%'))
        )
        AND (:dateFrom IS NULL OR b.dateCreated >= :dateFrom)
        AND (:dateTo IS NULL OR b.dateCreated <= :dateTo)
        ORDER BY b.id DESC
    """)
    Page<BatchFileProcess> searchBatchFiles(@Param("orgId") Integer orgId,
                                            @Param("keyword") String keyword,
                                            @Param("dateFrom") LocalDateTime dateFrom,
                                            @Param("dateTo") LocalDateTime dateTo,
                                            Pageable pageable);
}
