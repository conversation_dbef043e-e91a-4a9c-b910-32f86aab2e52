package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.AttendanceSummaryView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

public interface AttendanceSummaryViewRepository extends JpaRepository<AttendanceSummaryView, Long> {
    Page<AttendanceSummaryView> findByMeetingCode(String meetingCode, Pageable pageable);
}
