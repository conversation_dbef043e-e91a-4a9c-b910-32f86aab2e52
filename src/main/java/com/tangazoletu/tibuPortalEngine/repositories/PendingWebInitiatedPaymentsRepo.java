package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.PendingWebInitiatedPayments;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface PendingWebInitiatedPaymentsRepo extends JpaRepository<PendingWebInitiatedPayments, Integer>, JpaSpecificationExecutor<PendingWebInitiatedPayments> {
    @Query(value = """
        SELECT p FROM PendingWebInitiatedPayments p
        WHERE (:initiatorId IS NULL OR p.initiatorId = :initiatorId)
          AND (:orgId IS NULL OR p.orgId = :orgId)
          AND (
                :keywords IS NULL OR LOWER(p.batchNo) LIKE LOWER(CONCAT('%', :keywords, '%'))
                OR LOWER(p.budgetLine) LIKE LOWER(CONCAT('%', :keywords, '%'))
                OR LOWER(p.requestType) LIKE LOWER(CONCAT('%', :keywords, '%'))
                OR LOWER(p.phone) LIKE LOWER(CONCAT('%', :keywords, '%'))
                OR LOWER(p.beneficiary) LIKE LOWER(CONCAT('%', :keywords, '%'))
                OR LOWER(p.region) LIKE LOWER(CONCAT('%', :keywords, '%'))
                OR LOWER(p.county) LIKE LOWER(CONCAT('%', :keywords, '%'))
                OR CAST(p.requestTime AS string) LIKE LOWER(CONCAT('%', :keywords, '%'))
             )
          AND (:province IS NULL OR p.region = :province)
          AND (:budget IS NULL OR p.budgetLine = :budget)
          AND (:counties IS NULL OR p.county IN :counties)
          AND (:dateFrom IS NULL OR p.requestTime >= :dateFrom)
          AND (:dateTo IS NULL OR p.requestTime <= :dateTo)
        ORDER BY p.id DESC
        """)
    Page<PendingWebInitiatedPayments> searchWithFilters(
            @Param("initiatorId") Long initiatorId,
            @Param("orgId") Long orgId,
            @Param("keywords") String keywords,
            @Param("province") String province,
            @Param("budget") String budget,
            @Param("counties") List<String> counties,
            @Param("dateFrom") LocalDateTime dateFrom,
            @Param("dateTo") LocalDateTime dateTo,
            Pageable pageable
    );
}
