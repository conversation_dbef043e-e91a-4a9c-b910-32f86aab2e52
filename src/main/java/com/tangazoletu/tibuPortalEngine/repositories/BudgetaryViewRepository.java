package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.BudgetaryView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface BudgetaryViewRepository extends JpaRepository<BudgetaryView, Long>, JpaSpecificationExecutor<BudgetaryView> {

    @Query(value = """
        SELECT b FROM BudgetaryView b
        WHERE 
            (:keyword IS NULL OR LOWER(b.budgetLine) LIKE LOWER(CONCAT('%', :keyword, '%')) 
                              OR LOWER(b.county) LIKE LOWER(CONCAT('%', :keyword, '%'))
                              OR LOWER(b.province) LIKE LOWER(CONCAT('%', :keyword, '%')))
        AND (:province IS NULL OR b.province = :province)
        AND (:county IS NULL OR b.county = :county)
        AND (:budget IS NULL OR b.budgetLine IN :budget)
        AND (:month IS NULL OR b.month = :month)
        AND (:year IS NULL OR b.year = :year)
        """)
    Page<BudgetaryView> findBudgetaryData(
            @Param("keyword") String keyword,
            @Param("province") String province,
            @Param("county") String county,
            @Param("budget") List<String> budget,
            @Param("month") String month,
            @Param("year") String year,
            Pageable pageable
    );
}
