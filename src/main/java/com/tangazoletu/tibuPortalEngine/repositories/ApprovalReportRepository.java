package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.ApprovalReport;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ApprovalReportRepository extends JpaRepository<ApprovalReport, Long> {

    @Query("""
    SELECT ar FROM ApprovalReport ar
    WHERE (:approvalStatus IS NULL OR LOWER(TRIM(ar.approvalStatus)) LIKE LOWER(CONCAT('%', :approvalStatus, '%')))
    """)
    Page<ApprovalReport> fetchApprovalReportsFilteredByStatus(
            @Param("approvalStatus") String approvalStatus,
            Pageable pageable
    );

}
