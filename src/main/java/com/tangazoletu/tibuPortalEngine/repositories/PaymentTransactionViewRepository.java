package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.PaymentTransactionView;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface PaymentTransactionViewRepository extends JpaRepository<PaymentTransactionView, Long> {

    List<PaymentTransactionView> findByOrderIdOrderByTimeCompletedDesc(Long orderId);
}
