package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.MDRpatientsandcostpercounty;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MDRpatientsandcostpercountyRepo extends JpaRepository<MDRpatientsandcostpercounty,Integer>, JpaSpecificationExecutor<MDRpatientsandcostpercounty> {

    @Query("""
        SELECT v FROM MDRpatientsandcostpercounty v
        WHERE 
            (:treatmentModel IS NULL OR LOWER(v.requestType) = LOWER(:treatmentModel)) AND
            (:county IS NULL OR LOWER(v.county) = LOWER(:county)) AND
            (:keyword IS NULL OR :keyword = '' OR 
                LOWER(v.requestType) LIKE LOWER(CONCAT('%', :keyword, '%')) OR 
                LOWER(v.county) LIKE LOWER(CONCAT('%', :keyword, '%'))
            )
                    AND (:orgId is null or v.orgId in (v.orgId))
               
        """)
    Page<MDRpatientsandcostpercounty> findFilteredSummaryReport(
            @Param("treatmentModel") String treatmentModel,
            @Param("county") String county,
            @Param("keyword") String keyword,
            @Param("orgId") List<Integer> orgIds,
            Pageable pageable
    );
}
