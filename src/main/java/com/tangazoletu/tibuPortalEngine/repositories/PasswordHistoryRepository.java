package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.PasswordHistory;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface PasswordHistoryRepository extends JpaRepository<PasswordHistory, Long> {

    @Query("SELECT p.password FROM PasswordHistory p WHERE p.userId = :user ORDER BY p.dateCreated DESC")
    List<String> findRecentHashedPasswords(@Param("user") Long user, Pageable pageable);


    @Query(value = "SELECT PASSWORD FROM PASSWORD_HISTORY WHERE USER_ID = :userId ORDER BY DATE_CREATED DESC LIMIT 5", nativeQuery = true)
    List<String> findLast5Passwords(@Param("userId") String userId);

}