package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.MessageOutbox;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MessageOutboxRepository extends JpaRepository<MessageOutbox, Long>, JpaSpecificationExecutor<MessageOutbox> {
    Page<MessageOutbox> findBySmsBatchNo(String batchNo, Pageable pageable);
    Page<MessageOutbox> findByPhoneNumber(String phone, Pageable pageable);
    Page<MessageOutbox> findByOrigin(String origin, Pageable pageable);

    @Query("SELECT m FROM MessageOutbox m " +
            "WHERE (:batchNo IS NULL OR LOWER(m.smsBatchNo) LIKE LOWER(CONCAT('%', :batchNo, '%'))) " +
            "AND (:phoneNumber IS NULL OR LOWER(m.phoneNumber) LIKE LOWER(CONCAT('%', :phoneNumber, '%'))) " +
            "AND (:origin IS NULL OR LOWER(m.origin) LIKE LOWER(CONCAT('%', :origin, '%')))")
    Page<MessageOutbox> findFiltered(
            @Param("batchNo") String batchNo,
            @Param("phoneNumber") String phoneNumber,
            @Param("origin") String origin,
            Pageable pageable
    );
    @Query("SELECT m FROM MessageOutbox m WHERE m.status = 0")
    List<MessageOutbox> findTop100PendingMessages(Pageable pageable);
}
