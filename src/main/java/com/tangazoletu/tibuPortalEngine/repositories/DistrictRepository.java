package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.County;
import com.tangazoletu.tibuPortalEngine.entities.ViewDistrictInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DistrictRepository extends JpaRepository<ViewDistrictInfo, Integer>, JpaSpecificationExecutor<ViewDistrictInfo> {

    @Query("""
        SELECT dv FROM ViewDistrictInfo dv
        WHERE (:keyword IS NULL OR 
               LOWER(dv.subCounty) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
               LOWER(dv.region) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
               LOWER(dv.county) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
               dv.timeCreated LIKE CONCAT('%', :keyword, '%'))
        ORDER BY dv.id DESC
        """)
    Page<ViewDistrictInfo> searchByKeyword(@Param("keyword") String keyword, Pageable pageable);

}
