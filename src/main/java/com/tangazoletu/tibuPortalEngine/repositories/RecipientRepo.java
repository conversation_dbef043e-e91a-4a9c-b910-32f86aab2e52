package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.Recipient;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.data.repository.query.Param;

@Repository
public interface RecipientRepo extends JpaRepository<Recipient, Long>, JpaSpecificationExecutor<Recipient> {

    @Query("SELECT r FROM Recipient r " +
            "WHERE r.inTrash = 'No' AND " +
            "(:keyword IS NULL OR :keyword = '' OR " +
            "LOWER(r.idNumber) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "LOWER(r.emailAddress) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "LOWER(r.firstname) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "LOWER(r.lastname) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "LOWER(r.msisdn) LIKE LOWER(CONCAT('%', :keyword, '%')))" +
            "ORDER BY r.id DESC")
    Page<Recipient> findByKeyword(@Param("keyword") String keyword, Pageable pageable);

    Page<Recipient> findById(Long id, Pageable pageable);
}
