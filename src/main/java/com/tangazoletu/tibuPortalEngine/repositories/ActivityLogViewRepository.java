package com.tangazoletu.tibuPortalEngine.repositories;
import com.tangazoletu.tibuPortalEngine.entities.ActivityLogView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDateTime;

@Repository
public interface ActivityLogViewRepository extends JpaRepository<ActivityLogView, Long>, JpaSpecificationExecutor<ActivityLogView> {

    @Query("""
        SELECT a FROM ActivityLogView a
        WHERE (:orgId IS NULL OR a.organisation = :orgId)
          AND (
              :keyword IS NULL OR :keyword = '' OR
              LOWER(a.activity) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
              LOWER(a.sourceIp) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
              LOWER(a.actionBy) LIKE LOWER(CONCAT('%', :keyword, '%'))
          )
          AND (:user IS NULL OR LOWER(a.actionBy) = LOWER(:user))
          AND (:dateFrom IS NULL OR a.timeCreated >= :dateFrom)
          AND (:dateTo IS NULL OR a.timeCreated <= :dateTo)
        ORDER BY a.timeCreated DESC
    """)
    Page<ActivityLogView> filterActivityLogs(
            @Param("orgId") Integer orgId,
            @Param("keyword") String keyword,
            @Param("user") String user,
            @Param("dateFrom") LocalDateTime dateFrom,
            @Param("dateTo") LocalDateTime dateTo,
            Pageable pageable
    );
}
