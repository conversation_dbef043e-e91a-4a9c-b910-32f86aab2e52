package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.FailedPaymentEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

@Repository
public interface FailedPaymentRepository extends JpaRepository<FailedPaymentEntity, Long>, JpaSpecificationExecutor<FailedPaymentEntity> {

    @Query(value = """
        SELECT f FROM FailedPaymentEntity f
        WHERE (:keywords IS NULL OR 
              LOWER(f.beneficiary) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
              LOWER(f.batchNo) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
              LOWER(f.contactNumber) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
              LOWER(f.paymentType) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
              LOWER(f.status) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
              LOWER(f.region) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
              LOWER(f.county) LIKE LOWER(CONCAT('%', :keywords, '%')))
          AND (:province IS NULL OR f.region = :province)
          AND (:budget IS NULL OR 1=1) 
          AND (:paymentType IS NULL OR f.paymentType = :paymentType)
          AND (:dateFrom IS NULL OR f.date >= :dateFrom)
          AND (:dateTo IS NULL OR f.date <= :dateTo)
        ORDER BY f.primaryKey DESC
    """)
    Page<FailedPaymentEntity> filterFailedPayments(
            @Param("keywords") String keywords,
            @Param("province") String province,
            @Param("budget") String budget,
            @Param("paymentType") String paymentType,
            @Param("dateFrom") LocalDateTime dateFrom,
            @Param("dateTo") LocalDateTime dateTo,
            Pageable pageable
    );
}
