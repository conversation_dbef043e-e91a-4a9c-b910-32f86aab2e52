package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.ReservedApprovalView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ReservedApprovalViewRepository extends JpaRepository<ReservedApprovalView, Long> {

    @Query(value = """
        SELECT r FROM ReservedApprovalView r
        WHERE (:countyId IS NULL OR r.countyId = :countyId)
        ORDER BY r.id DESC
        """)
    Page<ReservedApprovalView> fetchReversedApprovals(@Param("countyId") Integer countyId, Pageable pageable);
}
