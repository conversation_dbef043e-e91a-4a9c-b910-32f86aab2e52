package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.EventCodesView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface EventCodesViewRepo extends JpaRepository<EventCodesView, Long> {

    @Query(value = "SELECT columnOne AS Codes FROM event_codes_view WHERE meetingCode = :meetingCode " +
            "UNION ALL SELECT columnTwo FROM event_codes_view WHERE meetingCode = :meetingCode " +
            "UNION ALL SELECT columnThree FROM event_codes_view WHERE meetingCode = :meetingCode " +
            "UNION ALL SELECT columnFour FROM event_codes_view WHERE meetingCode = :meetingCode " +
            "ORDER BY Codes ASC",
            countQuery = "SELECT COUNT(*) FROM (" +
                    "SELECT columnOne FROM event_codes_view WHERE meetingCode = :meetingCode AND columnOne IS NOT NULL " +
                    "UNION ALL SELECT columnTwo FROM event_codes_view WHERE meetingCode = :meetingCode AND columnTwo IS NOT NULL " +
                    "UNION ALL SELECT columnThree FROM event_codes_view WHERE meetingCode = :meetingCode AND columnThree IS NOT NULL " +
                    "UNION ALL SELECT columnFour FROM event_codes_view WHERE meetingCode = :meetingCode AND columnFour IS NOT NULL" +
                    ") AS total",
            nativeQuery = true)
    Page<Map<String, Object>> findCodesByMeetingCode(@Param("meetingCode") String meetingCode, Pageable pageable);

    @Query(value = "SELECT columnOne AS Codes, columnTwo AS Codes, columnThree AS Codes, columnFour AS Codes FROM event_codes_view ORDER BY id ASC", nativeQuery = true)
    Page<Map<String, Object>> findAllCodes(Pageable pageable);

    @Query(value = "SELECT DISTINCT day FROM event_codes_view WHERE meetingCode = :meetingCode AND day != '1'", nativeQuery = true)
    List<Integer> getDistinctDaysForAllCodes(@Param("meetingCode") String meetingCode);

    @Query(value = "SELECT columnFour, columnThree, columnTwo, columnOne, day, meetingCode FROM event_codes_view WHERE meetingCode = :meetingCode AND day = :day ORDER BY day ASC",nativeQuery = true)
    List<Object[]> getAllEventCodesForDay(String meetingCode, String day);

}
