package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.ApiOrderRequestView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

@Repository
public interface ApiOrderRequestViewRepository extends JpaRepository<ApiOrderRequestView, Integer>, JpaSpecificationExecutor<ApiOrderRequestView> {
    @Query("""
        SELECT a FROM ApiOrderRequestView a
        WHERE (:orgId IS NULL OR a.organisation = :orgId)
        AND (
            :keyword IS NULL OR :keyword = '' OR
            LOWER(a.msisdn) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(a.name) LIKE LOWER(CONCAT('%', :keyword, '%')) 
        )
        AND (:province IS NULL OR a.province = :province)
        AND (:budget IS NULL OR a.budget = :budget)
        AND (:county IS NULL OR a.county = :county)
        AND (:batch IS NULL OR a.batchno = :batch)
        AND (
            (:dateFrom IS NULL AND MONTH(a.requestTime) = MONTH(CURRENT_DATE) AND YEAR(a.requestTime) = YEAR(CURRENT_DATE))
            OR (:dateFrom IS NOT NULL AND a.requestTime >= :dateFrom)
        )
        AND (:dateTo IS NULL OR a.requestTime <= :dateTo)
        ORDER BY a.id DESC
        """)
    Page<ApiOrderRequestView> filterApiOrders(
            @Param("orgId") Integer orgId,
            @Param("keyword") String keyword,
            @Param("province") String province,
            @Param("budget") Integer budget,
            @Param("county") String county,
            @Param("batch") String batch,
            @Param("dateFrom") LocalDateTime dateFrom,
            @Param("dateTo") LocalDateTime dateTo,
            Pageable pageable
    );
}
