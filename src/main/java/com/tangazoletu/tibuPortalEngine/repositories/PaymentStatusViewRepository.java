package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.PaymentStatusView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface PaymentStatusViewRepository extends JpaRepository<PaymentStatusView, Long> {
    @Query("""
    SELECT p FROM PaymentStatusView p
    WHERE (:keyword IS NULL OR :keyword = '' OR 
        LOWER(p.province) LIKE LOWER(CONCAT('%', :keyword, '%')) OR 
        LOWER(p.county) LIKE LOWER(CONCAT('%', :keyword, '%')) OR 
        LOWER(p.msisdn) LIKE LOWER(CONCAT('%', :keyword, '%')) OR 
        LOWER(p.beneficiary) LIKE LOWER(CONCAT('%', :keyword, '%')) OR 
        LOWER(p.batchNo) LIKE LOWER(CONCAT('%', :keyword, '%')))
    AND (:province IS NULL OR p.province = :province)
    AND (:budget IS NULL OR :budget = '')  
    AND (:counties IS NULL OR p.county IN :counties)
    AND (:paymentType IS NULL OR p.paymentType = :paymentType)
    AND (:dateFrom IS NULL OR p.requestDate >= :dateFrom)
    AND (:dateTo IS NULL OR p.requestDate <= :dateTo)
    ORDER BY p.id DESC
""")
    Page<PaymentStatusView> filterPayments(
            @Param("keyword") String keyword,
            @Param("province") String province,
            @Param("budget") String budget, // optional or ignored
            @Param("counties") List<String> counties,
            @Param("paymentType") String paymentType,
            @Param("dateFrom") LocalDateTime dateFrom,
            @Param("dateTo") LocalDateTime dateTo,
            Pageable pageable
    );

}
