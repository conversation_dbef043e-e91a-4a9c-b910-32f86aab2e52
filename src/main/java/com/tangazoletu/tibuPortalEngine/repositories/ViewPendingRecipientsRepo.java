package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.ViewPendingRecipients;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ViewPendingRecipientsRepo extends JpaRepository<ViewPendingRecipients, Long> {

    @Query(value = "select distinct phoneNo, beneficiaryName from ViewPendingRecipients where batchNo=:batchNo")
    Page<ViewPendingRecipients> findAllByBatchNo(@Param("batchNo") String batchNo, Pageable pageable);
}
