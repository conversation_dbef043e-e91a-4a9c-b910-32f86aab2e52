package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.PendingBatchCountyOrderView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PendingBatchCountyOrderViewRepository extends JpaRepository<PendingBatchCountyOrderView, Long> {
    @Query("""
    SELECT p FROM PendingBatchCountyOrderView p
    WHERE (:batchNo IS NULL OR p.batchNumber LIKE %:batchNo%)
      AND (:orgId IS NULL OR p.orgId = :orgId)
      AND (:region IS NULL OR p.countyId = :region)
      AND (
            :keyword IS NULL OR
            LOWER(p.beneficiary) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(p.phoneNumber) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(p.requestType) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(p.county) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(p.region) LIKE LOWER(CONCAT('%', :keyword, '%'))
          )
    ORDER BY p.id DESC
""")
    Page<PendingBatchCountyOrderView> searchPendingBatches(
            @Param("batchNo") String batchNo,
            @Param("orgId") Long orgId,
            @Param("region") String region,
            @Param("keyword") String keyword,
            Pageable pageable
    );
}
