package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.PaymentTimelineView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface PaymentTimelineRepository extends JpaRepository<PaymentTimelineView, Integer> {

    // Find all rows for a given apiorder_id
    @Query(value = "SELECT t FROM PaymentTimelineView t WHERE t.apiorderId = :apiorderId" )
    Page<PaymentTimelineView> findAllByApiorderId(Integer apiorderId, Pageable pageable);
}
