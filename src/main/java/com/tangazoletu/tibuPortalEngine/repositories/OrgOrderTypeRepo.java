package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.OrgOrderType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Map;

@Repository
public interface OrgOrderTypeRepo extends JpaRepository<OrgOrderType, Long>, JpaSpecificationExecutor<OrgOrderType> {

    @Query(value = """
        SELECT 
            org_ordertype.ID AS primarykey,
            ordertype.ID AS ID,
            organisation.name AS name,
            organisation.id AS orgId,
            ordertype.title AS `Payment type`,
            ordertype.id AS paymentId,
            org_ordertype.approvalOrder AS approvalOrder,
            budget.title AS Budget,
            budget.id AS budgetId,
            org_ordertype.resubmitable_levels AS resubmitable_levels,
            FROM_UNIXTIME(org_ordertype.creationTime) AS `Time Created`
        FROM org_ordertype
        INNER JOIN organisation ON org_ordertype.org_id = organisation.ID
        INNER JOIN budget ON org_ordertype.budget_id = budget.ID
        INNER JOIN ordertype ON org_ordertype.ordertypeId = ordertype.ID
        WHERE org_ordertype.INTRASH = 'NO'
            AND (:orgFilter IS NULL OR :orgFilter = '' OR :orgFilter = 'ORG_ID' OR org_ordertype.ORG_ID = :orgFilter)
        ORDER BY ordertype.ID DESC
        """,
            countQuery = """
        SELECT COUNT(*)
        FROM org_ordertype
        INNER JOIN organisation ON org_ordertype.org_id = organisation.ID
        INNER JOIN budget ON org_ordertype.budget_id = budget.ID
        INNER JOIN ordertype ON org_ordertype.ordertypeId = ordertype.ID
        WHERE org_ordertype.INTRASH = 'NO'
            AND (:orgFilter IS NULL OR :orgFilter = '' OR :orgFilter = 'ORG_ID' OR org_ordertype.ORG_ID = :orgFilter)
        """,
            nativeQuery = true
    )
    Page<Map<String, Object>> findOrgOrdertypeData(
            @Param("keyword") String keyword,
            @Param("orgFilter") String orgFilter,
            Pageable pageable
    );

    Page<OrgOrderType> findById(Long id, Pageable pageable);

}
