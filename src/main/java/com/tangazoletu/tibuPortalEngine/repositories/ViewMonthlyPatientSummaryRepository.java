package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.ViewMonthlyPatientSummary;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ViewMonthlyPatientSummaryRepository extends JpaRepository<ViewMonthlyPatientSummary, Long>, JpaSpecificationExecutor<ViewMonthlyPatientSummary> {

    @Query("""
    SELECT v FROM ViewMonthlyPatientSummary v
    WHERE 
        (:keywords IS NULL OR :keywords = '' OR 
         LOWER(v.patient) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
         LOWER(v.telephoneNumber) LIKE LOWER(CONCAT('%', :keywords, '%')))
    AND (:province IS NULL OR :province = '' OR v.county = :province)
    AND (:county IS NULL OR :county = '' OR v.county = :county)
    AND (:month IS NULL OR :month = '' OR :month = :month)
    AND (:year IS NULL OR :year = '' OR v.year LIKE CONCAT('%', :year, '%'))
    AND (:dateFrom IS NULL OR v.requestTime >= :dateFrom)
    AND (:dateTo IS NULL OR v.requestTime <= :dateTo)
    AND (:orgFilter IS NULL OR v.orgId IN :orgFilter)
    AND (:region IS NULL OR :region = '0' OR v.county = :region)
    """)
    Page<ViewMonthlyPatientSummary> searchMonthlySummary(
            @Param("keywords") String keywords,
            @Param("province") String province,
            @Param("county") String county,
            @Param("month") String month,
            @Param("year") String year,
            @Param("dateFrom") LocalDateTime dateFrom,
            @Param("dateTo") LocalDateTime dateTo,
            @Param("orgFilter") List<Integer> orgFilter,
            @Param("region") String region,
            Pageable pageable
    );

}
