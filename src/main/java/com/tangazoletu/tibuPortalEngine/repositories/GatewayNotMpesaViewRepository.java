package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.GatewayNotMpesaView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface GatewayNotMpesaViewRepository extends JpaRepository<GatewayNotMpesaView, Long>, JpaSpecificationExecutor<GatewayNotMpesaView> {

    @Query("""
        SELECT g FROM GatewayNotMpesaView g
        WHERE (:orgIds IS NULL OR g.orgId IN :orgIds)
          AND (:keyword IS NULL OR :keyword = '' OR
               LOWER(g.recipientName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
               LOWER(g.trxId) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
               LOWER(g.phoneNo) LIKE LOWER(CONCAT('%', :keyword, '%')))
          AND (:dateFrom IS NULL OR g.transactionDate >= :dateFrom)
          AND (:dateTo IS NULL OR g.transactionDate <= :dateTo)
    """)
    Page<GatewayNotMpesaView> searchGatewayNotMpesa(
            @Param("keyword") String keyword,
            @Param("orgIds") List<Integer> orgIds,
            @Param("dateFrom") LocalDateTime dateFrom,
            @Param("dateTo") LocalDateTime dateTo,
            Pageable pageable
    );
}

