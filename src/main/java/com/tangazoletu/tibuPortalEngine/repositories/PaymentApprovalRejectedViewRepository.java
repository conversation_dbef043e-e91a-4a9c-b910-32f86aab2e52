package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.PaymentApprovalRejectedView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface PaymentApprovalRejectedViewRepository extends JpaRepository<PaymentApprovalRejectedView, Long> {

    Page<PaymentApprovalRejectedView> findAllByApiorderId(Long apiorderId, Pageable pageable);
}
