package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.MpesaNotGatewayView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface MpesaNotGatewayViewRepository extends JpaRepository<MpesaNotGatewayView, Long>, JpaSpecificationExecutor<MpesaNotGatewayView> {

    @Query("""
        SELECT m FROM MpesaNotGatewayView m
        WHERE (:orgIds IS NULL OR m.orgId IN :orgIds)
          AND (:keyword IS NULL OR :keyword = '' OR
               LOWER(m.beneficiary) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
               LOWER(m.trxId) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
               LOWER(m.contactNumber) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
               STR(m.amount) LIKE CONCAT('%', :keyword, '%') OR
               STR(m.trxDate) LIKE CONCAT('%', :keyword, '%'))
          AND (:dateFrom IS NULL OR m.trxDate >= :dateFrom)
          AND (:dateTo IS NULL OR m.trxDate <= :dateTo)
    """)
    Page<MpesaNotGatewayView> searchMpesaNotGateway(
            @Param("keyword") String keyword,
            @Param("orgIds") List<Integer> orgIds,
            @Param("dateFrom") LocalDateTime dateFrom,
            @Param("dateTo") LocalDateTime dateTo,
            Pageable pageable
    );
}
