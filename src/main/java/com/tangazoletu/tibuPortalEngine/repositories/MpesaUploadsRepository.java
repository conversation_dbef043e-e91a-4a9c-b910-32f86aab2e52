package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.MpesaUploads;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.jpa.repository.JpaRepository;

public interface MpesaUploadsRepository extends JpaRepository<MpesaUploads, Integer>, JpaSpecificationExecutor<MpesaUploads> {

    @Query("""
        SELECT m FROM MpesaUploads m 
        WHERE (:orgId IS NULL OR m.orgId = :orgId)
        ORDER BY m.id DESC
    """)
    Page<MpesaUploads> findByOrgId(@Param("orgId") Integer orgId, Pageable pageable);
}
