package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.Organisation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Map;
import java.util.Optional;

@Repository
public interface OrganizationRepository extends JpaRepository<Organisation, Long>, JpaSpecificationExecutor<Organisation> {
    Optional<Organisation> findByName(String name);
    @Query("""
    SELECT o FROM Organisation o
    WHERE (:orgId IS NULL OR o.id = :orgId)
    AND (
        :keyword IS NULL OR :keyword = '' OR 
        LOWER(o.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
        LOWER(o.shortCode) LIKE LOWER(CONCAT('%', :keyword, '%'))
    )
    ORDER BY o.id DESC
""")
    Page<Organisation> findFiltered(@Param("orgId") Integer orgId,
                                    @Param("keyword") String keyword,
                                    Pageable pageable);

    Page<Organisation> findById(Long id, Pageable pageable);

    @Query(value = """
        SELECT 
            org_ordertype.ID AS primarykey,
            ordertype.ID AS ID,
            organisation.name AS name,
            ordertype.title AS `Payment type`,
            org_ordertype.approvalOrder AS approvalOrder,
            budget.title AS Budget,
            org_ordertype.resubmitable_levels AS resubmitable_levels,
            FROM_UNIXTIME(org_ordertype.creationTime) AS `Time Created`
        FROM org_ordertype
        INNER JOIN organisation ON org_ordertype.org_id = organisation.ID
        INNER JOIN budget ON org_ordertype.budget_id = budget.ID
        INNER JOIN ordertype ON org_ordertype.ordertypeId = ordertype.ID
        WHERE org_ordertype.INTRASH = 'NO'
            AND (:keyword IS NULL OR :keyword = '' OR org_ordertype.meeting_code LIKE %:keyword%)
            AND (:orgFilter IS NULL OR :orgFilter = '' OR :orgFilter = 'ORG_ID' OR org_ordertype.ORG_ID = :orgFilter)
        ORDER BY ordertype.ID DESC
        """,
            countQuery = """
        SELECT COUNT(*)
        FROM org_ordertype
        INNER JOIN organisation ON org_ordertype.org_id = organisation.ID
        INNER JOIN budget ON org_ordertype.budget_id = budget.ID
        INNER JOIN ordertype ON org_ordertype.ordertypeId = ordertype.ID
        WHERE org_ordertype.INTRASH = 'NO'
            AND (:keyword IS NULL OR :keyword = '' OR org_ordertype.meeting_code LIKE %:keyword%)
            AND (:orgFilter IS NULL OR :orgFilter = '' OR :orgFilter = 'ORG_ID' OR org_ordertype.ORG_ID = :orgFilter)
        """,
            nativeQuery = true
    )
    Page<Map<String, Object>> findOrgOrdertypeData(
            @Param("keyword") String keyword,
            @Param("orgFilter") String orgFilter,
            Pageable pageable
    );

}
