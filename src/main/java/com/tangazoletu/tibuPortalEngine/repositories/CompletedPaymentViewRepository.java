package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.dto.PaymentApprovalDTO;
import com.tangazoletu.tibuPortalEngine.model.*;
import com.tangazoletu.tibuPortalEngine.entities.CompletedPaymentView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface CompletedPaymentViewRepository extends JpaRepository<CompletedPaymentView, Long> {

    @Query("""
        SELECT p FROM CompletedPaymentView p
        WHERE (:keyword IS NULL OR :keyword = '' OR 
            LOWER(p.beneficiary) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(p.batchNo) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(p.contactNumber) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(p.trxId) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(p.paymentType) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(p.budget) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(p.region) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(p.county) LIKE LOWER(CONCAT('%', :keyword, '%')))
        AND (:province IS NULL OR p.region = :province)
        AND (:budget IS NULL OR p.budget = :budget)
        AND (:counties IS NULL OR p.county IN :counties)
        AND (:paymentType IS NULL OR p.paymentType = :paymentType)
        AND (
            (:dateFrom IS NULL OR p.date >= :dateFrom) OR
            (:dateFrom IS NULL OR p.timeCompleted >= :dateFrom)
        )
        AND (
            (:dateTo IS NULL OR p.date <= :dateTo) OR
            (:dateTo IS NULL OR p.timeCompleted <= :dateTo)
        )
        ORDER BY p.timeCompleted DESC
    """)
    Page<CompletedPaymentView> filterCompletedPayments(
            @Param("keyword") String keyword,
            @Param("province") String province,
            @Param("budget") String budget,
            @Param("counties") List<String> counties,
            @Param("paymentType") String paymentType,
            @Param("dateFrom") LocalDateTime dateFrom,
            @Param("dateTo") LocalDateTime dateTo,
            Pageable pageable
    );
    @Query(value = "SELECT p.order_id AS orderId, ao.batchno AS batchno, p.trx_status AS trxStatus, " +
            "COALESCE(pr.title, pr.title) AS region, COALESCE(c.title, c.title) AS county, " +
            "p.recipient_name AS beneficiary, p.MSISDN AS msisdn, p.trx_id AS id, " +
            "p.trx_date AS date, ot.title AS title, p.REFERENCE_ID AS referenceId, " +
            "p.trx_desc AS trxDesc, p.TIME_INITIATED AS timeInitiated, p.TIME_COMPLETED AS timeCompleted, " +
            "p.sending_charge AS sendingCharge, p.withdrawal_charge AS withdrawalCharge, " +
            "p.debit_amount AS amount, p.running_bal AS runningBalance " +
            "FROM payment p " +
            "LEFT JOIN ordertype ot ON p.order_type = ot.ID " +
            "LEFT JOIN apiorder ao ON p.order_id = ao.ID " +
            "LEFT JOIN province pr ON pr.id = ao.province " +
            "LEFT JOIN county c ON c.id = ao.county " +
            "WHERE p.id = :paymentId", nativeQuery = true)
    List<PaymentDetailsDTO> getPaymentDetailsById(@Param("paymentId") Long paymentId);

    @Query(value = "select title from ordertype left join apiorder on apiorder.ordertype = ordertype.id where apiorder.id =:orderId", nativeQuery = true)
    Optional<String> getTitle(@Param("orderId") Long orderId);

    @Query(value = "select id,title,credit,url from supervision_details where apiorder_id=:orderId", nativeQuery = true)
    Optional<supervisionDetails> getSupervisionDetails(@Param("orderId") Long orderId);
    @Query(value = "select id,title,credit,url from supervision_details where apiorder_id=:orderId", nativeQuery = true)
    List<supervisionDetails> getSupervisionDetailsReport(@Param("orderId") Long orderId);
    @Query(value = """
    SELECT 
        pa.approval_level_name AS approvalLevelName,
        pa.approval_status AS approvalStatus,
        pa.approved_amount AS approvedAmount,
        pa.approval_time AS approvalTime,
        pa.approval_notes AS approvalNotes,
        CONCAT(u.firstname, ' ', u.lastname) AS approver,
        CASE 
            WHEN api.initiator_username IS NOT NULL THEN api.initiator_username
            ELSE (
                SELECT CONCAT(u2.firstname, ' ', u2.lastname)
                FROM user u2 
                WHERE u2.id = api.initiator_id
            )
        END AS initiator
    FROM payment_approval pa
    LEFT JOIN user u ON u.id = pa.approver_id
    LEFT JOIN apiorder api ON api.id = :orderId
    WHERE pa.apiorder_id = :orderId
    ORDER BY pa.id ASC
    """, nativeQuery = true)
    Optional<PaymentApprovalDTO> getPaymentApprovalsByOrderId(@Param("orderId") Long orderId);

    @Query(value = """
    SELECT 
        pa.approval_level_name AS approvalLevelName,
        pa.approval_status AS approvalStatus,
        pa.approved_amount AS approvedAmount,
        pa.approval_time AS approvalTime,
        pa.approval_notes AS approvalNotes,
        CONCAT(u.firstname, ' ', u.lastname) AS approver,
        CASE 
            WHEN api.initiator_username IS NOT NULL THEN api.initiator_username
            ELSE (
                SELECT CONCAT(u2.firstname, ' ', u2.lastname)
                FROM user u2 
                WHERE u2.id = api.initiator_id
            )
        END AS initiator
    FROM payment_approval pa
    LEFT JOIN user u ON u.id = pa.approver_id
    LEFT JOIN apiorder api ON api.id = :orderId
    WHERE pa.apiorder_id = :orderId
    ORDER BY pa.id ASC
    """, nativeQuery = true)
    List<PaymentApprovalDTO> getPaymentApprovalsByOrderIdReport(@Param("orderId") Long orderId);

    @Query(value = "SELECT RETURN_CODE AS returnCode FROM temp_payment " +
            "WHERE (RETURN_CODE != '7' AND RETURN_CODE != '0' AND RETURN_CODE != '00' AND RETURN_CODE != '07') " +
            "AND REQUEST_ID = :requestId ORDER BY TIME_COMPLETED DESC LIMIT 1", nativeQuery = true)
    ReturnCodeDTO findReturnCodeForFailedPayment(@Param("requestId") Long requestId);

    @Query(value = "SELECT ID AS id FROM payment " +
            "WHERE ID = :paymentId " +
            "AND DATEDIFF(CURRENT_TIMESTAMP, TIME_COMPLETED) <= " +
            "(SELECT VALUE FROM PARAM WHERE PARAMETER = 'ALLOWED RESUBMISSION TIME LIMIT')", nativeQuery = true)
    EligibleForResubmissionDTO checkResubmissionEligibility(@Param("paymentId") Long paymentId);

}
