package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.BeneficiarySummaryView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BeneficiarySummaryViewRepo extends JpaRepository<BeneficiarySummaryView, Long> {

    @Query("""
        SELECT b FROM BeneficiarySummaryView b
        WHERE (:province IS NULL OR b.province = :province)
          AND (:county IS NULL OR b.county = :county)
          AND (
              :keyword IS NULL OR LOWER(b.firstName) LIKE LOWER(CONCAT('%', :keyword, '%'))
                            OR LOWER(b.lastName) LIKE LOWER(CONCAT('%', :keyword, '%'))
                            OR LOWER(b.facility) LIKE LOWER(CONCAT('%', :keyword, '%'))
                            OR LOWER(b.district) LIKE LOWER(CONCAT('%', :keyword, '%'))
                            OR LOWER(b.beneficiaryCategory) LIKE LOWER(CONCAT('%', :keyword, '%'))
          )
        ORDER BY b.id DESC
    """)
    Page<BeneficiarySummaryView> searchFiltered(
            @Param("province") String province,
            @Param("county") String county,
            @Param("keyword") String keyword,
            Pageable pageable
    );
    @Query(value = "select b from BeneficiarySummaryView b")
    Page<BeneficiarySummaryView> findAll(Pageable pageable);

}
