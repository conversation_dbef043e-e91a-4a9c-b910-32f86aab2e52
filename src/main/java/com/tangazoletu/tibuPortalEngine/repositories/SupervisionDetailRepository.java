package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.SupervisionDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface SupervisionDetailRepository extends JpaRepository<SupervisionDetails, Integer>, JpaSpecificationExecutor<SupervisionDetails> {
    @Query("SELECT new map(s.expenseId as Expense_Id, s.approvalStatus as Expense_status, s.title as Expense_desc, s.credit as Amount) " +
            "FROM SupervisionDetails s WHERE s.apiOrderId = :apiOrderId")
    List<Map<String, Object>> fetchSupervisionExpensesByOrderId(Integer apiOrderId);

}
