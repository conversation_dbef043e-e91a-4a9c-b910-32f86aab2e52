package com.tangazoletu.tibuPortalEngine.repositories;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ParamRepository extends JpaRepository<com.tangazoletu.tibuPortalEngine.entities.Param, Long>, JpaSpecificationExecutor<com.tangazoletu.tibuPortalEngine.entities.Param> {
    @Query("SELECT p.value FROM Param p WHERE p.parameter = :parameter")
    String findValueByParameter(@Param("parameter") String parameter);
    @Query("""
        SELECT p FROM Param p 
        WHERE (
            :keyword IS NULL OR :keyword = '' OR 
            LOWER(p.parameter) LIKE LOWER(CONCAT('%', :keyword, '%')) OR 
            LOWER(p.value) LIKE LOWER(CONCAT('%', :keyword, '%')) OR 
            LOWER(p.description) LIKE LOWER(CONCAT('%', :keyword, '%'))
        )
        ORDER BY p.position ASC
    """)
    Page<com.tangazoletu.tibuPortalEngine.entities.Param> searchByKeyword(@Param("keyword") String keyword, Pageable pageable);

    Optional<com.tangazoletu.tibuPortalEngine.entities.Param> findByParameter(String parameter);
}
