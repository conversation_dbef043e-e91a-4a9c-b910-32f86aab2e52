package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.PermissionMap;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PermissionMapRepo extends JpaRepository<PermissionMap, Integer> {
    @Query("SELECT DISTINCT pm.permission FROM PermissionMap pm WHERE pm.role = :roleId")
    List<Long> findPermissionIdsByRole(@Param("roleId") int roleId);


}
