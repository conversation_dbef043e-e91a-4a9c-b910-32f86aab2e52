package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.TransactionChargesView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface TransactionChargesViewRepository extends JpaRepository<TransactionChargesView, Long>, JpaSpecificationExecutor<TransactionChargesView> {

    @Query("SELECT t FROM TransactionChargesView t " +
            "WHERE (LOWER(t.receipt) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(t.name) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(t.phoneNumber) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR STR(t.totalCharges) LIKE CONCAT('%', :keyword, '%') " +
            "OR STR(t.transactionDate) LIKE CONCAT('%', :keyword, '%')) " +
            "ORDER BY t.transactionDate DESC")
    Page<TransactionChargesView> searchWithKeyword(@Param("keyword") String keyword, Pageable pageable);
}
