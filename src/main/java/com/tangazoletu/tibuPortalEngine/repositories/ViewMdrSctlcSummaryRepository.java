package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.ViewMdrSctlcSummary;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ViewMdrSctlcSummaryRepository extends JpaRepository<ViewMdrSctlcSummary, String>, JpaSpecificationExecutor<ViewMdrSctlcSummary> {


    @Query("""
        SELECT v FROM ViewMdrSctlcSummary v
        WHERE 
            (:keyword IS NULL OR TRIM(:keyword) = '' 
             OR LOWER(v.sctlcName) LIKE LOWER(CONCAT('%', :keyword, '%'))
             OR LOWER(v.unpaidAmt) LIKE LOWER(CONCAT('%', :keyword, '%'))
             OR LOWER(v.paidAmt) LIKE LOWER(CONCAT('%', :keyword, '%'))
             OR LOWER(v.rejectedAmt) LIKE LOWER(CONCAT('%', :keyword, '%'))
             OR LOWER(v.supervisionsDone) LIKE LOWER(CONCAT('%', :keyword, '%')))
            AND (:orgIds IS NULL OR v.orgId IN :orgIds)
        """)
    Page<ViewMdrSctlcSummary> findByKeywordAndOrgIds(
            @Param("keyword") String keyword,
            @Param("orgIds") List<Integer> orgIds,
            Pageable pageable
    );
}

