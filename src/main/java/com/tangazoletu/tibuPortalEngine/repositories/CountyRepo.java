package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.County;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CountyRepo extends JpaRepository<County, Long>, JpaSpecificationExecutor<County> {

    @Query(value = "select e from County e where 1=1 and  e.inTrash='No' AND (" +
            ":keyword IS NULL OR :keyword = '' OR " +
            "LOWER(e.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "CAST(e.creationTime AS string) LIKE CONCAT('%', :keyword, '%')) " +
            "ORDER BY e.id desc")
    Page<County> findByName(String keyword, Pageable pageable);

    Page<County> findById(Long id, Pageable pageable);
}
