package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.ActualPayment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Repository
public interface ActualPaymentsRepo extends JpaRepository<ActualPayment, Long> {
    @Query(
            value = """
            SELECT * FROM (
                SELECT 
                    p.id AS primarykey,
                    p.trx_date AS `Transaction Date`,
                    p.trx_id AS `Receipt Number`,
                    p.trx_desc AS `Details`,
                    CASE 
                        WHEN ot.title = 'BusinessTransferFromMMFToUtility' THEN 'Transfer'
                        ELSE 'Deposit'
                    END AS `Transaction Type`,
                    CASE 
                        WHEN ot.title = 'BusinessTransferFromMMFToUtility' THEN p.debit_amount 
                        ELSE 0 
                    END AS `Debit MMF A/C`,
                    CASE 
                        WHEN ot.title = 'BusinessTransferFromUtilityToMMF' THEN p.debit_amount 
                        ELSE 0 
                    END AS `Credit MMF A/C`
                FROM payment p
                INNER JOIN ordertype ot ON p.order_type = ot.id
                WHERE 
                    (ot.title IN ('BusinessTransferFromMMFToUtility', 'BusinessTransferFromUtilityToMMF'))
                    AND p.trx_status = 'Completed'
                    AND p.trx_type = 'Pay-out'
                    AND (:orgIds IS NULL OR p.org_id IN (:orgIds))
                    AND (
                        :keyword IS NULL OR :keyword = '' OR
                        LOWER(p.trx_type) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                        LOWER(p.recipient_name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                        LOWER(p.MSISDN) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                        CAST(p.credit_amount AS CHAR) LIKE CONCAT('%', :keyword, '%') OR
                        CAST(p.trx_date AS CHAR) LIKE CONCAT('%', :keyword, '%')
                    )

                UNION

                SELECT 
                    ap.id AS primarykey,
                    ap.trx_date AS `Transaction Date`,
                    ap.trx_id AS `Receipt Number`,
                    ap.trx_type AS `Transaction Type`,
                    ap.details AS `Details`,
                    ap.withdrawn AS `Debit MMF A/C`,
                    ap.paid_in AS `Credit MMF A/C`
                FROM actual_payments ap
                WHERE 
                    ap.trx_type NOT IN ('Pay-out', 'Reversal')
                    AND (:orgIds IS NULL OR ap.org_id IN (:orgIds))
                    AND (
                        :keyword IS NULL OR :keyword = '' OR
                        LOWER(ap.trx_type) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                        LOWER(ap.details) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                        CAST(ap.withdrawn AS CHAR) LIKE CONCAT('%', :keyword, '%') OR
                        CAST(ap.paid_in AS CHAR) LIKE CONCAT('%', :keyword, '%') OR
                        CAST(ap.trx_date AS CHAR) LIKE CONCAT('%', :keyword, '%')
                    )
            ) AS t
            ORDER BY `Transaction Date` DESC
            """,
            countQuery = """
            SELECT COUNT(*) FROM (
                SELECT 1
                FROM payment p
                INNER JOIN ordertype ot ON p.order_type = ot.id
                WHERE 
                    (ot.title IN ('BusinessTransferFromMMFToUtility', 'BusinessTransferFromUtilityToMMF'))
                    AND p.trx_status = 'Completed'
                    AND p.trx_type = 'Pay-out'
                    AND (:orgIds IS NULL OR p.org_id IN (:orgIds))
                    AND (
                        :keyword IS NULL OR :keyword = '' OR
                        LOWER(p.trx_type) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                        LOWER(p.recipient_name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                        LOWER(p.MSISDN) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                        CAST(p.credit_amount AS CHAR) LIKE CONCAT('%', :keyword, '%') OR
                        CAST(p.trx_date AS CHAR) LIKE CONCAT('%', :keyword, '%')
                    )

                UNION

                SELECT 1
                FROM actual_payments ap
                WHERE 
                    ap.trx_type NOT IN ('Pay-out', 'Reversal')
                    AND (:orgIds IS NULL OR ap.org_id IN (:orgIds))
                    AND (
                        :keyword IS NULL OR :keyword = '' OR
                        LOWER(ap.trx_type) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                        LOWER(ap.details) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                        CAST(ap.withdrawn AS CHAR) LIKE CONCAT('%', :keyword, '%') OR
                        CAST(ap.paid_in AS CHAR) LIKE CONCAT('%', :keyword, '%') OR
                        CAST(ap.trx_date AS CHAR) LIKE CONCAT('%', :keyword, '%')
                    )
            ) AS count_subquery
            """,
            nativeQuery = true
    )
    Page<Map<String, Object>> findFundsTransferData(
            @Param("orgIds") List<Integer> orgIds,
            @Param("keyword") String keyword,
            Pageable pageable
    );
    @Query("""
        SELECT a FROM ActualPayment a
        WHERE a.trxType = 'pay-out'
        AND (:orgId IS NULL OR a.orgId = :orgId)
        AND (
            :keyword IS NULL OR :keyword = '' OR 
            LOWER(a.identityName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(a.trxId) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(a.msisdn) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            STR(a.trxDate) LIKE CONCAT('%', :keyword, '%')
        )
        AND (:dateFrom IS NULL OR a.trxDate >= :dateFrom)
        AND (:dateTo IS NULL OR a.trxDate <= :dateTo)
        ORDER BY a.id DESC
    """)
    Page<ActualPayment> findFilteredPayments(
            @Param("orgId") Integer orgId,
            @Param("keyword") String keyword,
            @Param("dateFrom") LocalDateTime dateFrom,
            @Param("dateTo") LocalDateTime dateTo,
            Pageable pageable
    );
}
