package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.FundAllocationView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface FundAllocationViewRepository extends JpaRepository<FundAllocationView, Long>, JpaSpecificationExecutor<FundAllocationView> {

    @Query("SELECT f FROM FundAllocationView f " +
            "WHERE (:keyword IS NULL OR LOWER(f.budgetLine) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(f.financier) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(f.description) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR CAST(f.creditAmount AS string) LIKE CONCAT('%', :keyword, '%'))")
    Page<FundAllocationView> searchByKeyword(String keyword, Pageable pageable);
}
