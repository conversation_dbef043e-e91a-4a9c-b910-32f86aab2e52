package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.AttendanceRecipientView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

public interface AttendanceRecipientViewRepository extends JpaRepository<AttendanceRecipientView, Long> {
    Page<AttendanceRecipientView> findByMeetingCode(String meetingCode, Pageable pageable);
}
