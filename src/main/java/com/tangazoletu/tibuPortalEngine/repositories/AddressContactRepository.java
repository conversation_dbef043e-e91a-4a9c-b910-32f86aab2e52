package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.AddressContact;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface AddressContactRepository extends JpaRepository<AddressContact,Long> {
    @Query("SELECT c FROM AddressContact c WHERE " +
            "LOWER(c.firstname) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
            "OR LOWER(c.lastname) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
            "OR CAST(c.phoneNumber AS string) LIKE CONCAT('%', :searchTerm, '%')")
    Page<AddressContact> findByNameOrPhone(@Param("searchTerm") String searchTerm, Pageable pageable);
}

