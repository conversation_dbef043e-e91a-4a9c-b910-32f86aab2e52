package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.Activity;
import com.tangazoletu.tibuPortalEngine.enums.Module;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository for Activity audit records with module-based filtering capabilities
 */
@Repository
public interface ActivityAuditRepository extends JpaRepository<Activity, Long> {

    /**
     * Find activities by module
     */
    Page<Activity> findByModule(Module module, Pageable pageable);

    /**
     * Find activities by module and organization
     */
    Page<Activity> findByModuleAndOrgId(Module module, Integer orgId, Pageable pageable);

    /**
     * Find activities by user and module
     */
    Page<Activity> findByUserAndModule(Integer userId, Module module, Pageable pageable);

    /**
     * Find activities by activity type and module
     */
    Page<Activity> findByActivityTypeAndModule(String activityType, Module module, Pageable pageable);

    /**
     * Find activities by form name
     */
    Page<Activity> findByFormName(String formName, Pageable pageable);

    /**
     * Find activities by organization
     */
    Page<Activity> findByOrgId(Integer orgId, Pageable pageable);

    /**
     * Find activities by user
     */
    Page<Activity> findByUser(Integer userId, Pageable pageable);

    /**
     * Find activities by activity type
     */
    Page<Activity> findByActivityType(String activityType, Pageable pageable);

    /**
     * Custom query to find activities with multiple filters
     */
    @Query("SELECT a FROM Activity a WHERE " +
           "(:module IS NULL OR a.module = :module) AND " +
           "(:orgId IS NULL OR a.orgId = :orgId) AND " +
           "(:userId IS NULL OR a.user = :userId) AND " +
           "(:activityType IS NULL OR a.activityType = :activityType) AND " +
           "(:formName IS NULL OR a.formName = :formName) " +
           "ORDER BY a.creationTime DESC")
    Page<Activity> findActivitiesWithFilters(
            @Param("module") Module module,
            @Param("orgId") Integer orgId,
            @Param("userId") Integer userId,
            @Param("activityType") String activityType,
            @Param("formName") String formName,
            Pageable pageable
    );

    /**
     * Get activity count by module
     */
    @Query("SELECT a.module, COUNT(a) FROM Activity a GROUP BY a.module")
    List<Object[]> getActivityCountByModule();

    /**
     * Get recent activities by module
     */
    @Query("SELECT a FROM Activity a WHERE a.module = :module ORDER BY a.creationTime DESC")
    Page<Activity> findRecentActivitiesByModule(@Param("module") Module module, Pageable pageable);

    /**
     * Find activities within a time range
     */
    @Query("SELECT a FROM Activity a WHERE " +
           "a.creationTime >= :startTime AND a.creationTime <= :endTime " +
           "ORDER BY a.creationTime DESC")
    Page<Activity> findActivitiesByTimeRange(
            @Param("startTime") Integer startTime,
            @Param("endTime") Integer endTime,
            Pageable pageable
    );

    /**
     * Find activities by module within a time range
     */
    @Query("SELECT a FROM Activity a WHERE " +
           "a.module = :module AND " +
           "a.creationTime >= :startTime AND a.creationTime <= :endTime " +
           "ORDER BY a.creationTime DESC")
    Page<Activity> findActivitiesByModuleAndTimeRange(
            @Param("module") Module module,
            @Param("startTime") Integer startTime,
            @Param("endTime") Integer endTime,
            Pageable pageable
    );
}
