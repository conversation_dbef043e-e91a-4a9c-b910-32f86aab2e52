package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.Payment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Repository
public interface PaymentsRepo extends JpaRepository<Payment, Long>, JpaSpecificationExecutor<Payment> {

    @Query(value = """
        SELECT * FROM payment 
        WHERE trx_status = 'Completed'
          AND trx_type = 'Pay-out'
          AND debit_commission >= 0
          AND (
                :keyword IS NULL OR :keyword = '' OR
                LOWER(trx_id) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                LOWER(recipient_name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                LOWER(MSISDN) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                CAST(debit_commission AS CHAR) LIKE CONCAT('%', :keyword, '%') OR
                CAST(trx_date AS CHAR) LIKE CONCAT('%', :keyword, '%')
              )
          AND (:orgIds IS NULL OR org_id IN (:orgIds))
        ORDER BY trx_date DESC
        """,
            countQuery = """
        SELECT COUNT(*) FROM payment 
        WHERE trx_status = 'Completed'
          AND trx_type = 'Pay-out'
          AND debit_commission >= 0
          AND (
                :keyword IS NULL OR :keyword = '' OR
                LOWER(trx_id) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                LOWER(recipient_name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                LOWER(MSISDN) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                CAST(debit_commission AS CHAR) LIKE CONCAT('%', :keyword, '%') OR
                CAST(trx_date AS CHAR) LIKE CONCAT('%', :keyword, '%')
              )
          AND (:orgIds IS NULL OR org_id IN (:orgIds))
        """,
            nativeQuery = true
    )
    Page<Payment> searchPayments(
            @Param("keyword") String keyword,
            @Param("orgIds") List<Integer> orgIds,
            Pageable pageable
    );
    @Query(
            value = """
        SELECT 
            payment.budget_id AS primarykey,
            DATE_FORMAT(MIN(payment.request_date), '%Y-%m-%d') AS `Date From`,
            DATE_FORMAT(MAX(payment.request_date), '%Y-%m-%d') AS `Date To`,
            budget.title AS `Budget Line`,
            COUNT(*) AS `No. of Transactions`,
            SUM(payment.credit_amount) AS `Funds Allocated`,
            SUM(payment.debit_amount) AS `Total Paid Out`,
            SUM(payment.credit_amount) - SUM(payment.debit_amount) AS `Balance`
        FROM payment
        LEFT JOIN budget ON payment.budget_id = budget.id
        WHERE payment.trx_status = 'Completed'
          AND (:keyword IS NULL OR :keyword = '' OR
                LOWER(payment.trx_type) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                LOWER(payment.recipient_name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                LOWER(payment.MSISDN) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                CAST(payment.credit_amount AS CHAR) LIKE CONCAT('%', :keyword, '%') OR
                CAST(payment.trx_date AS CHAR) LIKE CONCAT('%', :keyword, '%')
              )
          AND (:dateFrom IS NULL OR payment.request_date >= :dateFrom)
          AND (:dateTo IS NULL OR payment.request_date <= :dateTo)
          AND ((:budgetIds) IS NULL OR payment.budget_id IN (:budgetIds))
          AND ((:orgIds) IS NULL OR payment.ORG_ID IN (:orgIds))
        GROUP BY payment.budget_id
        """,
            countQuery = """
        SELECT COUNT(*) FROM (
            SELECT payment.budget_id
            FROM payment
            LEFT JOIN budget ON payment.budget_id = budget.id
            WHERE payment.trx_status = 'Completed'
              AND (:keyword IS NULL OR :keyword = '' OR
                    LOWER(payment.trx_type) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                    LOWER(payment.recipient_name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                    LOWER(payment.MSISDN) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                    CAST(payment.credit_amount AS CHAR) LIKE CONCAT('%', :keyword, '%') OR
                    CAST(payment.trx_date AS CHAR) LIKE CONCAT('%', :keyword, '%')
                  )
              AND (:dateFrom IS NULL OR payment.request_date >= :dateFrom)
              AND (:dateTo IS NULL OR payment.request_date <= :dateTo)
              AND ((:budgetIds) IS NULL OR payment.budget_id IN (:budgetIds))
              AND ((:orgIds) IS NULL OR payment.ORG_ID IN (:orgIds))
            GROUP BY payment.budget_id
        ) AS count_table
        """,
            nativeQuery = true
    )
    Page<Map<String, Object>> findPaymentSummary(
            @Param("keyword") String keyword,
            @Param("dateFrom") String dateFrom,
            @Param("dateTo") String dateTo,
            @Param("budgetIds") List<Integer> budgetIds,
            @Param("orgIds") List<Integer> orgIds,
            Pageable pageable
    );
    @Query(
            value = """
        SELECT 
            MIN(payment.trx_date) AS "Date From",
            MAX(payment.trx_date) AS "Date To",
            COUNT(*) AS "Transactions",
            SUM(payment.credit_amount) AS "Funds Allocated",
            SUM(payment.debit_amount) AS "Total Paid Out",
            SUM(payment.sending_charge + payment.withdrawal_charge) AS "Total Charges",
            SUM(payment.credit_amount) - SUM(payment.debit_amount + payment.sending_charge + payment.withdrawal_charge) AS "Balance"
        FROM payment
        LEFT JOIN budget ON payment.budget_id = budget.id
        LEFT JOIN ordertype ON payment.order_type = ordertype.id
        LEFT JOIN apiorder ON payment.order_id = apiorder.id
        WHERE payment.trx_status = 'Completed'
          AND (:orgIds IS NULL OR payment.ORG_ID IN (:orgIds))
        """,
            nativeQuery = true
    )
    List<Map<String, Object>> findDetailedPaymentSummary(
            @Param("orgIds") List<Integer> orgIds);

    @Query(value = """
        SELECT 
            payment.id AS primarykey,
            DATE_FORMAT(MIN(payment.request_date), '%Y-%m-%d') AS `Date From`,
            DATE_FORMAT(MAX(payment.request_date), '%Y-%m-%d') AS `Date To`,
            COUNT(*) AS `No. of Transactions`,
            SUM(payment.credit_amount) AS `Funds Allocated`,
            SUM(payment.debit_amount) AS `Total Paid Out`,
            SUM(payment.credit_amount) - SUM(payment.debit_amount) AS `Balance`
        FROM payment
        WHERE payment.trx_status = 'Completed'
          AND (:orgFilter IS NULL OR payment.ORG_ID IN (:orgFilter))
          AND (
              :keyword IS NULL OR :keyword = '' OR (
                  payment.trx_type LIKE %:keyword% OR
                  payment.recipient_name LIKE %:keyword% OR
                  payment.MSISDN LIKE %:keyword% OR
                  CAST(payment.credit_amount AS CHAR) LIKE %:keyword% OR
                  DATE_FORMAT(payment.trx_date, '%Y-%m-%d') LIKE %:keyword%
              )
          )
        GROUP BY payment.id
        """,
            countQuery = """
        SELECT COUNT(*) FROM payment
        WHERE payment.trx_status = 'Completed'
          AND (:orgFilter IS NULL OR payment.ORG_ID IN (:orgFilter))
          AND (
              :keyword IS NULL OR :keyword = '' OR (
                  payment.trx_type LIKE %:keyword% OR
                  payment.recipient_name LIKE %:keyword% OR
                  payment.MSISDN LIKE %:keyword% OR
                  CAST(payment.credit_amount AS CHAR) LIKE %:keyword% OR
                  DATE_FORMAT(payment.trx_date, '%Y-%m-%d') LIKE %:keyword%
              )
          )
        """,
            nativeQuery = true
    )
    Page<Map<String, Object>> searchCompletedTransactions(
            @Param("keyword") String keyword,
            @Param("orgFilter") java.util.List<Integer> orgIds,
            Pageable pageable
    );
    @Query(value = """
    SELECT p FROM Payment p 
    WHERE p.trxStatus = 'completed'
      AND p.trxType = 'pay-out'
      AND p.orgId = :orgId
      AND (
          :keyword IS NULL OR :keyword = '' OR (
              p.recipientName LIKE %:keyword% OR
              p.msisdn LIKE %:keyword% OR
              p.trxDesc  LIKE %:keyword% OR
              DATE_FORMAT(p.completionDate, '%Y-%m-%d') LIKE %:keyword%
          )
      )
          order by p.id desc 
    """)
    Page<Payment> findAllByTrxStatusAndOrd(
            @Param("orgId") Long orgId,
            @Param("keyword") String keyword,
            Pageable pageable
    );

    Optional<Payment> findByIdAndTrxStatus(Integer id, Payment.TrxStatus trxStatus);

}
