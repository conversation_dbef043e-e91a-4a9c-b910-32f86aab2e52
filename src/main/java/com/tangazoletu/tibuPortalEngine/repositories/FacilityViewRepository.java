package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.FacilityView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface FacilityViewRepository extends JpaRepository<FacilityView, Long> , JpaSpecificationExecutor<FacilityView> {
    @Query("""
        SELECT f FROM FacilityView f 
        WHERE 
          (:keyword IS NULL OR :keyword = '' OR
           LOWER(f.facility) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
           LOWER(f.province) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
           LOWER(f.subCounty) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
           f.timeCreated LIKE CONCAT('%', :keyword, '%')
        )
        ORDER BY f.primarykey DESC
        """)
    Page<FacilityView> searchFacilities(@Param("keyword") String keyword, Pageable pageable);
}
