package com.tangazoletu.tibuPortalEngine.repositories;
import com.tangazoletu.tibuPortalEngine.entities.ViewSupervisionPayments;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ViewSupervisionPaymentsRepository extends JpaRepository<ViewSupervisionPayments, String>, JpaSpecificationExecutor<ViewSupervisionPayments> {

    @Query("""
        SELECT v FROM ViewSupervisionPayments v
        WHERE 
            (:keyword IS NULL OR :keyword = '' OR
            LOWER(v.sctlcName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            CAST(v.supervisionsDone AS string) LIKE CONCAT('%', :keyword, '%') OR
            CAST(v.unpaidAmt AS string) LIKE CONCAT('%', :keyword, '%') OR
            CAST(v.paidAmt AS string) LIKE CONCAT('%', :keyword, '%') OR
            CAST(v.rejectedAmt AS string) LIKE CONCAT('%', :keyword, '%'))
    """)
    Page<ViewSupervisionPayments> searchByKeyword(@Param("keyword") String keyword, Pageable pageable);
}
