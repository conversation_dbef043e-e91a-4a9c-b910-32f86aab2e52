package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.JobGroup;
import org.antlr.v4.runtime.atn.SemanticContext;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface JobGroupRepo extends JpaRepository<JobGroup, Integer>, JpaSpecificationExecutor<JobGroup> {
    @Query("SELECT e FROM JobGroup e WHERE e.inTrash = 'No' AND " +
            "(:keyword IS NULL OR :keyword = '' OR " +
            "LOWER(e.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "STR(e.creationTime) LIKE CONCAT('%', :keyword, '%')) " +
            "ORDER BY e.id ASC")
    Page<JobGroup> findByKeyword(String keyword, Pageable pageable);


    Page<JobGroup> findById(Long id, Pageable pageable);
}
