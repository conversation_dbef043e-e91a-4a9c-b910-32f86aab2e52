package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.dto.ApprovalLevelProjection;
import com.tangazoletu.tibuPortalEngine.entities.ApprovalLevel;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ApprovalLevelRepo extends JpaRepository<ApprovalLevel, Integer> {
    @Query("SELECT t.id AS id, t.approvalLevelName AS approvalLevelName FROM ApprovalLevel t ORDER BY t.approvalLevel ASC")
    Page<ApprovalLevelProjection> findByApprovalLevels(Pageable pageable);

}
