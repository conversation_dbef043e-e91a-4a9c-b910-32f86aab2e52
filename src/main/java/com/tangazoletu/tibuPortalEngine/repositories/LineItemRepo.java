package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.LineItem;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface LineItemRepo extends JpaRepository<LineItem, Long>, JpaSpecificationExecutor<LineItem> {
    @Query("SELECT l FROM LineItem l WHERE l.inTrash = 'No' AND " +
            "(:keyword IS NULL OR :keyword = '' OR " +
            "LOWER(l.itemName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "STR(l.creationTime) LIKE CONCAT('%', :keyword, '%')) " +
            "ORDER BY l.id DESC")
    Page<LineItem> searchByKeyword(String keyword, Pageable pageable);

    Page<LineItem> findById(Long id, Pageable pageable);
}
