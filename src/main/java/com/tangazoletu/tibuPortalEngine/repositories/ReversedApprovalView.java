package com.tangazoletu.tibuPortalEngine.repositories;

import java.math.BigDecimal;
import java.security.Timestamp;
public interface ReversedApprovalView {
    Integer getPrimarykey();
    String getBatchNumber();
    String getUser();
    String getAction();
    String getApprovalLevel();
    String getSupervisionYear();
    String getSupervisionMonth();
    String getBudgetLine();
    String getRegion();
    String getCounty();
    String getFacility();
    String getRequestType();
    String getPhoneNo();
    String getBeneficiary();
    String getRequestTime();
    BigDecimal getAmount();
    String getComments();
}


