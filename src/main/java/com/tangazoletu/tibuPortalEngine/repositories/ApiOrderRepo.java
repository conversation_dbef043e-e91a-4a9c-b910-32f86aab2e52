package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.dto.ApiOrderApprovalDTO;
import com.tangazoletu.tibuPortalEngine.entities.ApiOrder;
import com.tangazoletu.tibuPortalEngine.entities.ViewCtclPendingApiOrders;
import com.tangazoletu.tibuPortalEngine.model.MonthlyOrderSummaryDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Repository
public interface ApiOrderRepo extends JpaRepository<ApiOrder, Long> {

    @Query(value = """
    SELECT 
        o.id AS primaryKey,
        COALESCE(o.month) AS month,
        o.year AS year,
        b.title AS budgetLine,
        COALESCE(p.title, 'N/A') AS region,
        COALESCE(c.title, 'N/A') AS county,
        o.facility AS facility,
        tm.title AS requestType,
        o.msisdn AS phoneNo,
        CONCAT(o.firstname, ' ', o.middlename, ' ', o.lastname) AS beneficiary,
        o.initiator_username AS initiator,
        o.requestTime AS requestTime,
        o.patient_registration_number AS patientNumber,
        o.date_treatment_started AS dateTreatmentStarted,
        CONCAT(o.recipient2Firstname, ' ', o.recipient2Middlename, ' ', o.recipient2Lastname) AS dotName,
        o.dot_nurse_name AS dotPhone,
        o.recipient2Credit AS dotAmount,
        o.driver_amount AS driverAmt,
        o.credit AS amount,
        CASE 
            WHEN NOW() BETWEEN 
                 DATE_FORMAT(CONVERT_TZ(NOW(), '+00:00', '+03:00'), '%Y-%m-01') 
                 AND 
                 DATE_FORMAT(CONVERT_TZ(NOW(), '+00:00', '+03:00'), :deadlineFormat)
        THEN 'CONFIRMABLE'
        ELSE 'INCONFIRMABLE'
        END AS confirmable
    FROM apiorder o
    LEFT JOIN budget b ON o.ID = b.id
    LEFT JOIN treatment_model tm ON o.treatment_model = tm.ID
    LEFT JOIN province p ON CAST(o.province AS UNSIGNED) = p.ID
    LEFT JOIN county c ON CAST(o.county AS UNSIGNED) = c.ID
    WHERE o.approval_level = 1
      AND o.approval_status = 'Pending'
      AND o.request_src = 'api'
      AND o.inTrash = 'No'
      AND (:searchFilter IS NULL OR :searchFilter = '' OR
           LOWER(CONCAT(o.firstname, ' ', o.middlename, ' ', o.lastname)) LIKE LOWER(CONCAT('%', :searchFilter, '%'))
           OR LOWER(o.msisdn) LIKE LOWER(CONCAT('%', :searchFilter, '%')))
      AND (:region = 0 OR CAST(o.county AS UNSIGNED) = :region)
    AND (:orgFilterList IS NULL OR o.org_id IN (:orgFilterList))
    ORDER BY o.id DESC
""", nativeQuery = true)
    Page<ViewCtclPendingApiOrders> findPendingApprovalOrders(
            @Param("searchFilter") String searchFilter,
            @Param("region") int region,
            @Param("orgFilterList") List<Integer> orgFilterList,
            @Param("deadlineFormat") String deadlineFormat,
            Pageable pageable
    );
    @Query(
            value = """
            SELECT
                apiorder.ID AS primarykey,
                apiorder.batchno AS `Batch Number`,
                approval_levels.approval_level_name AS `Approval level`,
                CASE
                    WHEN apiorder.treatment_model IN (1,2,4,5) AND apiorder.year IS NOT NULL THEN apiorder.year
                    WHEN apiorder.treatment_model IN (1,2,4,5) THEN YEAR(apiorder.requesttime)
                    WHEN apiorder.treatment_model = 3 THEN YEAR(apiorder.requesttime)
                    ELSE NULL
                END AS `Year of Claim`,
                CASE
                    WHEN apiorder.treatment_model IN (1,2,4,5) AND apiorder.month IS NOT NULL THEN MONTHNAME(STR_TO_DATE(CONCAT(apiorder.month,' 1'), '%m %d'))
                    WHEN apiorder.treatment_model IN (1,2,4,5) THEN MONTHNAME(apiorder.requesttime)
                    WHEN apiorder.treatment_model = 3 THEN MONTHNAME(apiorder.requesttime)
                    ELSE NULL
                END AS `Month of Claim`,
                budget.title AS `Budget Line`,
                COALESCE(prov.title, apiorder.province) AS `Region`,
                COALESCE(cnt.title, apiorder.district) AS `County`,
                apiorder.facility,
                treatment_model.title AS `Request Type`,
                apiorder.msisdn AS `Phone No.`,
                CONCAT(apiorder.firstname,' ',apiorder.middlename,' ',apiorder.lastname) AS `Beneficiary`,
                CONVERT_TZ(apiorder.requesttime, '+00:00', '+03:00') AS `Request Time`,
                apiorder.credit AS `Amount`,
                CONCAT(apiorder.recipient2firstname,' ',apiorder.recipient2middlename,' ',apiorder.recipient2lastname) AS `Dot name`,
                apiorder.dot_nurse_phoneno AS `DOT Phone`,
                apiorder.recipient2credit AS `DOT Amount`
            FROM apiorder
            LEFT JOIN ordertype ON apiorder.ordertype = ordertype.ID
            LEFT JOIN budget ON apiorder.budget = budget.ID
            LEFT JOIN treatment_model ON apiorder.treatment_model = treatment_model.ID
            LEFT JOIN approval_levels ON apiorder.approval_level = approval_levels.approval_level
            LEFT JOIN province prov ON prov.id = apiorder.province
            LEFT JOIN county cnt ON cnt.id = apiorder.county
            WHERE apiorder.approval_status = 'Pending'
              AND apiorder.inTrash = 'No'
              AND (:keyword IS NULL OR :keyword = '' OR
                   LOWER(apiorder.batchno) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                   LOWER(approval_levels.approval_level_name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                   LOWER(budget.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                   LOWER(apiorder.msisdn) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                   CAST(apiorder.credit AS CHAR) LIKE CONCAT('%', :keyword, '%') OR
                   CAST(apiorder.requesttime AS CHAR) LIKE CONCAT('%', :keyword, '%')
              )
              AND (:orgIds IS NULL OR apiorder.org_id IN (:orgIds))
            ORDER BY apiorder.ID DESC
            """,
            countQuery = """
            SELECT COUNT(*) FROM apiorder
            LEFT JOIN ordertype ON apiorder.ordertype = ordertype.ID
            LEFT JOIN budget ON apiorder.budget = budget.ID
            LEFT JOIN approval_levels ON apiorder.approval_level = approval_levels.approval_level
            WHERE apiorder.approval_status = 'Pending'
              AND apiorder.inTrash = 'No'
              AND (:keyword IS NULL OR :keyword = '' OR
                   LOWER(apiorder.batchno) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                   LOWER(approval_levels.approval_level_name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                   LOWER(budget.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                   LOWER(apiorder.msisdn) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                   CAST(apiorder.credit AS CHAR) LIKE CONCAT('%', :keyword, '%') OR
                   CAST(apiorder.requesttime AS CHAR) LIKE CONCAT('%', :keyword, '%')
              )
              AND (:orgIds IS NULL OR apiorder.org_id IN (:orgIds))
            """,
            nativeQuery = true
    )
    Page<Map<String, Object>> searchApiOrders(
            @Param("orgIds") List<Integer> orgIds,
            @Param("keyword") String keyword,
            Pageable pageable
    );
    @Query(
            value = """
            SELECT 
                ao.initiator_id,
                ao.initiator_username AS `SCTLC Name`,
                COUNT(*) AS `Supervisions Done`,
                SUM(CASE WHEN ao.approval_status = 'Pending' THEN ao.credit ELSE 0 END) AS `UnPaid Amt`,
                SUM(CASE WHEN ao.approval_status = 'Approved' THEN ao.credit ELSE 0 END) AS `Paid Amt`,
                SUM(CASE WHEN ao.approval_status = 'Rejected' THEN ao.credit ELSE 0 END) AS `Rejected Amt`
            FROM apiorder ao
            WHERE ao.ordertype = (SELECT id FROM ordertype WHERE title = 'Supervision')
              AND ao.initiator_id IS NOT NULL
              AND ao.approval_status != 'Resubmitted'
              AND (:keyword IS NULL OR :keyword = '' OR (
                  ao.recipient2firstname LIKE %:keyword% OR
                  ao.msisdn LIKE %:keyword% OR
                  CAST(ao.credit AS CHAR) LIKE %:keyword% OR
                  DATE_FORMAT(ao.requestTime, '%Y-%m-%d') LIKE %:keyword%
              ))
              AND (:orgIds IS NULL OR ao.ORG_ID IN (:orgIds))
            GROUP BY ao.initiator_id, ao.initiator_username
            """,
            countQuery = """
            SELECT COUNT(DISTINCT ao.initiator_id)
            FROM apiorder ao
            WHERE ao.ordertype = (SELECT id FROM ordertype WHERE title = 'Supervision')
              AND ao.initiator_id IS NOT NULL
              AND ao.approval_status != 'Resubmitted'
              AND (:keyword IS NULL OR :keyword = '' OR (
                  ao.recipient2firstname LIKE %:keyword% OR
                  ao.msisdn LIKE %:keyword% OR
                  CAST(ao.credit AS CHAR) LIKE %:keyword% OR
                  DATE_FORMAT(ao.requestTime, '%Y-%m-%d') LIKE %:keyword%
              ))
              AND (:orgIds IS NULL OR ao.ORG_ID IN (:orgIds))
            """,
            nativeQuery = true
    )
    Page<Map<String, Object>> findSupervisionSummaryByKeywordAndOrg(
            @Param("keyword") String keyword,
            @Param("orgIds") List<Integer> orgIds,
            Pageable pageable
    );

    @Query(
            value = """
        (
            SELECT 
                ao.initiator_id AS `initiator_id`,
                ao.initiator_username AS `SCTLC Name`,
                COUNT(ao.id) AS `Supervisions Done`,
                SUM(CASE WHEN ao.approval_status = 'Pending' THEN ao.credit ELSE 0 END) AS `UnPaid Amt`,
                SUM(CASE WHEN ao.approval_status = 'Approved' THEN ao.credit ELSE 0 END) AS `Paid Amt`,
                SUM(CASE WHEN ao.approval_status = 'Rejected' THEN ao.credit ELSE 0 END) AS `Rejected Amt`
            FROM apiorder ao
            WHERE ao.ordertype = (SELECT id FROM ordertype WHERE title = 'MDR Other Payments')
              AND ao.initiator_id IS NOT NULL
              AND ao.approval_status != 'Resubmitted'
              AND (:keyword IS NULL OR :keyword = '' OR (
                    ao.recipient2firstname LIKE %:keyword% OR
                    ao.MSISDN LIKE %:keyword% OR
                    CAST(ao.credit AS CHAR) LIKE %:keyword% OR
                    DATE_FORMAT(ao.requestTime, '%Y-%m-%d') LIKE %:keyword%
              ))
              AND (:orgIds IS NULL OR ao.ORG_ID IN (:orgIds))
            GROUP BY ao.initiator_id, ao.initiator_username
        )
        UNION
        (
            SELECT 
                '<h3>Summary</h3>' AS `initiator_id`,
                '<h3>Totals</h3>' AS `SCTLC Name`,
                CONCAT('<h3><u>', COUNT(ao.id), '</u></h3>') AS `Supervisions Done`,
                CONCAT('<h3><u>', SUM(CASE WHEN ao.approval_status = 'Pending' THEN ao.credit ELSE 0 END), '</u></h3>') AS `UnPaid Amt`,
                CONCAT('<h3><u>', SUM(CASE WHEN ao.approval_status = 'Approved' THEN ao.credit ELSE 0 END), '</u></h3>') AS `Paid Amt`,
                CONCAT('<h3><u>', SUM(CASE WHEN ao.approval_status = 'Rejected' THEN ao.credit ELSE 0 END), '</u></h3>') AS `Rejected Amt`
            FROM apiorder ao
            WHERE ao.ordertype = (SELECT id FROM ordertype WHERE title = 'MDR Other Payments')
              AND ao.initiator_id IS NOT NULL
              AND ao.approval_status != 'Resubmitted'
              AND (:keyword IS NULL OR :keyword = '' OR (
                    ao.recipient2firstname LIKE %:keyword% OR
                    ao.msisdn LIKE %:keyword% OR
                    CAST(ao.credit AS CHAR) LIKE %:keyword% OR
                    DATE_FORMAT(ao.requestTime, '%Y-%m-%d') LIKE %:keyword%
              ))
              AND (:orgIds IS NULL OR ao.ORG_ID IN (:orgIds))
        )
        """,
            countQuery = """
        SELECT COUNT(*) FROM (
            SELECT ao.initiator_id
            FROM apiorder ao
            WHERE ao.ordertype = (SELECT id FROM ordertype WHERE title = 'MDR Other Payments')
              AND ao.initiator_id IS NOT NULL
              AND ao.approval_status != 'Resubmitted'
              AND (:keyword IS NULL OR :keyword = '' OR (
                    ao.recipient2firstname LIKE %:keyword% OR
                  ao.msisdn LIKE %:keyword% OR
                    CAST(ao.credit AS CHAR) LIKE %:keyword% OR
                    DATE_FORMAT(ao.requestTime, '%Y-%m-%d') LIKE %:keyword%
              ))
              AND (:orgIds IS NULL OR ao.ORG_ID IN (:orgIds))
            GROUP BY ao.initiator_id, ao.initiator_username
            UNION
            SELECT 'summary'
        ) as union_table
        """,
            nativeQuery = true
    )
    Page<Map<String, Object>> getMdrOtherPaymentsWithSummary(
            @Param("keyword") String keyword,
            @Param("orgIds") List<Integer> orgIds,
            Pageable pageable
    );

    @Query(
            value = """
        (
            SELECT 
                CONCAT(b.month_of_claim, ' ', YEAR(b.requesttime)) AS `Month of Claim`,
                c.title AS `title`,
                COUNT(DISTINCT b.patient_registration_number) AS `No of Patients`,
                COUNT(DISTINCT b.dot_nurse_name) AS `No of Dot Nurse`
            FROM apiorder b
            INNER JOIN county c ON c.id = b.county
            WHERE b.ordertype = '1'
              AND b.patient_registration_number IS NOT NULL
              AND b.dot_nurse_name IS NOT NULL
              AND (:keyword IS NULL OR :keyword = '' OR (
                    b.initiator_username LIKE %:keyword% OR
                    b.msisdn LIKE %:keyword% OR
                    CAST(b.credit AS CHAR) LIKE %:keyword% OR
                    DATE_FORMAT(b.requestTime, '%Y-%m-%d') LIKE %:keyword%
              ))
              AND (:orgIds IS NULL OR b.ORG_ID IN (:orgIds))
            GROUP BY b.month_of_claim, b.county
        )
        UNION
        (
            SELECT 
                '' AS `Month of Claim`,
                '<h4><u>Summary</u></h4>' AS `title`,
                SUM(P.NoofPatients) AS `No of Patients`,
                SUM(P.NoofDotNurse) AS `No of Dot Nurse`
            FROM (
                SELECT 
                    b.month_of_claim,
                    c.title,
                    COUNT(DISTINCT b.patient_registration_number) AS NoofPatients,
                    COUNT(DISTINCT b.dot_nurse_name) AS NoofDotNurse
                FROM apiorder b
                INNER JOIN county c ON c.id = b.county
                WHERE b.ordertype = '1'
                  AND b.patient_registration_number IS NOT NULL
                  AND b.dot_nurse_name IS NOT NULL
                  AND (:keyword IS NULL OR :keyword = '' OR (
                        b.initiator_username LIKE %:keyword% OR
                        b.msisdn LIKE %:keyword% OR
                        CAST(b.credit AS CHAR) LIKE %:keyword% OR
                        DATE_FORMAT(b.requestTime, '%Y-%m-%d') LIKE %:keyword%
                  ))
                  AND (:orgIds IS NULL OR b.ORG_ID IN (:orgIds))
                GROUP BY b.month_of_claim, b.county
            ) AS P
        )
        """,
            countQuery = """
        SELECT COUNT(*) FROM (
            SELECT b.month_of_claim
            FROM apiorder b
            INNER JOIN county c ON c.id = b.county
            WHERE b.ordertype = '1'
              AND b.patient_registration_number IS NOT NULL
              AND b.dot_nurse_name IS NOT NULL
              AND (:keyword IS NULL OR :keyword = '' OR (
                    b.initiator_username LIKE %:keyword% OR
                    b.msisdn LIKE %:keyword% OR
                    CAST(b.credit AS CHAR) LIKE %:keyword% OR
                    DATE_FORMAT(b.requestTime, '%Y-%m-%d') LIKE %:keyword%
              ))
              AND (:orgIds IS NULL OR b.ORG_ID IN (:orgIds))
            GROUP BY b.month_of_claim, b.county
            UNION
            SELECT 'summary'
        ) as total_union
        """,
            nativeQuery = true
    )
    Page<Map<String, Object>> getPatientSummaryByClaimMonth(
            @Param("keyword") String keyword,
            @Param("orgIds") List<Integer> orgIds,
            Pageable pageable
    );
    @Query(value = """
        SELECT 
            apiorder.ID AS primarykey,
            IFNULL(apiorder.month, apiorder.month_of_claim) AS Month,
            apiorder.year AS `Year`,
            budget.title AS `Budget Line`,
            COALESCE((SELECT title FROM province WHERE province.id = apiorder.province), 'N/A') AS `Region`,
            COALESCE((SELECT title FROM county WHERE county.id = apiorder.county), 'N/A') AS `County`,
            apiorder.facility AS facility,
            treatment_model.title AS `Request Type`,
            apiorder.dot_nurse_name AS `Dot name`,
            apiorder.dot_nurse_phoneno AS `DOT Phone`,
            apiorder.initiator_username AS `Initator`,
            CONVERT_TZ(apiorder.requesttime,'+00:00','+03:00') AS `Request Time`,
            apiorder.recipient2credit AS `Amount`
        FROM apiorder
        LEFT JOIN ordertype ON apiorder.ordertype = ordertype.ID
        LEFT JOIN budget ON apiorder.budget = budget.ID
        LEFT JOIN treatment_model ON apiorder.treatment_model = treatment_model.ID
        WHERE apiorder.approval_status = 'Approved'
        AND apiorder.inTrash = 'No'
        AND ( :keywords IS NULL OR :keywords = '' OR (
                budget.title LIKE %:keywords% OR
                treatment_model.title LIKE %:keywords% OR
                apiorder.dot_nurse_name LIKE %:keywords% OR
                apiorder.recipient2credit LIKE %:keywords% OR
                apiorder.dot_nurse_phoneno LIKE %:keywords%
            ))
        AND (:province IS NULL OR apiorder.province = :province)
        AND (:budget IS NULL OR apiorder.budget = :budget)
        AND (:county IS NULL OR apiorder.county = :county)
        AND (
            :month IS NULL OR 
            apiorder.month_of_claim = :month OR 
            apiorder.month = :month
        )
        AND (:year IS NULL OR apiorder.year LIKE %:year%)
        AND (:dateFrom IS NULL OR apiorder.requesttime >= :dateFrom)
        AND (:dateTo IS NULL OR apiorder.requesttime <= :dateTo)
        AND (:orgIds IS NULL OR apiorder.ORG_ID IN (:orgIds))
        AND (apiorder.dot_nurse_phoneno IS NOT NULL AND apiorder.dot_nurse_phoneno <> '')
        AND apiorder.treatment_model IN ('1','2','5')
        ORDER BY apiorder.ID DESC
        """,
            countQuery = """
        SELECT COUNT(*)
        FROM apiorder
        LEFT JOIN ordertype ON apiorder.ordertype = ordertype.ID
        LEFT JOIN budget ON apiorder.budget = budget.ID
        LEFT JOIN treatment_model ON apiorder.treatment_model = treatment_model.ID
        WHERE apiorder.approval_status = 'Approved'
        AND apiorder.inTrash = 'No'
        AND ( :keywords IS NULL OR :keywords = '' OR (
                budget.title LIKE %:keywords% OR
                treatment_model.title LIKE %:keywords% OR
                apiorder.dot_nurse_name LIKE %:keywords% OR
                apiorder.recipient2credit LIKE %:keywords% OR
                apiorder.dot_nurse_phoneno LIKE %:keywords%
            ))
        AND (:province IS NULL OR apiorder.province = :province)
        AND (:budget IS NULL OR apiorder.budget = :budget)
        AND (:county IS NULL OR apiorder.county = :county)
        AND (
            :month IS NULL OR 
            apiorder.month_of_claim = :month OR 
            apiorder.month = :month
        )
        AND (:year IS NULL OR apiorder.year LIKE %:year%)
        AND (:dateFrom IS NULL OR apiorder.requesttime >= :dateFrom)
        AND (:dateTo IS NULL OR apiorder.requesttime <= :dateTo)
        AND (:orgIds IS NULL OR apiorder.ORG_ID IN (:orgIds))
        AND (apiorder.dot_nurse_phoneno IS NOT NULL AND apiorder.dot_nurse_phoneno <> '')
        AND apiorder.treatment_model IN ('1','2','5')
        """,
            nativeQuery = true
    )
    Page<Map<String, Object>> findFilteredOrders(
            @Param("keywords") String keywords,
            @Param("province") Integer province,
            @Param("budget") Integer budget,
            @Param("county") Integer county,
            @Param("month") String month,
            @Param("year") String year,
            @Param("dateFrom") Timestamp dateFrom,
            @Param("dateTo") Timestamp dateTo,
            @Param("orgIds") List<Integer> orgIds,
            Pageable pageable
    );

    @Query(
            value = """
        SELECT 
            tm.title AS `Request Type`,
            c.title AS `County`,
            SUM(CASE WHEN ao.treatment_model IN ('1','2','5') THEN 1 ELSE 0 END) AS `Patients Requests`,
            SUM(CASE WHEN ao.treatment_model IN ('1','2','5') AND (ao.dot_nurse_phoneno IS NOT NULL AND ao.dot_nurse_phoneno != '') THEN 1 ELSE 0 END) AS `DOT Requests`,
            SUM(CASE WHEN ao.treatment_model IN ('1','2','5') AND ao.approval_status = 'Approved' THEN 1 ELSE 0 END) AS `Patients Paid`,
            SUM(CASE WHEN ao.treatment_model IN ('1','2','5') AND (ao.dot_nurse_phoneno IS NOT NULL AND ao.dot_nurse_phoneno != '') AND ao.approval_status = 'Approved' THEN 1 ELSE 0 END) AS `DOTs Paid`,
            SUM(CASE WHEN ao.treatment_model IN ('1','2','5') AND ao.approval_status = 'Rejected' THEN 1 ELSE 0 END) AS `Patients Payments Rejected`,
            SUM(CASE WHEN ao.treatment_model IN ('1','2','5') AND (ao.dot_nurse_phoneno IS NOT NULL AND ao.dot_nurse_phoneno != '') AND ao.approval_status = 'Rejected' THEN 1 ELSE 0 END) AS `DOTs Payments Rejected`,
            SUM(CASE WHEN ao.treatment_model IN ('1','2','5') AND ao.approval_status = 'Approved' THEN ao.credit ELSE 0 END) AS `Total Patients Amount Paid`,
            SUM(CASE WHEN ao.treatment_model IN ('1','2','5') AND (ao.dot_nurse_phoneno IS NOT NULL AND ao.dot_nurse_phoneno != '') AND ao.approval_status = 'Approved' THEN ao.recipient2credit ELSE 0 END) AS `Total amount paid to DOT`
        FROM 
            apiorder ao
        INNER JOIN treatment_model tm ON tm.id = ao.treatment_model
        INNER JOIN county c ON c.id = ao.county
        WHERE 
            (:treatmentModel IS NULL OR ao.treatment_model = :treatmentModel)
            AND (:county IS NULL OR ao.county = :county)
            AND (
                :keywords IS NULL OR 
                tm.title LIKE CONCAT('%', :keywords, '%') OR 
                c.title LIKE CONCAT('%', :keywords, '%')
            )
            AND (:orgFilter IS NULL OR ao.ORG_ID IN (:orgFilter))
        GROUP BY ao.treatment_model, ao.county
        """,
            countQuery = """
        SELECT COUNT(*) FROM (
            SELECT 1
            FROM apiorder ao
            INNER JOIN treatment_model tm ON tm.id = ao.treatment_model
            INNER JOIN county c ON c.id = ao.county
            WHERE 
                (:treatmentModel IS NULL OR ao.treatment_model = :treatmentModel)
                AND (:county IS NULL OR ao.county = :county)
                AND (
                    :keywords IS NULL OR 
                    tm.title LIKE CONCAT('%', :keywords, '%') OR 
                    c.title LIKE CONCAT('%', :keywords, '%')
                )
                AND (:orgFilter IS NULL OR ao.ORG_ID IN (:orgFilter))
            GROUP BY ao.treatment_model, ao.county
        ) AS sub
        """,
            nativeQuery = true
    )
    Page<Map<String, Object>> getFilteredSummaryReport(
            @Param("treatmentModel") String treatmentModel,
            @Param("county") String county,
            @Param("keywords") String keywords,
            @Param("orgFilter") List<Integer> orgFilter,
            Pageable pageable
    );

    @Query(value = """
    SELECT 
        p.ID AS primarykey,
        county.title AS county,
        CONCAT(p.firstname, ' ', p.lastname) AS patient,
        p.patient_registration_number,
        p.msisdn AS TelephoneNumber,
        p.date_treatment_started,
        p.approval_status,
        p.year AS Year,
        SUM(Jan) AS Jan,
        SUM(Feb) AS Feb,
        SUM(Mar) AS Mar,
        SUM(Apr) AS Apr,
        SUM(May) AS May,
        SUM(Jun) AS Jun,
        SUM(Jul) AS Jul,
        SUM(Aug) AS Aug,
        SUM(Sep) AS Sep,
        SUM(Oct) AS Oct,
        SUM(Nov) AS Nov,
        SUM(Dece) AS Dece,
        (SUM(Jan)+SUM(Feb)+SUM(Mar)+SUM(Apr)+SUM(May)+SUM(Jun)+SUM(Jul)+SUM(Aug)+SUM(Sep)+SUM(Oct)+SUM(Nov)+SUM(Dece)) AS Total
    FROM (
        SELECT r.*,
            CASE WHEN month = 'January' THEN credit ELSE 0 END AS Jan,
            CASE WHEN month = 'February' THEN credit ELSE 0 END AS Feb,
            CASE WHEN month = 'March' THEN credit ELSE 0 END AS Mar,
            CASE WHEN month = 'April' THEN credit ELSE 0 END AS Apr,
            CASE WHEN month = 'May' THEN credit ELSE 0 END AS May,
            CASE WHEN month = 'June' THEN credit ELSE 0 END AS Jun,
            CASE WHEN month = 'July' THEN credit ELSE 0 END AS Jul,
            CASE WHEN month = 'August' THEN credit ELSE 0 END AS Aug,
            CASE WHEN month = 'September' THEN credit ELSE 0 END AS Sep,
            CASE WHEN month = 'October' THEN credit ELSE 0 END AS Oct,
            CASE WHEN month = 'November' THEN credit ELSE 0 END AS Nov,
            CASE WHEN month = 'December' THEN credit ELSE 0 END AS Dece
        FROM (
            SELECT ao.* 
            FROM apiorder ao
            WHERE ao.year = :year
            AND (:keywords IS NULL OR 
                ao.firstname LIKE %:keywords% OR 
                ao.lastname LIKE %:keywords% OR 
                ao.msisdn LIKE %:keywords%)
            AND (:province IS NULL OR ao.province = :province)
            AND (:county IS NULL OR ao.county = :county)
            AND (:month IS NULL OR ao.month_of_claim = :month OR ao.month = :month)
            AND (:dateFrom IS NULL OR ao.requesttime >= CONCAT(:dateFrom, ' 00:00:00'))
            AND (:dateTo IS NULL OR ao.requesttime <= CONCAT(:dateTo, ' 23:59:59'))
            AND (:region IS NULL OR :region = 0 OR ao.county = :region)
            AND ao.treatment_model IN ('1', '2', '4')
            AND ao.msisdn IS NOT NULL AND ao.msisdn != ''
            AND ao.inTrash = 'No'
            AND (:orgFilter IS NULL OR ao.ORG_ID IN (:orgFilter))
            ORDER BY ao.msisdn, ao.id ASC
        ) AS r
    ) p
    JOIN county ON p.county = county.ID
    LEFT JOIN payment ON p.ID = payment.order_id
    GROUP BY p.msisdn
    """,
            nativeQuery = true)
    Page<Map<String, Object>> getPatientMonthlySummaryReport(
            @Param("keywords") String keywords,
            @Param("province") String province,
            @Param("county") String county,
            @Param("month") String month,
            @Param("year") String year,
            @Param("dateFrom") Timestamp dateFrom,
            @Param("dateTo") Timestamp dateTo,
            @Param("orgFilter") List<Integer> orgFilter,
            @Param("region") String region,
            Pageable pageable
    );
    @Query(value = """
    SELECT 
        ao.ID AS primarykey,
        ao.batchno AS batchno,
        al.approval_level_name AS approval_level,
        IFNULL(ao.month, ao.month_of_claim) AS month,
        DATE_FORMAT(ao.requesttime, '%Y') AS year,
        b.title AS budget_line,
        COALESCE((SELECT p.title FROM province p WHERE p.id = ao.province), 'N/A') AS region,
        COALESCE((SELECT c.title FROM county c WHERE c.id = ao.county), 'N/A') AS county,
        ao.facility AS facility,
        tm.title AS request_type,
        ao.msisdn AS phone_no,
        ao.approval_status AS status,
        CONCAT(ao.firstname, ' ', ao.middlename, ' ', ao.lastname) AS beneficiary,
        ao.initiator_username AS initiator,
        CONVERT_TZ(ao.requesttime, '+00:00', '+03:00') AS request_time,
        ao.patient_registration_number AS patient_number,
        ao.date_treatment_started AS date_treatment_started,
        ao.dot_nurse_name AS dot_name,
        ao.dot_nurse_phoneno AS dot_phone,
        ao.recipient2credit AS dot_amount,
        ao.driver_amount AS driver_amt,
        ao.credit AS amount
    FROM 
        apiorder ao
        LEFT JOIN ordertype ot ON ao.ordertype = ot.ID
        LEFT JOIN budget b ON ao.budget = b.ID
        LEFT JOIN treatment_model tm ON ao.treatment_model = tm.ID
        LEFT JOIN approval_levels al ON ao.approval_level = al.approval_level
    WHERE 
        ao.inTrash = 'No'
        AND (:keywords IS NULL OR (
            b.title LIKE %:keywords% OR 
            tm.title LIKE %:keywords% OR 
            ao.msisdn LIKE %:keywords% OR 
            ao.credit LIKE %:keywords% OR 
            ao.firstname LIKE %:keywords% OR 
            ao.middlename LIKE %:keywords% OR 
            ao.lastname LIKE %:keywords% OR 
            ao.facility LIKE %:keywords% OR 
            ao.batchno LIKE %:keywords%
        ))
        AND (:province IS NULL OR ao.province = :province)
        AND (:counties IS NULL OR ao.county IN (:counties))
        AND (:budgets IS NULL OR ao.budget IN (:budgets))
        AND (:dateFrom IS NULL OR ao.requesttime >= CONCAT(:dateFrom, ' 00:00:00'))
        AND (:dateTo IS NULL OR ao.requesttime <= CONCAT(:dateTo, ' 23:59:59'))
        AND (:batch IS NULL OR ao.batchno = :batch)
        AND (:month IS NULL OR UPPER(ao.month_of_claim) = UPPER(:month))
        AND (:year IS NULL OR DATE_FORMAT(ao.requesttime, '%Y') = :year)
        AND (:approvalStatus IS NULL OR ao.approval_status = :approvalStatus)
        AND (:region = 0 OR ao.county = :region)
        AND ao.msisdn IN (:phoneNumbers)
        AND ao.approval_status = 'Pending'
    ORDER BY 
        ao.ID DESC
    """,
            countQuery = """
        SELECT COUNT(*) FROM apiorder ao
        LEFT JOIN budget b ON ao.budget = b.ID
        LEFT JOIN treatment_model tm ON ao.treatment_model = tm.ID
        WHERE 
            ao.inTrash = 'No'
            AND (:keywords IS NULL OR (
                b.title LIKE %:keywords% OR 
                tm.title LIKE %:keywords% OR 
                ao.msisdn LIKE %:keywords% OR 
                ao.credit LIKE %:keywords% OR 
                ao.firstname LIKE %:keywords% OR 
                ao.middlename LIKE %:keywords% OR 
                ao.lastname LIKE %:keywords% OR 
                ao.facility LIKE %:keywords% OR 
                ao.batchno LIKE %:keywords%
            ))
            AND (:province IS NULL OR ao.province = :province)
            AND (:counties IS NULL OR ao.county IN (:counties))
            AND (:budgets IS NULL OR ao.budget IN (:budgets))
            AND (:dateFrom IS NULL OR ao.requesttime >= CONCAT(:dateFrom, ' 00:00:00'))
            AND (:dateTo IS NULL OR ao.requesttime <= CONCAT(:dateTo, ' 23:59:59'))
            AND (:batch IS NULL OR ao.batchno = :batch)
            AND (:month IS NULL OR UPPER(ao.month_of_claim) = UPPER(:month))
            AND (:year IS NULL OR DATE_FORMAT(ao.requesttime, '%Y') = :year)
            AND (:approvalStatus IS NULL OR ao.approval_status = :approvalStatus)
            AND (:region = 0 OR ao.county = :region)
            AND ao.msisdn IN (:phoneNumbers)
            AND ao.approval_status = 'Pending'
    """,
            nativeQuery = true
    )
    Page<Map<String, Object>> findFilteredApiOrders(
            @Param("keywords") String keywords,
            @Param("province") String province,
            @Param("budgets") List<String> budgets,
            @Param("counties") List<String> counties,
            @Param("dateFrom") Timestamp dateFrom,
            @Param("dateTo") Timestamp dateTo,
            @Param("batch") String batch,
            @Param("month") String month,
            @Param("year") String year,
            @Param("approvalStatus") String approvalStatus,
            @Param("region") Integer region,
            @Param("phoneNumbers") List<String> phoneNumbers,
            Pageable pageable
    );

    int countAllByBatchnoAndApprovalLevel(String batchNo, String approvalStatus);




}

