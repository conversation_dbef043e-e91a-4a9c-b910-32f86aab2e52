package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.ApiOrderCommunityView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

@Repository
public interface ApiOrderCommunityViewRepository extends JpaRepository<ApiOrderCommunityView, Integer> {
    @Query("SELECT v FROM ApiOrderCommunityView v WHERE " +
            "(:keywords IS NULL OR " +
            "LOWER(v.budgetLine) LIKE LOWER(CONCAT('%', :keywords, '%')) OR " +
            "LOWER(v.requestType) LIKE LOWER(CONCAT('%', :keywords, '%')) OR " +
            "LOWER(v.phoneNo) LIKE LOWER(CONCAT('%', :keywords, '%')) OR " +
            "LOWER(v.dotName) LIKE LOWER(CONCAT('%', :keywords, '%')) OR " +
            "LOWER(v.facility) LIKE LOWER(CONCAT('%', :keywords, '%')) OR " +
            "LOWER(v.batchno) LIKE LOWER(CONCAT('%', :keywords, '%')) OR " +
            "LOWER(v.region) LIKE LOWER(CONCAT('%', :keywords, '%')) OR " +
            "LOWER(v.approvalLevel) LIKE LOWER(CONCAT('%', :keywords, '%')))" +
            "AND (:province IS NULL OR v.region = :province) " +
            "AND (:county IS NULL OR v.county = :county) " +
            "AND (:budget IS NULL OR v.budgetLine = :budget) " +
            "AND (:month IS NULL OR UPPER(v.month) = UPPER(:month)) " +
            "AND (:year IS NULL OR v.year = :year) " +
            "AND (:status IS NULL OR v.status = :status) " +
            "AND (:dateFrom IS NULL OR v.requestTime >= :dateFrom) " +
            "AND (:dateTo IS NULL OR v.requestTime <= :dateTo)")
    Page<ApiOrderCommunityView> searchCommunityOrders(
            @Param("keywords") String keywords,
            @Param("province") String province,
            @Param("county") String county,
            @Param("budget") String budget,
            @Param("month") String month,
            @Param("year") String year,
            @Param("status") String status,
            @Param("dateFrom") LocalDateTime dateFrom,
            @Param("dateTo") LocalDateTime dateTo,
            Pageable pageable
    );
}
