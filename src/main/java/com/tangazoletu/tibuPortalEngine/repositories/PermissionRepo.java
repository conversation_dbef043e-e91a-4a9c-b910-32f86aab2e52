package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.dto.PermissionDto;
import com.tangazoletu.tibuPortalEngine.entities.Permission;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PermissionRepo extends JpaRepository<Permission, Integer>, JpaSpecificationExecutor<Permission> {
    @Query("SELECT DISTINCT p.module FROM Permission p WHERE p.module <> :excludedModule ORDER BY p.module")
    List< com.tangazoletu.tibuPortalEngine.enums.Module> findDistinctModulesExcludingSystem(@Param("excludedModule") com.tangazoletu.tibuPortalEngine.enums.Module excludedModule);


    @Query("""
    SELECT p.id, p.description
    FROM Permission p
    WHERE p.module = :module
      AND p.id IN (
          SELECT DISTINCT pm.permission FROM PermissionMap pm
          WHERE pm.role IN (
              SELECT rm.role FROM RoleMap rm WHERE rm.user = :userId
          )
      )
    ORDER BY p.id ASC
""")
    List<PermissionDto> findPermissionsByModuleAndUser(@Param("module") com.tangazoletu.tibuPortalEngine.enums.Module module, @Param("userId") String userId);

    @Query("""
    SELECT m FROM Permission m 
    WHERE (
        :keyword IS NULL OR :keyword = '' OR 
        LOWER(m.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR 
        LOWER(CAST(m.creationTime AS string)) LIKE LOWER(CONCAT('%', :keyword, '%')) OR 
        LOWER(m.description) LIKE LOWER(CONCAT('%', :keyword, '%'))
    )
    ORDER BY m.id DESC
""")
    Page<Permission> findAll(@Param("keyword") String keyword, Pageable pageable);



}
