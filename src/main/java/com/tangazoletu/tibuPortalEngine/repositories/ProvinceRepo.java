package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.Province;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Map;

@Repository
public interface ProvinceRepo extends JpaRepository<Province, Long>, JpaSpecificationExecutor<Province> {

    @Query(
            value = "SELECT " +
                    "ID AS primarykey, " +
                    "ID AS ID, " +
                    "title AS Province, " +
                    "FROM_UNIXTIME(creationTime) AS `Time Created` " +
                    "FROM province " +
                    "WHERE inTrash = 'No' " +
                    "AND (:keyword IS NULL OR :keyword = '' " +
                    "OR title LIKE %:keyword% " +
                    "OR creationTime LIKE %:keyword%) " +
                    "ORDER BY title ASC",
            countQuery = "SELECT COUNT(*) FROM province " +
                    "WHERE inTrash = 'No' " +
                    "AND (:keyword IS NULL OR :keyword = '' " +
                    "OR title LIKE %:keyword% " +
                    "OR creationTime LIKE %:keyword%)",
            nativeQuery = true
    )
    Page<Map<String, Object>> findProvincesWithKeyword(
            @Param("keyword") String keyword,
            Pageable pageable
    );
    @Query(value = "select title from province where id = :id", nativeQuery = true)
    Page<Map<String, Object>> findProvinces(@Param("id") Long id, Pageable pageable);
}
