package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.BatchMetadataView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface BatchMetadataViewRepository extends JpaRepository<BatchMetadataView, Long> {

    Page<BatchMetadataView> findAllByBatchNo(String batchNo, Pageable pageable);
}
