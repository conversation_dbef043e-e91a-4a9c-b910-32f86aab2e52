package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.ApiOrderApprovalSummaryView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Repository
public interface ApiOrderApprovalSummaryViewRepository extends JpaRepository<ApiOrderApprovalSummaryView, Long> {

    @Query("""
    SELECT v FROM ApiOrderApprovalSummaryView v
    WHERE (:batchNumber IS NULL OR v.batchNumber = :batchNumber)
      AND (:county IS NULL OR v.county = :county)
      AND (:region IS NULL OR v.region = :region)
      AND (:requestType IS NULL OR v.requestType = :requestType)
      AND (:approvalStatus IS NULL OR v.approvalStatus = :approvalStatus)
      AND (:supervisorName IS NULL OR v.supervisorName = :supervisorName)
      AND (:startDate IS NULL OR v.requestTime >= :startDate)
      AND (:endDate IS NULL OR v.requestTime <= :endDate)
""")
    Page<ApiOrderApprovalSummaryView> searchApprovalSummary(
            @Param("batchNumber") String batchNumber,
            @Param("county") String county,
            @Param("region") String region,
            @Param("requestType") String requestType,
            @Param("approvalStatus") String approvalStatus,
            @Param("supervisorName") String supervisorName,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            Pageable pageable
    );

}
