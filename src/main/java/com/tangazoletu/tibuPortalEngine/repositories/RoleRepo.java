package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.Role;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface RoleRepo extends JpaRepository<Role, Integer>, JpaSpecificationExecutor<Role> {

    @Query(value = """
        SELECT r.primarykey,
               r.ID,
               r.organisation,
               r.role,
               r.time_created,
               r.org_id,
               r.Permissions
        FROM role_view r
        WHERE r.isSystem = 'No'
          AND r.inTrash = 'No'
          AND (:orgId IS NULL OR r.organisation = :orgId)
          AND (
            :keyword IS NULL OR :keyword = '' OR 
            r.role LIKE %:keyword% OR 
            r.time_created LIKE %:keyword%
          )
        ORDER BY r.ID ASC
        """,
            countQuery = """
        SELECT COUNT(*) FROM role_view r
        WHERE r.isSystem = 'No'
          AND r.inTrash = 'No'
          AND (:orgId IS NULL OR r.organisation = :orgId)
          AND (
            :keyword IS NULL OR :keyword = '' OR 
            r.role LIKE %:keyword% OR 
            r.time_created LIKE %:keyword%
          )
        """,
            nativeQuery = true)
    Page<Object[]> getFilteredRoles(@Param("orgId") String orgId,
                                    @Param("keyword") String keyword,
                                    Pageable pageable);
}
