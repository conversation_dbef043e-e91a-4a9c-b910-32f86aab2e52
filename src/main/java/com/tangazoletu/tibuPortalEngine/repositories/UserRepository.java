package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.User;
import com.tangazoletu.tibuPortalEngine.entities.UserView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    Optional<User> findByLogin(String username);
    Optional<User> findByLoginAndOrganizationName(String username, String organizationName);
    Boolean existsByLogin(String username);
    Boolean existsByEmail(String email);

    Page<User> findByLoginContainingIgnoreCase(String name, Pageable pageable);
    Page<User> findByEmail(String email, Pageable pageable);

    @Query("SELECT u FROM User u WHERE LOWER(u.email) LIKE LOWER(CONCAT('%', :identifier, '%')) " +
            "OR LOWER(u.login) LIKE LOWER(CONCAT('%', :identifier, '%'))")
    Page<User> findByEmailOrLoginNonExactMatch(@Param("identifier") String identifier, Pageable pageable);

    @Query("SELECT u FROM User u WHERE u.inTrash = :inTrash")
    Page<User> findInTrash(@Param("inTrash") String inTrash, Pageable pageable);



    @Query("SELECT u FROM User u WHERE u.login = :username " +
            "AND u.status = 1 " +
            "AND LOWER(u.inTrash) = 'no' " +
            "AND u.organization.id = :orgId")
    User findValidUserWithTempPassword(@Param("username") String username,
                                        @Param("hashedPassword") String hashedPassword,
                                        @Param("orgId") Long orgId);


    @Query("SELECT u FROM User u WHERE u.login = :username " +
            "AND u.status = 1 " +
            "AND LOWER(u.inTrash) = 'no' " +
            "AND u.organization.id = :orgId")
    User findValidUserWithPassword(@Param("username") String username,
                                       @Param("orgId") Long orgId);


    @Query("SELECT u FROM User u WHERE u.inTrash = true")
    Page<User> findeactivated(Pageable pageable);

    @Query("SELECT u FROM User u WHERE u.inTrash = true AND " +
            "(LOWER(u.email) LIKE LOWER(CONCAT('%', :user, '%')) " +
            "OR LOWER(u.login) LIKE LOWER(CONCAT('%', :user, '%')))")
    Page<User> searchDeactivatedUserById(@Param("user") String user, Pageable pageable);

    @Query(
            value = """
        SELECT 
            o.name AS organisation,
            u.login,
            u.firstname,
            u.lastname,
            (
                SELECT GROUP_CONCAT(r.title)
                FROM rolemap rm
                INNER JOIN role r ON r.ID = rm.role
                WHERE rm.user = u.ID
            ) AS roles,
            u.email,
            u.status
        FROM `user` u
        JOIN organisation o ON u.org_id = o.id
        WHERE u.inTrash = 'No'
          AND (:organisation IS NULL OR LOWER(o.name) LIKE CONCAT('%', LOWER(:organisation), '%'))
          AND (:firstName IS NULL OR LOWER(u.firstname) LIKE CONCAT('%', LOWER(:firstName), '%'))
          AND (:lastName IS NULL OR LOWER(u.lastname) LIKE CONCAT('%', LOWER(:lastName), '%'))
          AND (:email IS NULL OR LOWER(u.email) LIKE CONCAT('%', LOWER(:email), '%'))
          AND (
            :role IS NULL OR EXISTS (
              SELECT 1
              FROM rolemap rm
              JOIN role r ON r.ID = rm.role
              WHERE rm.user = u.ID AND LOWER(r.title) LIKE CONCAT('%', LOWER(:role), '%')
            )
          )
        ORDER BY u.firstname ASC
        """,
            countQuery = """
        SELECT COUNT(*) 
        FROM `user` u 
        JOIN organisation o ON u.org_id = o.id
        WHERE u.inTrash = 'No'
          AND (:organisation IS NULL OR LOWER(o.name) LIKE CONCAT('%', LOWER(:organisation), '%'))
          AND (:firstName IS NULL OR LOWER(u.firstname) LIKE CONCAT('%', LOWER(:firstName), '%'))
          AND (:lastName IS NULL OR LOWER(u.lastname) LIKE CONCAT('%', LOWER(:lastName), '%'))
          AND (:username IS NULL OR LOWER(u.login) LIKE CONCAT('%', LOWER(:username), '%'))
          AND (:email IS NULL OR LOWER(u.email) LIKE CONCAT('%', LOWER(:email), '%'))
          AND (
            :role IS NULL OR EXISTS (
              SELECT 1
              FROM rolemap rm
              JOIN role r ON r.ID = rm.role
              WHERE rm.user = u.ID AND LOWER(r.title) LIKE CONCAT('%', LOWER(:role), '%')
            )
          )
        """,
            nativeQuery = true
    )
    Page<Object[]> fetchUsersFiltered(
            @Param("organisation") String organisation,
            @Param("username") String username,
            @Param("firstName") String firstName,
            @Param("lastName") String lastName,
            @Param("role") String role,
            @Param("email") String email,
            Pageable pageable
    );




}
