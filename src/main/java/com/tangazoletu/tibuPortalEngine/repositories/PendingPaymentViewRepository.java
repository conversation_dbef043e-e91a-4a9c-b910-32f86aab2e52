package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.PendingPaymentView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface PendingPaymentViewRepository extends JpaRepository<PendingPaymentView, Long> {

    @Query(value = "select p from PendingPaymentView p where p.organisation = :orgId AND (\n" +
            "          :keyword IS NULL OR :keyword = '' OR (\n" +
            "              p.recipientName LIKE %:keyword% OR\n" +
            "              p.phoneNo LIKE %:keyword% OR\n" +
            "              p.trxId  LIKE %:keyword% OR\n" +
            "              DATE_FORMAT(p.transactionDate, '%Y-%m-%d') LIKE %:keyword%\n" +
            "          )\n" +
            "      )")
    Page<PendingPaymentView> findAllByTrxStatusAndOrd(@Param("orgId") Integer orgId, @Param("keyword") String keyword, Pageable pageable);

    Optional<PendingPaymentView> findById(@Param("id") Long id);
}

