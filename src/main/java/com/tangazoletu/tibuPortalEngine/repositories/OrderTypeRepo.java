package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.OrderType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Map;

@Repository
public interface OrderTypeRepo extends JpaRepository<OrderType, Long>, JpaSpecificationExecutor<OrderType> {

    @Query(
            value = "SELECT " +
                    "ordertype.ID AS primarykey, " +

                    "organisation.name AS Organisation, " +
                    "ordertype.title AS `Payment type`, " +
                    "FROM_UNIXTIME(ordertype.creationTime) AS `Time Created` " +
                    "FROM ordertype " +
                    "INNER JOIN organisation ON ordertype.org_id = organisation.ID " +
                    "WHERE ordertype.inTrash = 'No' " +
                    "AND (:keyword IS NULL OR :keyword = '' " +
                    "OR ordertype.title LIKE %:keyword% " +
                    "OR ordertype.creationTime LIKE %:keyword%) " +
                    "ORDER BY ordertype.ID ASC",
            countQuery = "SELECT COUNT(*) FROM ordertype " +
                    "INNER JOIN organisation ON ordertype.org_id = organisation.ID " +
                    "WHERE ordertype.inTrash = 'No' " +
                    "AND (:keyword IS NULL OR :keyword = '' " +
                    "OR ordertype.title LIKE %:keyword% " +
                    "OR ordertype.creationTime LIKE %:keyword%)",
            nativeQuery = true
    )
    Page<Map<String, Object>> findOrderTypesWithKeyword(
            @Param("keyword") String keyword,
            Pageable pageable
    );
    @Query(value = "select title,org_id, `shareble` from ordertype  where ordertype.id = :id", nativeQuery = true)
    Page<Map<String, Object>> findById(@Param("id") Long id, Pageable pageable);
}
