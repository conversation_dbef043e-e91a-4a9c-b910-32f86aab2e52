package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.User;
import com.tangazoletu.tibuPortalEngine.entities.UserView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;

public interface UserViewRepository extends JpaRepository<UserView, Long>, JpaSpecificationExecutor<UserView> {

    @Query("SELECT u FROM UserView u WHERE LOWER(u.email) LIKE LOWER(CONCAT('%', :identifier, '%')) " +
            "OR LOWER(u.username) LIKE LOWER(CONCAT('%', :identifier, '%'))")
    Page<UserView> findByEmailOrLoginNonExactMatch(@Param("identifier") String identifier, Pageable pageable);

    @Query("SELECT u FROM UserView u WHERE u.inTrash = :inTrash")
    Page<UserView> findInTrash(@Param("inTrash") String inTrash, Pageable pageable);

    @Query("SELECT u FROM UserView u WHERE u.inTrash = 'Yes'")
    Page<UserView> findeactivated(Pageable pageable);

    @Query(
            value = """
        SELECT 
            u.organisation,
            u.Username,
            u.firstname,
            u.lastname,
            u.roles,
            u.email,
            u.status,
            u.organizationId AS orgId,
            u.roleIds,
            u.county
        FROM `user_details_view` u
        JOIN organisation o ON u.organizationId = o.id
        WHERE u.inTrash = 'No'
          AND (:organisation IS NULL OR LOWER(o.name) LIKE CONCAT('%', LOWER(:organisation), '%'))
          AND (:firstName IS NULL OR LOWER(u.firstname) LIKE CONCAT('%', LOWER(:firstName), '%'))
          AND (:lastName IS NULL OR LOWER(u.lastname) LIKE CONCAT('%', LOWER(:lastName), '%'))
          AND (:email IS NULL OR LOWER(u.email) LIKE CONCAT('%', LOWER(:email), '%'))
          AND (
            :role IS NULL OR EXISTS (
              SELECT 1
              FROM rolemap rm
              JOIN role r ON r.ID = rm.role
              WHERE rm.user = u.primarykey AND LOWER(r.title) LIKE CONCAT('%', LOWER(:role), '%')
            )
          )
        ORDER BY u.primarykey DESC
        """,
            countQuery = """
        SELECT COUNT(*) 
        FROM `user_details_view` u 
        JOIN organisation o ON u.organizationId = o.id
        WHERE u.inTrash = 'No'
          AND (:organisation IS NULL OR LOWER(o.name) LIKE CONCAT('%', LOWER(:organisation), '%'))
          AND (:firstName IS NULL OR LOWER(u.firstname) LIKE CONCAT('%', LOWER(:firstName), '%'))
          AND (:lastName IS NULL OR LOWER(u.lastname) LIKE CONCAT('%', LOWER(:lastName), '%'))
          AND (:username IS NULL OR LOWER(u.username) LIKE CONCAT('%', LOWER(:username), '%'))
          AND (:email IS NULL OR LOWER(u.email) LIKE CONCAT('%', LOWER(:email), '%'))
          AND (
            :role IS NULL OR EXISTS (
              SELECT 1
              FROM rolemap rm
              JOIN role r ON r.ID = rm.role
              WHERE rm.user = u.primarykey AND LOWER(r.title) LIKE CONCAT('%', LOWER(:role), '%')
            )
          )
        ORDER BY u.primarykey DESC
        """,
            nativeQuery = true
    )
    Page<Object[]> fetchUsersFiltered(
            @Param("organisation") String organisation,
            @Param("username") String username,
            @Param("firstName") String firstName,
            @Param("lastName") String lastName,
            @Param("role") String role,
            @Param("email") String email,
            Pageable pageable
    );
}
