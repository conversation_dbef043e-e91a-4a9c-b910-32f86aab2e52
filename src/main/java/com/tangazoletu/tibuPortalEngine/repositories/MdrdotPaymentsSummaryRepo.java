package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.MdrdotPaymentsSummary;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface MdrdotPaymentsSummaryRepo extends JpaRepository<MdrdotPaymentsSummary, Integer>, JpaSpecificationExecutor<MdrdotPaymentsSummary> {
    @Query("""
        SELECT v FROM MdrdotPaymentsSummary v
        WHERE 
            (:keyword IS NULL OR :keyword = '' OR 
                LOWER(v.dotName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                LOWER(v.dotNursePhoneNumber) LIKE LOWER(CONCAT('%', :keyword, '%')))
            AND (:year IS NULL OR v.year LIKE CONCAT('%', :year, '%'))
    """)
    Page<MdrdotPaymentsSummary> searchDotSummary(
            @Param("keyword") String keyword,
            @Param("year") String year,
            Pageable pageable
    );
}
