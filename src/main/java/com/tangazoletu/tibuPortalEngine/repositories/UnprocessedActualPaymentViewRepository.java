package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.UnprocessedActualPaymentView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UnprocessedActualPaymentViewRepository extends JpaRepository<UnprocessedActualPaymentView, Long> {
    @Query("""
        SELECT u FROM UnprocessedActualPaymentView u
        WHERE (:keyword IS NULL OR :keyword = '' OR
               u.beneficiary LIKE %:keyword% OR
               u.contactNumber LIKE %:keyword% OR
               u.trxId LIKE %:keyword%)
                       AND u.organisation = :orgId
        """)
    Page<UnprocessedActualPaymentView> searchByKeyword(@Param("keyword") String keyword, @Param("orgId") Integer organisation, Pageable pageable);
}
