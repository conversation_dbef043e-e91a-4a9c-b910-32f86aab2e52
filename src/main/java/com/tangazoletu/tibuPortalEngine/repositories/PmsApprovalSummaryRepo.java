package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.PmsApprovalSummary;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface PmsApprovalSummaryRepo extends JpaRepository<PmsApprovalSummary, Integer>, JpaSpecificationExecutor<PmsApprovalSummary> {

    Optional<PmsApprovalSummary> findFirstByPaymentId(Long paymentId);

}
