package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.PaymentRequestDetails;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Map;

@Repository
public interface PaymentRequestDetailsRepo extends JpaRepository<PaymentRequestDetails, Long> {

    Page<PaymentRequestDetails> findAllById(Long id, Pageable pageable);
    @Query(value = """
    SELECT 
        prd.recipient AS recipient,
        prd.credit - (SELECT ao.driver_amount FROM apiorder ao WHERE ao.id = :orderId) AS credit_diff,
        prd.msisdn AS msisdn,
        prd.onbehalfrecipient AS onbehalfrecipient,
        prd.recipient2credit AS recipient2credit,
        prd.recipient2msisdn AS recipient2msisdn,
        prd.recipient2 AS recipient2,
        prd.notes AS notes,
        prd.ordertype AS ordertype,
        prd.treatment_model AS treatment_model,
        (SELECT ao.beneficiaryType FROM apiorder ao WHERE ao.id = :orderId) AS beneficiaryType,
        (SELECT ao.beneficiary2Type FROM apiorder ao WHERE ao.id = :orderId) AS beneficiary2Type
    FROM 
        paymentrequestdetails prd
    WHERE 
        prd.id = :orderId
    """,
            countQuery = "SELECT COUNT(*) FROM paymentrequestdetails WHERE id = :orderId",
            nativeQuery = true
    )
    Page<Map<String, Object>> getPaymentRequestDetailsByOrderId(
            @Param("orderId") String orderId,
            Pageable pageable
    );

    @Query(value = "SELECT COUNT(*) FROM patients_info", nativeQuery = true)
    Page<Map<String, Object>> getPatientCompleteTreatment(Pageable pageable);
    @Query(value = "SELECT recipient,credit,msisdn,onbehalfrecipient, recipient2credit, recipient2msisdn, recipient2, notes, ordertype  FROM paymentrequestdetails WHERE id = :id", nativeQuery = true)
    Page<Map<String, Object>> getCompletedTreatmentById(@Param("id") Long id, Pageable pageable);

}
