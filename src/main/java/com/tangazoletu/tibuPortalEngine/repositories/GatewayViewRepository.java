package com.tangazoletu.tibuPortalEngine.repositories;
import com.tangazoletu.tibuPortalEngine.entities.GatewayView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface GatewayViewRepository extends JpaRepository<GatewayView, Long>, JpaSpecificationExecutor<GatewayView> {

    @Query("""
        SELECT g FROM GatewayView g
        WHERE (:orgIds IS NULL OR g.orgId IN :orgIds)
          AND (:keyword IS NULL OR :keyword = '' OR
                LOWER(g.organisation) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                LOWER(g.mpesaId) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                LOWER(g.recipientName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                LOWER(g.phoneNo) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                CAST(g.amount AS string) LIKE CONCAT('%', :keyword, '%') OR
                CAST(g.date AS string) LIKE CONCAT('%', :keyword, '%')
          )
          AND (:dateFrom IS NULL OR g.date >= :dateFrom)
          AND (:dateTo IS NULL OR g.date <= :dateTo)
    """)
    Page<GatewayView> searchGatewayData(
            @Param("keyword") String keyword,
            @Param("orgIds") Integer orgIds,
//            @Param("dateFrom") LocalDateTime dateFrom,
//            @Param("dateTo") LocalDateTime dateTo,
            Pageable pageable
    );
}

