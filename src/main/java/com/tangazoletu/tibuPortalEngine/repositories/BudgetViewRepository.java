package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.BudgetView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface BudgetViewRepository extends JpaRepository<BudgetView, Integer> {

    @Query("""
        SELECT b FROM BudgetView b
        WHERE (:keyword IS NULL OR :keyword = '' OR
              LOWER(b.organisation) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
              LOWER(b.budgetLine) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
              LOWER(b.description) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
              b.timeCreated LIKE CONCAT('%', :keyword, '%'))
        ORDER BY b.primarykey DESC
        """)
    Page<BudgetView> searchBudget(@Param("keyword") String keyword, Pageable pageable);
}
