package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.ApiParams;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ApiParamsRepository extends JpaRepository<ApiParams, Integer>, JpaSpecificationExecutor<ApiParams> {

    @Query("""
        SELECT a FROM ApiParams a 
        WHERE (:orgId IS NULL OR a.orgId = :orgId)  
          AND (
            :keyword IS NULL OR :keyword = '' OR 
            LOWER(a.initiatorUsername) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(a.serviceId) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(a.commandId) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(a.shortcode) LIKE LOWER(CONCAT('%', :keyword, '%'))
          )
        ORDER BY a.id DESC
    """)
    Page<ApiParams> searchParams(@Param("orgId") Integer orgId,
                                 @Param("keyword") String keyword,
                                 Pageable pageable);
}
