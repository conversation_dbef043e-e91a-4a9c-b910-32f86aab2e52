package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.Perdiem;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.data.jpa.repository.JpaRepository;

import java.math.BigDecimal;
import java.util.Map;

@Repository
public interface PerdiemRepo extends JpaRepository<Perdiem, Long>, JpaSpecificationExecutor<Perdiem> {

    @Query(value = """
        SELECT 
            pd.ID AS primarykey,
            pd.ID AS id,
            org.name AS Organisation,
            pd.Designation AS Designation,
            pd.Amount AS Amount,
            pd.lunchDinnerRate AS lunchDinnerRate,
            pd.dinnerRate AS dinnerRate,
            pd.creationTime AS `Time Created`
        FROM perdiem pd
        LEFT JOIN organisation org ON org.id = pd.org_id
        WHERE pd.inTrash = 'No'
            AND (:keyword IS NULL OR :keyword = '' 
                OR org.name LIKE %:keyword%
                OR pd.Designation LIKE %:keyword%
                OR pd.creationTime LIKE %:keyword%)
            AND (:orgFilter IS NULL OR :orgFilter = '' OR :orgFilter = 'ORG_ID' OR pd.ORG_ID = :orgFilter)
        ORDER BY pd.ID ASC
        """,
            countQuery = """
        SELECT COUNT(*) FROM perdiem pd
        LEFT JOIN organisation org ON org.id = pd.org_id
        WHERE pd.inTrash = 'No'
            AND (:keyword IS NULL OR :keyword = '' 
                OR org.name LIKE %:keyword%
                OR pd.Designation LIKE %:keyword%
                OR pd.creationTime LIKE %:keyword%)
            AND (:orgFilter IS NULL OR :orgFilter = '' OR :orgFilter = 'ORG_ID' OR pd.ORG_ID = :orgFilter)
        """,
            nativeQuery = true
    )
    Page<Map<String, Object>> findFilteredPerDiem(
            @Param("keyword") String keyword,
            @Param("province") String province,
            @Param("orgFilter") String orgFilter,
            Pageable pageable
    );

    Page<Perdiem> findById(Long id, Pageable pageable);

    @Query("SELECT CASE WHEN :type = 'DayOnly' THEN p.lunchDinnerRate WHEN :type = 'HalfBoard' THEN p.dinnerRate ELSE p.amount END FROM Perdiem p WHERE p.id = :designationId")
    BigDecimal getRateByTypeAndDesignation(@Param("type") String type, @Param("designationId") Long designationId);

    @Query("SELECT p.amount FROM Perdiem p WHERE p.id = :did")
    BigDecimal getAmountByDesignation(@Param("did") Long designationId);
}
