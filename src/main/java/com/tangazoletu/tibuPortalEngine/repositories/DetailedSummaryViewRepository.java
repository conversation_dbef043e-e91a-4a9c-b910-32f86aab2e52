package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.DetailedSummaryView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DetailedSummaryViewRepository extends JpaRepository<DetailedSummaryView, Integer>, JpaSpecificationExecutor<DetailedSummaryView> {

    @Query("""
        SELECT d FROM DetailedSummaryView d
        WHERE (:orgIds IS NULL OR d.orgId IN :orgIds)
    """)
    Page<DetailedSummaryView> findSummaryByOrgIds(@Param("orgIds") List<Integer> orgIds, Pageable pageable);
}

