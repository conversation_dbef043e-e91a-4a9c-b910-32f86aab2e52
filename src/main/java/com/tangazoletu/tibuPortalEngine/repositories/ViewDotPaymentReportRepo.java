package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.ViewDotPaymentReport;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ViewDotPaymentReportRepo extends JpaRepository<ViewDotPaymentReport, Long>, JpaSpecificationExecutor<ViewDotPaymentReport> {
    @Query("""
        SELECT v FROM ViewDotPaymentReport v
        WHERE
            (:keywords IS NULL OR :keywords = '' OR (
                LOWER(v.budgetLine) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
                LOWER(v.requestType) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
                LOWER(v.dotName) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
                LOWER(v.dotPhone) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
                CAST(v.amount AS string) LIKE %:keywords%
            ))
            AND (:province IS NULL OR v.region = :province)
            AND (:county IS NULL OR v.county = :county)
            AND (:budget IS NULL OR v.budgetLine = :budget)
            AND (:month IS NULL OR v.month = :month)
            AND (:year IS NULL OR v.year LIKE %:year%)
            AND (:dateFrom IS NULL OR v.requestTime >= :dateFrom)
            AND (:dateTo IS NULL OR v.requestTime <= :dateTo)
            AND (:orgIds IS NULL OR v.orgId IN :orgIds)
        """)
    Page<ViewDotPaymentReport> findFilteredDotReports(
            @Param("keywords") String keywords,
            @Param("province") Integer province,
            @Param("budget") String budget,
            @Param("county") String county,
            @Param("month") String month,
            @Param("year") String year,
            @Param("dateFrom") LocalDateTime dateFrom,
            @Param("dateTo") LocalDateTime dateTo,
            @Param("orgIds") List<Integer> orgIds,
            Pageable pageable
    );
}
