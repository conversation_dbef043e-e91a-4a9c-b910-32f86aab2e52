package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.PaymentApproval;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;


@Repository
public interface PaymentApprovalRepository extends JpaRepository<PaymentApproval,Long> {
    @Query(value = """
        SELECT 
            apiorder.ID AS primarykey,
            apiorder.batchno AS BatchNumber,
            CONCAT(u.firstname, ' ', u.lastname) AS User,
            pa.approval_status AS Action,
            pa.approval_level_name AS ApprovalLevel,
            apiorder.YEAR AS SupervisionYear,
            apiorder.MONTH AS SupervisionMonth,
            b.title AS BudgetLine,
            COALESCE((SELECT title FROM province p WHERE p.id = apiorder.province), apiorder.province) AS Region,
            COALESCE((SELECT title FROM county c WHERE c.id = apiorder.county), apiorder.district) AS County,
            apiorder.facility AS Facility,
            tm.title AS RequestType,
            apiorder.msisdn AS PhoneNo,
            CONCAT(apiorder.firstname, ' ', apiorder.middlename, ' ', apiorder.lastname) AS Beneficiary,
            CONVERT_TZ(apiorder.requesttime, '+00:00', '+03:00') AS RequestTime,
            apiorder.credit AS Amount,
            pa.approval_notes AS Comments
        FROM payment_approval pa
        LEFT JOIN apiorder ON pa.apiorder_id = apiorder.ID
        LEFT JOIN user u ON u.id = pa.approver_id
        LEFT JOIN ordertype ot ON apiorder.ordertype = ot.ID
        LEFT JOIN budget b ON apiorder.budget = b.ID
        LEFT JOIN treatment_model tm ON apiorder.treatment_model = tm.ID
        LEFT JOIN approval_levels al ON apiorder.approval_level = al.approval_level
        WHERE pa.approval_status IN ('Reversed Rejection', 'Reversed Approval')
          AND apiorder.inTrash = 'No'
          AND (:countyId IS NULL OR apiorder.county = :countyId)
        ORDER BY apiorder.ID DESC
    """, nativeQuery = true)
    Page<ReversedApprovalView> fetchReversedApprovals(@Param("countyId") Integer countyId, Pageable pageable);
    Optional<PaymentApproval> findTopByApiOrderIdOrderByIdDesc(Long apiOrderId);
}

