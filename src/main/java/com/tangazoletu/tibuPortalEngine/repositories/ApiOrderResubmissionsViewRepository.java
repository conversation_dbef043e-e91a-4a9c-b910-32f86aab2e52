package com.tangazoletu.tibuPortalEngine.repositories;


import com.tangazoletu.tibuPortalEngine.entities.ApiOrderResubmissionsView;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;


import java.awt.print.Pageable;
@Repository
public interface ApiOrderResubmissionsViewRepository extends JpaRepository<ApiOrderResubmissionsView, Integer> {

    @Query(value = "SELECT * FROM apiorder_resubmissions_view WHERE (:countyId IS NULL OR County = :countyId)",
            countQuery = "SELECT count(*) FROM apiorder_resubmissions_view WHERE (:countyId IS NULL OR County = :countyId)",
            nativeQuery = true)

    Page<ApiOrderResubmissionsView> fetchFilteredResubmissions(Integer countyId, org.springframework.data.domain.Pageable pageable);
}
