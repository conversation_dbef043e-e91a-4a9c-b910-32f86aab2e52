package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.BeneficiaryType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface BeneficiaryTypeRepo extends JpaRepository<BeneficiaryType, Long>, JpaSpecificationExecutor<BeneficiaryType> {
    @Query("SELECT e FROM BeneficiaryType e " +
            "WHERE e.inTrash = 'No' AND (" +
            ":keyword IS NULL OR :keyword = '' OR " +
            "LOWER(e.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "CAST(e.creationTime AS string) LIKE CONCAT('%', :keyword, '%')) " +
            "ORDER BY e.id ASC")
    Page<BeneficiaryType> findByName(String keyword, Pageable pageable);

    Page<BeneficiaryType> findById(Long id, Pageable pageable);
}
