package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.MonthlyOrderSummary;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

@Repository
public interface MonthlyOrderSummaryRepo extends JpaRepository<MonthlyOrderSummary, Long> {

    @Query("""
        SELECT m FROM MonthlyOrderSummary m
        WHERE (:province IS NULL OR m.province = :province)
          AND (:county IS NULL OR m.county = :county)
          AND (:budget IS NULL OR m.budget = :budget)
          AND (:batch IS NULL OR m.batchno = :batch)
          AND (:dateFrom IS NULL OR m.requesttime >= :dateFrom)
          AND (:dateTo IS NULL OR m.requesttime <= :dateTo)
        ORDER BY m.year DESC, m.month DESC
    """)
    Page<MonthlyOrderSummary> searchSummary(
            @Param("province") String province,
            @Param("county") String county,
            @Param("budget") Long budget,
            @Param("batch") String batch,
            @Param("dateFrom") LocalDateTime dateFrom,
            @Param("dateTo") LocalDateTime dateTo,
            Pageable pageable
    );
}
