package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.Budget;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Repository
public interface BudgetRepo extends JpaRepository<Budget, Long> {

    @Query(value = """
        SELECT 
            payment.ID AS primarykey,
            budget.title AS BudgetLine,
            DATE_FORMAT(trx_date, '%M') AS Month,
            DATE_FORMAT(trx_date,'%Y') AS Year,
            SUM(payment.credit_amount) AS AmountAllocated,
            SUM(IF(trx_type='Pay-out' AND trx_status='Completed', debit_amount, 0)) AS PaidOut,
            SUM(IF(trx_type='Pay-out' AND trx_status='Completed', (sending_charge + withdrawal_charge), 0)) AS Charges,
            county.title AS county,
            province.title AS province
        FROM budget
        LEFT JOIN payment ON payment.budget_id = budget.ID
        LEFT JOIN apiorder ON payment.order_id = apiorder.ID
        LEFT JOIN province ON province.id = apiorder.province
        LEFT JOIN county ON county.id = apiorder.county
        WHERE 1=1
          AND (:keyword IS NULL OR budget.title LIKE %:keyword% OR county.title LIKE %:keyword% OR province.title LIKE %:keyword%)
          AND (:provinceId IS NULL OR province.id = :provinceId)
          AND (:budgetIds IS NULL OR budget.id IN (:budgetIds))
          AND (:countyIds IS NULL OR county.id IN (:countyIds))
          AND (:month IS NULL OR DATE_FORMAT(payment.trx_date, '%M') = :month)
          AND (:year IS NULL OR DATE_FORMAT(payment.trx_date, '%Y') = :year)
          AND (:startDate IS NULL OR (trx_date >= :startDate OR TIME_COMPLETED >= :startDate))
          AND (:endDate IS NULL OR (trx_date <= :endDate OR TIME_COMPLETED <= :endDate))
        GROUP BY budget.id, YEAR(trx_date), MONTH(trx_date)
        """,
            countQuery = """
        SELECT COUNT(*) FROM (
            SELECT budget.id
            FROM budget
            LEFT JOIN payment ON payment.budget_id = budget.ID
            LEFT JOIN apiorder ON payment.order_id = apiorder.ID
            LEFT JOIN province ON province.id = apiorder.province
            LEFT JOIN county ON county.id = apiorder.county
            WHERE 1=1
              AND (:keyword IS NULL OR budget.title LIKE %:keyword% OR county.title LIKE %:keyword% OR province.title LIKE %:keyword%)
              AND (:provinceId IS NULL OR province.id = :provinceId)
              AND (:budgetIds IS NULL OR budget.id IN (:budgetIds))
              AND (:countyIds IS NULL OR county.id IN (:countyIds))
              AND (:month IS NULL OR DATE_FORMAT(payment.trx_date, '%M') = :month)
              AND (:year IS NULL OR DATE_FORMAT(payment.trx_date, '%Y') = :year)
              AND (:startDate IS NULL OR (trx_date >= :startDate OR TIME_COMPLETED >= :startDate))
              AND (:endDate IS NULL OR (trx_date <= :endDate OR TIME_COMPLETED <= :endDate))
            GROUP BY budget.id, YEAR(trx_date), MONTH(trx_date)
        ) temp
        """,
            nativeQuery = true
    )
    Page<Map<String, Object>> getBudgetPaymentSummary(
            @Param("keyword") String keyword,
            @Param("provinceId") Integer provinceId,
            @Param("budgetIds") List<Integer> budgetIds,
            @Param("countyIds") List<Integer> countyIds,
            @Param("month") String month,
            @Param("year") String year,
            @Param("startDate") Timestamp startDate,
            @Param("endDate") Timestamp endDate,
            Pageable pageable
    );

    Page<Budget> findTopById(Integer id, Pageable pageable);

    List<Budget> findAllByInTrashOrderByIdAsc(String inTrash);
}
