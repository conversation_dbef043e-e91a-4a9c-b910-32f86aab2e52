package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.IntraTransfers;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

@Repository
public interface IntraTransferRepo extends JpaRepository<IntraTransfers, Integer> {
    @Query("""
        SELECT v FROM IntraTransfers v
        WHERE (:keywords IS NULL OR 
              LOWER(v.beneficiary) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
              LOWER(v.contactNumber) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
              LOWER(v.trxId) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
              LOWER(v.paymentType) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
              LOWER(CAST(v.amount AS string)) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
              LOWER(v.county) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
              LOWER(v.province) LIKE LOWER(CONCAT('%', :keywords, '%')))
          AND (:province IS NULL OR v.province = :province)
          AND (:county IS NULL OR v.county = :county)
          AND (:paymentType IS NULL OR v.paymentType = :paymentType)
          AND (:dateFrom IS NULL OR v.date >= :dateFrom)
          AND (:dateTo IS NULL OR v.date <= :dateTo)
        ORDER BY v.primaryKey DESC
    """)
    Page<IntraTransfers> searchCompletedPayouts(
            @Param("keywords") String keywords,
            @Param("province") String province,
            @Param("county") String county,
            @Param("paymentType") String paymentType,
            @Param("dateFrom") LocalDateTime dateFrom,
            @Param("dateTo") LocalDateTime dateTo,
            Pageable pageable
    );
}
