package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.EventReport;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.sql.Date;

public interface ActivityRepository  extends JpaRepository<EventReport,Long> {
    @Query(
            value = """
        SELECT
            `primarykey`,
            `Organisation`,
            `Activity Code`,
            `Activity Name`,
            `Budget`,
            `venue`,
            `Start Date`,
            `End Date`,
            `Status`
        FROM event_report
        WHERE 1 = 1
          AND (`Organisation` = :organisation OR :organisation IS NULL)
          AND (`Budget` = :budget OR :budget IS NULL)
          AND (`Status` = :status OR :status IS NULL)
          AND (`venue` LIKE %:venue% OR :venue IS NULL)
          AND (:startDate IS NULL OR `Start Date` = :startDate)
          AND (:endDate IS NULL OR `End Date` = :endDate)
        ORDER BY `primarykey` DESC
        """,
            countQuery = """
        SELECT COUNT(*) FROM event_report
        WHERE 1 = 1
          AND (`Organisation` = :organisation OR :organisation IS NULL)
          AND (`Budget` = :budget OR :budget IS NULL)
          AND (`Status` = :status OR :status IS NULL)
          AND (`venue` LIKE %:venue% OR :venue IS NULL)
          AND (:startDate IS NULL OR `Start Date` = :startDate)
          AND (:endDate IS NULL OR `End Date` = :endDate)
        """,
            nativeQuery = true
    )
    Page<Object[]> fetchFilteredEvents(
            @Param("organisation") String organisation,
            @Param("budget") String budget,
            @Param("status") String status,
            @Param("venue") String venue,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            Pageable pageable
    );



}
