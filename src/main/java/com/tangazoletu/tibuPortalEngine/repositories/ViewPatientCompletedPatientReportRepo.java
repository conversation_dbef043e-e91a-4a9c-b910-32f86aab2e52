package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.ViewPatientCompletedPatientReport;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ViewPatientCompletedPatientReportRepo extends JpaRepository<ViewPatientCompletedPatientReport, Integer>, JpaSpecificationExecutor<ViewPatientCompletedPatientReport> {
    @Query("""
        SELECT v FROM ViewPatientCompletedPatientReport v
        WHERE (:keyword IS NULL OR :keyword = '' OR 
               LOWER(v.budgetLine) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
               LOWER(v.requestType) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
               LOWER(v.phoneNo) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
               LOWER(v.beneficiary) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
               LOWER(v.facility) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
               LOWER(v.batchno) LIKE LOWER(CONCAT('%', :keyword, '%')))
          AND (:province IS NULL OR v.region = :province)
          AND (:county IS NULL OR v.county = :county)
          AND (:batch IS NULL OR v.batchno = :batch)
          AND (:month IS NULL OR LOWER(v.month) = LOWER(:month))
          AND (:year IS NULL OR v.year = :year)
          AND (:approvalStatus IS NULL OR v.status = :approvalStatus)
          AND (:regionFilter IS NULL OR v.county = :regionFilter)
          AND (:phoneNumbers IS NULL OR v.phoneNo IN :phoneNumbers)
          AND (:dateFrom IS NULL OR v.requestTime >= :dateFrom)
          AND (:dateTo IS NULL OR v.requestTime <= :dateTo)
    """)
    Page<ViewPatientCompletedPatientReport> searchPendingOrders(
            @Param("keyword") String keyword,
            @Param("province") String province,
            @Param("county") String county,
            @Param("batch") String batch,
            @Param("month") String month,
            @Param("year") String year,
            @Param("approvalStatus") String approvalStatus,
            @Param("regionFilter") String regionFilter,
            @Param("phoneNumbers") List<String> phoneNumbers,
            @Param("dateFrom") LocalDateTime dateFrom,
            @Param("dateTo") LocalDateTime dateTo,
            Pageable pageable
    );
}
