package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.Financier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FinancierRepo extends JpaRepository<Financier, Long> {

    @Query(value = "SELECT e FROM Financier e " +
            "WHERE e.inTrash = 'No' " +
            "AND (:keyword IS NULL OR :keyword = '' " +
            "OR LOWER(e.title) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(e.code) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR STR(e.creationTime) LIKE CONCAT('%', :keyword, '%')) " +
            "ORDER BY e.id")
    Page<Financier> findByKeyword(@Param("keyword") String keyword, Pageable pageable);
    Page<Financier> findById(Integer id, Pageable pageable);
    List<Financier> findByInTrashOrderByIdAsc(Financier.TrashStatus status);

}
