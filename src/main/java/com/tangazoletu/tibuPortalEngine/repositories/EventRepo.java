package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.dto.EventDetails;
import com.tangazoletu.tibuPortalEngine.entities.Event;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Repository
public interface EventRepo extends JpaRepository<Event, Long> {

    Page<Event> findByIdAndInTrash(Long id, String inTrash, Pageable pageable);
    @Query(value = "select e from Event e where e.meetingCode = :meetingCode or e.meetingCode = :mcode")
    Optional<Event> findByMeetingCode(@Param("meetingCode") String meetingCode, @Param("mcode") String mcode);
    @Query(value = "select e from Event e where e.meetingCode = :meetingCode")
    Optional<Event> findTopByMeetingCode(String meetingCode);
    Optional<Event> findFirstByMeetingCode(String meetingCode);
    Page<Event> findAll(Pageable pageable);
    @Query(value = "SELECT event_name, venue, start_date, end_date, " +
            "DATEDIFF(CURRENT_DATE(), start_date) AS day, " +
            "DATEDIFF(end_date, start_date) AS noOfDays, " +
            "(SELECT COUNT(id) FROM event_codes WHERE meeting_code = :meetingCode) AS codeCount " +
            "FROM event WHERE meeting_code = :meetingCode",
            nativeQuery = true)
    List<Object[]> findEventDetailsForMeetingCode(@Param("meetingCode") String meetingCode);

}
