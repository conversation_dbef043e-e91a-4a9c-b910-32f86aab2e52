package com.tangazoletu.tibuPortalEngine.repositories;
import com.tangazoletu.tibuPortalEngine.entities.StatementView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface StatementViewRepository extends JpaRepository<StatementView, Long>, JpaSpecificationExecutor<StatementView> {

    @Query("""
        SELECT s FROM StatementView s
        WHERE (:orgIds IS NULL OR s.orgId IN :orgIds)
          AND (
              :keyword IS NULL OR :keyword = '' OR
              LOWER(s.budgetLine) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
              LOWER(s.region) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
              LOWER(s.county) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
              LOWER(s.transactionId) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
              LOWER(s.transactionDetails) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
              STR(s.amountSent) LIKE CONCAT('%', :keyword, '%') OR
              STR(s.dateTime) LIKE CONCAT('%', :keyword, '%')
          )
        ORDER BY s.id DESC
    """)
    Page<StatementView> findFilteredStatements(
            @Param("orgIds") java.util.List<Integer> orgIds,
            @Param("keyword") String keyword,
            Pageable pageable
    );
}

