package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.EventSummaryView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface EventSummaryViewRepo extends JpaRepository<EventSummaryView, Long>, JpaSpecificationExecutor<EventSummaryView> {
    @Query("""
        SELECT e FROM EventSummaryView e
        WHERE (:keywords IS NULL OR
               LOWER(e.activityCode) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
               LOWER(e.activityName) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
               LOWER(e.venue) LIKE LOWER(CONCAT('%', :keywords, '%')))
          AND (:budgetIds IS NULL OR e.budgetId IN :budgetIds)
          AND (:countyIds IS NULL OR e.countyId IN :countyIds)
          AND (
               (:meetingStatus IS NULL) OR
               (:meetingStatus = 0 AND (e.status = 0 OR e.endDate < CURRENT_DATE)) OR
               (:meetingStatus = 1 AND (e.status = 1 AND e.endDate >= CURRENT_DATE))
          )
          AND (:dateFrom IS NULL OR e.startDate >= :dateFrom)
          AND (:dateTo IS NULL OR e.endDate <= :dateTo)
        ORDER BY e.id DESC
    """)
    Page<EventSummaryView> filterEvents(
            @Param("keywords") String keywords,
            @Param("budgetIds") List<Long> budgetIds,
            @Param("countyIds") List<Long> countyIds,
            @Param("meetingStatus") Integer meetingStatus,
            @Param("dateFrom") LocalDateTime dateFrom,
            @Param("dateTo") LocalDateTime dateTo,
            Pageable pageable
    );
}
