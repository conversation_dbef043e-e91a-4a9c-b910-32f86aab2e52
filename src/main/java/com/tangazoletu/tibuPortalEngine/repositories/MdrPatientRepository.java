package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.MdrPatient;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

@Repository
public interface MdrPatientRepository extends JpaRepository<MdrPatient, Integer>, JpaSpecificationExecutor<MdrPatient> {
    @Query("""
        SELECT p FROM MdrPatient p
        WHERE
            (:keyword IS NULL OR :keyword = '' OR
                LOWER(p.firstname) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                LOWER(p.lastname) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                LOWER(p.nominee) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                LOWER(p.facility) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
                STR(p.creationTime) LIKE CONCAT('%', :keyword, '%')
            )
        AND (:county IS NULL OR p.county.id = :county)
        AND (:beneficiaryType IS NULL OR p.beneficiaryType = :beneficiaryType)
        AND (:dateFrom IS NULL OR p.creationTime >= :dateFrom)
        AND (:dateTo IS NULL OR p.creationTime <= :dateTo)
        AND (:confirmed IS NULL OR p.confirmed = :confirmed)
        ORDER BY p.id DESC
        """)
    Page<MdrPatient> searchPatients(
            @Param("keyword") String keyword,
            @Param("county") Integer county,
            @Param("beneficiaryType") Integer beneficiaryType,
            @Param("dateFrom") LocalDateTime dateFrom,
            @Param("dateTo") LocalDateTime dateTo,
            @Param("confirmed") Integer confirmed,
            Pageable pageable
    );


}
