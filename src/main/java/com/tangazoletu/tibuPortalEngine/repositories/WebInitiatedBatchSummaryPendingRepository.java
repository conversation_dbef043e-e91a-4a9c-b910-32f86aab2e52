package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.WebInitiatedBatchSummaryPending;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

@Repository
public interface WebInitiatedBatchSummaryPendingRepository extends JpaRepository<WebInitiatedBatchSummaryPending, Long>, JpaSpecificationExecutor<WebInitiatedBatchSummaryPending> {
    @Query("""
        SELECT w FROM WebInitiatedBatchSummaryPending w
        WHERE (:search IS NULL OR LOWER(w.batchNo) LIKE LOWER(CONCAT('%', :search, '%')) 
                             OR LOWER(w.budgetLine) LIKE LOWER(CONCAT('%', :search, '%')) 
                             OR LOWER(w.requestType) LIKE LOWER(CONCAT('%', :search, '%')))
          AND (:dateFrom IS NULL OR w.requestTime >= :dateFrom)
          AND (:dateTo IS NULL OR w.requestTime <= :dateTo)
        ORDER BY w.requestTime DESC
    """)
    Page<WebInitiatedBatchSummaryPending> findByFilters(
            @Param("search") String search,
            @Param("dateFrom") LocalDateTime dateFrom,
            @Param("dateTo") LocalDateTime dateTo,
            Pageable pageable
    );

}
