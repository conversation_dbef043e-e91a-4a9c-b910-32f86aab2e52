package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.ApprovalProcessing;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import org.springframework.data.domain.Pageable;


@Repository
public interface ApprovalProcessingRepo  extends JpaRepository<ApprovalProcessing, Integer> {
    @Query(value = """
    SELECT 
        ap.ID AS `ID`,
        ap.date_created AS `Date Created`,
        al.approval_level_name AS `Approval Level`,
        (LENGTH(REPLACE(ap.ids_string,'all,','')) - LENGTH(REPLACE(REPLACE(ap.ids_string,'all,',''), ',', '')))+1 AS `Total Records`,
        ap.TIME_INITIATED,
        ap.TIME_COMPLETED,
        (CASE 
            WHEN ap.status = '0' THEN 'Awaiting Processing'
            WHEN ap.status = '1' THEN 'Processing'
            WHEN ap.status = '4' THEN 'Completed'
            ELSE 'Abnormal'
        END) AS `Status`,
        u.login AS `Approved By`
    FROM approval_processing ap
    LEFT JOIN approval_levels al ON ap.approval_level = al.approval_level
    LEFT JOIN user u ON u.id = ap.approver_id
    WHERE (:orgId IS NULL OR ap.org_id = :orgId)
    ORDER BY ap.ID DESC
""",
            countQuery = """
    SELECT COUNT(*) 
    FROM approval_processing ap
    LEFT JOIN approval_levels al ON ap.approval_level = al.approval_level
    LEFT JOIN user u ON u.id = ap.approver_id
    WHERE (:orgId IS NULL OR ap.org_id = :orgId)
""",
            nativeQuery = true)
    Page<Object[]> fetchApiBuliApprovals(@Param("orgId") Integer orgId, Pageable pageable);


}
