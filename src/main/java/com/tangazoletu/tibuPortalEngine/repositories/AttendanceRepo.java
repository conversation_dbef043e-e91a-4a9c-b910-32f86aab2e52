package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.Attendance;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Repository
public interface AttendanceRepo extends JpaRepository<Attendance, Long> {

    @Query(
            value = """
        SELECT t.meeting_code, 
               SUM(t.unprocessed), 
               SUM(t.processed), 
               SUM(t.approved), 
               SUM(t.rejected), 
               SUM(t.individual_amount), 
               SUM(t.total)
        FROM (
            SELECT meeting_code,
                   SUM(CASE WHEN processing_status = 0 THEN 1 ELSE 0 END) AS unprocessed,
                   SUM(CASE WHEN processing_status = 1 THEN 1 ELSE 0 END) AS processed,
                   SUM(CASE WHEN processing_status = 4 THEN 1 ELSE 0 END) AS approved,
                   SUM(CASE WHEN processing_status = 3 THEN 1 ELSE 0 END) AS rejected,
                   payable_amount_1 AS individual_amount,
                   COUNT(*) AS total
            FROM attendance
            WHERE meeting_code != ''
              AND (:from IS NULL OR requestTime >= :from)
              AND (:to IS NULL OR requestTime <= :to)
              AND (
                    :keyword IS NULL 
                    OR LOWER(:keyword) = 'unprocess' AND processing_status = 0
                    OR LOWER(:keyword) = 'process' AND processing_status = 1
                    OR LOWER(:keyword) = 'approve' AND processing_status = 4
                    OR LOWER(:keyword) = 'rejected' AND processing_status = 3
                    OR meeting_code LIKE %:keyword%
              )
            GROUP BY meeting_code, msisdn
        ) t
        GROUP BY t.meeting_code
        ORDER BY t.meeting_code DESC
        """,
            countQuery = """
        SELECT COUNT(*) FROM (
            SELECT 1
            FROM attendance
            WHERE meeting_code != ''
              AND (:from IS NULL OR requestTime >= :from)
              AND (:to IS NULL OR requestTime <= :to)
              AND (
                    :keyword IS NULL 
                    OR LOWER(:keyword) = 'unprocessed' AND processing_status = 0
                    OR LOWER(:keyword) = 'processed' AND processing_status = 1
                    OR LOWER(:keyword) = 'approved' AND processing_status = 4
                    OR LOWER(:keyword) = 'rejected' AND processing_status = 3
                    OR meeting_code LIKE %:keyword%
              )
            GROUP BY meeting_code, msisdn
        ) t
        """,
            nativeQuery = true
    )
    Page<Object[]> findCodeVerificationReport(@Param("budget") String budget,
                                              @Param("from") Timestamp from,
                                              @Param("to") Timestamp to,
                                              @Param("keyword") String keyword,
                                              Pageable pageable);



    @Query(value = """
    SELECT 
        SUM(CASE WHEN processing_status = 0 THEN 1 ELSE 0 END) AS processingStage,
        SUM(CASE WHEN processing_status = 1 THEN 1 ELSE 0 END) AS approvalStage
    FROM attendance 
    WHERE meeting_code = :meetingCode
    """, nativeQuery = true)
    Object[] getAttendanceStatusCounts(@Param("meetingCode") String meetingCode);

    @Query("SELECT COUNT(a) FROM Attendance a WHERE a.processingStatus != 0 AND a.meetingCode = :meetingCode AND a.recipientId = :recipientId")
    int countByMeetingCodeAndRecipient(String meetingCode, Long recipientId);

    @Query("SELECT COUNT(a) FROM Attendance a WHERE DATE(a.requestTime) = :date AND a.msisdn = :msisdn AND a.verificationStatus = 'Approved' AND a.processingStatus = 0 AND a.meetingCode = :meetingCode")
    int countApprovedForDay(@Param("date") LocalDate date,
                            @Param("msisdn") String msisdn,
                            @Param("meetingCode") String meetingCode);

    @Query("SELECT COUNT(a) FROM Attendance a WHERE a.msisdn = :msisdn AND a.verificationStatus = 'Approved' AND a.processingStatus = 0 AND a.meetingCode = :meetingCode")
    int countTotalApproved(@Param("msisdn") String msisdn,
                           @Param("meetingCode") String meetingCode);
    @Query(value = """
    SELECT 
        CONCAT(r.firstname, ' ', r.middlename, ' ', r.lastname) AS name,
        cnt.title AS county,
        dist.title AS station,
        pd.designation AS designation,
        jg.title AS jobGroup,
        a.msisdn AS telephoneNumber,
        c.designationid AS designationId,
        r.id AS recipientId,
        a.id AS attendanceId
    FROM attendance a
    JOIN recipient r ON a.recipient_id = r.id
    JOIN recipient c ON a.recipient_id = c.id
    JOIN county cnt ON c.countyid = cnt.id
    JOIN district dist ON c.subcountyid = dist.id
    JOIN perdiem pd ON c.designationid = pd.id
    JOIN jobgroup jg ON c.jobgroupid = jg.id
    WHERE a.meeting_code = :meetingCode AND a.processing_status = 0
    GROUP BY a.msisdn
""", nativeQuery = true)
    List<Object[]> fetchAttendanceRecipients(@Param("meetingCode") String meetingCode);


    @Query("SELECT COUNT(a) FROM Attendance a WHERE a.meetingCode = :meetingCode AND a.recipientId = :recipientId AND a.processingStatus = :status")
    int countByMeetingCodeAndRecipientAndProcessingStatus(String meetingCode, Long recipientId, int status);

    @Query("SELECT COUNT(a) FROM Attendance a WHERE a.msisdn = :msisdn AND a.meetingCode = :meetingCode AND a.processingStatus = :status AND FUNCTION('DATE', a.requestTime) = :day AND a.verificationStatus = 'Approved'")
    int countApprovedByDayAndProcessingStatus(LocalDate day, String msisdn, String meetingCode, int status);

    @Query("SELECT COUNT(a) FROM Attendance a WHERE a.msisdn = :msisdn AND a.meetingCode = :meetingCode AND a.processingStatus = :status AND a.verificationStatus = 'Approved'")
    int countTotalApprovedByProcessingStatus(String msisdn, String meetingCode, int status);

    @Query("SELECT SUM(a.payableAmount1) FROM Attendance a WHERE a.meetingCode = :meetingCode AND a.processingStatus = :status")
    BigDecimal sumTotalPayableAmountByMeetingCodeAndStatus(String meetingCode, int status);
    // AttendanceRepository.java
    @Query(value = """
    SELECT R.id, C.countyTitle, C.subcountyTitle, C.designation, C.jobGroup,
           R.msisdn, C.designationId, R.recipientId, R.id
      FROM Attendance R
     INNER JOIN Recipient C ON R.recipientId = C.id
     WHERE R.meetingCode = :code AND R.processingStatus = 1
     GROUP BY R.msisdn
""", nativeQuery = true)
    List<Object[]> fetchRecipientsForMode6(@Param("code") String meetingCode);

    @Query(value = """
    SELECT COUNT(*) 
      FROM attendance 
     WHERE DATE(requesttime) = :day 
       AND MSISDN = :msisdn 
       AND meeting_code = :code 
       AND processing_status = :status
""", nativeQuery = true)
    int countByDayAndStatus(@Param("day") LocalDate day,
                            @Param("msisdn") String msisdn,
                            @Param("code") String meetingCode,
                            @Param("status") int status);

    @Query(value = """
    SELECT SUM(payable_amount_1)
      FROM attendance
     WHERE meeting_code = :code AND processing_status = :status
""", nativeQuery = true)
    BigDecimal sumPayableAmount(@Param("code") String meetingCode,
                                @Param("status") int status);
    @Query(value = """
        SELECT
          C.firstname || ' ' || C.middlename || ' ' || C.lastname AS name,
          CNT.title, DIST.title, PD.designation, PD.jobGroup,
          R.msisdn, C.designationId, R.recipientId, R.id,
          /* dynamic daily counts */
          :#{#countsSql} AS daily_counts,
          SUM(TSD.amount) AS amount,
          SUM(CASE WHEN TSD.title='Lunch' THEN TSD.credit ELSE 0 END) AS less_lunch,
          SUM(CASE WHEN TSD.title='Transport/Fuel' THEN TSD.credit ELSE 0 END) AS transport,
          SUM(CASE WHEN TSD.title='Extra Per Diem' THEN TSD.credit ELSE 0 END) AS extra_per_diem,
          SUM(CASE WHEN TSD.title='Others' THEN TSD.credit ELSE 0 END) AS others,
          SUM(R.payableAmount) AS net_pay,
          CASE WHEN R.processingStatus='0' THEN 'Pending Processing'
               WHEN R.processingStatus='1' THEN 'Processed Pending Approval'
               WHEN R.processingStatus='3' THEN 'Rejected'
               WHEN R.processingStatus='4' THEN 'Processed and Approved' END AS status,
          R.processingComments, R.approvalComments,
          GROUP_CONCAT(R.id) AS IDs
        FROM attendance R
        JOIN recipient C ON R.recipientId = C.id
        LEFT JOIN county CNT ON C.countyId = CNT.id
        LEFT JOIN district DIST ON C.subcountyId = DIST.id
        LEFT JOIN perdiem PD ON C.designationId = PD.id
        LEFT JOIN temp_supervision_details TSD ON TSD.apiorderId = R.id
        WHERE R.meetingCode = :code
        GROUP BY R.msisdn
    """, nativeQuery = true)
    List<Object[]> fetchRecipientsWithCountsForMode7(
            @Param("code") String meetingCode,
            @Param("countsSql") String countsSql
    );




        @Query(value = """
        SELECT
          CONCAT(C.firstname,' ',C.middlename,' ',C.lastname), CNT.title, DIST.title, PD.designation, PD.jobGroup,
          R.msisdn, C.designationId, R.recipientId, R.id,
          /* dynamic per-day counts */
          :#{#countsCol} ,
          SUM(TSD.credit) FILTER (WHERE TSD.title='Per Diem'), 
          SUM(TSD.credit) FILTER (WHERE TSD.title='Lunch'),
          SUM(TSD.credit) FILTER (WHERE TSD.title='Transport/Fuel'),
          SUM(TSD.credit) FILTER (WHERE TSD.title='Extra Per Diem'),
          SUM(TSD.credit) FILTER (WHERE TSD.title='Others'),
          SUM(R.payableAmount1), 
          CASE R.processingStatus
            WHEN '0' THEN 'Pending Processing'
            WHEN '1' THEN 'Processed Pending Approval'
            WHEN '3' THEN 'Rejected'
            WHEN '4' THEN 'Processed and Approved'
            ELSE 'Unknown' END,
          R.processingComments, R.approvalComments,
          GROUP_CONCAT(R.id)
        FROM attendance R
        JOIN recipient C ON R.recipientId = C.id
        LEFT JOIN county CNT ON C.countyId = CNT.id
        LEFT JOIN district DIST ON C.subcountyId = DIST.id
        LEFT JOIN perdiem PD ON C.designationId = PD.id
        LEFT JOIN temp_supervision_details TSD ON TSD.apiorderId = R.id
        WHERE R.meetingCode = :code
        GROUP BY R.msisdn
    """, nativeQuery = true)
        List<Object[]> fetchMode7Summary(
                @Param("code") String meetingCode,
                @Param("countsCol") String countsCol,
                Pageable pageable);

        @Query(value = """
        SELECT SUM(R.payable_amount_1)
        FROM attendance R
        WHERE R.meeting_code = :code
    """, nativeQuery = true)
        BigDecimal sumTotalNetPay(@Param("code") String meetingCode);


    @Query(value = """
    SELECT 
      CONCAT(C.firstname, ' ', C.middlename, ' ', C.lastname) AS Name,
      cnt.title AS NameofCounty,
      dist.title AS Station,
      per.designation AS Designation,
      jg.title AS JobGroup,
      R.MSISDN AS TelephoneNumber,
      CASE WHEN :meetingType = 'DayOnly' THEN per.lunchDinnerRate 
           WHEN :meetingType = 'HalfBoard' THEN per.dinnerRate 
           ELSE per.amount END AS Rate,
      SUM(CASE WHEN tsd.title = 'Per Diem' THEN tsd.credit ELSE 0 END) AS Amount,
      SUM(CASE WHEN tsd.title = 'Lunch' THEN tsd.credit ELSE 0 END) AS LessLunch,
      SUM(CASE WHEN tsd.title = 'Transport/Fuel' THEN tsd.credit ELSE 0 END) AS Transport,
      SUM(CASE WHEN tsd.title = 'Extra Per Diem' THEN tsd.credit ELSE 0 END) AS ExtraPerDiem,
      SUM(CASE WHEN tsd.title = 'Others' THEN tsd.credit ELSE 0 END) AS Others,
      R.payable_amount_1 AS NetPay,
      'YES' AS Approve,
      '' AS Comments,
      GROUP_CONCAT(R.id) AS ID
    FROM attendance R
    JOIN recipient C ON R.recipient_id = C.ID
    JOIN county cnt ON C.countyid = cnt.id
    JOIN district dist ON C.subcountyid = dist.id
    JOIN perdiem per ON C.designationid = per.id
    JOIN jobgroup jg ON C.jobgroupid = jg.id
    LEFT JOIN temp_supervision_details tsd 
      ON tsd.apiorder_id = R.id AND tsd.approval_status = 'Approved'
    WHERE R.meeting_code = :meetingCode
      AND R.processing_status = 1
    GROUP BY R.MSISDN
    """,
            nativeQuery = true)
    List<Object[]> fetchRecipientsForMode6(
            @Param("meetingCode") String meetingCode,
            @Param("meetingType") String meetingType
    );

    @Query(value = """
        SELECT COALESCE(SUM(payable_amount_1), 0)
        FROM attendance
        WHERE meeting_code = :meetingCode
          AND processing_status = 1
        """, nativeQuery = true)
    BigDecimal sumPayableNetForMode6(@Param("meetingCode") String meetingCode);



}

