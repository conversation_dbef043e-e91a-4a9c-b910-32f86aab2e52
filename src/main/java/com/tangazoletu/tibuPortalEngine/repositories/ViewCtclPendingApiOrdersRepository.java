package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.ViewCtclPendingApiOrders;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

@Repository
public interface ViewCtclPendingApiOrdersRepository extends JpaRepository<ViewCtclPendingApiOrders, Long>, JpaSpecificationExecutor<ViewCtclPendingApiOrders> {
    @Query("""
    SELECT v FROM ViewCtclPendingApiOrders v
    ORDER BY v.requestTime DESC
""")
    Page<ViewCtclPendingApiOrders> findFilteredPendingOrders(
            @Param("search") String search,
            @Param("region") String region,
            @Param("county") String county,
            @Param("dateFrom") LocalDateTime dateFrom,
            @Param("dateTo") LocalDateTime dateTo,
            @Param("orgId") Long orgId,
            Pageable pageable
    );



}
