package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.AllApiPayments;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

@Repository
public interface AllApiPaymentsRepo extends JpaRepository<AllApiPayments, Integer> {

    @Query("""
        SELECT v FROM AllApiPayments v
        WHERE (:keywords IS NULL OR
              LOWER(v.beneficiary) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
              LOWER(v.batchNo) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
              LOWER(v.contactNumber) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
              LOWER(v.paymentType) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
              LOWER(v.mpesaId) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
              LOWER(v.trxDesc) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
              LOWER(v.region) LIKE LOWER(CONCAT('%', :keywords, '%')) OR
              LOWER(v.countyId) LIKE LOWER(CONCAT('%', :keywords, '%')))
          AND (:province IS NULL OR v.region = :province)
          AND (:county IS NULL OR v.countyId = :county)
          AND (:paymentType IS NULL OR v.paymentType = :paymentType)
          AND (:dateFrom IS NULL OR v.date >= :dateFrom)
          AND (:dateTo IS NULL OR v.date <= :dateTo)
        ORDER BY v.id DESC
    """)
    Page<AllApiPayments> filterReport(
            @Param("keywords") String keywords,
            @Param("province") String province,
            @Param("county") String county,
            @Param("paymentType") String paymentType,
            @Param("dateFrom") LocalDateTime dateFrom,
            @Param("dateTo") LocalDateTime dateTo,
            Pageable pageable
    );
}
