package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.PendingLevel2OrderView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface PendingLevel2OrderViewRepository extends JpaRepository<PendingLevel2OrderView, Long> {

    @Query("""
        SELECT p FROM PendingLevel2OrderView p
        WHERE (:county IS NULL OR p.county = :county)
                   AND (
            :keyword IS NULL OR :keyword = '' OR
            LOWER(p.beneficiary) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(p.phoneNo) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(p.budgetLine) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(p.region) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(p.requestType) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(p.initiator) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(p.dotName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
            LOWER(p.dotPhone) LIKE LOWER(CONCAT('%', :keyword, '%'))
        )
    """)
    Page<PendingLevel2OrderView> searchByKeywordAndCounty(@Param("keyword") String keyword, @Param("county") String county, Pageable pageable);
}
