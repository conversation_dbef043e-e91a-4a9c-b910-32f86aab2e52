package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.BatchReferenceSummary;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface BatchReferenceSummaryRepository extends JpaRepository<BatchReferenceSummary, String> {

    @Query("""
        SELECT b FROM BatchReferenceSummary b
        WHERE (:keyword IS NULL OR LOWER(b.batchNo) LIKE LOWER(CONCAT('%', :keyword, '%')))
          AND (:orgId IS NULL OR b.orgId = :orgId)
        ORDER BY b.requestTime DESC
    """)
    Page<BatchReferenceSummary> searchByKeywordAndOrgId(
            @Param("keyword") String keyword,
            @Param("orgId") Long orgId,
            Pageable pageable
    );
}
