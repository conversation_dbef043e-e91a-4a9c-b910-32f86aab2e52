package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.PendingApprovedReport;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PendingApprovedReportRepository extends JpaRepository<PendingApprovedReport, Long> {

    @Query(value = """
        SELECT r FROM PendingApprovedReport r
        WHERE (:approvalLevel IS NULL OR r.approvalLevel = :approvalLevel)
          AND (
                :keyword IS NULL
                OR LOWER(r.approvalStatus) LIKE LOWER(CONCAT('%', :keyword, '%'))
                OR LOWER(r.county) LIKE LOWER(CONCAT('%', :keyword, '%'))
                OR LOWER(r.region) LIKE LOWER(CONCAT('%', :keyword, '%'))
                OR LOWER(r.beneficiary) LIKE LOWER(CONCAT('%', :keyword, '%'))
                OR LOWER(r.phoneNo) LIKE LOWER(CONCAT('%', :keyword, '%'))
                OR LOWER(r.requestType) LIKE LOWER(CONCAT('%', :keyword, '%'))
                OR LOWER(r.batchNumber) LIKE LOWER(CONCAT('%', :keyword, '%'))
              )
        """)
    Page<PendingApprovedReport> fetchPendingApprovedReportsFiltered(
            @Param("approvalLevel") Integer approvalLevel,
            @Param("keyword") String keyword,
            Pageable pageable
    );

}
