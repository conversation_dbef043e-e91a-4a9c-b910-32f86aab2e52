package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.EventCodesLatest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface EventCodesLatestRepo extends JpaRepository<EventCodesLatest, Long> {
    @Query(value = "SELECT columnOne AS Codes, columnTwo AS Codes, columnThree AS Codes, columnFour AS Codes FROM event_codes_latest WHERE meetingCode = :meetingCode ORDER BY id ASC", nativeQuery = true)
    Page<Map<String, Object>> findLatestCodes(@Param("meetingCode") String meetingCode, Pageable pageable);

    @Query(value = "SELECT DISTINCT day FROM event_codes_latest WHERE meetingCode = :meetingCode AND day != '1'", nativeQuery = true)
    List<Integer> getDistinctDaysForLatestCodes(@Param("meetingCode") String meetingCode);

    @Query(value = "SELECT columnFour, columnThree, columnTwo, columnOne, day, meetingCode FROM event_codes_latest WHERE meetingCode = :meetingCode AND day = :day ORDER BY day ASC", nativeQuery = true)
    List<Object[]> getLatestEventCodesForDay(String meetingCode, String day);

    @Query(value = "SELECT columnOne AS Codes, columnTwo AS Codes, columnThree AS Codes, columnFour AS Codes FROM event_codes_latest ORDER BY id ASC", nativeQuery = true)
    Page<Map<String, Object>> findAllLatestCodes(Pageable pageable);

}
