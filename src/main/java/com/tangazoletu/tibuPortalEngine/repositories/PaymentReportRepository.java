package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.dto.ApiResponse;
import com.tangazoletu.tibuPortalEngine.dto.PaymentApprovalReportDTO;
import com.tangazoletu.tibuPortalEngine.entities.PaymentReportEntity;
import com.tangazoletu.tibuPortalEngine.entities.ApiOrder;
import com.tangazoletu.tibuPortalEngine.entities.ApiOrderReport;
import com.tangazoletu.tibuPortalEngine.entities.Payment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PaymentReportRepository  extends JpaRepository<PaymentReportEntity, Long> {
    @Query(value = """
SELECT 
    ao.ID AS `primarykey`,
    ao.batchno AS `Batch Number`,
    ao.requestTime AS `Request Time`,
    CONCAT(ao.firstname, ' ', ao.middlename, ' ', ao.lastname) AS `Beneficiary`,
    ao.msisdn AS `Phone No.`,
    tm.title AS `Request Type`,
    b.title AS `Budget Line`,
    COALESCE(p.title, ao.province) AS `Region`,
    COALESCE(c.title, ao.county) AS `County`,
    COALESCE(d.title, ao.district) AS `District`,
    ao.facility AS `Facility`,
    ao.credit AS `Amount`,
    ao.dot_nurse_name AS `Dot Name`,
    ao.dot_nurse_phoneno AS `DOT Phone`,
    ao.recipient2credit AS `DOT Amount`,
    ao.approval_level AS `Approval Leverl`,
    ao.approval_status AS `Approval Status`,
    ao.requestTime AS `Date`,
    ao.notes AS `Comment`
FROM apiorder ao
LEFT JOIN ordertype ot ON ao.ordertype = ot.ID
LEFT JOIN budget b ON ao.budget = b.ID
LEFT JOIN treatment_model tm ON ao.treatment_model = tm.ID
LEFT JOIN province p ON ao.province = p.id
LEFT JOIN county c ON ao.county = c.id
LEFT JOIN district d ON ao.district = d.id
WHERE ao.approval_level = :approval_level
  AND ao.approval_status = 'Pending'     
  AND ao.inTrash = 'No'
ORDER BY ao.ID DESC
""", nativeQuery = true)
    Page<Object[]> fetchPendingApprovedReportsFilteredByOrg(
            @Param("approval_level") int approvalLevel,
            Pageable pageable
    );


    @Query(value = """
    SELECT 
        pa.ID,
        pa.approval_level,
        pa.approval_level_name,
        pa.approval_status,
        pa.approved_amount,
        (SELECT CONCAT(u.firstname, ' ', u.lastname) FROM user u WHERE u.id = pa.approver_id),
        pa.approval_time,
        pa.approval_notes,
        (
            SELECT 
                CASE 
                    WHEN ao.initiator_username IS NOT NULL THEN ao.initiator_username
                    ELSE (SELECT CONCAT(u.firstname, ' ', u.lastname) FROM user u WHERE u.id = ao.initiator_id)
                END
            FROM apiorder ao
            WHERE ao.id = pa.apiorder_id
        )
    FROM payment_approval pa
    WHERE pa.apiorder_id = :apiorderId
      AND pa.approval_status = 'Approved'
    ORDER BY pa.ID DESC
    LIMIT 1
""", nativeQuery = true)
    Object[] findLatestApprovedRaw(@Param("apiorderId") Long apiorderId);



    @Query(value = """
SELECT 
    ao.ID AS `primarykey`,
    ao.batchno AS `Batch Number`,
    ao.requestTime AS `Request Time`,
    CONCAT(ao.firstname, ' ', ao.middlename, ' ', ao.lastname) AS `Beneficiary`,
    ao.msisdn AS `Phone No.`,
    tm.title AS `Request Type`,
    b.title AS `Budget Line`,
    COALESCE(p.title, ao.province) AS `Region`,
    COALESCE(c.title, ao.county) AS `County`,
    COALESCE(d.title, ao.district) AS `District`,
    ao.facility AS `Facility`,
    ao.credit AS `Amount`,
    ao.dot_nurse_name AS `Dot Name`,
    ao.dot_nurse_phoneno AS `DOT Phone`,
    ao.recipient2credit AS `DOT Amount`,
    ao.notes,
    ot.title AS `Order Type`, 
    sd.title AS `Supervision Title`,
    sd.credit AS `Supervision Credit`,
    sd.url AS `Supervision URL`,

    prd.recipient,
    prd.credit AS `PRD Credit`,
    prd.msisdn AS `PRD MSISDN`,
    prd.onbehalfrecipient,
    prd.recipient2credit AS `PRD Recipient2 Credit`,
    prd.recipient2msisdn,
    prd.recipient2,
    prd.notes AS `PRD Notes`,
    prd.ordertype AS `PRD Order Type`,
    
    pa.id AS `paId`,    
    pa.approval_level_name,
    pa.approval_status,
    pa.approved_amount,
    pa.approval_time,
    pa.approval_notes,
    CONCAT(u.firstname, ' ', u.lastname) AS approver,
    CASE 
        WHEN pa.approver_id IS NOT NULL THEN pa.approver_id 
        ELSE CONCAT(ui.firstname, ' ', ui.lastname) 
    END AS initiator,
    pa.phone_number AS `Approval contact`

FROM apiorder ao
LEFT JOIN ordertype ot ON ao.ordertype = ot.ID
LEFT JOIN budget b ON ao.budget = b.ID
LEFT JOIN treatment_model tm ON ao.treatment_model = tm.ID
LEFT JOIN province p ON ao.province = p.id
LEFT JOIN county c ON ao.county = c.id
LEFT JOIN district d ON ao.district = d.id
LEFT JOIN supervision_details sd ON sd.apiorder_id = ao.ID
LEFT JOIN paymentrequestdetails prd ON prd.treatment_model = ao.treatment_model
            LEFT JOIN payment_approval pa ON pa.apiorder_id = ao.ID
            AND (:approval_status IS NULL OR LOWER(pa.approval_status) = LOWER(:approval_status))            
            
LEFT JOIN `user` u ON pa.approver_id = u.id
LEFT JOIN `user` ui ON ao.initiator_id = ui.id

WHERE  ao.inTrash = 'No'

ORDER BY ao.ID DESC
""", nativeQuery = true)
    Page<Object[]> fetchApprovalReportsFilteredByStatus(
            @Param("approval_status") String approvalStatus,
            Pageable pageable
    );
    @Query("""
        SELECT p FROM Payment p
        WHERE p.trxType = 'Reversal'
          AND p.trxStatus = 'Completed'
          AND (
              :keyword IS NULL OR 
              LOWER(p.recipientName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
              LOWER(p.msisdn) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
              STR(p.creditAmount) LIKE CONCAT('%', :keyword, '%') OR
              STR(p.completionDate) LIKE CONCAT('%', :keyword, '%')
          )
        ORDER BY p.id DESC
    """)
    Page<Payment> findReversalsByKeyword(@Param("keyword") String keyword, Pageable pageable);



    @Query(value = """
        SELECT 
            apiorder.ID AS primarykey,
            apiorder.batchno AS BatchNumber,
            CONCAT(u.firstname, ' ', u.lastname) AS User,
            pa.approval_status AS Action,
            pa.approval_level_name AS ApprovalLevel,
            apiorder.YEAR AS SupervisionYear,
            apiorder.MONTH AS SupervisionMonth,
            b.title AS BudgetLine,
            COALESCE((SELECT title FROM province p WHERE p.id = apiorder.province), apiorder.province) AS Region,
            COALESCE((SELECT title FROM county c WHERE c.id = apiorder.county), apiorder.district) AS County,
            apiorder.facility AS Facility,
            tm.title AS RequestType,
            apiorder.msisdn AS PhoneNo,
            CONCAT(apiorder.firstname, ' ', apiorder.middlename, ' ', apiorder.lastname) AS Beneficiary,
            CONVERT_TZ(apiorder.requesttime, '+00:00', '+03:00') AS RequestTime,
            apiorder.credit AS Amount,
            pa.approval_notes AS Comments
        FROM payment_approval pa
        LEFT JOIN apiorder ON pa.apiorder_id = apiorder.ID
        LEFT JOIN user u ON u.id = pa.approver_id
        LEFT JOIN ordertype ot ON apiorder.ordertype = ot.ID
        LEFT JOIN budget b ON apiorder.budget = b.ID
        LEFT JOIN treatment_model tm ON apiorder.treatment_model = tm.ID
        LEFT JOIN approval_levels al ON apiorder.approval_level = al.approval_level
        WHERE pa.approval_status IN ('Reversed Rejection', 'Reversed Approval')
          AND apiorder.inTrash = 'No'
          AND (:countyId IS NULL OR apiorder.county = :countyId)
        ORDER BY apiorder.ID DESC
        """,
            countQuery = """
        SELECT COUNT(*)
        FROM payment_approval pa
        LEFT JOIN apiorder ON pa.apiorder_id = apiorder.ID
        WHERE pa.approval_status IN ('Reversed Rejection', 'Reversed Approval')
          AND apiorder.inTrash = 'No'
          AND (:countyId IS NULL OR apiorder.county = :countyId)
        """,
            nativeQuery = true
    )
    Page<ReversedApprovalView> fetchReversedApprovals(@Param("countyId") Integer countyId, Pageable pageable);





}