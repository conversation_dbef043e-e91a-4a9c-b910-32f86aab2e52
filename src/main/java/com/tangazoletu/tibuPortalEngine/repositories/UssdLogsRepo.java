package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.UssdLogs;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDateTime;
import java.util.Date;

@Repository
public interface UssdLogsRepo extends JpaRepository<UssdLogs, Integer> {

    @Query("""
    SELECT e FROM UssdLogs e 
    WHERE (:dateFrom IS NULL OR e.dateCreated >= :dateFrom)
      AND (:dateTo IS NULL OR e.dateCreated <= :dateTo)
      AND (:keyword IS NULL OR :keyword = '' OR LOWER(e.msisdn) LIKE LOWER(CONCAT('%', :keyword, '%')) 
           OR LOWER(e.stage) LIKE LOWER(CONCAT('%', :keyword, '%')))
    ORDER BY e.id DESC
""")
    Page<UssdLogs> findFilteredLogs(
            @Param("keyword") String keyword,
            @Param("dateFrom") Date dateFrom,
            @Param("dateTo") Date dateTo,
            Pageable pageable
    );
}
