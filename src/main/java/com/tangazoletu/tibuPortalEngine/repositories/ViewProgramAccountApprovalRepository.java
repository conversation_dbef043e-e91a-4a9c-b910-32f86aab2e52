package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.ViewProgramAccountApproval;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface ViewProgramAccountApprovalRepository extends JpaRepository<ViewProgramAccountApproval, Long>, JpaSpecificationExecutor<ViewProgramAccountApproval> {
    // Optional: Add filtering methods if needed
}
