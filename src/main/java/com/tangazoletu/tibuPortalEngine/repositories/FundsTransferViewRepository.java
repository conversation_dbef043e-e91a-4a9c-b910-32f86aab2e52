package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.FundsTransferView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FundsTransferViewRepository extends JpaRepository<FundsTransferView, Long> , JpaSpecificationExecutor<FundsTransferView> {

    @Query("""
        SELECT f FROM FundsTransferView f
        WHERE (:orgIds IS NULL OR f.orgId IN :orgIds)
          AND (
              :keyword IS NULL OR :keyword = '' OR
              LOWER(f.transactionType) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
              LOWER(f.details) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
              LOWER(f.receiptNumber) LIKE LOWER(CONCAT('%', :keyword, '%')) OR
              STR(f.debitMmfAc) LIKE CONCAT('%', :keyword, '%') OR
              STR(f.creditMmfAc) LIKE CONCAT('%', :keyword, '%') OR
              STR(f.transactionDate) LIKE CONCAT('%', :keyword, '%')
          )
        ORDER BY f.transactionDate DESC
    """)
    Page<FundsTransferView> findFundsTransferData(
            @Param("orgIds") List<Integer> orgIds,
            @Param("keyword") String keyword,
            Pageable pageable
    );
}
