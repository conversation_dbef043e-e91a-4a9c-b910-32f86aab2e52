package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.Mode6RecipientView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

public interface Mode6RecipientViewRepository extends JpaRepository<Mode6RecipientView, String> {
    Page<Mode6RecipientView> findByMeetingCode(String meetingCode, Pageable pageable);
}

