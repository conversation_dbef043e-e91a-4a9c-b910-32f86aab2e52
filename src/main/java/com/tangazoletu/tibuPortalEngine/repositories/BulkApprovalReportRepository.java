package com.tangazoletu.tibuPortalEngine.repositories;

import com.tangazoletu.tibuPortalEngine.entities.BulkApprovalReport;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface BulkApprovalReportRepository extends JpaRepository<BulkApprovalReport, Long> {

    @Query(value = """
        SELECT b FROM BulkApprovalReport b
        WHERE (:orgId IS NULL OR b.orgId = :orgId)
        ORDER BY b.id DESC
        """)
    Page<BulkApprovalReport> fetchApiBulkApprovals(@Param("orgId") Integer orgId, Pageable pageable);
}
