/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tangazoletu.tibuPortalEngine.repositories;


import com.google.gson.Gson;
import com.tangazoletu.tibuPortalEngine.dto.QueryDto;
import jakarta.annotation.PostConstruct;
import jakarta.persistence.*;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.*;
import org.hibernate.exception.ConstraintViolationException;
import org.hibernate.query.Query;
import org.hibernate.transform.AliasToEntityMapResultTransformer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

@Service
@Slf4j
@RequiredArgsConstructor
public class CrudService {
    private final EntityManagerFactory entityManagerFactory;
    @Getter
    private SessionFactory sessionFactory;

    /**
     * Default query results page when none is specified.
     */
    public final int DEFAULT_PAGE_SIZE = 1000000;

    @PersistenceContext
    private EntityManager entityManager;

    @PostConstruct
    protected void init() {
        log.info("Initializing crud service...");
        sessionFactory = entityManagerFactory.unwrap(SessionFactory.class);
        
        log.info("Crud service initialized. SessionFactory properties {}", entityManagerFactory.getProperties());
    }



    public Long executeCountQuery(String query, Map<String, Object> params) {
        try {
            Query nativeQuery = (Query) entityManager.createNativeQuery(query);
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                nativeQuery.setParameter(entry.getKey(), entry.getValue());
            }

            Object result = nativeQuery.getSingleResult();
            if (result instanceof Number) {
                return ((Number) result).longValue();
            } else {
                return 0L;
            }
        } catch (Exception e) {
            log.error("Error executing count query: {}", e.getMessage(), e);
            return 0L;
        }
    }

    public <T> T executeNativeQueryForSingleResult(String queryStr, Map<String, Object> params, Class<T> resultType) {
        Query query = (Query) entityManager.createNativeQuery(queryStr);

        for (Map.Entry<String, Object> entry : params.entrySet()) {
            query.setParameter(entry.getKey(), entry.getValue());
        }

        Object result = query.getSingleResult();

        if (resultType == Integer.class) {
            return resultType.cast(((Number) result).intValue());
        } else if (resultType == Long.class) {
            return resultType.cast(((Number) result).longValue());
        } else if (resultType == Double.class) {
            return resultType.cast(((Number) result).doubleValue());
        } else if (resultType.isInstance(result)) {
            return resultType.cast(result);
        }

        throw new IllegalArgumentException("Unsupported result type: " + resultType);
    }

    public Map<String, Object> fetchRecordById(String tableName, String whereClause) {
        try {
            String queryStr = "SELECT * FROM " + tableName + " WHERE " + whereClause + " FETCH FIRST 1 ROWS ONLY";

            // Unwrap to Hibernate Query
            Query<Object[]> hibernateQuery = entityManager
                    .createNativeQuery(queryStr)
                    .unwrap(Query.class);

            List<Object[]> resultList = hibernateQuery.getResultList();
            if (resultList.isEmpty()) return Collections.emptyMap();

            List<String> columnNames = getColumnNames(tableName);
            Object[] row = resultList.get(0);

            Map<String, Object> result = new HashMap<>();
            for (int i = 0; i < columnNames.size(); i++) {
                result.put(columnNames.get(i), row[i]);
            }

            return result;
        } catch (Exception e) {
            log.error("Failed to fetch record from {} with condition {}: {}", tableName, whereClause, e.getMessage());
            return Collections.emptyMap();
        }
    }
    private List<String> getColumnNames(String tableName) {
        String sql = "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = :tableName AND TABLE_SCHEMA = DATABASE() ORDER BY ORDINAL_POSITION";
        return entityManager.createNativeQuery(sql)
                .setParameter("tableName", tableName)
                .getResultList();
    }

    public List<?> runQuery(String sql, Map<String, Object> params) {
        try {
            jakarta.persistence.Query query = entityManager.createNativeQuery(sql);
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                query.setParameter(entry.getKey(), entry.getValue());
            }
            return query.getResultList();
        } catch (Exception e) {
            log.error("Failed to run native query [{}] with params [{}]: {}", sql, params, e.getMessage(), e);
            return Collections.emptyList();
        }
    }
    @Transactional
    public long fetchCount(String sql, Map<String, Object> params) {
        try (Session session = sessionFactory.openSession()) {
            org.hibernate.query.Query<?> query = session.createNativeQuery(sql);
            params.forEach(query::setParameter);

            Object result = query.getSingleResult();
            if (result instanceof Number) {
                return ((Number) result).longValue();
            } else {
                throw new IllegalStateException("Expected numeric result but got: " + result);
            }
        } catch (Exception e) {
            log.error("Error running fetchCount for SQL: {}, params: {}, error: {}", sql, params, e.getMessage(), e);
            throw e;
        }
    }

    public <T> T findEntity(Serializable primaryKey, Class<T> clazz) throws HibernateException {
        try (Session session = sessionFactory.openSession()) {
            IdentifierLoadAccess loadAccess = session.byId(clazz);
            return (T) loadAccess.load(primaryKey);
        } catch (HibernateException e) {
            log.error("Failed to find entity {} by id {}. {}", clazz, primaryKey, e.getMessage());
            throw e;
        }
    }

    public <T> List<T> fetchWithNamedQuery(String queryName, Map<String, Object> params) {
        return fetchWithNamedQuery(queryName, params, 0, DEFAULT_PAGE_SIZE);
    }

    public <T> List<T> fetchWithHibernateQuery(String query, Map<String, Object> params) throws HibernateException {
        return fetchWithHibernateQuery(query, params, 0, DEFAULT_PAGE_SIZE);
    }

    public <T> T fetchWithHibernateQuerySingleResult(String query, Map<String, Object> params) throws HibernateException {
        List<T> results = fetchWithHibernateQuery(query, params, 0, 1);
        return results.isEmpty() ? null : results.iterator().next();
    }


    public <T> List<T> fetchWithHibernateQuery(String query, Map<String, Object> params, int start, int end) throws HibernateException {
        log.debug("Executing Hibernate={}, start={},end={} params=[{}]", query, start, end, params);
        try (Session session = sessionFactory.openSession()) {
            Query q = session.createQuery(query);
            params.entrySet().forEach((param) -> {
                if (param.getValue() instanceof Collection) {
                    q.setParameterList(param.getKey(), (Collection) param.getValue());
                } else {
                    q.setParameter(param.getKey(), param.getValue());
                }
            });
            start = start < 0 ? 0 : start;
            if (end >= start && start >= 0) {
                q.setFirstResult(start);
                q.setMaxResults(end - start);
            }
            return q.list();
        } catch (HibernateException e) {
            log.error("Failed in executing hibernate query {} with params [{}]. {}.", query, params, e.getMessage());
            throw e;
        }
    }

    public <T> Object fetchWithHibernateQuerySingleResult(String query, Map<String, Object> params, int start, int end) throws HibernateException {
        log.debug("Executing Hibernate={}, start={},end={} params=[{}]", query, start, end, params);
        try (Session session = sessionFactory.openSession()) {

            Query q = session.createQuery(query);
            params.entrySet().forEach((param) -> {
                if (param.getValue() instanceof Collection) {
                    q.setParameterList(param.getKey(), (Collection) param.getValue());
                } else {
                    q.setParameter(param.getKey(), param.getValue());
                }
            });

            return q.getFirstResult();
        } catch (HibernateException e) {
            log.error("Failed in executing hibernate query {} with params [{}]. {}.", query, params, e.getMessage());
            throw e;
        }
    }

    public int executeHibernateQuery(String queryString, Map<String, Object> params) throws HibernateException {
        log.debug("Executing Hibernate={}, params=[{}]", queryString, params);
        Transaction tx = null;
        try (Session session = sessionFactory.openSession()) {
            tx = session.beginTransaction();
            Query q = session.createQuery(queryString);
            params.entrySet().forEach((param) -> {
                if (param.getValue() instanceof Collection) {
                    q.setParameterList(param.getKey(), (Collection) param.getValue());
                } else {
                    q.setParameter(param.getKey(), param.getValue());
                }
            });

            int executeUpdate = q.executeUpdate();
            tx.commit();
            return executeUpdate;
        } catch (Exception e) {
            log.error("Failed in executing hibernate query {} with params [{}]. {}.", queryString, params, e.getMessage());
            if (tx != null) {
                tx.rollback();
            }
            throw e;
        }
    }

    public int executeNativeQuery(String queryString, Map<String, Object> params) throws HibernateException {
        log.debug("Executing Hibernate={}, params=[{}]", queryString, params);
        Transaction tx = null;
        try (Session session = sessionFactory.openSession()) {
            tx = session.beginTransaction();
            Query q = session.createNativeQuery(queryString);
            params.entrySet().forEach((param) -> {
                if (param.getValue() instanceof Collection) {
                    q.setParameterList(param.getKey(), (Collection) param.getValue());
                } else {
                    q.setParameter(param.getKey(), param.getValue());
                }
            });

            int executeUpdate = q.executeUpdate();
            tx.commit();
            return executeUpdate;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Failed in executing hibernate query {} with params [{}]. {}.", queryString, params, e.getMessage());
            if (tx != null && tx.isActive()) {
                tx.rollback();
            }
            throw e;
        }
    }


    /**
     * Function to execute multiple queries. If one query fails all the executed queries are rolled back.
     * @param queries Queries to be executed
     * @return True: All queries were executed successfully, False: Error occurred on execution.
     * @throws HibernateException
     */
    public boolean executeNativeQueries(@NotEmpty List<QueryDto> queries) throws HibernateException {
        log.debug("Executing multiple queries: {}", new Gson().toJson(queries));
        Transaction tx = null;

        try (Session session = sessionFactory.openSession()) {
            tx = session.beginTransaction();

            for (QueryDto queryDto : queries) {
                String query = queryDto.getQuery();
                Map<String, Object> params = queryDto.getParams();

                Query q = session.createNativeQuery(query);
                params.forEach((key, value) -> {
                    if (value instanceof Collection) q.setParameterList(key, (Collection) value);
                    else q.setParameter(key, value);
                });

                q.executeUpdate();
            }

            tx.commit(); //Commit the transaction after all the queries have been executed

            return true;
        } catch (Exception e) {
            log.error("Failed in executing hibernate query. Error {}.", e.getMessage());
            e.printStackTrace();
            if (tx != null && tx.isActive()) tx.rollback();
            return false;
        }
    }


    /**
     * @param <T>
     * @param query
     * @param start
     * @param end
     * @return
     * @throws HibernateException
     */
    public <T> List<T> fetchWithNativeQuery(String query, Map<String, Object> params, int start, int end) throws HibernateException {
        log.debug("Executing nativeQuery={}, start={},end={} params=[{}]", query, start, end, params);
        try (Session session = sessionFactory.openSession()) {
            Query q = session.createNativeQuery(query);
            params.entrySet().forEach((param) -> {
                if (param.getValue() instanceof Collection) {
                    q.setParameterList(param.getKey(), (Collection) param.getValue());
                } else {
                    q.setParameter(param.getKey(), param.getValue());
                }
            });
            start = start < 0 ? 0 : start;
            if (end >= start && start >= 0) {
                q.setFirstResult(start);
                q.setMaxResults(end - start);
            }
            return q.list();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Failed in executing hibernate query {} with params [{}]. {}.", query, e.getMessage());
            throw e;
        }
    }

    public <T> List<T> fetchWithNativeQuery(String query) throws HibernateException {
        log.debug("Executing nativeQuery={}, start={},end={}", query);
        try (Session session = sessionFactory.openSession()) {
            Query q = session.createNativeQuery(query);

            return q.list();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Failed in executing hibernate query {} with params [{}]. {}.", query, e.getMessage());
            throw e;
        }
    }

    public <T> List<T> fetchWithNamedQuery(String queryName, Map<String, Object> params, int start, int end) {
        log.debug("Executing NamedQuery={}, start={},end={} params=[{}]", queryName, start, end, params);
        try (Session session = sessionFactory.openSession()) {
            Query query = session.getNamedQuery(queryName);
            params.entrySet().forEach((param) -> {
                if (param.getValue() instanceof Collection) {
                    query.setParameterList(param.getKey(), (Collection) param.getValue());
                } else {
                    query.setParameter(param.getKey(), param.getValue());
                }
            });
            start = start < 0 ? 0 : start;
            if (end >= start && start >= 0) {
                query.setFirstResult(start);
                query.setMaxResults(end - start);
            }
            return query.list();
        } catch (HibernateException e) {
            log.error("Failed in executing named query {} with params [{}]. {}.", queryName, params, e.getMessage());
            throw e;
        }
    }

    public <T> void saveOrUpdate(T entity) throws HibernateException {
        log.debug("Perist or merge {}", entity);
        Transaction tx = null;
        try (Session session = sessionFactory.openSession()) {
            tx = session.getTransaction();
            tx.begin();
            session.saveOrUpdate(entity);
            tx.commit();
        } catch (Exception e) {
            log.error("Failed to persist or merge {}. {}", entity, e.getMessage());
            if (tx != null) {
                tx.rollback();
            }
            throw e;
        }
    }

    public <T> void saveOrUpdate(List<T> entities) throws HibernateException {
        log.debug("Perist or merge {} entities.", entities.size());
        Transaction tx = null;
        try (Session session = sessionFactory.openSession()) {
            tx = session.getTransaction();
            tx.begin();
            entities.forEach((entity) -> {
                session.saveOrUpdate(entity);
            });
            tx.commit();
        } catch (Exception e) {
            log.error("Failed to persist or merge {} entities. {}", entities.size(), e.getMessage(), e);
            if (tx != null) {
                tx.rollback();
            }
            throw e;
        }
    }

    public <T> Object save(T entity) throws HibernateException {
        log.debug("Perist or merge {}", entity);
        Transaction tx = null;
        Object savedEntity = null;
        try (Session session = sessionFactory.openSession()) {
            tx = session.getTransaction();
            tx.begin();
            savedEntity = session.save(entity);
            tx.commit();
        } catch (ConstraintViolationException e) {
            log.error("Constraint validation failed {}. {}", entity, e.getMessage(), e);
            if (tx != null) {
                tx.rollback();
            }
            throw e;
        } catch (RollbackException e) {
            log.error("RollbackException validation failed {}. {}", entity, e.getMessage(), e);
            if (tx != null) {
                tx.rollback();
            }
            throw e;
        } catch (Exception e) {
            log.error("Failed to persist or merge {}. {}", entity, e.getMessage(), e);
            if (tx != null) {
                tx.rollback();
            }
            throw e;
        }

        return savedEntity;
    }

    public <T> void remove(T entity) throws HibernateException {
        log.debug("Deleting {}", entity);
        Session session = null;
        Transaction tx = null;
        try {
            session = sessionFactory.openSession();
            tx = session.getTransaction();
            tx.begin();
            session.delete(entity);
            tx.commit();
        } catch (Exception e) {
            log.error("Failed to delete {}. {}", entity, e.getMessage());
            if (tx != null) {
                tx.rollback();
            }
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }

    public void persist(Object entity) {
        log.debug("Persisting {}", entity);
        Transaction tx = null;
        try (Session session = sessionFactory.openSession()) {
            tx = session.beginTransaction();
            session.save(entity);
            tx.commit();
        } catch (Exception e) {
            log.error("Failed to persist {}. {}", entity, e.getMessage(), e);
            if (tx != null) {
                tx.rollback();
            }
            throw new HibernateException("Failed to persist entity", e);
        }
    }
    public <T> List<T> findEntitiesByAttribute(Class<T> entityClass, String attributeName, Object attributeValue) {
        try (Session session = sessionFactory.openSession()) {
            CriteriaBuilder criteriaBuilder = session.getCriteriaBuilder();
            CriteriaQuery<T> criteriaQuery = criteriaBuilder.createQuery(entityClass);
            Root<T> root = criteriaQuery.from(entityClass);
            criteriaQuery.select(root).where(criteriaBuilder.equal(root.get(attributeName), attributeValue));
            Query<T> query = session.createQuery(criteriaQuery);
            return query.getResultList();
        } catch (HibernateException e) {
            log.error("Failed to find entities of type {} by attribute {}. {}", entityClass.getSimpleName(), attributeName, e.getMessage());
            throw e;
        }
    }
    public Map<String, Object> getSingleResult(String sql, Map<String, Object> params) {
        try {
            Query query = (Query) entityManager.createNativeQuery(sql);

            // Bind parameters
            if (params != null) {
                params.forEach(query::setParameter);
            }

            query.unwrap(org.hibernate.query.NativeQuery.class)
                    .setResultTransformer(AliasToEntityMapResultTransformer.INSTANCE);

            List<?> resultList = query.getResultList();

            if (resultList != null && !resultList.isEmpty()) {
                return (Map<String, Object>) resultList.get(0);
            } else {
                return null;
            }
        } catch (NoResultException e) {
            log.warn("No result found for query: {}", sql);
            return null;
        } catch (Exception e) {
            log.error("Error executing getSingleResult query: {}", sql, e);
            throw e;
        }
    }
    public List<Map<String, Object>> fetchWithNativeQueryAsMap(String sql, Map<String, Object> params, int offset, int limit) {
        jakarta.persistence.Query nativeQuery = entityManager.createNativeQuery(sql);
        params.forEach(nativeQuery::setParameter);
        nativeQuery.setFirstResult(offset);
        nativeQuery.setMaxResults(limit);

        org.hibernate.query.NativeQuery<Map<String, Object>> hibernateQuery = nativeQuery.unwrap(org.hibernate.query.NativeQuery.class);
        hibernateQuery.setResultTransformer(AliasToEntityMapResultTransformer.INSTANCE);

        return hibernateQuery.getResultList();
    }



}
