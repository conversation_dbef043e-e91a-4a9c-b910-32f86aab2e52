package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.Entity;
import jakarta.persistence.*;
import jakarta.persistence.Table;
import lombok.Data;

import java.time.LocalDateTime;

@Entity
@Table(name = "mpesa_resubmission")
@Data
public class MpesaResubmissionView {

    @Id
    @Column(name = "primaryKey")
    private Long primaryKey;

    @Column(name = "region")
    private String region;

    @Column(name = "county")
    private String county;

    @Column(name = "beneficiary")
    private String beneficiary;

    @Column(name = "contactNumber")
    private String contactNumber;

    @Column(name = "trxId")
    private String trxId;

    @Column(name = "date")
    private LocalDateTime date;

    @Column(name = "paymentType")
    private String paymentType;

    @Column(name = "mpesaId")
    private String mpesaId;

    @Column(name = "trxDesc")
    private String trxDesc;

    @Column(name = "timeInitiated")
    private LocalDateTime timeInitiated;

    @Column(name = "timeCompleted")
    private LocalDateTime timeCompleted;

    @Column(name = "sendingCharges")
    private Double sendingCharges;

    @Column(name = "withdrawalCharges")
    private Double withdrawalCharges;

    @Column(name = "amount")
    private Double amount;

    @Column(name = "runningBalance")
    private Double runningBalance;

    // Getters & Setters
}
