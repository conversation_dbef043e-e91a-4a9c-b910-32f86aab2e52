package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.*;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

@Entity
@Table(name = "view_apiorder_approval_summary")
@Data
public class ApiOrderApprovalSummaryView {

    @Id
    @Column(name = "primaryKey")
    private Long primaryKey;

    @Column(name = "batchNumber")
    private String batchNumber;

    @Column(name = "month")
    private String month;

    @Column(name = "year")
    private String year;

    @Column(name = "requestTime")
    private LocalDateTime requestTime;

    @Column(name = "supervisorName")
    private String supervisorName;

    @Column(name = "phoneNo")
    private String phoneNo;

    @Column(name = "requestType")
    private String requestType;

    @Column(name = "beneficiary")
    private String beneficiary;

    @Column(name = "budgetLine")
    private String budgetLine;

    @Column(name = "region")
    private String region;

    @Column(name = "county")
    private String county;

    @Column(name = "dotAmount")
    private Double dotAmount;

    @Column(name = "driver")
    private String driver;

    @Column(name = "driverAmount")
    private Double driverAmount;

    @Column(name = "approvalStatus")
    private String approvalStatus;

    @Column(name = "amount")
    private Double amount;

    @Column(name = "confirmable")
    private String confirmable;
}
