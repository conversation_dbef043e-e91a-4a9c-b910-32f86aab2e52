package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.*;
import lombok.Data;

@Entity
@Data
@Table(name = "attendance_recipient_view")
public class AttendanceRecipientView {

    @Id
    @Column(name = "attendanceId")
    private Long attendanceId;

    private String name;
    private String county;
    private String station;
    private String designation;
    private String jobGroup;

    @Column(name = "telephoneNumber")
    private String telephoneNumber;

    private Long designationId;
    private Long recipientId;

    @Column(name = "meetingCode")
    private String meetingCode;
}
