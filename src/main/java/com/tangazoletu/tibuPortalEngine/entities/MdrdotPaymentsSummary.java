package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Table(name = "view_mdrdot_paymentssummary")
@Data
public class MdrdotPaymentsSummary {
    @Id
    @Column(name = "primarykey")
    private Long id;

    private String dotName;

    @Column(name = "dotNursePhoneNumber")
    private String dotNursePhoneNumber;

    private String year;

    private Double jan;
    private Double feb;
    private Double mar;
    private Double apr;
    private Double may;
    private Double jun;
    private Double jul;
    private Double aug;
    private Double sep;
    private Double oct;
    private Double nov;
    private Double dece;

    private Double total;
}
