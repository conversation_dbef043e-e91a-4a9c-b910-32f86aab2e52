package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.*;
import lombok.Data;

@Entity
@Data
@Table(name = "bulk_approvals_view")
public class BulkApprovalReport {

    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "date_created")
    private String dateCreated;

    @Column(name = "approval_level")
    private String approvalLevel;

    @Column(name = "total_records")
    private Integer totalRecords;

    @Column(name = "time_initiated")
    private String timeInitiated;

    @Column(name = "time_completed")
    private String timeCompleted;

    @Column(name = "status")
    private String status;

    @Column(name = "approved_by")
    private String approvedBy;

    @Column(name = "org_id")
    private Integer orgId;
}
