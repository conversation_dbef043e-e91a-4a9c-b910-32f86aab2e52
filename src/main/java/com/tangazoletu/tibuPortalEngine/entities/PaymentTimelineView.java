package com.tangazoletu.tibuPortalEngine.entities;

import lombok.Data;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@Entity
@Table(name = "payment_timeline")
public class PaymentTimelineView {
    @Id
    @Column(name = "id")
    private Integer id;  // Now each row is uniquely identified
    @Column(name = "apiorder_id")
    private Integer apiorderId;

    @Column(name = "orderType")
    private Integer orderType;

    @Column(name = "order_type_title")
    private String orderTypeTitle;

    @Column(name = "level")
    private Integer level;

    @Column(name = "approval_level_name")
    private String approvalLevelName;

    @Column(name = "status_position")
    private String statusPosition;

    @Column(name = "approval_status")
    private String approvalStatus;

    @Column(name = "approval_notes")
    private String approvalNotes;

    @Column(name = "approval_time")
    private Timestamp approvalTime;

    @Column(name = "phone_number")
    private String phoneNumber;

    @Column(name = "approved_amount")
    private BigDecimal approvedAmount;
}
