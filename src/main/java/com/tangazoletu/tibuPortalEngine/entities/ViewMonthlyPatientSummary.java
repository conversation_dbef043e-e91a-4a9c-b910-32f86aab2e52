package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.Immutable;

import java.io.Serializable;
import java.time.LocalDateTime;

@Entity
@Table(name = "view_monthly_patient_summary")
@Data
@Immutable
public class ViewMonthlyPatientSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "primarykey")
    private Long id;

    @Column(name = "org_id")
    private Integer orgId;

    private String county;

    @Column(name = "request_time")
    private LocalDateTime requestTime;

    private String patient;

    @Column(name = "patient_registration_number")
    private String patientRegistrationNumber;

    @Column(name = "telephonenumber")
    private String telephoneNumber;

    @Column(name = "date_treatment_started")
    private String dateTreatmentStarted;

    @Column(name = "approval_status")
    private String approvalStatus;

    private String year;

    private Double jan;
    private Double feb;
    private Double mar;
    private Double apr;
    private Double may;
    private Double jun;
    private Double jul;
    private Double aug;
    private Double sep;
    private Double oct;
    private Double nov;

    @Column(name = "dece")
    private Double dec;

    private Double total;



}
