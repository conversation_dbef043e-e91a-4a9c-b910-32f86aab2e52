package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "view_supervision_payments")
@Getter
@Setter
public class ViewSupervisionPayments {
    @Id
    @Column(name = "initiator_id")
    private String initiatorId;

    @Column(name = "sctlc_name")
    private String sctlcName;

    @Column(name = "supervisions_done")
    private Integer supervisionsDone;

    @Column(name = "unpaid_amt")
    private Double unpaidAmt;

    @Column(name = "paid_amt")
    private Double paidAmt;

    @Column(name = "rejected_amt")
    private Double rejectedAmt;
}
