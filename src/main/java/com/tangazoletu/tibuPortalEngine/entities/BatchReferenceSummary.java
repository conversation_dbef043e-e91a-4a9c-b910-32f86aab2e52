package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.time.LocalDateTime;

@Entity
@Table(name = "batchreference_summary")
@Data
public class BatchReferenceSummary {

    @Id
    @Column(name = "primaryKey")
    private String primaryKey;

    @Column(name = "batchno")
    private String batchNo;

    @Column(name = "total")
    private Long total;

    @Column(name = "region")
    private String region;

    @Column(name = "budgetLine")
    private String budgetLine;

    @Column(name = "paymentType")
    private String paymentType;

    @Column(name = "requestTime")
    private LocalDateTime requestTime;

    @Column(name = "orgId")
    private Long orgId;

    // Getters and Setters
}
