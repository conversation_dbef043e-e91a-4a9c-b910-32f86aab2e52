package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "view_api_order_community_based")
@Data
public class ApiOrderCommunityView {

    @Id
    @Column(name = "primaryKey")
    private Long id;

    private String batchno;

    private String approvalLevel;

    private String month;

    private String year;

    private String budgetLine;

    private String region;

    private String county;

    private String facility;

    private String requestType;

    private String phoneNo;

    private String status;

    private String beneficiary;

    private String initiator;

    private LocalDateTime requestTime;

    private String patientNumber;

    private String dateTreatmentStarted;

    private String dotName;

    private String dotPhone;

    private BigDecimal dotAmount;

    private BigDecimal driverAmount;

    private BigDecimal amount;
}
