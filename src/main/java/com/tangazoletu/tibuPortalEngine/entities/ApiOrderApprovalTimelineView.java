package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

@Entity
@Table(name = "api_order_approval_timeline_view")
@Data
public class ApiOrderApprovalTimelineView {

    @Id
    @Column(name = "id")
    private Long id;
    @Column(name = "Organisation")
    private Integer organisation;
    @Transient
    private String orgName;
    private LocalDateTime requestTime;


    private String ordertype;
    private String province;
    private String county;

    @Column(name = "level_1")
    private Integer level1;
    @Column(name = "level_2")
    private Integer level2;
    @Column(name = "level_3")
    private Integer level3;
    @Column(name = "level_4")
    private Integer level4;
    @Column(name = "level_5")
    private Integer level5;
    @Column(name = "level_6")
    private Integer level6;
    @Column(name = "level_7")
    private Integer level7;
    @Column(name = "level_8")
    private Integer level8;
    @Column(name = "level_9")
    private Integer level9;
    @Column(name = "level_10")
    private Integer level10;

}
