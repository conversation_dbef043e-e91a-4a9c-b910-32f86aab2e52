package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Immutable;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Immutable
@Table(name = "view_finance_officer_approval")
public class ViewFinanceOfficerApproval {

    @Id
    @Column(name = "primarykey")
    private Long id;

    @Column(name = "batch_number")
    private String batchNumber;

    @Column(name = "budget_line")
    private String budgetLine;

    @Column(name = "request_type")
    private String requestType;

    @Column(name = "phone_no")
    private String phoneNumber;

    @Column(name = "beneficiary")
    private String beneficiary;

    @Column(name = "region")
    private String region;

    @Column(name = "county")
    private String county;

    @Column(name = "sub_county")
    private String subCounty;

    @Column(name = "facility")
    private String facility;

    @Column(name = "request_time")
    private LocalDateTime requestTime;

    @Column(name = "amount")
    private BigDecimal amount;

    @Column(name = "dot_name")
    private String dotName;

    @Column(name = "dot_phone")
    private String dotPhone;

    @Column(name = "dot_amount")
    private BigDecimal dotAmount;
}
