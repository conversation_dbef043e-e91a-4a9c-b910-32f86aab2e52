package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Immutable;

import java.time.LocalDateTime;

@Entity
@Data
@Immutable
@Table(name = "reserved_mpesa_payments")
public class ReservedMpesaPayment {

    @Id
    @Column(name = "primarykey")
    private Long id;

    @Column(name = "transaction_date")
    private LocalDateTime transactionDate;

    @Column(name = "phone_number")
    private String phoneNumber;

    @Column(name = "name")
    private String name;

    @Column(name = "transaction_type")
    private String transactionType;

    @Column(name = "notes")
    private String notes;

    @Column(name = "amount")
    private Double amount;
}
