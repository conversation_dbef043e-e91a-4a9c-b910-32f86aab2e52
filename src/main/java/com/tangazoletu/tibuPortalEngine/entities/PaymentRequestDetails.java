package com.tangazoletu.tibuPortalEngine.entities;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Immutable;
import jakarta.persistence.*;

import java.io.Serializable;

@Entity
@Immutable  // Optional if you use Hibernate and want to mark it as read-only
@Table(name = "paymentrequestdetails")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaymentRequestDetails implements Serializable {
    @Id
    @Column(name = "ID")
    private Long id;

    @Column(name = "recipient")
    private String recipient;

    @Column(name = "credit")
    private Double credit;

    @Column(name = "recipient2credit")
    private Double recipient2Credit;

    @Column(name = "msisdn")
    private String msisdn;

    @Column(name = "patient_reg_no")
    private String patientRegNo;

    @Column(name = "recipient2msisdn")
    private String recipient2Msisdn;

    @Column(name = "onbehalfrecipient")
    private String onBehalfRecipient;

    @Column(name = "recipient2")
    private String recipient2;

    @Column(name = "notes")
    private String notes;

    @Column(name = "ordertype")
    private String orderType;

    @Column(name = "treatment_model")
    private Integer treatmentModel;
}
