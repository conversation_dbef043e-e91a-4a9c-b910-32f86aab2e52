package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "completed_payment_view") // You can define this view in DB
@Data
public class CompletedPaymentView {
    @Id
    private Long primarykey;

    private String batchNo;
    private String region; // province
    private String county;
    private String beneficiary; // recipient_name
    private String contactNumber; // MSISDN
    private String trxId; // trx_id
    private LocalDateTime date; // trx_date
    private String paymentType;
    private String mpesaId; // REFERENCE_ID
    private LocalDateTime timeInitiated;
    private LocalDateTime timeCompleted;
    private String budget;
    private String idNumber;
    private String jobGroup;
    private BigDecimal sendingCharges;
    private BigDecimal withdrawalCharges;
    private BigDecimal amount;
    private BigDecimal runningBalance;
}
