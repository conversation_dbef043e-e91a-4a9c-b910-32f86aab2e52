package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Entity
@Table(name = "view_treatment_model_summary")
@Getter
@Setter
public class MDRpatientsandcostpercounty {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id; // Dummy ID for JPA compliance (optional if view has natural PK)

    @Column(name = "request_type")
    private String requestType;

    @Column(name = "county")
    private String county;

    @Column(name = "patients_requests")
    private Long patientsRequests;

    @Column(name = "dot_requests")
    private Long dotRequests;

    @Column(name = "patients_paid")
    private Long patientsPaid;

    @Column(name = "dots_paid")
    private Long dotsPaid;

    @Column(name = "patients_payments_rejected")
    private Long patientsPaymentsRejected;

    @Column(name = "dots_payments_rejected")
    private Long dotsPaymentsRejected;

    @Column(name = "total_patients_amount_paid")
    private BigDecimal totalPatientsAmountPaid;

    @Column(name = "total_amount_paid_to_dot")
    private BigDecimal totalAmountPaidToDot;
    @Column(name = "org_id")
    private Integer orgId;

}

