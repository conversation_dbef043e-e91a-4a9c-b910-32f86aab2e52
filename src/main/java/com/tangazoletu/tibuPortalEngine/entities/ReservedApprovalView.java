package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.*;
import lombok.Data;

@Entity
@Data
@Table(name = "reserved_approvals_view")
public class ReservedApprovalView {

    @Id
    @Column(name = "primarykey")
    private Long id;

    @Column(name = "batch_number")
    private String batchNumber;

    @Column(name = "user")
    private String user;

    @Column(name = "action")
    private String action;

    @Column(name = "approval_level")
    private String approvalLevel;

    @Column(name = "supervision_year")
    private Integer supervisionYear;

    @Column(name = "supervision_month")
    private Integer supervisionMonth;

    @Column(name = "budget_line")
    private String budgetLine;

    @Column(name = "region")
    private String region;

    @Column(name = "county")
    private String county;

    @Column(name = "facility")
    private String facility;

    @Column(name = "request_type")
    private String requestType;

    @Column(name = "phone_no")
    private String phoneNo;

    @Column(name = "beneficiary")
    private String beneficiary;

    @Column(name = "request_time")
    private String requestTime;

    @Column(name = "amount")
    private Double amount;

    @Column(name = "comments")
    private String comments;

    @Column(name = "county_id")
    private Integer countyId;
}
