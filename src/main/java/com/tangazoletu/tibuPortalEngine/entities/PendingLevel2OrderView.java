package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@Table(name = "view_pending_level2_orders") // View name
public class PendingLevel2OrderView {

    @Id
    @Column(name = "primarykey")
    private Long id;

    @Column(name = "Month")
    private String month;

    @Column(name = "Year")
    private String year;

    @Column(name = "Budget Line")
    private String budgetLine;

    @Column(name = "Region")
    private String region;

    @Column(name = "County")
    private String county;

    @Column(name = "facility")
    private String facility;

    @Column(name = "Request Type")
    private String requestType;

    @Column(name = "Phone No.")
    private String phoneNo;

    @Column(name = "Beneficiary")
    private String beneficiary;

    @Column(name = "Initiator")
    private String initiator;

    @Column(name = "Request Time")
    private LocalDateTime requestTime;

    @Column(name = "Patient Number")
    private String patientNumber;

    @Column(name = "Date Treatment Started")
    private String dateTreatmentStarted;

    @Column(name = "Dot Name")
    private String dotName;

    @Column(name = "DOT Phone")
    private String dotPhone;

    @Column(name = "DOT Amount")
    private Double dotAmount;

    @Column(name = "Driver AMT")
    private Double driverAmount;

    @Column(name = "Amount")
    private Double amount;
}
