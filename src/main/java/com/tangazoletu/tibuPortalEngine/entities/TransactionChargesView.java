package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.*;
import jakarta.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.Immutable;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "view_transaction_charges")
@Immutable
@Data
public class TransactionChargesView implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    @Column(name = "primarykey")
    private Long id;

    @Column(name = "budget_line")
    private String budgetLine;

    @Column(name = "payment_type")
    private String paymentType;

    @Column(name = "phone_number")
    private String phoneNumber;

    @Column(name = "name")
    private String name;

    @Column(name = "receipt")
    private String receipt;

    @Column(name = "transaction_date")
    private LocalDateTime transactionDate;
    @Column(name = "org_id")
    private Long orgId;

    @Column(name = "sending_charge")
    private BigDecimal sendingCharge;

    @Column(name = "withdrawal_charge")
    private BigDecimal withdrawalCharge;

    @Column(name = "total_charges")
    private BigDecimal totalCharges;

    @Column(name = "status")
    private String status;
}
