package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.Immutable;
import java.io.Serializable;

/**
 * Entity class for the user_details_view MySQL view
 * This is a read-only entity representing a database view
 */
@Entity
@Immutable
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "user_details_view")
public class UserView implements Serializable {

    @Id
    @Column(name = "primarykey")
    private Long id;

    @Column(name = "organisation")
    private String organisation;

    @Column(name = "Username")
    private String username;

    @Column(name = "Firstname")
    private String firstname;

    @Column(name = "Lastname")
    private String lastname;

    @Column(name = "Roles")
    private String roles;

    @Column(name = "inTrash")
    private String inTrash;

    @Column(name = "status")
    private String status;

    @Column(name = "email")
    private String email;

    @Column(name = "organizationId")
    private Long organizationId;

    @Column(name = "login_attempts")
    private Integer loginAttempts = 0;
    @Column(name = "county")
    private Integer countyId;
    @Column(name = "RoleIds")
    private String roleIds;


}