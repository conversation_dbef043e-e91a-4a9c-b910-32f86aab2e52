package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "view_completed_treatment_payment_report")
@Data
public class ViewPatientCompletedPatientReport {

    @Id
    @Column(name = "primarykey")
    private Long id;

    private String batchno;
    @Column(name = "approval_level")
    private String approvalLevel;
    @Column(name = "month")
    private String month;
    @Column(name = "year")
    private String year;
    @Column(name = "budget_line")
    private String budgetLine;
    private String region;
    private String county;
    private String facility;
    @Column(name = "request_type")
    private String requestType;
    @Column(name = "phone_no")
    private String phoneNo;
    private String status;
    private String beneficiary;
    private String initiator;

    @Column(name = "request_time")
    private LocalDateTime requestTime;

    @Column(name = "patient_number")
    private String patientNumber;
    @Column(name = "date_treatment_started")
    private String dateTreatmentStarted;
    @Column(name = "dot_name")
    private String dotName;
    @Column(name = "dot_phone")
    private String dotPhone;
    @Column(name = "dot_amount")
    private Double dotAmount;
    @Column(name = "driver_amt")
    private Double driverAmt;
    private Double amount;
}
