package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;

@Entity
@Data
@Table(name = "view_attendance_summary")
public class AttendanceSummaryView {

    @Id
    @Column(name = "attendance_id")
    private Long attendanceId;

    private String name;
    private String county;
    private String station;
    private String designation;

    @Column(name = "job_group")
    private String jobGroup;

    @Column(name = "telephone_number")
    private String telephoneNumber;

    @Column(name = "designation_id")
    private Long designationId;

    @Column(name = "recipient_id")
    private Long recipientId;

    @Column(name = "attendance_date")
    private LocalDate attendanceDate;

    @Column(name = "approved_count")
    private Integer approvedCount;

    @Column(name = "day0")
    private Integer day0;
    @Column(name = "day1")
    private Integer day1;
    @Column(name = "day2")
    private Integer day2;
    private BigDecimal amount;
    @Column(name = "less_lunch")
    private BigDecimal lessLunch;
    private BigDecimal transport;
    @Column(name = "extra_per_diem")
    private BigDecimal extraPerDiem;
    private BigDecimal others;

    @Column(name = "net_pay")
    private BigDecimal netPay;

    private String status;

    @Column(name = "processing_comments")
    private String processingComments;

    @Column(name = "approval_comments")
    private String approvalComments;

    @Column(name = "meeting_code")
    private String meetingCode;
}

