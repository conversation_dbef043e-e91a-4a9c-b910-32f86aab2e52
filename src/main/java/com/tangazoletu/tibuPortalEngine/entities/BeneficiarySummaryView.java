package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Table(name = "vw_beneficiary_summary")
@Data
public class BeneficiarySummaryView {

    @Id
    @Column(name = "primaryKey")
    private Long id;

    @Column(name = "firstName")
    private String firstName;

    @Column(name = "lastName")
    private String lastName;

    @Column(name = "MSISDN")
    private String msisdn;

    @Column(name = "facility")
    private String facility;

    @Column(name = "district")
    private String district;

    @Column(name = "beneficiaryCategory")
    private String beneficiaryCategory;

    @Column(name = "province")
    private String province;

    @Column(name = "county")
    private String county;
}
