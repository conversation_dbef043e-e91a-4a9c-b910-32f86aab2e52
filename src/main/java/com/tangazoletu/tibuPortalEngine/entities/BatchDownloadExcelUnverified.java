package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "batch_download_excel_unverified")
@Getter
@Setter
public class BatchDownloadExcelUnverified {
    @Id
    @Column(name = "id")
    private Long id;
    @Column(name = "`Phone No.`")
    private String phoneNo; // assuming phoneNo is unique; otherwise, add a @GeneratedValue field
    @Column(name = "Beneficiary")
    private String beneficiary;
}
