package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Immutable;

import java.io.Serializable;

@Entity
@Table(name = "view_pending_unconfirmed_phones")
@Data
@Immutable
public class ViewUnconfirmedDriversNumbers  implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;
    @Column(name = "phone_no")
    private String phoneNo;
    @Column(name = "batchno")
    private String batchNo;

}
