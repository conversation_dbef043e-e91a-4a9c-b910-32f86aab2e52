package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.*;
import lombok.Data;

@Entity
@Data
@Table(name = "view_approval_reports")
public class ApprovalReport {

    @Id
    @Column(name = "id")
    private Long id;
    @Column(name = "primarykey")
    private Long primaryKey;

    @Column(name = "batch_number")
    private String batchNumber;

    @Column(name = "request_time")
    private String requestTime;

    @Column(name = "beneficiary")
    private String beneficiary;

    @Column(name = "phone_no")
    private String phoneNo;

    @Column(name = "request_type")
    private String requestType;

    @Column(name = "budget_line")
    private String budgetLine;

    @Column(name = "region")
    private String region;

    @Column(name = "county")
    private String county;

    @Column(name = "district")
    private String district;

    @Column(name = "facility")
    private String facility;

    @Column(name = "amount")
    private Double amount;

    @Column(name = "dot_name")
    private String dotName;

    @Column(name = "dot_phone")
    private String dotPhone;

    @Column(name = "dot_amount")
    private Double dotAmount;

    @Column(name = "notes")
    private String notes;

    @Column(name = "order_type")
    private String orderType;

    @Column(name = "supervision_title")
    private String supervisionTitle;

    @Column(name = "supervision_credit")
    private Double supervisionCredit;

    @Column(name = "supervision_url")
    private String supervisionUrl;

    @Column(name = "recipient")
    private String recipient;

    @Column(name = "prd_credit")
    private Double prdCredit;

    @Column(name = "prd_msisdn")
    private String prdMsisdn;

    @Column(name = "onbehalfrecipient")
    private String onbehalfrecipient;

    @Column(name = "prd_recipient2_credit")
    private Double prdRecipient2Credit;

    @Column(name = "recipient2msisdn")
    private String recipient2msisdn;

    @Column(name = "recipient2")
    private String recipient2;

    @Column(name = "prd_notes")
    private String prdNotes;

    @Column(name = "prd_order_type")
    private String prdOrderType;

    @Column(name = "pa_id")
    private Long paId;

    @Column(name = "approval_level_name")
    private String approvalLevelName;

    @Column(name = "approval_status")
    private String approvalStatus;

    @Column(name = "approved_amount")
    private Double approvedAmount;

    @Column(name = "approval_time")
    private String approvalTime;

    @Column(name = "approval_notes")
    private String approvalNotes;

    @Column(name = "approver")
    private String approver;

    @Column(name = "initiator")
    private String initiator;

    @Column(name = "approval_contact")
    private String approvalContact;
}
