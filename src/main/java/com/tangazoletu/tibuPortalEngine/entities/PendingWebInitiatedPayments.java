package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.Immutable;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;

@Data
@Table(name = "view_web_initiated_pending_approvals")
@Entity
@Immutable
public class PendingWebInitiatedPayments {

    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "batch_no")
    private String batchNo;

    @Column(name = "budget_line")
    private String budgetLine;

    @Column(name = "request_type")
    private String requestType;

    @Column(name = "phone")
    private String phone;

    @Column(name = "beneficiary")
    private String beneficiary;

    @Column(name = "mpesa_confirmation")
    private String mpesaConfirmation;

    @Column(name = "confirmed_name")
    private String confirmedName;

    @Column(name = "region")
    private String region;

    @Column(name = "county")
    private String county;

    @Column(name = "request_time")
    private LocalDateTime requestTime;

    @Column(name = "amount")
    private BigDecimal amount;

    @Column(name = "initiator_id")
    private Long initiatorId;

    @Column(name = "ORG_ID")
    private Long orgId;
}
