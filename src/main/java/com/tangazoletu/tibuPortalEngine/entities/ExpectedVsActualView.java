package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Immutable;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Data
@Immutable
@Table(name = "expected_vs_actual_view")
public class ExpectedVsActualView {

    @Id
    @Column(name = "primarykey")
    private Long id;

    @Column(name = "date_from")
    private LocalDate dateFrom;

    @Column(name = "date_to")
    private LocalDate dateTo;

    @Column(name = "no_of_transactions")
    private Long numberOfTransactions;

    @Column(name = "funds_allocated")
    private BigDecimal fundsAllocated;

    @Column(name = "total_paid_out")
    private BigDecimal totalPaidOut;

    @Column(name = "balance")
    private BigDecimal balance;

    @Column(name = "org_id")
    private Integer orgId;

    @Column(name = "trx_type")
    private String trxType;

    @Column(name = "recipient_name")
    private String recipientName;

    @Column(name = "msisdn")
    private String msisdn;

    @Column(name = "trx_date")
    private LocalDateTime trxDate;

    @Column(name = "credit_amount")
    private BigDecimal creditAmount;
}

