package com.tangazoletu.tibuPortalEngine.entities;

import com.tangazoletu.tibuPortalEngine.enums.Module;
import com.tangazoletu.tibuPortalEngine.util.ModuleConverter;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Entity representing the activity audit table
 * Tracks all user actions and system activities with module categorization
 */
@Entity
@Table(name = "activity")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Activity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "user")
    private Integer user;

    @Column(name = "activityType", length = 100)
    private String activityType;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "resource", length = 100)
    private String resource;

    @Column(name = "getData", columnDefinition = "TEXT")
    private String getData;

    @Column(name = "sourceip", length = 45)
    private String sourceip;

    @Column(name = "postData", columnDefinition = "TEXT")
    private String postData;

    @Column(name = "creationTime")
    private Integer creationTime;

    @Column(name = "org_id")
    private Integer orgId;

    /**
     * New field to store the module information
     * Uses the ModuleConverter to automatically convert between Module enum and String
     */
    @Convert(converter = ModuleConverter.class)
    @Column(name = "module", length = 50)
    private Module module;

    /**
     * Additional field to store the form name for better tracking
     */
    @Column(name = "form_name", length = 100)
    private String formName;

    /**
     * Field to store additional context or metadata as JSON
     */
    @Column(name = "metadata", columnDefinition = "TEXT")
    private String metadata;
}
