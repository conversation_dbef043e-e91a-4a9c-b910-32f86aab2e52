package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "payment_status_view")
@Data
public class PaymentStatusView {

    @Id
    private Long id;

    private String batchNo;

    private String province;

    private String county;

    private String beneficiary;

    private String msisdn;

    private LocalDateTime requestDate;

    private String paymentType;

    private String status;

    private BigDecimal amount;
}
