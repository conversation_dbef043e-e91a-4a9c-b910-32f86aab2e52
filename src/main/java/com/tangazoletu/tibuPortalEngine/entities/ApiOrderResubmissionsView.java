package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Entity
@Data
@Table(name = "apiorder_resubmissions_view")
public class ApiOrderResubmissionsView {

    @Id
    @Column(name = "primarykey")
    private Integer id;

    @Column(name = "Month")
    private String month;

    @Column(name = "Year")
    private Integer year;

    @Column(name = "Budget Line")
    private String budgetLine;

    @Column(name = "Region")
    private String region;

    @Column(name = "County")
    private String county;

    @Column(name = "facility")
    private String facility;

    @Column(name = "Request Type")
    private String requestType;

    @Column(name = "Phone No.")
    private String phoneNo;

    @Column(name = "Beneficiary")
    private String beneficiary;

    @Column(name = "Initator")
    private String initiator;

    @Column(name = "Request Time")
    private Timestamp requestTime;

    @Column(name = "Patient Number")
    private String patientNumber;

    @Column(name = "Date Treatement Started")
    private String dateTreatmentStarted;

    @Column(name = "Dot name")
    private String dotName;

    @Column(name = "DOT Phone")
    private String dotPhone;

    @Column(name = "DOT Amount")
    private BigDecimal dotAmount;

    @Column(name = "Amount")
    private BigDecimal amount;
}
