package com.tangazoletu.tibuPortalEngine.views;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@Table(name = "view_program_manager_approval")
public class ViewProgramManagerApproval {

    @Id
    @Column(name = "primarykey")
    private Long primaryKey;

    @Column(name = "Batch Number")
    private String batchNumber;

    @Column(name = "Budget Line")
    private String budgetLine;

    @Column(name = "Request Type")
    private String requestType;

    @Column(name = "Phone No.")
    private String phoneNumber;

    @Column(name = "Beneficiary")
    private String beneficiary;

    @Column(name = "Region")
    private String region;

    @Column(name = "County")
    private String county;

    @Column(name = "Sub-County")
    private String subCounty;

    @Column(name = "facility")
    private String facility;

    @Column(name = "Request Time")
    private LocalDateTime requestTime;

    @Column(name = "Amount")
    private BigDecimal amount;

    @Column(name = "Dot name")
    private String dotName;

    @Column(name = "DOT Phone")
    private String dotPhone;

    @Column(name = "DOT Amount")
    private BigDecimal dotAmount;
}
