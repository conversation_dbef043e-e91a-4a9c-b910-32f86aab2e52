package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "view_mdr_sctlc_summary")
@Getter
@Setter
public class ViewMdrSctlcSummary {

    @Id
    @Column(name = "initiatorId")
    private String initiatorId;

    @Column(name = "sctlcName")
    private String sctlcName;

    @Column(name = "supervisionsDone")
    private String supervisionsDone; // Use String because UNION includes <h3> HTML

    @Column(name = "unpaidAmt")
    private String unpaidAmt;

    @Column(name = "paidAmt")
    private String paidAmt;

    @Column(name = "rejectedAmt")
    private String rejectedAmt;

    @Column(name = "rowType")
    private String rowType;
    @Column(name = "orgId")
    private Integer orgId;
}

