package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "unprocessed_actual_payments_view")
@Data
public class UnprocessedActualPaymentView {

    @Id
    @Column(name = "primarykey")
    private Long id;

    @Column(name = "Beneficiary")
    private String beneficiary;

    @Column(name = "Contact number")
    private String contactNumber;

    @Column(name = "ID")
    private String trxId;

    @Column(name = "DATE")
    private LocalDateTime date;

    @Column(name = "Amount")
    private BigDecimal amount;
    @Column(name = "Organisation")
    private Integer organisation;
    @Transient
    private String orgName;
}
