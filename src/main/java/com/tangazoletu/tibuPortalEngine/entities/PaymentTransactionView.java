package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@Table(name = "view_payment_transaction_details") // This is the view name
public class PaymentTransactionView {

    @Id
    @Column(name = "ID")
    private String id;

    @Column(name = "Beneficiary")
    private String beneficiary;

    @Column(name = "Contact Number")
    private String contactNumber;

    @Column(name = "Date")
    private LocalDate date;

    @Column(name = "Payment Type")
    private String paymentType;

    @Column(name = "Mpesa ID")
    private String mpesaId;

    @Column(name = "Description")
    private String description;

    @Column(name = "TIME_INITIATED")
    private LocalDateTime timeInitiated;

    @Column(name = "TIME_COMPLETED")
    private LocalDateTime timeCompleted;

    @Column(name = "Sending Charges")
    private Double sendingCharges;

    @Column(name = "Withdrawal Charges")
    private Double withdrawalCharges;

    @Column(name = "Amount")
    private Double amount;

    @Column(name = "Running Balance")
    private Double runningBalance;

    @Column(name = "Order ID")
    private Long orderId;

    @Column(name = "Status")
    private String status;

    @Column(name = "Transaction Type")
    private String transactionType;

    @Column(name = "Province")
    private String province;

    @Column(name = "County")
    private String county;
}
