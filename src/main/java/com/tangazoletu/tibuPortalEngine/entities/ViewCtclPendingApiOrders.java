package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Immutable;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Immutable
@Table(name = "view_ctcl_pending_api_orders")
@Data
public class ViewCtclPendingApiOrders {

    @Id
    @Column(name = "primary_key")
    private Long id;

    @Column(name = "month")
    private String month;

    @Column(name = "year")
    private String year;

    @Column(name = "budget_line")
    private String budgetLine;

    @Column(name = "org_id")
    private Long orgId;

    @Column(name = "region")
    private String region;

    @Column(name = "county")
    private String county;

    @Column(name = "facility")
    private String facility;

    @Column(name = "request_type")
    private String requestType;

    @Column(name = "phone_no")
    private String phoneNumber;

    @Column(name = "beneficiary")
    private String beneficiary;

    @Column(name = "initiator")
    private String initiator;

    @Column(name = "request_time")
    private LocalDateTime requestTime;

    @Column(name = "patient_number")
    private String patientNumber;

    @Column(name = "date_treatment_started")
    private String dateTreatmentStarted;

    @Column(name = "dot_name")
    private String dotName;

    @Column(name = "dot_phone")
    private String dotPhone;

    @Column(name = "dot_amount")
    private BigDecimal dotAmount;

    @Column(name = "driver_amt")
    private BigDecimal driverAmount;

    @Column(name = "amount")
    private BigDecimal amount;

    @Column(name = "confirmable")
    private String confirmable;
}
