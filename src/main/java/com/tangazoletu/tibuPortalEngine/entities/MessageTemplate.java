package com.tangazoletu.tibuPortalEngine.entities;

import com.tangazoletu.tibuPortalEngine.annotations.ExcelColumn;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;
import java.time.LocalDateTime;

@Entity
@Table(name = "message_templates")
@Getter
@Setter
public class MessageTemplate {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "payment_type")
    private Long paymentType;

    @Lob
    private String message;

    @ExcelColumn(exclude = true)
    @ManyToOne(optional = true)
    @JoinColumn(name = "org_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Organisation organisation;

    @Column(name = "created_by")
    private Long createdBy;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "intrash")
    private String inTrash;
    @ExcelColumn(exclude = false)
    @Transient
    private String orgName;

    public enum InTrash {
        Yes, No
    }

    public String getOrgName() {
        return organisation != null ? organisation.getName() : null;
    }

    public void setOrgName(String orgName) {
        this.orgName = organisation.getName();
    }
}
