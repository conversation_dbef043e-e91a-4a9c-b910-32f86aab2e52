package com.tangazoletu.tibuPortalEngine.entities;

import com.tangazoletu.tibuPortalEngine.repositories.ViewUnconfirmedDriversNumbersRepo;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Immutable;

import java.io.Serializable;

@Entity
@Immutable
@Table(name = "view_pending_recipients")
@Data
public class ViewPendingRecipients implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;
    @Column(name = "phone_no")
    private String phoneNo;
    @Column(name = "batchNo")
    private String batchNo;
    @Column(name = "beneficiary_name")
    private String beneficiaryName;
}
