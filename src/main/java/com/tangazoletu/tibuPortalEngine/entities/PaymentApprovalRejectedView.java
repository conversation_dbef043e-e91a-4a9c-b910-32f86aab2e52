package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Immutable;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Immutable
@Table(name = "view_payment_approval_rejected_latest")
@Data
public class PaymentApprovalRejectedView {

    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "approval_level_name")
    private String approvalLevelName;

    @Column(name = "approval_status")
    private String approvalStatus;

    @Column(name = "approved_amount")
    private BigDecimal approvedAmount;

    @Column(name = "approver")
    private String approver;

    @Column(name = "approval_time")
    private LocalDateTime approvalTime;

    @Column(name = "approval_notes")
    private String approvalNotes;

    @Column(name = "initiator")
    private String initiator;

    @Column(name = "apiorder_id")
    private Long apiorderId;
}
