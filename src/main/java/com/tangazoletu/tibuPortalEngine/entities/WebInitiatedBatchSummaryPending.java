package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Immutable;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Immutable
@Table(name = "view_web_initiated_batch_summary_pending")
@Data
public class WebInitiatedBatchSummaryPending {

    @Id
    @Column(name = "primarykey")
    private String id;

    @Column(name = "batch_no")
    private String batchNo;

    @Column(name = "number_of_payments")
    private Integer numberOfPayments;

    @Column(name = "budget_line")
    private String budgetLine;

    @Column(name = "request_type")
    private String requestType;

    @Column(name = "request_time")
    private LocalDateTime requestTime;

    @Column(name = "total_batch_amount")
    private BigDecimal totalBatchAmount;
}
