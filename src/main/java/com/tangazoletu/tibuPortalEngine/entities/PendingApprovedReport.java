package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.*;
import lombok.Data;

@Entity
@Data
@Table(name = "view_pending_approved_reports")
public class PendingApprovedReport {

    @Id
    @Column(name = "primarykey")
    private Long id;

    @Column(name = "batch_number")
    private String batchNumber;

    @Column(name = "request_time")
    private String requestTime;

    private String beneficiary;

    @Column(name = "phone_no")
    private String phoneNo;

    @Column(name = "request_type")
    private String requestType;

    @Column(name = "budget_line")
    private String budgetLine;

    private String region;
    private String county;
    private String district;
    private String facility;
    private Double amount;

    @Column(name = "dot_name")
    private String dotName;

    @Column(name = "dot_phone")
    private String dotPhone;

    @Column(name = "dot_amount")
    private Double dotAmount;

    @Column(name = "approval_level")
    private Integer approvalLevel;

    @Column(name = "approval_status")
    private String approvalStatus;

    private String date;
    private String comment;
}

