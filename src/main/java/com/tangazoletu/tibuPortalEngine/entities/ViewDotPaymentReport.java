package com.tangazoletu.tibuPortalEngine.entities;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;

@Entity
@Table(name = "view_dot_payment_report")
@Getter
@Setter
public class ViewDotPaymentReport {

    @Id
    @Column(name = "primarykey")
    private Long id;

    private String month;

    private String year;

    @Column(name = "budgetLine")
    private String budgetLine;

    private String region;

    private String county;

    private String facility;

    @Column(name = "requestType")
    private String requestType;

    @Column(name = "dotName")
    private String dotName;

    @Column(name = "dotPhone")
    private String dotPhone;

    private String initiator;

    private LocalDateTime requestTime;

    private BigDecimal amount;

    private Integer orgId;
}
