package com.tangazoletu.tibuPortalEngine.entities;

import jakarta.persistence.Entity;
import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

@Entity
@Table(name = "pending_county_batch_orders_view")
@Data
public class PendingBatchCountyOrderView {
    @Id
    private Long id;

    @Column(name = "batch_number")
    private String batchNumber;

    @Column(name = "budget_line")
    private String budgetLine;

    @Column(name = "request_type")
    private String requestType;

    @Column(name = "phone_number")
    private String phoneNumber;

    private String beneficiary;

    private String region;
    private String county;
    private String district;
    private String facility;

    @Column(name = "request_time")
    private LocalDateTime requestTime;

    private Double amount;

    @Column(name = "dot_name")
    private String dotName;

    @Column(name = "dot_phone")
    private String dotPhone;

    @Column(name = "dot_amount")
    private Double dotAmount;

    @Column(name = "org_id")
    private Long orgId;

    @Column(name = "county_id")
    private Long countyId;
}
