package com.tangazoletu.tibuPortalEngine.entities;

import com.tangazoletu.tibuPortalEngine.enums.ApprovalStatus;
import com.tangazoletu.tibuPortalEngine.enums.InTrash;
import com.tangazoletu.tibuPortalEngine.enums.StatusSent;
import jakarta.persistence.*;


import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

@Data
@Entity
@Table(name = "api_orders_view")
public class PaymentReportEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Column(name = "beneficiary_id")
    private Integer beneficiaryId;
    @Column(name ="treatment_model")
    private Integer treatmentModel ;
    @Column(name ="initiator_id")
    private Integer initiatorId;
    @Column(name ="initiator_username")
    private String initiatorUsername;
    @Column(name ="initiator_firstname")
    private String initiatorFirstname;
    @Column(name ="initiator_lastname")
    private String initiatorLastname;
    @Column(name ="firstname")
    private String firstname;
    @Column(name ="middlename")
    private String middlename;
    @Column(name ="lastname")
    private String lastname;
    @Column(name ="beneficiaryType")
    private Integer beneficiaryType ;
    @Column(name ="onbehalffirstname")
    private String onbehalffirstname;
    @Column(name ="onbehalfmiddlename")
    private String onbehalfmiddlename;
    @Column(name ="onbehalflastname")
    private String onbehalflastname;
    @Column(name ="onbehalfType")
    private Integer onbehalfType;
    @Column(name ="recipient2firstname")
    private String recipient2firstname;
    @Column(name ="recipient2middlename")
    private String recipient2middlename;
    @Column(name ="recipient2lastname")
    private String recipient2lastname;
    @Column(name ="beneficiary2Type")
    private Integer beneficiary2Type;
    @Column(name ="orderType")
    private Integer orderType;
    @Column(name ="budget")
    private Integer budget;
    @Column(name ="credit")
    private BigDecimal credit;
    @Column(name ="recipient2credit")
    private BigDecimal recipient2credit ;
    @Column(name ="msisdn")
    private String msisdn;
    @Column(name ="recipient2msisdn")
    private String recipient2msisdn;
    @Column(name ="notes")
    private String notes;
    @Column(name ="latitude")
    private String latitude;
    @Column(name ="longitude")
    private String longitude;
    @Column(name ="province")
    private String province;
    @Column(name ="county")
    private String county;
    @Column(name ="district")
    private String district;
    @Column(name ="facility")
    private String facility;
    @Column(name ="sourceIP")
    private String sourceIP;
    @Column(name ="requestTime")
    private Timestamp requestTime;
    @Column(name ="approval_level")
    private Integer approvalLevel ;
    @Enumerated(EnumType.STRING)
    @Column(name = "approval_status")
    private ApprovalStatus approvalStatus;
    @Column(name ="picked_status")
    private String pickedStatus ;
    @Column(name ="request_src")
    private String requestSrc ;
    @Column(name ="payment_id")
    private Integer paymentId;
    @Column(name ="month")
    private String month;
    @Enumerated(EnumType.STRING)
    @Column(name = "status_sent")
    private StatusSent statusSent;

    @Column(name ="year")
    private Integer year;
    @Column(name ="batchno")
    private String batchno ;
    @Column(name ="attached_to")
    private String attachedTo;
    @Column(name ="inTrash")
    @Enumerated(EnumType.STRING)
    private InTrash inTrash ;
    @Column(name ="visitdate")
    private Date visitdate;
    @Column(name ="CountyBatchNo")
    private String countyBatchNo;
    @Column(name ="originalmsisdn")
    private String originalmsisdn;
    @Column(name ="editedMSISDNComments")
    private String editedMSISDNComments;
    @Column(name ="org_id")
    private Integer orgId ;
    @Column(name ="zone_id")
    private Integer zoneId;
    @Column(name ="resubmission_status")
    private String resubmissionStatus;
    @Column(name ="child_of")
    private String childOf;
    @Column(name ="resubmission_comments")
    private String resubmissionComments;
    @Column(name ="month_of_claim")
    private String monthOfClaim;
    @Column(name ="patient_registration_number")
    private String patientRegistrationNumber;
    @Column(name ="date_treatment_started")
    private String dateTreatmentStarted;
    @Column(name ="dot_nurse_name")
    private String dotNurseName;
    @Column(name ="dot_nurse_phoneno")
    private String dotNursePhoneno;
    @Column(name ="MPESA_Attachment")
    private String mpesaAttachment;
    @Column(name ="local_mpesa_url")
    private String localMpesaUrl;
    @Column(name ="general_comments")
    private String generalComments;
    @Column(name ="driver_name")
    private String driverName;
    @Column(name ="driver_phone")
    private String driverPhone;
    @Column(name ="driver_amount")
    private BigDecimal driverAmount ;
    @Column(name ="payment_due_to")
    private String paymentDueTo;
    @Column(name ="original_credit")
    private BigDecimal originalCredit;
    @Column(name ="migrationId")
    private Integer migrationId;
    @Column(name ="approvalone_notification_sent")
    private String approvaloneNotificationSent;
    @Column(name ="approvaltwo_notification_sent")
    private String approvaltwoNotificationSent;
    @Column(name ="approvalthree_notification_sent")
    private String approvalthreeNotificationSent;
    @Column(name ="Confirmed_Beneficairy")
    private String confirmedBeneficairy;
    @Column(name ="Confirmed_Driver")
    private String confirmedDriver;
    @Column(name ="Confirmed_DOT")
    private String confirmedDOT;
    @Column(name ="original_line_items_num")
    private Integer originalLineItemsNum;






}