package com.tangazoletu.tibuPortalEngine.enums;

import lombok.Data;


public enum Module {
    System("System"),
    Dashboard("Dashboard"),
    Finance("Finance"),
    Reports("Reports"),
    Reconciliation("Reconciliation"),
    MasterRecords("Master Records"),
    Activities("Activities"),
    Sms("Sms"),
    UserManagement("User Management"),
    Configuration("Configuration");

    private final String dbValue;

    Module(String dbValue) {
        this.dbValue = dbValue;
    }

    public String getDbValue() {
        return dbValue;
    }

    public static Module fromDbValue(String dbValue) {
        for (Module m : values()) {
            if (m.dbValue.equalsIgnoreCase(dbValue)) {
                return m;
            }
        }
        throw new IllegalArgumentException("Unknown value: " + dbValue);
    }
}
