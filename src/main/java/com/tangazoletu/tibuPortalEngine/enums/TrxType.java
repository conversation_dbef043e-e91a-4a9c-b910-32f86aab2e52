package com.tangazoletu.tibuPortalEngine.enums;

import com.fasterxml.jackson.annotation.JsonCreator;

public enum TrxType {
    Pay_in("Pay-in"),
    Pay_out("Pay-out"),
    Reversal("Reversal"),
    Fund_Allocation("Fund Allocation"),
    DOT_Pay_out("DOT-Pay-out"),
    Driver_Pay_out("Driver-Pay-out");

    private final String dbValue;

    TrxType(String dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public String toString() {
        return dbValue;
    }

    @JsonCreator
    public static TrxType fromDbValue(String dbValue) {
        for (TrxType type : TrxType.values()) {
            if (type.dbValue.equalsIgnoreCase(dbValue)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown enum value: " + dbValue);
    }

}
