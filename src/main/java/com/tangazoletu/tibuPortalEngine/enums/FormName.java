package com.tangazoletu.tibuPortalEngine.enums;

public enum FormName {
    USER,ORDER,BEN<PERSON><PERSON>IARYTYPE,COUNTY,DISTRIC<PERSON>,FACILITY,FINANCIER,ORDERTYP<PERSON>,PROVINC<PERSON>,BUDGET,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>_<PERSON>YMENT_TYPES,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>Y<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,SMS<PERSON><PERSON>LATE<PERSON>,CONT<PERSON>TUPLOAD,OR<PERSON><PERSON>P<PERSON>ILTE<PERSON>,AD<PERSON><PERSON>SB<PERSON>,
    SELECT_COUNTIES,ROLE,B2CACCOUNTSETTINGS,PARAMETE<PERSON>,EX<PERSON>MPT_USER,MEETING,APPROVALTOP,AP<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    CONFIRMRECIPIENT,ATTENDAN<PERSON><PERSON>OCESSINGWITHEXCEL,ATTENDANCEAPPROVAL,ATTENDANCEPROCESSING,DEACTIVATE_USER,ATTENDANCEAPPROVALEXCE<PERSON>,
    PAYMENTS_INBUILD_PAYMENT,STAG<PERSON><PERSON>RO<PERSON>L,TRANS_ADD,RES<PERSON><PERSON><PERSON>,<PERSON>UN<PERSON><PERSON><PERSON><PERSON><PERSON>,R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
}
