package com.tangazoletu.tibuPortalEngine.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum ApiResponseCode {
    SUCCESS("00"),
    FAIL("01"),
    BAD_REQUEST("400");

    private final String code;

    public String getCode() {
        return code;
    }

    ApiResponseCode(String code){this.code = code;}

    @JsonValue public String intValue() {return this.getCode();}
}
