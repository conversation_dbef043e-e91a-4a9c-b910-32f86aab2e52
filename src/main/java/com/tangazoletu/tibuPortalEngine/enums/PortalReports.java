package com.tangazoletu.tibuPortalEngine.enums;

import java.util.Arrays;

public enum PortalReports {
    ADDRESS_BOOK("Address_Book"),
    USERS("Users"),
    ORDERS("Orders"),
    CONTACTS("Contacts"),
    BUDGET("Budget"),
    ORDER_TYPE("Order_Type"),
    PROVINCE("Province"),
    BENEFICIARYTYPE("Beneficiary"),
    COUNTY("County"),
    DISTRICT("District"),
    PERDIEM("Perdiem"),
    JOB_GROUP("Job_Group"),
    RECIPIENTS("Recipient"),
    ORGANSATIONS("Organization"),
    ORGANISATIONTYPES("Organization_Type"),
    LINEITEM("LineItem"),
    FACILITY("Facility"),
    MDRPATIENTS("MdrPatient"),
    PTLCApproval("PTLCApproval"),
    FINANCEBATCH("FinanceBatch"),
    FUNDALLOCATION("Fundallocation"),
    BENEFICIARIES("Beneficiaries"),
    APPROVALTURNAROUNDTIME("Approval_Turnaround_time"),
    EVENTS("Events"),
    MESSAGEOUTBOX("Message_Outbox"),
    MESSAGETEMPLATE("MessageTemplates"),
    ALLMPESATRANSATIONS("TransactionCharges"),
    PTLCAPPROVALPENDING("ptlcApprovalPending"),
    ROLE("role"),
    AUDITTRAIL("Audit Trail"),
    APISETTINGS("API Settings"),
    SYSTEMPARAMS("systemparams"),
    FINANCEAPPROVALPENDING("financeApprovalPending"),
    PERMISSIONS("Permissions"),
    FINANCEPROGRAMOFFICER("Program Officer"),
    PROGRAMOFFICER("ProgramAccPending"),
    PROGRAMMANAGERAPPROVAL("ProgramMgrPending"),
    SYNCEDPAYMENTS("synced_payments"),
    MDRSURVEILLANCEPAYMENTS("MDR Surveillance Payments"),
    MDRDOTPAYMENTS("MDR Dot Payments"),
    MDRPATIENTSANDCOSTPERCOUNTY("MDR Surveillance Payments"),
    PATIENTPAYMENTOVERTIME("MDRPaymentsOverTime"),
    PATIENTCOMPLETEDTREATMENT("AllApproved"),
    MDRDOTPAYMENTSSUMMARY("MDR Surveillance Payments"),
    SUPERVISIONPAYMENTS("Supervision Payments"),
    DOWNLOADBATCHUNVERFIED("DownloadBatch_Unverified"),
    TRANSACTIONCHARGES("Transaction_Charges"),
    FUNDSTRANSFER("Fundstransfer"),
    STATEMENT("Statement"),
    BUDGETARY("BudgetaryReport"),
    DETAILEDSUMMARY("DetailedSummaryPayments"),
    EXPECTEDVSACTUAL("ExpectedVSActual"),
    GATEWAY("Gateway"),
    MPESA("mpesa"),
    GATEWAYNOTMPESA("gatewaynotmpesa"),
    MPESANOTGATEWAY("mpesanotgateway"),;



    private final String value;

    PortalReports(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    // Optional: Convert string to enum safely
    public static PortalReports fromString(String input) {
        return Arrays.stream(values())
                .filter(rt -> rt.value.equalsIgnoreCase(input))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Invalid report type: " + input));
    }

    // Optional: Check if valid
    public static boolean isValid(String input) {
        return Arrays.stream(values())
                .anyMatch(rt -> rt.value.equalsIgnoreCase(input));
    }
}
