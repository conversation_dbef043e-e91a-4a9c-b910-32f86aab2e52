package com.tangazoletu.tibuPortalEngine.configurations;

import com.tangazoletu.tibuPortalEngine.repositories.ParamRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import java.util.Properties;

@Configuration
public class EmailConfig {

    @Autowired
    private ParamRepository paramRepository;

    @Bean
    public JavaMailSender javaMailSender() {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();

        try {
            // Try to get email credentials from database
            String username = paramRepository.findValueByParameter("MAIL_SERVER_USERNAME");
            String password = paramRepository.findValueByParameter("MAIL_SERVER_PASSWORD");

            mailSender.setHost("smtp.gmail.com");
            mailSender.setPort(587);
            mailSender.setUsername(username);
            mailSender.setPassword(password);
        } catch (Exception e) {
            // Fallback to application.properties values if database retrieval fails
            // This would typically happen during application startup before DB is ready
            mailSender.setHost("smtp.gmail.com");
            mailSender.setPort(587);
            mailSender.setUsername("<EMAIL>");
            mailSender.setPassword("fallback_password");
        }

        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.smtp.ssl.trust", "smtp.gmail.com");
        props.put("mail.debug", "false");

        return mailSender;
    }
}
