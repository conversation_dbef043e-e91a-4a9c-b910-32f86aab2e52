package com.tangazoletu.tibuPortalEngine.configurations;

import com.tangazoletu.tibuPortalEngine.entities.User;
import lombok.Getter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.util.ObjectUtils;

import java.util.Collection;

@Getter
public class SecurityUser implements UserDetails {
    private final User user;

    public SecurityUser(User user) {
        this.user = user;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return null; //Todo: Implementation to get all the Roles of the user
    }

    @Override
    public String getPassword() { return user.getPassword();}

    @Override
    public String getUsername() { return user.getLogin(); }

    public Integer getloginAttempts() { return user.getLoginAttempts(); }

    @Override
    public boolean isAccountNonExpired() {
        return !ObjectUtils.isEmpty(user.getInTrash()) && !user.getInTrash().equalsIgnoreCase("YES");
    }

    @Override
    public boolean isAccountNonLocked() { return true; }

    @Override
    public boolean isCredentialsNonExpired() { return true; }

    @Override
    public boolean isEnabled() {
        return !ObjectUtils.isEmpty(user.getStatus()) && user.getStatus() == 1;
    }
}



