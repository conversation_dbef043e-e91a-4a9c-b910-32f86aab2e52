package com.tangazoletu.tibuPortalEngine.configurations;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.tangazoletu.tibuPortalEngine.enums.JwtClaims;
import com.tangazoletu.tibuPortalEngine.exceptions.EncryptionException;
import com.tangazoletu.tibuPortalEngine.service.AesEncryptionService;
import com.tangazoletu.tibuPortalEngine.service.UserDetailsImpl;
import io.jsonwebtoken.*;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
public class JwtUtils {
    private static final Logger logger = LoggerFactory.getLogger(JwtUtils.class);

    @Value("${encryption.key}")
    private String jwtSecret;

    @Value("${encryption.expirationMs}")
    private int jwtExpirationMs;

    private JWTVerifier jwtVerifier = null;

    @Autowired
    private AesEncryptionService encyptionService;

    public String generateJwtToken(Authentication authentication) {
        UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();

        Map<String, Object> claims = new HashMap<>();
        // add more claims
        claims.put("organizationName", userPrincipal.getOrganizationName());

        return Jwts.builder()
                .setIssuer("TIBU_PORTAL_ENGINE")
                .setSubject((userPrincipal.getUsername()))
                .setIssuedAt(new Date())
                .setExpiration(new Date((new Date()).getTime() + jwtExpirationMs))
                .addClaims(claims)
                .signWith(key(), SignatureAlgorithm.HS256)
                .compact();
    }

    public String generateJwtToken(String username, String organizationName, String secretKey) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(JwtClaims.ORG_NAME.name(), organizationName);
        claims.put(JwtClaims.DATA_KEY.name(), secretKey);

        return Jwts.builder()
                .setIssuer("TIBU_PORTAL_ENGINE")
                .setSubject(username)
                .setIssuedAt(new Date())
                .setExpiration(new Date((new Date()).getTime() + jwtExpirationMs))
                .addClaims(claims)
                .signWith(key(), SignatureAlgorithm.HS256)
                .compact();
    }

//    private Key key() {
//        return Keys.hmacShaKeyFor(Decoders.BASE64.decode(jwtSecret));
//    }
    private SecretKey key() {
        return new SecretKeySpec(jwtSecret.getBytes(StandardCharsets.UTF_8), SignatureAlgorithm.HS256.getJcaName());
    }

    public String getUserNameFromJwtToken(String token) {
        return Jwts.parserBuilder().setSigningKey(key()).build()
                .parseClaimsJws(token).getBody().getSubject();
    }

    public String getOrganizationFromJwtToken(String token) {
        return Jwts.parserBuilder().setSigningKey(key()).build()
                .parseClaimsJws(token).getBody().get(JwtClaims.ORG_NAME.name(), String.class);
    }

    public String getSecretKeyFromJwtToken(String token) {
        return Jwts.parserBuilder().setSigningKey(key()).build()
                .parseClaimsJws(token).getBody().get("secretKey", String.class);
    }
    public DecodedJWT decodeJwt(String token){
        DecodedJWT decodedJWT;
        try {decodedJWT = getJwtVerifier().verify(token);}
        catch (JWTVerificationException exception){
            //log.error("JWT TOKEN FOR API USER:{}, IS INVALID :: {}", JWT.decode(token).getSubject(), exception.getMessage());
            throw new JWTVerificationException(exception.getMessage(), exception);
        }

        return decodedJWT;
    }
    public String getDynamicEncryptionKey(String token) throws EncryptionException {
        String encryptedDynamicKey = decodeJwt(token).getClaim(JwtClaims.DATA_KEY.name()).asString();
        return encryptedDynamicKey;
        //return encyptionService.decrypt(encryptedDynamicKey);
    }

    private JWTVerifier getJwtVerifier() {
        Algorithm algorithm = Algorithm.HMAC256(jwtSecret);
        if(jwtVerifier == null){
            jwtVerifier = JWT.require(algorithm)
                    // specify any specific claim validations
                    .withIssuer("TIBU_PORTAL_ENGINE")
                    // reusable verifier instance
                    .build();
        }
        return jwtVerifier;
    }

    public boolean validateJwtToken(String authToken) {
        try {
            Jwts.parserBuilder().setSigningKey(key()).build().parse(authToken);
            return true;
        } catch (MalformedJwtException e) {
            logger.error("Invalid JWT token: {}", e.getMessage());
        } catch (ExpiredJwtException e) {
            logger.error("JWT token is expired: {}", e.getMessage());
        } catch (UnsupportedJwtException e) {
            logger.error("JWT token is unsupported: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
            logger.error("JWT claims string is empty: {}", e.getMessage());
        }

        return false;
    }
}