package com.tangazoletu.tibuPortalEngine.model;

import java.time.LocalDateTime;

public interface PaymentDetailsDTO {
    Long getOrderId();
    String getBatchno();
    String getTrxStatus();
    String getRegion();
    String getCounty();
    String getBeneficiary();
    String getMsisdn();
    String getId();
    LocalDateTime getDate();
    String getTitle();
    String getReferenceId();
    String getTrxDesc();
    LocalDateTime getTimeInitiated();
    LocalDateTime getTimeCompleted();
    Double getSendingCharge();
    Double getWithdrawalCharge();
    Double getAmount();
    Double getRunningBalance();
}
