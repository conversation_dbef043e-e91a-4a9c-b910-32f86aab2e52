package com.tangazoletu.tibuPortalEngine.examples;

import com.tangazoletu.tibuPortalEngine.enums.FormName;
import com.tangazoletu.tibuPortalEngine.service.AuditService;
import com.tangazoletu.tibuPortalEngine.util.SharedFunctions;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * Example class showing how to use the new module-aware audit system
 * This demonstrates the different ways to audit actions with module categorization
 */
@Component
@RequiredArgsConstructor
public class AuditUsageExample {

    private final AuditService auditService;
    private final SharedFunctions sharedFunctions;

    /**
     * Example: Auditing role-related actions
     */
    public void auditRoleActions() {
        String sourceIp = "*************";
        String postData = "{\"title\":\"Admin Role\",\"permissions\":[1,2,3]}";
        Integer userId = 123;
        Integer orgId = 1;

        // Method 1: Using AuditService directly with FormName
        auditService.auditActionWithModuleNative(
            "CREATE", 
            "Created new admin role", 
            sourceIp, 
            postData, 
            null, 
            userId, 
            orgId, 
            FormName.ROLE
        );

        // Method 2: Using SharedFunctions convenience method for roles
        sharedFunctions.auditRoleAction(
            "UPDATE", 
            "Updated role permissions", 
            sourceIp, 
            postData, 
            null, 
            userId, 
            orgId
        );

        // Method 3: Using SharedFunctions with specific FormName
        sharedFunctions.auditActionWithModule(
            "DELETE", 
            "Deleted role ID: 456", 
            sourceIp, 
            postData, 
            null, 
            userId, 
            orgId, 
            FormName.ROLE
        );
    }

    /**
     * Example: Auditing user management actions
     */
    public void auditUserActions() {
        String sourceIp = "*************";
        String postData = "{\"username\":\"john.doe\",\"email\":\"<EMAIL>\"}";
        Integer userId = 123;
        Integer orgId = 1;

        // User creation
        sharedFunctions.auditUserAction(
            "CREATE", 
            "Created new user: john.doe", 
            sourceIp, 
            postData, 
            null, 
            userId, 
            orgId
        );

        // User deactivation
        sharedFunctions.auditActionWithModule(
            "UPDATE", 
            "Deactivated user: john.doe", 
            sourceIp, 
            postData, 
            null, 
            userId, 
            orgId, 
            FormName.DEACTIVATE_USER
        );
    }

    /**
     * Example: Auditing finance-related actions
     */
    public void auditFinanceActions() {
        String sourceIp = "*************";
        String postData = "{\"amount\":1000,\"recipient\":\"John Doe\"}";
        Integer userId = 123;
        Integer orgId = 1;

        // Payment order creation
        sharedFunctions.auditFinanceAction(
            "CREATE", 
            "Created payment order for $1000", 
            sourceIp, 
            postData, 
            null, 
            userId, 
            orgId, 
            FormName.ORDER
        );

        // Fund allocation
        sharedFunctions.auditFinanceAction(
            "CREATE", 
            "Allocated funds to budget", 
            sourceIp, 
            postData, 
            null, 
            userId, 
            orgId, 
            FormName.FUNDALLOCATION
        );

        // Approval action
        sharedFunctions.auditFinanceAction(
            "UPDATE", 
            "Approved payment batch", 
            sourceIp, 
            postData, 
            null, 
            userId, 
            orgId, 
            FormName.APPROVAL
        );
    }

    /**
     * Example: Auditing SMS/Communication actions
     */
    public void auditSmsActions() {
        String sourceIp = "*************";
        String postData = "{\"message\":\"Payment notification\",\"recipients\":100}";
        Integer userId = 123;
        Integer orgId = 1;

        // Bulk SMS sending
        sharedFunctions.auditActionWithModule(
            "CREATE", 
            "Sent bulk SMS to 100 recipients", 
            sourceIp, 
            postData, 
            null, 
            userId, 
            orgId, 
            FormName.BULKSMS
        );

        // SMS template creation
        sharedFunctions.auditActionWithModule(
            "CREATE", 
            "Created SMS template for payment notifications", 
            sourceIp, 
            postData, 
            null, 
            userId, 
            orgId, 
            FormName.SMSTEMPLATES
        );
    }

    /**
     * Example: Auditing master records actions
     */
    public void auditMasterRecordsActions() {
        String sourceIp = "*************";
        String postData = "{\"name\":\"Nairobi County\",\"code\":\"001\"}";
        Integer userId = 123;
        Integer orgId = 1;

        // County creation
        sharedFunctions.auditActionWithModule(
            "CREATE", 
            "Created new county: Nairobi", 
            sourceIp, 
            postData, 
            null, 
            userId, 
            orgId, 
            FormName.COUNTY
        );

        // Budget creation
        sharedFunctions.auditActionWithModule(
            "CREATE", 
            "Created budget for Q1 2024", 
            sourceIp, 
            postData, 
            null, 
            userId, 
            orgId, 
            FormName.BUDGET
        );
    }

    /**
     * Example: Auditing activities/meetings actions
     */
    public void auditActivitiesActions() {
        String sourceIp = "*************";
        String postData = "{\"venue\":\"Conference Hall\",\"date\":\"2024-01-15\"}";
        Integer userId = 123;
        Integer orgId = 1;

        // Meeting creation
        sharedFunctions.auditActionWithModule(
            "CREATE", 
            "Created meeting for January 15th", 
            sourceIp, 
            postData, 
            null, 
            userId, 
            orgId, 
            FormName.MEETING
        );

        // Attendance processing
        sharedFunctions.auditActionWithModule(
            "UPDATE", 
            "Processed attendance for meeting", 
            sourceIp, 
            postData, 
            null, 
            userId, 
            orgId, 
            FormName.ATTENDANCEPROCESSING
        );
    }

    /**
     * Example: Getting module information
     */
    public void demonstrateModuleInfo() {
        // Get module name for a specific form
        String roleModule = auditService.getModuleForForm(FormName.ROLE);
        System.out.println("ROLE belongs to module: " + roleModule); // Output: User Management

        String orderModule = auditService.getModuleForForm(FormName.ORDER);
        System.out.println("ORDER belongs to module: " + orderModule); // Output: Finance

        String meetingModule = auditService.getModuleForForm(FormName.MEETING);
        System.out.println("MEETING belongs to module: " + meetingModule); // Output: Activities
    }
}
