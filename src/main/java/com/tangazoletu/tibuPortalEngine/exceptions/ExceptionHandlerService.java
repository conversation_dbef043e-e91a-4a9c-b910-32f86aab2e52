package com.tangazoletu.tibuPortalEngine.exceptions;

import com.tangazoletu.tibuPortalEngine.dto.BadRequest;
import com.tangazoletu.tibuPortalEngine.enums.ApiResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.ConstraintViolation;


import jakarta.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@RestControllerAdvice
@Slf4j
public class ExceptionHandlerService {

    @ExceptionHandler({MethodArgumentNotValidException.class})
    public ResponseEntity<BadRequest> handleValidationErrors(MethodArgumentNotValidException ex, HttpServletRequest req) {
        List<String> errors = ex.getBindingResult().getFieldErrors()
                .stream().map(FieldError::getDefaultMessage).collect(Collectors.toList());

        //log.error("BAD REQUEST({}) :: {}", req.getRequestURI(), errors);

        BadRequest badRequest = new BadRequest();
        badRequest.setResponseCode(ApiResponseCode.BAD_REQUEST);
        badRequest.setResponseMessage(String.join(", ", errors));

        return new ResponseEntity<>(badRequest, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler({HttpMessageNotReadableException.class})
    public ResponseEntity<?> handleJsonParsingError(HttpMessageNotReadableException ex, HttpServletRequest req) {
        String errorMsg = (ex.getCause() != null) ? ex.getCause().getMessage() : ex.getMessage();

        log.error("BAD REQUEST({}) :: {}", req.getRequestURI(), errorMsg);

        BadRequest badRequest = new BadRequest();
        badRequest.setResponseCode(ApiResponseCode.BAD_REQUEST);
        badRequest.setResponseMessage(errorMsg);

        return new ResponseEntity<>(badRequest, HttpStatus.BAD_REQUEST);
    }



    @ExceptionHandler({ConstraintViolationException.class})
    public ResponseEntity<?> handleConstraintViolationExceptionErrors(ConstraintViolationException ex, HttpServletRequest req) {
        List<String> errors = ex.getConstraintViolations().stream().map(ConstraintViolation::getMessage)
                .collect(Collectors.toList());

        //log.error("BAD REQUEST({}) :: {}", req.getRequestURI(), errors);

        BadRequest badRequest = new BadRequest();
        badRequest.setResponseCode(ApiResponseCode.BAD_REQUEST);
        badRequest.setResponseMessage(String.join(", ", errors));

        return new ResponseEntity<>(badRequest, HttpStatus.BAD_REQUEST);
    }

}
