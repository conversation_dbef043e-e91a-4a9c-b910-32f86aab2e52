package com.tangazoletu.tibuPortalEngine.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
public class MDRpatientsandcostpercountyRequest {

    private String treatmentModel;
    private String county;
    private List<Integer> orgIds;
    private MDRpatientsandcostpercountyRequest.SearchQuery search;
    @Data
    @Builder
    public static class SearchQuery {
        private String value; // Holds the search query value
        private boolean regex; // Holds the regex flag
    }
    private int page;
    private int pageSize;

}
