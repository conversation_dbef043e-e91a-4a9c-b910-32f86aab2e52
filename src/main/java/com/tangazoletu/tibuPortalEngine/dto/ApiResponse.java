package com.tangazoletu.tibuPortalEngine.dto;

import com.tangazoletu.tibuPortalEngine.entities.Financier;
import com.tangazoletu.tibuPortalEngine.enums.ApiResponseCode;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class ApiResponse {

    private ApiResponseCode responseCode;

    private String responseMessage;

    private String data;

    private Object jdata;

    public ApiResponse() {
        this.responseCode = responseCode;
        this.data = data;
        this.responseMessage = responseMessage;
    }

    public ApiResponse(String data) {
        this.data = data;
    }

    public ApiResponse(ApiResponseCode responseCode, String responseMessage, String data, Object jdata){

        this.responseCode = responseCode;
        this.data = data;
        this.responseMessage = responseMessage;
        this.jdata = jdata;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public ApiResponseCode getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(ApiResponseCode responseCode) {
        this.responseCode = responseCode;
    }

    public String getResponseMessage() {
        return responseMessage;
    }

    public void setResponseMessage(String responseMessage) {
        this.responseMessage = responseMessage;
    }
    public Object getJdata() {
        return jdata;
    }

    public void setJdata(Object jdata) {
        this.jdata = jdata;
    }
}
