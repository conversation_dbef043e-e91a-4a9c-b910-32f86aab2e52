package com.tangazoletu.tibuPortalEngine.dto;

import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class BudgetaryRequest {
    private String keyword;
    private Integer provinceId;
    private List<Integer> budgetIds;
    private List<Integer> countyIds;
    private String month;
    private String year;
    private Date startDate;
    private Date endDate;
    private String startTimestamp; // Holds a Timestamp
    private String endTimestamp; // Holds a Timestamp
    private Integer page;
    private Integer pageSize;


}
