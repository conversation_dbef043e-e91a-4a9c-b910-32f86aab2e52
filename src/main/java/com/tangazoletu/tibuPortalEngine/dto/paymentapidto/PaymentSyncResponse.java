package com.tangazoletu.tibuPortalEngine.dto.paymentapidto;

import com.tangazoletu.tibuPortalEngine.enums.ApiResponse;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class PaymentSyncResponse {
    private String code;
    private String message;
    private Object data;

    public static PaymentSyncResponse success(String message){
        return PaymentSyncResponse.builder()
                .code("200")
                .message(message)
                .build();
    }
    public static PaymentSyncResponse failed(String message){
        return PaymentSyncResponse.builder()
                .code("500")
                .message(message)
                .build();
    }
}
