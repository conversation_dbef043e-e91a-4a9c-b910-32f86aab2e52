package com.tangazoletu.tibuPortalEngine.dto.paymentapidto;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class SupervisionRequestDto {
    private String supervisor_pms_id;
    private String payment_id;
    private String supervisor_name;
    private String payment_due_to;
    private String msisdn;
    private Double total_amount;
    private String notes;
    private Integer province_id;
    private Integer county_id;
    private Integer district_id;
    private String zone_id;
    private Double latitude;
    private Double longitude;
    private String resubmission_type;
    private String resubmission_status;
    private String year;
    private String month;
    private String total_driver_amount;

    private List<Map<String, Object>> sup_driver_details;
    private List<Map<String, Object>> supItems;
    private String driver_details;
    private String items;

}
