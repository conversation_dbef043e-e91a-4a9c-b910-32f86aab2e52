package com.tangazoletu.tibuPortalEngine.dto.paymentapidto;

import com.tangazoletu.tibuPortalEngine.entities.ViewCtclPendingApiOrders;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.domain.Page;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
@Builder
public class FinanceApprovalPageWrapper {
    private List<?> content;
    private Map<String, Object> page;
    private Map<String, BigDecimal> totals;
}
