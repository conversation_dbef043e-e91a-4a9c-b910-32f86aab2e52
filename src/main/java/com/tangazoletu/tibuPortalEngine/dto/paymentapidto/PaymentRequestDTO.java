package com.tangazoletu.tibuPortalEngine.dto.paymentapidto;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class PaymentRequestDTO {
    private String dtlc;
    private Long Payment_id;
    private Integer treatment;

    private String recipient_firstname;
    private String recipient_middlename;
    private String recipient_lastname;
    private Integer recipient_category;

    private String onbehalf_firstname;
    private String onbehalf_middlename;
    private String onbehalf_lastname;

    private String recipient2_firstname;
    private String recipient2_middlename;
    private String recipient2_lastname;
    private Integer recipient2_category;

    private Integer payment_category;
    private Double amount;
    private Double recipient2amount;

    private Integer budget;
    private String msisdn;
    private String recipient2msisdn;
    private Integer onbehalf_category;

    private String notes;
    private List<Map<String, Object>> supnNotes;

    private Double latitude;
    private Double longitude;

    private Integer province;
    private Integer county;
    private Integer district;
    private String facility;

    private String month_of_claim;
    private String patient_registration_number;
    private String date_treatment_started;

    private String dot_nurse_name;
    private String dot_nurse_phoneno;

    private String Resubmission_Type;
    private String Resubmission_Status;

    private String year;

    private List<Map<String, Object>> supItems;

    private String items;
    private String local_url_MPESA_Attachments;
    private String MPESA_Attachments;

}
