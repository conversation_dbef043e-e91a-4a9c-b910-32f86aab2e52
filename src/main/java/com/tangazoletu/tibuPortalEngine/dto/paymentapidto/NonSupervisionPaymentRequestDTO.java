package com.tangazoletu.tibuPortalEngine.dto.paymentapidto;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class NonSupervisionPaymentRequestDTO {

    private Integer supervisor_pms_id;
    private Long payment_id;
    private String supervisor_name;
    private String payment_due_to;

    private String msisdn;

    private String beneficiary_firstname;
    private String beneficiary_middlename;
    private String beneficiary_lastname;

    private String beneficiary_msisdn;
    private Double total_amount;

    private String notes;

    private Integer province_id;
    private Integer county_id;
    private Integer district_id;
    private Integer Zone_id;

    private Double latitude;
    private Double longitude;

    private String Resubmission_Type;
    private String Resubmission_Status;

    private String year;
    private String month;

    // Raw JSON string from URL
    private String items;

    // Parsed version of items (deserialized in controller)
    private List<Map<String, Object>> supItems;

}
