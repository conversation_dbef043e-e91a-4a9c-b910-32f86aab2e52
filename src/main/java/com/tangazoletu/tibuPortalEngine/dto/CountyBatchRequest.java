package com.tangazoletu.tibuPortalEngine.dto;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
public class CountyBatchRequest {
    Integer approvalLevel;
    Integer type;
    String keywords;
    List<Long> provinceIds;
    List<Long> budgetIds;
    List<Long> countyIds;
    String month;
    String year;
    String dateFrom;
    String dateTo;
    Integer orgId;
    Integer all;
    Integer page;
    Integer pageSize;
}
