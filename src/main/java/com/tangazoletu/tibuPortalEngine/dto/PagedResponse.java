package com.tangazoletu.tibuPortalEngine.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
@AllArgsConstructor
public class PagedResponse<T> {
    private String responseCode;

    private String responseMessage;

    private List<T> data;

    private PageInfo page;

    @Data
    @AllArgsConstructor
    public static class PageInfo {
        @JsonProperty("page_size")
        private int pageSize;

        @JsonProperty("current_page")
        private int currentPage;

        @JsonProperty("count")
        private long totalElements;

        @JsonProperty("total_pages")
        private int totalPages;
    }
}
