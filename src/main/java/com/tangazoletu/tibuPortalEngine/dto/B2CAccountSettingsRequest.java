package com.tangazoletu.tibuPortalEngine.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class B2CAccountSettingsRequest {
    @SerializedName("cell")
    private String formCell = "";
    @SerializedName("action")
    private String action;
    @SerializedName("spid")
    private String spid;
    @SerializedName("serviceId")
    private String serviceId;
    @SerializedName("password")
    private String password;
    @SerializedName("initiatorUsername")
    private String initiatorUsername;
    @SerializedName("initiatorPwd")
    private String initiatorPwd;
    @SerializedName("commandId")
    private String commandId;
    @SerializedName("shortcode")
    private String shortcode;
    @SerializedName("permissions")
    private String[] permissions;
}
