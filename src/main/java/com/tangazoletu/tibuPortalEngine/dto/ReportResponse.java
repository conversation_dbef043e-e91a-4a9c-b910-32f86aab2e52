package com.tangazoletu.tibuPortalEngine.dto;

import com.tangazoletu.tibuPortalEngine.entities.ApiOrder;
import com.tangazoletu.tibuPortalEngine.entities.PaymentRequestDetails;
import com.tangazoletu.tibuPortalEngine.entities.PaymentTransactionView;
import com.tangazoletu.tibuPortalEngine.entities.TempPayment;
import com.tangazoletu.tibuPortalEngine.model.*;
import com.tangazoletu.tibuPortalEngine.model.PaymentApprovalDTO;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Optional;

@Data
@Builder
public class ReportResponse {
    private String responseCode;
    private String responseMessage;
    Page<?> data;
    Optional<?> payment;
    PaymentDetailsDTO paymentDetailsDTO;
    supervisionDetails supervisionDetails;
    Optional<TempPayment> tempPayment;
    PaymentApprovalDTO paymentApprovalDTO;
    ReturnCodeDTO returnCodeDTO;
    EligibleForResubmissionDTO eligibleForResubmissionDTO;
    Optional<ApiOrder> apiOrder;
    Optional<PaymentRequestDetails> paymentRequestDetails;
    List<PaymentTransactionView> paymentTransactionViews;
    boolean permission;
    List<supervisionDetails> supervisionDetailsReport;
    List<com.tangazoletu.tibuPortalEngine.dto.PaymentApprovalDTO> paymentApprovalDTOReport;



    public void setPaymentApprovalDTO(com.tangazoletu.tibuPortalEngine.dto.PaymentApprovalDTO paymentApprovalDTO) {
    }
}
