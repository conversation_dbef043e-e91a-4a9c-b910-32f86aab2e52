package com.tangazoletu.tibuPortalEngine.dto;

import com.tangazoletu.tibuPortalEngine.entities.MessageOutbox;
import com.tangazoletu.tibuPortalEngine.entities.Organisation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageOutboxDTO {
    private Long id;

    private String smsBatchNo;

    private Integer beneficiaryID;

    private String phoneNumber;

    private Organisation organisation;

    private String messageOut;

    private String shortcode;

    private Integer status;

    private String traceID;

    private Integer createdBy;

    private String origin;

    private Long paymentID;

    private Timestamp processedAt;

    private Timestamp createdAt;

    private Integer deliveryCode;

    private String deliveryStatus;

    private Integer retries;

    // Manual constructor for mapping from MessageOutbox entity
    public MessageOutboxDTO(MessageOutbox messageOutbox) {
        this.id = messageOutbox.getId();
        this.beneficiaryID = messageOutbox.getBeneficiaryID();
        this.smsBatchNo = messageOutbox.getSmsBatchNo();
        this.organisation = messageOutbox.getOrganisation();
        this.phoneNumber = messageOutbox.getPhoneNumber();
        this.messageOut = messageOutbox.getMessageOut();
        this.shortcode = messageOutbox.getShortcode();
        this.status = messageOutbox.getStatus();
        this.traceID = messageOutbox.getTraceID();
        this.createdBy = messageOutbox.getCreatedBy();
        this.origin = messageOutbox.getOrigin();
        this.paymentID = messageOutbox.getPaymentID();
        this.processedAt = Timestamp.valueOf(messageOutbox.getProcessedAt());
        this.createdAt = Timestamp.valueOf(messageOutbox.getCreatedAt());
        this.deliveryCode = messageOutbox.getDeliveryCode();
        this.deliveryStatus = messageOutbox.getDeliveryStatus();
        this.retries = messageOutbox.getRetries();
    }

}
