package com.tangazoletu.tibuPortalEngine.dto;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ApiOrderApprovalTwoDTO {
    private boolean stageApproval;
    private String orderType;
    private String beneficiaryTitle;
    private String nomineeTitle;
    private String confirmableStatus;
    private String msisdn;
    private String beneficiarytye1;
    private String beneficiarytye2;
    private String treatmentModel;
    private  String patientRegNo;
    private String patientName;
    private String recipient1Title;
    private String amountToSentToRecipient1;
    private String recipientPhoneNumber;
    private String recipient2Title;
    private String amountToSentToRecipient2;
    private String secondRecipientPhoneNumber;
    private String description;
    private String paymentType;
    private String recipientName;
    private String onBehalfRecipient;


}
