package com.tangazoletu.tibuPortalEngine.dto;

import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@Builder
public class PatientCompletedTreatmentPayments {
    private String keywords;
    private Integer province;
    private String provinceStr;
    private List<String> phoneNumbers;
    private List<String> budgets;
    private List<String> counties;
    private String month;
    private String year;
    private Date startDate;
    private Date endDate;
    private String dateFrom; // Holds a Timestamp 2013
    private String dateTo; // Holds a Timestamp 2025
    private String county;
    private Integer page;
    private Integer pageSize;
    private Integer region;
    private String approvalStatus;
    private String batch;

}
