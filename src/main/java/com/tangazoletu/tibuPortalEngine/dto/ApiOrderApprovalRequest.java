package com.tangazoletu.tibuPortalEngine.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Min;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class ApiOrderApprovalRequest {
    private int page;
    private int pageSize=10;
    private String region;
    private List<Integer> orgFilterList;
    private String batchId;
    private Integer approval;
    private String currentApprovalLevel;
    private String apiOrderId;
    private String confirmAbleColumn;
    private String currentLevel="1";
    private String orgId;
    private String keyword;
    private String county;
    private String dateFrom;
    private String dateTo;

}
