package com.tangazoletu.tibuPortalEngine.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class UserRequest {

    @SerializedName("action")
    private String formAction;
    @SerializedName("f")
    private String formName;
    @SerializedName("cell")
    private String formCell = "";
    @SerializedName("login")
    private String username;
    @SerializedName("firstname")
    private String firstName;
    @SerializedName("lastname")
    private String lastName;
    @SerializedName("email")
    private String email;
    @SerializedName("province")
    private String province;
    @SerializedName("organisation")
    private String organisation;
    @SerializedName("accountLocked")
    private String accountLocked;
    @SerializedName("permission")
    private String[] permission;
    @SerializedName("provinces")
    private String provinces;
    private int pageNumber;
    private int pageSize;
    private String role;
    




}
