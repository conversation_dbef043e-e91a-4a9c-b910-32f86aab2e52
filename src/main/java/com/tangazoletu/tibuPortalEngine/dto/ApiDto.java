package com.tangazoletu.tibuPortalEngine.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiDto {
    @NotBlank(message = "Invalid Request data. Payload must be provided.")
    private String payload; //Holds the encrypted jsonData

    // Optional field
    private String file;
}