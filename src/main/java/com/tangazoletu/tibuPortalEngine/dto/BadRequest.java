package com.tangazoletu.tibuPortalEngine.dto;

import com.tangazoletu.tibuPortalEngine.enums.ApiResponseCode;
import lombok.Data;

@Data
public class BadRequest {
    private ApiResponseCode responseCode;
    private String responseMessage;

    public ApiResponseCode getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(ApiResponseCode responseCode) {
        this.responseCode = responseCode;
    }

    public String getResponseMessage() {
        return responseMessage;
    }

    public void setResponseMessage(String responseMessage) {
        this.responseMessage = responseMessage;
    }
}
