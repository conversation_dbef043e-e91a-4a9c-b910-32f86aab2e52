package com.tangazoletu.tibuPortalEngine.dto;

import ch.qos.logback.core.status.Status;
import com.tangazoletu.tibuPortalEngine.entities.Payment;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
@Data
public class PaymentDTO {
    private Long id;
    private String trxId;
    private String recipientName;
    private BigDecimal debitCommission;
    private LocalDateTime trxDate;
    private String budgetLine;
    private String batchNumber;
    private java.sql.Timestamp requestTime;
    private String beneficiary;
    private String phoneNo;
    private String requestType;
    private String region;
    private String county;
    private String district;
    private String facility;
    private BigDecimal amount;
    private String dotName;
    private String dotPhone;
    private BigDecimal dotAmount;
    private BigDecimal sendingCharge;
    private  BigDecimal withdrawalCharge;
    private Status status;


    public void setStatus(Payment.Status status) {
    }
}
