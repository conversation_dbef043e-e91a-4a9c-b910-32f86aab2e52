package com.tangazoletu.tibuPortalEngine.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Min;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@Builder
public class PaymentRequest {
    private int page;
    private int pageSize=10;
    private Integer region;
    private Date startDate;
    private Date endDate;
    private String startTimestamp; // Holds a Timestamp
    private String endTimestamp; // Holds a Timestamp
    private String orderId;
    private String id;

    private List<Integer> orgFilterList;
    private List<Integer> budgetIds;
    private String keyword; // New field for search query

    @Data
    @Builder
    public static class SearchQuery {
        private String value; // Holds the search query value
        private boolean regex; // Holds the regex flag
    }
}
