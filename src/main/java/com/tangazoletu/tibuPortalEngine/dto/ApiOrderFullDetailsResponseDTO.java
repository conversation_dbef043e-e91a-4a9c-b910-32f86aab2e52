package com.tangazoletu.tibuPortalEngine.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class ApiOrderFullDetailsResponseDTO {
    private ApiOrderApprovalTwoDTO orderApproval;
    private List<SupervisionItemDTO> supervisionItems;
    private List<ApprovalCommentDTO> approvalComments;
    private List<PatientPaymentRecordHistoryDTO> patientPaymentRecordHistoryDTOS;
    private List<PaymentApprovalDTO> paymentApprovalDTOS;
    private List<DriverDetailsDTO> driverDetailsDTOS;
    private List<PatientPaymentRecordDTO> patientPaymentRecordDTOS;
    private List<RelatedSupervisionItemDTO> relatedSupervisionItemDTOS;
    private SupervisionDetailsDTO supervisionDetailsDTOS;
}
