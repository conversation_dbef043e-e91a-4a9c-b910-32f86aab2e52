package com.tangazoletu.tibuPortalEngine.dto;

import com.tangazoletu.tibuPortalEngine.entities.Event;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.domain.Page;

import java.util.Optional;

@Data
@Builder
public class MeetingResponse {
    private String responseCode;
    private String responseMessage;
    Page<?> data;
    Optional<Event> event;
    AttendanceStatusDTO attendanceStatusDTO;
}
