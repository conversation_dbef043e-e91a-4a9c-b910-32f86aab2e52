package com.tangazoletu.tibuPortalEngine.dto;

import com.tangazoletu.tibuPortalEngine.annotations.ExcelColumn;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@AllArgsConstructor
@Builder
public class EventCodesDTO {
    private String columnOne;
    private String columnTwo;
    private String columnThree;
    private String columnFour;
    @ExcelColumn(exclude = true)
    private Integer day;
    @ExcelColumn(exclude = true)
    private String meetingCode;

}
