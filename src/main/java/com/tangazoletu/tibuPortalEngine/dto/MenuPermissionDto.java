package com.tangazoletu.tibuPortalEngine.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@AllArgsConstructor
@ToString
@Slf4j
public class MenuPermissionDto {
    @JsonProperty("module")
    private String module;

    @JsonProperty("menuItems")
    private List<MenuItemDto> menuItems;

    // Default constructor (required for <PERSON>)
    public MenuPermissionDto() {
        this.menuItems = new ArrayList<>();
    }
}
