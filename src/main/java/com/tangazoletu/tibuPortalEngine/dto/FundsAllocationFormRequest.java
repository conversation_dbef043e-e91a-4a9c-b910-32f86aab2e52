package com.tangazoletu.tibuPortalEngine.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class FundsAllocationFormRequest {
    @SerializedName("cell")
    private String formCell = "";
    @SerializedName("action")
    private String action;
    @SerializedName("trx_type")
    private String trxType;
    @SerializedName("available_funds")
    private String availableFunds;
    @SerializedName("budget_from")
    private String budgetFrom;
    @SerializedName("budget_to")
    private String budgetTo;
    @SerializedName("financier")
    private String financier;
    @SerializedName("credit")
    private String amountToCredit;
    @SerializedName("notes")
    private String description;
    @SerializedName("budget_line_id")
    private String budgetLineId;
    @SerializedName("fund_allo_amount")
    private String fundalloAmount;
    @SerializedName("budget")
    private String budget;
    @SerializedName("org_id")
    private String orgId;
    @SerializedName("rowcount")
    private String rowcount;
    @SerializedName("fund_allo_amount_")
    private String fundalloAmount_;
    @SerializedName("budget_line_id_")
    private String budgetLineId_;

}
