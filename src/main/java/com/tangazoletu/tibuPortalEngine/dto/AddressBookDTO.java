package com.tangazoletu.tibuPortalEngine.dto;

import com.tangazoletu.tibuPortalEngine.annotations.ExcelColumn;
import com.tangazoletu.tibuPortalEngine.entities.AddressBookView;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Builder;

import java.sql.Timestamp;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddressBookDTO {

    @ExcelColumn(exclude = true)
    private Integer id;

    @ExcelColumn(value = "Address Book Name", order = 1)
    @NotBlank(message = "Title is required")
    @Size(max = 50, message = "Title must not exceed 50 characters")
    private String title;

    @ExcelColumn(value = "Organization", order = 2)
    private String organization;

    @ExcelColumn(value = "Address Type", order = 3)
    @Size(max = 50, message = "Address type must not exceed 50 characters")
    private String addressType;

    @ExcelColumn(value = "Address Book", order = 4)
    private String addressBook;

    @ExcelColumn(value = "Total Contacts", order = 5)
    private Long totalContacts;


    // Manual constructor for mapping from AddressBook entity
    public AddressBookDTO(AddressBookView addressBook) {
        this.id = addressBook.getId();
        this.title = addressBook.getTitle();
        this.organization = addressBook.getOrganization();
        this.addressType = addressBook.getAddressType();
        this.totalContacts=addressBook.getTotalContacts();

    }

}

