package com.tangazoletu.tibuPortalEngine.dto;

import com.tangazoletu.tibuPortalEngine.entities.AddressBookView;
import com.tangazoletu.tibuPortalEngine.entities.AddressContact;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AddressContactDTO {

    private Integer id;

    private AddressBookView addressBook;

    private Timestamp createdAt;

    private String inTrash;



    public AddressContactDTO(AddressContact addressContact){
        this.id = addressContact.getId();
        this.addressBook = addressContact.getAddressBook();
        this.createdAt = addressContact.getCreatedAt();
        this.inTrash = addressContact.getInTrash();
    }
}
