package com.tangazoletu.tibuPortalEngine.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.Date;

@Data
public class MeetingRequest {
    @SerializedName("cell")
    private String formCell = "";
    @SerializedName("action")
    private String action;
    @SerializedName("county")
    private Number county;
    @SerializedName("budget")
    private Number budget;
    @SerializedName("meetingname")
    private String meetingname;
    @SerializedName("venue")
    private String venue;
    @SerializedName("numberExpected")
    private int numberExpected;
    @SerializedName("paybletravellingdays")
    private String paybletravellingdays;
    @SerializedName("startdate")
    private String startdate;
    @SerializedName("enddate")
    private String enddate;
    @SerializedName("meetingtype")
    private String meetingtype;
    @SerializedName("paymenttimes")
    private String paymenttimes;
    @SerializedName("status")
    private String status;
    private String keywords;
    private Integer budgetIds;
    private Integer countyIds;
    private Integer meetingStatus;
    private Date dateFrom;
    private Date dateTo;
    private Integer page;
    private Integer pageSize;
    private Long id;

}
