package com.tangazoletu.tibuPortalEngine.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EventDetails {
    private String eventName;
    private String venue;
    private Date startDate;
    private Date endDate;
    private int day;
    private int noOfDays;
    private int codeCount;
}
