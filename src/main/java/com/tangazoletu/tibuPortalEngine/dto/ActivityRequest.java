package com.tangazoletu.tibuPortalEngine.dto;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;

@Data
@RequiredArgsConstructor
@Slf4j
public class ActivityRequest {
    private int pageNumber;
    private int pageSize;
    private  String organisation;
    private String budget;
    private String status;
    private  String venue;
    private LocalDate startDate;
    private LocalDate endDate;



}
