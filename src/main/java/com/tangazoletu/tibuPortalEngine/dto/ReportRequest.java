package com.tangazoletu.tibuPortalEngine.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


@Data
@AllArgsConstructor
@Slf4j
@RequiredArgsConstructor

public class ReportRequest {
    @JsonProperty("approval_level")
    private int approvalLevel;

    private int pageNumber = 0;
    private int pageSize = 10;


    private String batchNumber;
    private String beneficiary;
    private String phoneNo;
    private String requestType;
    private String budgetLine;
    private String region;
    private String county;
    private String district;
    private String facility;
    private String dotName;
    private String dotPhone;
    private BigDecimal amount;
    private BigDecimal dotAmount;
    private Integer orgId;
    private Integer page;
    private String province;
    List<String> counties;
    private String paymentType;
    String dateFrom;
    String dateTo;
    private String keyword;
    private String id;
    private String month;
    private String year;
    private String status;
    private String currentLevel;
    private Integer approval;





}
