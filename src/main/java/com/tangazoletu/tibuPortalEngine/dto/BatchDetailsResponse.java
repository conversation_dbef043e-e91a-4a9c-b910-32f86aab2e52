package com.tangazoletu.tibuPortalEngine.dto;

import com.tangazoletu.tibuPortalEngine.entities.ApiOrderApprovalSummaryView;
import com.tangazoletu.tibuPortalEngine.entities.BatchMetadataView;
import com.tangazoletu.tibuPortalEngine.entities.ViewPendingRecipients;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.domain.Page;

import java.util.List;

@Data
@AllArgsConstructor
@Builder
public class BatchDetailsResponse {
    private Page<ApiOrderApprovalSummaryView> approvalSummaryPage;
    private Page<ViewPendingRecipients> pendingRecipientsPage;
    private Page<BatchMetadataView> batchMetadataViewPage;
    private boolean permission;
    private Page<BatchApprovalSummaryDTO> batches;

}
