package com.tangazoletu.tibuPortalEngine.dto;

import com.tangazoletu.tibuPortalEngine.enums.Module;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class RoleResponse {
    private String responseCode;
    private String responseMessage;
    Page<?> data;
    private String roleTitle;
    private String roleOrgId;

    private List<Long> inRolePermissionIds; // permission IDs in the role
    private List<Module> modules; // distinct module names
    private Map<String, List<PermissionDto>> permissionsByModule; // grouped by modul
}
