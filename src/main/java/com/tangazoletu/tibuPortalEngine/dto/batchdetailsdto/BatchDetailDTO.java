package com.tangazoletu.tibuPortalEngine.dto.batchdetailsdto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;

@Data
@Builder
public class BatchDetailDTO {
    private Long id;
    private String batchNo;
    private String month;
    private Integer year;
    private Timestamp requestTime;
    private String initiatedBy;  // or "Supervisor Name" based on query
    private String phoneNo;
    private String mpesaConfirmation; // if approvalLevel == 6
    private String requestType;
    private String beneficiary;
    private String budgetLine;
    private String region;
    private String county;
    private BigDecimal dotAmount;
    private String driver;
    private BigDecimal driverAmount;
    private String approvalStatus;
    private BigDecimal amount;
    private String confirmable; // optional column added if approvalLevel matches
    private BigDecimal total;
    private BigDecimal patientAmount;
    private String dotNurseName;
    private String patientNumber;
    private String treatmentStartDate;
    private String facility;
}
