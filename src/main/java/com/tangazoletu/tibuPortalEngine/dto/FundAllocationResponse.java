package com.tangazoletu.tibuPortalEngine.dto;

import com.tangazoletu.tibuPortalEngine.entities.FundAllocationView;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.domain.Page;

import java.util.Optional;

@Data
@Builder
public class FundAllocationResponse {
    private String responseMessage;
    private String responseCode;
    Page<?> data;
    Optional<FundAllocationView> fundAllocationView;
}
