package com.tangazoletu.tibuPortalEngine.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Min;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class FundsTransferRequest {
    private int page;
    private int pageSize=10;
    private List<Integer> orgFilterList;
    private String keyword; // New field for search query
}
