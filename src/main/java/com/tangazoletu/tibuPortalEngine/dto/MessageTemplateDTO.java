package com.tangazoletu.tibuPortalEngine.dto;

import com.tangazoletu.tibuPortalEngine.entities.MessageTemplate;
import com.tangazoletu.tibuPortalEngine.entities.Organisation;
import lombok.*;

import java.sql.Timestamp;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageTemplateDTO {
    private Long id;

    private Long paymentType;

    private String message;

    private Organisation organisation;

    private Long createdBy;

    private Timestamp createdAt;

    private String inTrash;

    // Manual constructor for mapping from MessageTemplate entity
    public MessageTemplateDTO(MessageTemplate messageTemplate) {
        this.id = messageTemplate.getId();
        this.paymentType = messageTemplate.getPaymentType();
        this.message = messageTemplate.getMessage();
        this.organisation = messageTemplate.getOrganisation();
        this.createdBy = messageTemplate.getCreatedBy();
        this.createdAt = Timestamp.valueOf(messageTemplate.getCreatedAt());
        this.inTrash = messageTemplate.getInTrash();
    }
}
