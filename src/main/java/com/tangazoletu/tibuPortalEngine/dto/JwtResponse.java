package com.tangazoletu.tibuPortalEngine.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
public class JwtResponse {
    private String responseCode;
    private String accessToken;
    private String refreshToken;
    private String secretKey;
    private String username;
    private String organization;
    private String email;
    private Integer region;
    private Long userId;
    private Long orgId;


    public JwtResponse() {
    }

    public JwtResponse(JwtResponse response) {
        this.accessToken = response.getAccessToken();
        this.refreshToken = response.getRefreshToken();
        this.secretKey = response.getSecretKey();
        this.username = response.getUsername();
        this.organization = response.getOrganization();
        this.email = response.getEmail();
        this.region = response.getRegion();
        this.userId = response.getUserId();
        this.responseCode = response.getResponseCode();
        this.orgId = response.getOrgId();
    }

    public JwtResponse(String responseCode,String accessToken, String refreshToken, String secretKey,
                       String username, String organization,String email,Integer province,Long userId, Long orgId) {
        this.responseCode = responseCode;
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        this.secretKey = secretKey;
        this.username = username;
        this.organization = organization;
        this.email = email;
        this.region = province;
        this.userId = userId;
        this.orgId = orgId;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getOrganization() {
        return organization;
    }

    public void setOrganization(String organization) {
        this.organization = organization;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getRegion() {
        return region;
    }

    public void setRegion(Integer region) {
        this.region = region;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }
}

