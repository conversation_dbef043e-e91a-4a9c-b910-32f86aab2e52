package com.tangazoletu.tibuPortalEngine.dto;

import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@Builder
public class MdrDotPaymetsRequest {
    private String keyword;
    private Integer province;
    private Integer budgetId;
    private String budget;
    private String county;
    private Integer countyId;
    private String month;
    private String year;
    private Date startDate;
    private Date endDate;
    private String startTimestamp; // Holds a Timestamp
    private String endTimestamp; // Holds a Timestamp
    private Integer page;
    private Integer pageSize;
    List<Integer> orgIds;


}
