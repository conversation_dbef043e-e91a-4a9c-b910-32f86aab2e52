package com.tangazoletu.tibuPortalEngine.dto;

import com.tangazoletu.tibuPortalEngine.entities.Organisation;
import com.tangazoletu.tibuPortalEngine.entities.User;
import com.tangazoletu.tibuPortalEngine.entities.UserView;
import jakarta.persistence.Column;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDTO {
    private Long id;

    private String organisation;

    private String username;

    private String firstname;

    private String lastname;

    private String roles;

    private String email;

    private String inTrash;

    private String status;


    public UserDTO(UserView user) {
        this.id = user.getId();
        this.username = user.getUsername();
        this.firstname = user.getFirstname();
        this.lastname = user.getLastname();
        this.inTrash = user.getInTrash();
        this.roles = user.getRoles();
        this.organisation = user.getOrganisation();
        this.status = user.getStatus();
        this.email = user.getEmail();
    }

    public UserDTO(Long primaryKey, String organisation, String username,
                           String firstname, String lastname, String roles) {
        this.id = primaryKey;
        this.organisation = organisation;
        this.username = username;
        this.firstname = firstname;
        this.lastname = lastname;
        this.roles = roles;
    }

}