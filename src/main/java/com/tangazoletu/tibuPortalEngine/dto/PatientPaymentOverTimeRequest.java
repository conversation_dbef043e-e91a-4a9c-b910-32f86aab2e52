package com.tangazoletu.tibuPortalEngine.dto;

import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@Builder
public class PatientPaymentOverTimeRequest {
    private String keyword;
    private Integer province;
    private Integer countyId;
    private Integer orderId;
    private String month;
    private String year;
    private Date startDate;
    private Date endDate;
    private String startTimestamp; // Holds a Timestamp
    private String endTimestamp; // Holds a Timestamp
    private Integer page;
    private Integer pageSize;
    List<Integer> orgIds;
    private String region;


}
