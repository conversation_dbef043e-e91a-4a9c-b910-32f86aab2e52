package com.tangazoletu.tibuPortalEngine.dto;

import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Data
@Builder
public class ColumnResizeRow {
    private String name;
    private String county;
    private String station;
    private String designation;
    private String jobGroup;
    private Map<String, Object> dayAttendance;
    private String telephoneNumber;
    private BigDecimal rate;
    private BigDecimal amount;
    private BigDecimal lessLunch;
    private BigDecimal transport;
    private BigDecimal extraPerDiem;
    private BigDecimal others;
    private BigDecimal netPay;
    private String approve;
    private String process;
    private String comments;
    private Long attendanceId;
    private BigDecimal total;
    private String status;
    private String processingComments;
    private String approvalComments;
    private String idNumber;
    private Integer payableTravellingDays;
    private String noOfDays;
}
