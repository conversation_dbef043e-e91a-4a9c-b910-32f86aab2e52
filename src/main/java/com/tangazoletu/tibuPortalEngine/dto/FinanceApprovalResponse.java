package com.tangazoletu.tibuPortalEngine.dto;

import com.tangazoletu.tibuPortalEngine.dto.paymentapidto.FinanceApprovalPageWrapper;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.domain.Page;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
@Builder
public class FinanceApprovalResponse {
    private String responseCode;
    private String responseMessage;
    private FinanceApprovalPageWrapper data; // instead of Page<?>
}
