package com.tangazoletu.tibuPortalEngine.dto;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class DotNurseSummaryRequest {
    Integer year;
    Integer month;
    Integer day;
    private DotNurseSummaryRequest.SearchQuery search; // New field for search query
    private String province;
    private String budget;
    private String county;
    private String DateFrom; // Holds a Timestamp
    private String DateTo; // Holds a Timestamp
    private Integer region;

    @Data
    @Builder
    public static class SearchQuery {
        private String value; // Holds the search query value
        private boolean regex; // Holds the regex flag
    }
}
