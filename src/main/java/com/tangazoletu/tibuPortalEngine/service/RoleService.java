package com.tangazoletu.tibuPortalEngine.service;

import com.tangazoletu.tibuPortalEngine.dto.PermissionDto;
import com.tangazoletu.tibuPortalEngine.dto.RolePermissionRequest;
import com.tangazoletu.tibuPortalEngine.dto.RoleResponse;
import com.tangazoletu.tibuPortalEngine.entities.Role;
import com.tangazoletu.tibuPortalEngine.enums.Module;
import com.tangazoletu.tibuPortalEngine.repositories.PermissionMapRepo;
import com.tangazoletu.tibuPortalEngine.repositories.PermissionRepo;
import com.tangazoletu.tibuPortalEngine.repositories.RoleRepo;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class RoleService {

    private final RoleRepo roleRepo;
    private final PermissionRepo permissionRepo;
    private final PermissionMapRepo permissionMapRepo;

    public RoleResponse getRoles(RolePermissionRequest rolePermissionRequest,
                                 HttpServletRequest request,
                                 HttpServletResponse response) {

        RoleResponse roleResponse = RoleResponse.builder()
                .responseCode("01")
                .responseMessage("Failed")
                .build();

        try {
            String orgId = rolePermissionRequest.getOrgId();
            String keyword = rolePermissionRequest.getKeyword();
            int page = rolePermissionRequest.getPage();
            int pageSize = rolePermissionRequest.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);
            String roleId = rolePermissionRequest.getId();

            // If roleId is provided and not empty, fetch role and permissions
            if (roleId != null && !roleId.isEmpty()) {
                String userId = rolePermissionRequest.getUserId();

                // 1. Get role
                String roleTitle = null;
                String roleOrgId = null;
                Optional<Role> roleOpt = roleRepo.findById(Integer.parseInt(roleId));
                if (roleOpt.isPresent()) {
                    Role role = roleOpt.get();
                    roleTitle = role.getTitle();
                    roleOrgId = String.valueOf(role.getOrgId());
                }

                // 2. Get permission IDs for the role
                List<Long> inRole = permissionMapRepo.findPermissionIdsByRole(Integer.parseInt(roleId));

                // 3. Get modules excluding 'System'
                List<com.tangazoletu.tibuPortalEngine.enums.Module> modules = permissionRepo.findDistinctModulesExcludingSystem(Module.System);

                // 4. Get permissions by module for the user
                Map<String, List<PermissionDto>> permissionsByModule = new HashMap<>();
                for (com.tangazoletu.tibuPortalEngine.enums.Module module : modules) {
                    List<PermissionDto> permissions = permissionRepo.findPermissionsByModuleAndUser(module, userId);
                    permissionsByModule.put(module.getDbValue(), permissions); // use readable string key
                }



                return RoleResponse.builder()
                        .responseCode("00")
                        .responseMessage("Success")
                        .roleTitle(roleTitle)
                        .roleOrgId(roleOrgId)
                        .inRolePermissionIds(inRole)
                        .modules(modules)
                        .permissionsByModule(permissionsByModule)
                        .build();
            } else {
                // No roleId provided: return paginated role list
                Page<Object[]> resultPage = roleRepo.getFilteredRoles(orgId, keyword, pageable);

                Page<Map<String, Object>> mappedPage = resultPage.map(row -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("primarykey", row[0]);
                    map.put("ID", row[1]);
                    map.put("organisation", row[2]);
                    map.put("role", row[3]);
                    map.put("timeCreated", row[4]);
                    map.put("orgId", row[5]);
                    String permissionsStr = (String) row[6];
                    List<Integer> permissionsList = (permissionsStr != null && !permissionsStr.isEmpty())
                            ? Arrays.stream(permissionsStr.split(","))
                            .map(String::trim)
                            .map(Integer::parseInt)
                            .collect(Collectors.toList())
                            : Collections.emptyList();
                    map.put("Permissions", permissionsList);
                    return map;
                });

                return RoleResponse.builder()
                        .responseCode("00")
                        .responseMessage("Success")
                        .data(mappedPage)
                        .build();
            }

        } catch (Exception e) {
            log.error("An error occurred fetching roles: {}, request: {}", e.getMessage(), rolePermissionRequest);
            e.printStackTrace();
            return roleResponse;
        }
    }



}
