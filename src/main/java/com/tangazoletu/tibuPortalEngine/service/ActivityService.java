package com.tangazoletu.tibuPortalEngine.service;

import com.tangazoletu.tibuPortalEngine.dto.ActivityRequest;
import com.tangazoletu.tibuPortalEngine.dto.ApiResponse;
import com.tangazoletu.tibuPortalEngine.enums.ApiResponseCode;
import com.tangazoletu.tibuPortalEngine.repositories.ActivityRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.sql.Date;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class ActivityService {
    private final ActivityRepository activityRepository;

    public ApiResponse getActivity(@RequestBody ActivityRequest request) {
        try {
            Pageable pageable = PageRequest.of(request.getPageNumber(), request.getPageSize());

            // Get filters from the request payload
            String organisation = request.getOrganisation();
            String budget = request.getBudget();
            String status = request.getStatus();
            String venue = request.getVenue();
            Date startDt = request.getStartDate() != null ? Date.valueOf(request.getStartDate()) : null;
            Date endDt = request.getEndDate() != null ? Date.valueOf(request.getEndDate()) : null;


            Page<Object[]> page = activityRepository.fetchFilteredEvents(
                    organisation, budget, status, venue, startDt, endDt, pageable);

            List<Map<String, Object>> reports = new ArrayList<>();
            for (Object[] row : page.getContent()) {
                Map<String, Object> record = new HashMap<>();
                record.put("primaryKey", row[0]);
                record.put("organisation", row[1]);
                record.put("activityCode", row[2]);
                record.put("activityName", row[3]);
                record.put("budget", row[4]);
                record.put("venue", row[5]);
                record.put("startDate", row[6]);
                record.put("endDate", row[7]);
                record.put("status", row[8]);
                reports.add(record);
            }

            Map<String, Object> paginatedResponse = new HashMap<>();
            paginatedResponse.put("content", reports);
            paginatedResponse.put("totalElements", page.getTotalElements());
            paginatedResponse.put("totalPages", page.getTotalPages());
            paginatedResponse.put("pageNumber", page.getNumber());
            paginatedResponse.put("pageSize", page.getSize());

            return ApiResponse.builder()
                    .responseCode(ApiResponseCode.SUCCESS)
                    .responseMessage("Filtered report fetched successfully.")
                    .jdata(paginatedResponse)
                    .build();

        } catch (Exception ex) {
            log.error("Error while fetching filtered activity report", ex);
            return ApiResponse.builder()
                    .responseCode(ApiResponseCode.FAIL)
                    .responseMessage("Error while fetching activity report.")
                    .build();
        }
    }
}
