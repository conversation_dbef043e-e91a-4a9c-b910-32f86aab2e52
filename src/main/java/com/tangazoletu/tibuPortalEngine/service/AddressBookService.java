package com.tangazoletu.tibuPortalEngine.service;

import com.tangazoletu.tibuPortalEngine.dto.AddressBookDTO;
import com.tangazoletu.tibuPortalEngine.dto.AddressBookRequest;
import com.tangazoletu.tibuPortalEngine.repositories.AddressBookRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
public class AddressBookService {

    private final AddressBookRepository addressBookRepository;

    public AddressBookService(AddressBookRepository addressBookRepository) {
        this.addressBookRepository = addressBookRepository;
    }
    public Page<AddressBookDTO> getFilteredBooks(AddressBookRequest request) {
        Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize());

        if (request.getId() != null) {
            Optional<AddressBookDTO> dtoOpt = addressBookRepository.findById(request.getId())
                    .map(AddressBookDTO::new);
            return dtoOpt.map(dto -> new PageImpl<>(List.of(dto), pageable, 1))
                    .orElse(new PageImpl<>(Collections.emptyList(), pageable, 0));
        }

        if (request.getTitle() != null) {
            return addressBookRepository
                    .findByTitleContainingIgnoreCase(request.getTitle(), pageable)
                    .map(AddressBookDTO::new);
        }

        return addressBookRepository.findAll(pageable).map(AddressBookDTO::new);
    }

}



