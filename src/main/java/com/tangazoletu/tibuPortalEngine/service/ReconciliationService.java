package com.tangazoletu.tibuPortalEngine.service;

import com.tangazoletu.tibuPortalEngine.dto.ReconciliationRequest;
import com.tangazoletu.tibuPortalEngine.dto.ReconciliationResponse;
import com.tangazoletu.tibuPortalEngine.dto.UserReportsRequest;
import com.tangazoletu.tibuPortalEngine.dto.UserResponse;
import com.tangazoletu.tibuPortalEngine.entities.*;
import com.tangazoletu.tibuPortalEngine.enums.ApiResponse;
import com.tangazoletu.tibuPortalEngine.enums.ApiResponseCode;
import com.tangazoletu.tibuPortalEngine.entities.*;
import com.tangazoletu.tibuPortalEngine.enums.ApiResponse;
import com.tangazoletu.tibuPortalEngine.repositories.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

import static org.apache.commons.compress.utils.ArchiveUtils.sanitize;

@Service
@RequiredArgsConstructor
@Slf4j
public class ReconciliationService {
    private final PaymentsRepo paymentsRepo;
    private final OrganizationRepository organizationRepository;
    private final PendingPaymentViewRepository pendingPaymentViewRepository;
    private final UnprocessedActualPaymentViewRepository unprocessedActualPaymentViewRepository;
    private final ApiOrderRequestViewRepository apiOrderRequestViewRepository;
    private final ApiOrderApprovalTimelineViewRepository apiOrderApprovalTimelineViewRepository;
    private final BatchFileProcessRepository batchFileProcessRepository;
    private final ActualPaymentsRepo actualPaymentsRepo;
    private final GatewayViewRepository gatewayViewRepository;


    public ReconciliationResponse getGateway(ReconciliationRequest request, HttpServletResponse httpServletResponse, HttpServletRequest httpServletRequest) {

        ReconciliationResponse reconciliationResponse = ReconciliationResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();
        try {

            int page = request.getPage();
            int pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);
            String keyword = request.getKeyword();
            Long orgId = null;
            if (request.getOrgId() != null) {
             orgId  = Long.valueOf(request.getOrgId());
            }
            if (request.getId() != null) {
                Optional<Payment> payments = paymentsRepo.findByIdAndTrxStatus(request.getId(), Payment.TrxStatus.Completed);
                if (payments.isPresent()) {
                    reconciliationResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    reconciliationResponse.setResponseMessage("Success");
                    reconciliationResponse.setData1(payments);
                }
            }else {


                Page<GatewayView> payments = gatewayViewRepository.searchGatewayData(keyword, Math.toIntExact(orgId), pageable);
                Optional<Organisation> organisation = organizationRepository.findById(orgId);

                if (payments.getTotalElements() == 0) {
                    log.info("No payments found for orgId: {}, keyword: {}", orgId, keyword);

                } else {
                    reconciliationResponse.setResponseMessage("Success");
                    reconciliationResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    reconciliationResponse.setData(payments);
                }
            }

        }catch (Exception e){
            reconciliationResponse.setResponseMessage("Failed");
            reconciliationResponse.setResponseCode(ApiResponse.FAIL.getCode());
            log.info("getGateway error", e.getMessage());
            e.printStackTrace();
        }
        return reconciliationResponse;


    }

    public ReconciliationResponse getGatewayNotMpesa(ReconciliationRequest request, HttpServletResponse httpServletResponse, HttpServletRequest httpServletRequest) {

        ReconciliationResponse reconciliationResponse = ReconciliationResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();
        try {

            int page = request.getPage();
            int pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);
            String keyword = request.getKeyword();
            Long orgId = null;
            if (request.getOrgId() != null) {
                orgId  = Long.valueOf(request.getOrgId());
            }
            if (request.getId() != null) {
                Optional<PendingPaymentView> pendingPaymentViews = pendingPaymentViewRepository.findById(Long.valueOf(request.getId()));
                if (pendingPaymentViews.isPresent()) {
                    reconciliationResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    reconciliationResponse.setResponseMessage("Success");
                    reconciliationResponse.setData1(pendingPaymentViews);
                }
            }else {


                Page<PendingPaymentView> pendingPaymentViews = pendingPaymentViewRepository.findAllByTrxStatusAndOrd(Math.toIntExact(orgId), keyword, pageable);
                Optional<Organisation> organisation = organizationRepository.findById(orgId);
                pendingPaymentViews.forEach(payment -> {
                    payment.setOrgName(organisation.get().getName());
                });
                if (pendingPaymentViews.getTotalElements() == 0) {
                    log.info("No payments found for orgId: {}, keyword: {}", orgId, keyword);

                } else {
                    reconciliationResponse.setResponseMessage("Success");
                    reconciliationResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    reconciliationResponse.setData(pendingPaymentViews);
                }
            }

        }catch (Exception e){
            reconciliationResponse.setResponseMessage("Failed");
            reconciliationResponse.setResponseCode(ApiResponse.FAIL.getCode());
            log.info("getGateway error", e.getMessage());
            e.printStackTrace();
        }
        return reconciliationResponse;


    }
    public ReconciliationResponse getMpesaNotGateway(ReconciliationRequest request, HttpServletResponse httpServletResponse, HttpServletRequest httpServletRequest) {

        ReconciliationResponse reconciliationResponse = ReconciliationResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();
        try {

            int page = request.getPage();
            int pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);
            String keyword = request.getKeyword();
            Integer orgId = null;
            if (request.getOrgId() != null) {
                orgId  = request.getOrgId();
            }



                Page<UnprocessedActualPaymentView> unprocessedActualPaymentViews = unprocessedActualPaymentViewRepository.searchByKeyword(keyword, orgId, pageable);
                Optional<Organisation> organisation = organizationRepository.findById(Long.valueOf(orgId));
            unprocessedActualPaymentViews.forEach(payment -> {
                    payment.setOrgName(organisation.get().getName());
                });
                if (unprocessedActualPaymentViews.getTotalElements() == 0) {
                    reconciliationResponse.setResponseMessage("No records found");
                    reconciliationResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    log.info("No payments found for orgId: {}, keyword: {}", orgId, keyword);

                } else {
                    reconciliationResponse.setResponseMessage("Success");
                    reconciliationResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    reconciliationResponse.setData(unprocessedActualPaymentViews);
                }


        }catch (Exception e){
            reconciliationResponse.setResponseMessage("Failed");
            reconciliationResponse.setResponseCode(ApiResponse.FAIL.getCode());
            log.info("getGateway error", e.getMessage());
            e.printStackTrace();
        }
        return reconciliationResponse;


    }
    public ReconciliationResponse getSupervisionExpenses(ReconciliationRequest request, HttpServletResponse httpServletResponse, HttpServletRequest httpServletRequest) {

        ReconciliationResponse reconciliationResponse = ReconciliationResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();
        try {

            int page = request.getPage();
            int pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);
            Integer orgId = null;
            if (request.getOrgId() != null) {
                orgId  = request.getOrgId();
            }
            String keyword = sanitize(request.getKeyword());
            String province = sanitize(request.getProvince());
            Integer budget = request.getBudget();
            String batchNo = sanitize(request.getBatch());
            String county = sanitize(request.getCounty());

            LocalDateTime dateFrom = null;
            LocalDateTime dateTo = null;

            if (request.getDateFrom() != null && !request.getDateFrom().trim().isEmpty()) {
                try {
                    dateFrom = Instant.ofEpochMilli(Long.parseLong(request.getDateFrom().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateFrom value: {}", request.getDateFrom());
                }
            }

            if (request.getDateTo() != null && !request.getDateTo().trim().isEmpty()) {
                try {
                    dateTo = Instant.ofEpochMilli(Long.parseLong(request.getDateTo().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateTo value: {}", request.getDateTo());
                }
            }

            Page<ApiOrderRequestView> apiOrderRequestViews = apiOrderRequestViewRepository.filterApiOrders(
                    orgId, keyword, province, budget, county, batchNo, dateFrom, dateTo, pageable
            );
            if (orgId != null) {
                Optional<Organisation> organisation = organizationRepository.findById(Long.valueOf(orgId));
                apiOrderRequestViews.forEach(payment -> {
                    payment.setOrgName(organisation.get().getName());
                });
            }
            if (apiOrderRequestViews.getTotalElements() == 0) {
                reconciliationResponse.setResponseMessage("No records found");
                reconciliationResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                log.info("No payments found for orgId: {}, keyword: {}", orgId, keyword);

            } else {
                reconciliationResponse.setResponseMessage("Success");
                reconciliationResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                reconciliationResponse.setData(apiOrderRequestViews);
            }


        }catch (Exception e){
            reconciliationResponse.setResponseMessage("Failed");
            reconciliationResponse.setResponseCode(ApiResponse.FAIL.getCode());
            log.info("getGateway error", e.getMessage());
            e.printStackTrace();
        }
        return reconciliationResponse;


    }
    public ReconciliationResponse getApprovalTurnroundTime(ReconciliationRequest request, HttpServletResponse httpServletResponse, HttpServletRequest httpServletRequest) {

        ReconciliationResponse reconciliationResponse = ReconciliationResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();
        try {

            int page = request.getPage();
            int pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);
            Integer orgId = null;
            if (request.getOrgId() != null) {
                orgId  = request.getOrgId();
            }
            String keyword = sanitize(request.getKeyword());
            String province = sanitize(request.getProvince());
            Integer budget = request.getBudget();
            String batchNo = sanitize(request.getBatch());
            String county = sanitize(request.getCounty());

            LocalDateTime dateFrom = null;
            LocalDateTime dateTo = null;

            if (request.getDateFrom() != null && !request.getDateFrom().trim().isEmpty()) {
                try {
                    dateFrom = Instant.ofEpochMilli(Long.parseLong(request.getDateFrom().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateFrom value: {}", request.getDateFrom());
                }
            }

            if (request.getDateTo() != null && !request.getDateTo().trim().isEmpty()) {
                try {
                    dateTo = Instant.ofEpochMilli(Long.parseLong(request.getDateTo().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateTo value: {}", request.getDateTo());
                }
            }

            Page<ApiOrderApprovalTimelineView> apiOrderApprovalTimelineViews = apiOrderApprovalTimelineViewRepository.searchWithFilters(
                     province,  county, keyword,  dateFrom, dateTo, pageable
            );
            if (orgId != null) {
                Optional<Organisation> organisation = organizationRepository.findById(Long.valueOf(orgId));
                apiOrderApprovalTimelineViews.forEach(payment -> {
                    payment.setOrgName(organisation.get().getName());
                });
            }
            if (apiOrderApprovalTimelineViews.getTotalElements() == 0) {
                reconciliationResponse.setResponseMessage("No records found");
                reconciliationResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                log.info("No payments found for orgId: {}, keyword: {}", orgId, keyword);

            } else {
                reconciliationResponse.setResponseMessage("Success");
                reconciliationResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                reconciliationResponse.setData(apiOrderApprovalTimelineViews);
            }


        }catch (Exception e){
            reconciliationResponse.setResponseMessage("Failed");
            reconciliationResponse.setResponseCode(ApiResponse.FAIL.getCode());
            log.info("getGateway error", e.getMessage());
            e.printStackTrace();
        }
        return reconciliationResponse;


    }
    public  ReconciliationResponse getMpesaUploads(ReconciliationRequest request, HttpServletResponse httpServletResponse, HttpServletRequest httpServletRequest) {
        ReconciliationResponse reconciliationResponse = ReconciliationResponse.builder()
                .responseCode(ApiResponseCode.FAIL.getCode())
                .responseMessage("Failed to fetch user report")
                .build();
        try {
            int page = request.getPage();
            int pageSize = request.getPageSize();
            int orgId = 0;
            if (request.getOrgId() != null) {
                 orgId = request.getOrgId();
            }
            Pageable pageable = PageRequest.of(page, pageSize);
            String keyword = request.getKeyword();
            LocalDateTime dateFrom = null;
            LocalDateTime dateTo = null;
            if (request.getId() != null) {
                Optional<BatchFileProcess> batchFileProcess = batchFileProcessRepository.findById(request.getId());
                if (batchFileProcess.isPresent()) {
                    reconciliationResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                    reconciliationResponse.setResponseMessage("Filtered user report fetched successfully.");
                    reconciliationResponse.setData1(batchFileProcess);
                }
            }

            if (request.getDateFrom() != null && !request.getDateFrom().trim().isEmpty()) {
                try {
                    dateFrom = Instant.ofEpochMilli(Long.parseLong(request.getDateFrom().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateFrom value: {}", request.getDateFrom());
                }
            }

            if (request.getDateTo() != null && !request.getDateTo().trim().isEmpty()) {
                try {
                    dateTo = Instant.ofEpochMilli(Long.parseLong(request.getDateTo().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateTo value: {}", request.getDateTo());
                }
            }

            Page<BatchFileProcess> auditActivity = batchFileProcessRepository.searchBatchFiles(orgId,keyword,dateFrom,dateTo,pageable);
            if (auditActivity.getTotalElements() > 0) {
                reconciliationResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                reconciliationResponse.setResponseMessage("Filtered user report fetched successfully.");
                reconciliationResponse.setData(auditActivity);
            }else {
                reconciliationResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                reconciliationResponse.setResponseMessage("No records for  user report.");
            }



        }catch (Exception ex) {
            log.error("Error while fetching user report", ex);
            reconciliationResponse.setResponseCode(ApiResponseCode.FAIL.getCode());
            reconciliationResponse.setResponseMessage("Failed to fetch user report");
            ex.printStackTrace();
        }
        return reconciliationResponse;
    }
    public  ReconciliationResponse getMpesaActualPayments(ReconciliationRequest request, HttpServletResponse httpServletResponse, HttpServletRequest httpServletRequest) {
        ReconciliationResponse reconciliationResponse = ReconciliationResponse.builder()
                .responseCode(ApiResponseCode.FAIL.getCode())
                .responseMessage("Failed to fetch user report")
                .build();
        try {
            int page = request.getPage();
            int pageSize = request.getPageSize();

            Pageable pageable = PageRequest.of(page, pageSize);
            String keyword = request.getKeyword();
            LocalDateTime dateFrom = null;
            LocalDateTime dateTo = null;
            if (request.getId() != null) {
                Optional<ActualPayment> actualPayment = actualPaymentsRepo.findById(Long.valueOf(request.getId()));
                if (actualPayment.isPresent()) {
                    reconciliationResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                    reconciliationResponse.setResponseMessage("Filtered user report fetched successfully.");
                    reconciliationResponse.setData1(actualPayment);
                }
            }

            if (request.getDateFrom() != null && !request.getDateFrom().trim().isEmpty()) {
                try {
                    dateFrom = Instant.ofEpochMilli(Long.parseLong(request.getDateFrom().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateFrom value: {}", request.getDateFrom());
                }
            }

            if (request.getDateTo() != null && !request.getDateTo().trim().isEmpty()) {
                try {
                    dateTo = Instant.ofEpochMilli(Long.parseLong(request.getDateTo().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateTo value: {}", request.getDateTo());
                }
            }
            int orgId = 0;
            if (request.getOrgId() != null) {
                orgId = request.getOrgId();
            }

            Page<ActualPayment> actualPayments = actualPaymentsRepo.findFilteredPayments(orgId,keyword,dateFrom,dateTo,pageable);
            if (actualPayments.getTotalElements() > 0) {
                reconciliationResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                reconciliationResponse.setResponseMessage("Filtered user report fetched successfully.");
                reconciliationResponse.setData(actualPayments);
            }else {
                reconciliationResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                reconciliationResponse.setResponseMessage("No records for  user report.");
            }



        }catch (Exception ex) {
            log.error("Error while fetching user report", ex);
            reconciliationResponse.setResponseCode(ApiResponseCode.FAIL.getCode());
            reconciliationResponse.setResponseMessage("Failed to fetch user report");
            ex.printStackTrace();
        }
        return reconciliationResponse;
    }


    private String sanitize(String value) {
        return (value == null || value.trim().isEmpty()) ? null : value.trim();
    }







}
