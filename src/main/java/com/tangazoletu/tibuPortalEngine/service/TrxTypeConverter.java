package com.tangazoletu.tibuPortalEngine.service;


import com.tangazoletu.tibuPortalEngine.enums.TrxType;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter(autoApply = true)
public class TrxTypeConverter implements AttributeConverter<TrxType, String> {

    @Override
    public String convertToDatabaseColumn(TrxType attribute) {
        return attribute != null ? attribute.toString() : null;
    }

    @Override
    public TrxType convertToEntityAttribute(String dbValue) {
        return TrxType.fromDbValue(dbValue);
    }
}

