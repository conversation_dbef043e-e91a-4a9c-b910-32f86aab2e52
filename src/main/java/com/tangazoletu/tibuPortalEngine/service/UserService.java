package com.tangazoletu.tibuPortalEngine.service;

import com.tangazoletu.tibuPortalEngine.dto.*;
import com.tangazoletu.tibuPortalEngine.entities.*;
import com.tangazoletu.tibuPortalEngine.dto.*;
import com.tangazoletu.tibuPortalEngine.entities.*;
import com.tangazoletu.tibuPortalEngine.dto.ApiResponse;
import com.tangazoletu.tibuPortalEngine.dto.ReportRequest;
import com.tangazoletu.tibuPortalEngine.dto.UserRequest;
import com.tangazoletu.tibuPortalEngine.enums.ApiResponseCode;
import com.tangazoletu.tibuPortalEngine.repositories.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import com.tangazoletu.tibuPortalEngine.repositories.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import com.tangazoletu.tibuPortalEngine.repositories.UserRepository;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@Data
public class UserService {
    private  final  UserRepository  userRepository;
    private final UserViewRepository userViewRepository;
    private final ActivityLogViewRepository activityLogViewRepository;
    private final ParamRepository paramRepository;
    private final ApiParamsRepository apiParamsRepository;
    private final OrganizationRepository organizationRepository;
    private final PermissionRepo permissionRepo;
    private final BatchFileProcessRepository batchFileProcessRepository;

    public UserResponse getUser(@RequestBody UserRequest request) {
        UserResponse userResponse = UserResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.SUCCESS.getCode())
                .build();
        try {

            Pageable pageable = PageRequest.of(request.getPageNumber(), request.getPageSize());

            Page<UserView> page = userViewRepository.fetchUsersFiltered(
                    request.getOrganisation(),
                    request.getUsername(),
                    request.getFirstName(),
                    request.getLastName(),
                    request.getRole(),
                    request.getEmail(),
                    pageable
            );
            if (page.getTotalElements() > 0) {
                userResponse.setData(page);
                userResponse.setResponseMessage("success");
                userResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
            }


            return userResponse;

        } catch (Exception ex) {
            log.error("Error while fetching filtered user report", ex);
            return userResponse.builder()
                    .responseCode(ApiResponseCode.FAIL.getCode())
                    .responseMessage("Error while fetching user report.")
                    .build();
        }
    }
    public UserResponse getAuditTrail(UserReportsRequest request, HttpServletResponse httpServletResponse, HttpServletRequest httpServletRequest) {
        UserResponse userResponse = UserResponse.builder()
                .responseCode(ApiResponseCode.FAIL.getCode())
                .responseMessage("Failed to fetch user report")
                .build();
        try {
            int page = request.getPage();
            int pageSize = request.getPageSize();
            int orgId = 0;
            if (request.getOrgId() !=null) {
                orgId = request.getOrgId();
            }
            Pageable pageable = PageRequest.of(page, pageSize);
            String keyword = request.getKeyword();
            LocalDateTime dateFrom = null;
            LocalDateTime dateTo = null;
            String user = request.getUser();

            if (request.getDateFrom() != null && !request.getDateFrom().trim().isEmpty()) {
                try {
                    dateFrom = Instant.ofEpochMilli(Long.parseLong(request.getDateFrom().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateFrom value: {}", request.getDateFrom());
                }
            }

            if (request.getDateTo() != null && !request.getDateTo().trim().isEmpty()) {
                try {
                    dateTo = Instant.ofEpochMilli(Long.parseLong(request.getDateTo().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateTo value: {}", request.getDateTo());
                }
            }

            Page<ActivityLogView> auditActivity = activityLogViewRepository.filterActivityLogs(orgId,keyword,user,dateFrom,dateTo,pageable);
            if (auditActivity.getTotalElements() > 0) {
                userResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                userResponse.setResponseMessage("Filtered user report fetched successfully.");
                userResponse.setData(auditActivity);
            }else {
                userResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                userResponse.setResponseMessage("No records for  user report.");
            }



        }catch (Exception ex) {
            log.error("Error while fetching user report", ex);
            userResponse.setResponseCode(ApiResponseCode.FAIL.getCode());
            userResponse.setResponseMessage("Failed to fetch user report");
            ex.printStackTrace();
        }
        return userResponse;
    }
    public UserResponse getApiParam(UserReportsRequest request, HttpServletResponse httpServletResponse, HttpServletRequest httpServletRequest) {
        UserResponse userResponse = UserResponse.builder()
                .responseCode(ApiResponseCode.FAIL.getCode())
                .responseMessage("Failed to fetch user report")
                .build();
        try {
            int page = request.getPage();
            int pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);
            String keyword = request.getKeyword();
            if (request.getId() != null) {

                Optional<Param> params = paramRepository.findById(Long.valueOf(request.getId()));
                if (params.isPresent()) {
                    userResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                    userResponse.setResponseMessage("Filtered user report fetched successfully.");
                    userResponse.setParam(params);
                }
            }

            Page<Param> params = paramRepository.searchByKeyword(keyword,pageable);
            if (params.getTotalElements() > 0) {
                userResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                userResponse.setResponseMessage("Filtered user report fetched successfully.");
                userResponse.setData(params);
            }else {
                userResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                userResponse.setResponseMessage("No records for  user report.");
            }



        }catch (Exception ex) {
            log.error("Error while fetching user report", ex);
            userResponse.setResponseCode(ApiResponseCode.FAIL.getCode());
            userResponse.setResponseMessage("Failed to fetch user report");
            ex.printStackTrace();
        }
        return userResponse;
    }
    public UserResponse getB2AccountSettings(UserReportsRequest request, HttpServletResponse httpServletResponse, HttpServletRequest httpServletRequest) {
        UserResponse userResponse = UserResponse.builder()
                .responseCode(ApiResponseCode.FAIL.getCode())
                .responseMessage("Failed to fetch user report")
                .build();
        try {
            int page = request.getPage();
            int pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);
            String keyword = request.getKeyword();
            if (request.getId() != null) {

                Optional<ApiParams> apiParams = apiParamsRepository.findById(request.getId());
                if (apiParams.isPresent()) {
                    userResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                    userResponse.setResponseMessage("Filtered user report fetched successfully.");
                    userResponse.setParam(apiParams);
                }
            }
            int orgId = request.getOrgId();


            Page<ApiParams> apiParams = apiParamsRepository.searchParams(orgId,keyword,pageable);
            if (apiParams.getTotalElements() > 0) {
                Optional<Organisation> organisation = organizationRepository.findById(Long.parseLong(String.valueOf(orgId)));
                apiParams.forEach(payment -> {
                    payment.setOrgName(organisation.get().getName());
                });
                userResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                userResponse.setResponseMessage("Filtered user report fetched successfully.");
                userResponse.setData(apiParams);
            }else {
                userResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                userResponse.setResponseMessage("No records for  user report.");
            }



        }catch (Exception ex) {
            log.error("Error while fetching user report", ex);
            userResponse.setResponseCode(ApiResponseCode.FAIL.getCode());
            userResponse.setResponseMessage("Failed to fetch user report");
            ex.printStackTrace();
        }
        return userResponse;
    }
    public UserResponse getPermissions(UserReportsRequest request, HttpServletResponse httpServletResponse, HttpServletRequest httpServletRequest) {
        UserResponse userResponse = UserResponse.builder()
                .responseCode(ApiResponseCode.FAIL.getCode())
                .responseMessage("Failed to fetch user report")
                .build();
        try {
            int page = request.getPage();
            int pageSize = request.getPageSize();
            //int orgId = request.getOrgId();
            Pageable pageable = PageRequest.of(page, pageSize);
            String keyword = request.getKeyword();
            if (request.getId() != null) {

                Optional<Permission> permission = permissionRepo.findById(request.getId());
                if (permission.isPresent()) {
                    userResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                    userResponse.setResponseMessage("Filtered user report fetched successfully.");
                    userResponse.setParam(permission);
                }
            }

            Page<Permission> permissions = permissionRepo.findAll(keyword,pageable);
            if (permissions.getTotalElements() > 0) {
                userResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                userResponse.setResponseMessage("Filtered user report fetched successfully.");
                userResponse.setData(permissions);
            }else {
                userResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                userResponse.setResponseMessage("No records for  user report.");
            }



        }catch (Exception ex) {
            log.error("Error while fetching user report", ex);
            userResponse.setResponseCode(ApiResponseCode.FAIL.getCode());
            userResponse.setResponseMessage("Failed to fetch user report");
            ex.printStackTrace();
        }
        return userResponse;
    }
    public UserResponse getMpesaUploads(UserReportsRequest request, HttpServletResponse httpServletResponse, HttpServletRequest httpServletRequest) {
        UserResponse userResponse = UserResponse.builder()
                .responseCode(ApiResponseCode.FAIL.getCode())
                .responseMessage("Failed to fetch user report")
                .build();
        try {
            int page = request.getPage();
            int pageSize = request.getPageSize();
            int orgId = request.getOrgId();
            Pageable pageable = PageRequest.of(page, pageSize);
            String keyword = request.getKeyword();
            LocalDateTime dateFrom = null;
            LocalDateTime dateTo = null;
            String user = request.getUser();
            if (request.getId() != null) {
                Optional<BatchFileProcess> batchFileProcess = batchFileProcessRepository.findById(request.getId());
                if (batchFileProcess.isPresent()) {
                    userResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                    userResponse.setResponseMessage("Filtered user report fetched successfully.");
                    userResponse.setParam(batchFileProcess);
                }
            }

            if (request.getDateFrom() != null && !request.getDateFrom().trim().isEmpty()) {
                try {
                    dateFrom = Instant.ofEpochMilli(Long.parseLong(request.getDateFrom().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateFrom value: {}", request.getDateFrom());
                }
            }

            if (request.getDateTo() != null && !request.getDateTo().trim().isEmpty()) {
                try {
                    dateTo = Instant.ofEpochMilli(Long.parseLong(request.getDateTo().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateTo value: {}", request.getDateTo());
                }
            }

            Page<BatchFileProcess> auditActivity = batchFileProcessRepository.searchBatchFiles(orgId,keyword,dateFrom,dateTo,pageable);
            if (auditActivity.getTotalElements() > 0) {
                userResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                userResponse.setResponseMessage("Filtered user report fetched successfully.");
                userResponse.setData(auditActivity);
            }else {
                userResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                userResponse.setResponseMessage("No records for  user report.");
            }



        }catch (Exception ex) {
            log.error("Error while fetching user report", ex);
            userResponse.setResponseCode(ApiResponseCode.FAIL.getCode());
            userResponse.setResponseMessage("Failed to fetch user report");
            ex.printStackTrace();
        }
        return userResponse;
    }




}
