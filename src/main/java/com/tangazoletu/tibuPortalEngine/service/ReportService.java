package com.tangazoletu.tibuPortalEngine.service;

import com.tangazoletu.tibuPortalEngine.dto.AddressBookDTO;
import com.tangazoletu.tibuPortalEngine.dto.AddressBookRequest;
import com.tangazoletu.tibuPortalEngine.dto.EventCodesDTO;
import com.tangazoletu.tibuPortalEngine.entities.*;
import com.tangazoletu.tibuPortalEngine.enums.PortalReports;
import com.tangazoletu.tibuPortalEngine.repositories.*;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class ReportService {

    @Autowired
    private AddressBookService addressBookService;
    @Autowired
    private BudgetRepo budgetRepo;
    @Autowired
    private OrderTypeRepo orderTypeRepo;
    private final ProvinceRepo provinceRepo;
    private final CountyRepo countyRepo;
    private final BeneficiaryTypeRepo beneficiaryTypeRepo;
    private final DistrictRepository districtRepo;
    private final JobGroupRepo jobGroupRepo;
    private final RecipientRepo recipientRepo;
    private final OrganizationRepository organizationRepo;
    private final OrgOrderTypeRepo orgOrderTypeRepo;
    private final LineItemRepo lineItemRepo;
    private final FacilityViewRepository facilityViewRepo;
    private final MdrPatientRepository mdrPatientRepo;
    private final ViewCtclPendingApiOrdersRepository viewCtclPendingApiOrdersRepository;
    private final BatchApprovalViewRepo batchApprovalViewRepo;
    private final FundAllocationViewRepository fundAllocationViewRepo;
    private final ApiOrderRequestViewRepository apiOrderRequestViewRepository;
    private final ApiOrderApprovalTimelineViewRepository apiOrderApprovalTimelineViewRepository;
    private final EventSummaryViewRepo eventSummaryViewRepo;
    private final EventCodesLatestRepo eventCodesLatestRepo;
    private final EventCodesViewRepo eventCodesViewRepo;
    private final MessageOutboxRepository messageOutboxRepository;
    private final MessageTemplateRepository messageTemplateRepository;
    private final MpesaUploadsRepository mpesaUploadsRepository;
    private final BatchFileProcessRepository batchFileProcessRepository;
    private final UserViewRepository userViewRepository;
    private final RoleRepo roleRepo;
    private final ActivityLogViewRepository activityLogViewRepository;
    private final ApiParamsRepository apiParamsRepository;
    private final ParamRepository paramRepository;
    private final FinanceViewPendingRepo financeViewPendingRepo;
    private final PermissionRepo permissionRepo;
    private final ViewFinanceOfficerApprovalRepository viewFinanceOfficerApprovalRepository;
    private final ViewProgramAccountApprovalRepository viewProgramAccountApprovalRepository;
    private final ViewProgramManagerApprovalRepository viewProgramManagerApprovalRepository;
    private final SyncedPaymentRepository syncedPaymentRepository;
    private final ViewMdrSctlcSummaryRepository viewMdrSctlcSummaryRepository;
    private final ViewDotPaymentReportRepo viewDotPaymentReportRepo;
    private final MDRpatientsandcostpercountyRepo mdRpatientsandcostpercountyRepo;
    private final ViewMonthlyPatientSummaryRepository viewMonthlyPatientSummaryRepository;
    private final ViewPatientCompletedPatientReportRepo viewPatientCompletedPatientReportRepo;
    private final MdrdotPaymentsSummaryRepo mdrdotPaymentsSummaryRepo;
    private final ViewSupervisionPaymentsRepository viewSupervisionPaymentsRepository;
    private final PaymentsRepo paymentsRepo;
    private final BatchDownloadExcelUnverifiedRepo batchDownloadExcelUnverifiedRepo;
    private final TransactionChargesViewRepository transactionChargesViewRepository;
    private final FundsTransferViewRepository fundsTransferViewRepository;
    private final StatementViewRepository statementViewRepository;
    private final BudgetaryViewRepository budgetaryViewRepository;
    private final DetailedSummaryViewRepository detailedSummaryViewRepository;
    private final ExpectedVsActualViewRepository expectedVsActualViewRepository;
    private final GatewayViewRepository gatewayViewRepository;
    private final MpesaReportViewRepository mpesaReportViewRepository;
    private final GatewayNotMpesaViewRepository gatewayNotMpesaViewRepository;
    private final MpesaNotGatewayViewRepository mpesaNotGatewayViewRepository;
    @Autowired
    private PerdiemRepo perdiemRepo;


    /**
     * Get report data based on report name and filters
     */
    public List<?> getReportData(String reportName, Map<String, Object> filters) {

        PortalReports type = PortalReports.fromString(reportName.toUpperCase());
        switch (type) {
            case ADDRESS_BOOK:
                return getAddressBookReport(filters);

            case USERS:
                return getUserReport(filters);

            case ORDERS:
                return getOrderReport(filters);

            case CONTACTS:
                return getContactReport(filters);
            case BUDGET:
                return getBudgetReport(filters);
            case ORDER_TYPE:
                return getOrderTypeReport(filters);
            case PROVINCE:
                return getProvinceReport(filters);
            case COUNTY:
                return getCountyReport(filters);
            case BENEFICIARYTYPE:
                return getBeneficiaryTypeReport(filters);
            case DISTRICT:
                return getDistrictReport(filters);
            case PERDIEM:
                return getPerdiemReport(filters);
            case JOB_GROUP:
                return getJobGroupReport(filters);
            case RECIPIENTS:
                return getRecipientReport(filters);
            case ORGANSATIONS:
                return getOrganisationReport(filters);
            case ORGANISATIONTYPES:
                return getOrganisationTypeReport(filters);
            case LINEITEM:
                return getLineItemReport(filters);
            case FACILITY:
                return getFacilityReport(filters);
            case MDRPATIENTS:
                return getMdrPatientReport(filters);
            case PTLCApproval:
                return getCTLCReport(filters);
            case FINANCEBATCH:
                return getFinanceBatchReport(filters);
            case FUNDALLOCATION:
                return getFundAllocationReport(filters);
            case BENEFICIARIES:
                return getSupervisionBeneficiaries(filters);
            case APPROVALTURNAROUNDTIME:
                return getApprovalTurnAround(filters);
            case EVENTS:
                return getEventsReport(filters);
            case MESSAGEOUTBOX:
                return getMessageOutbox(filters);
            case MESSAGETEMPLATE:
                return  getMessageTemplateReport(filters);
            case ALLMPESATRANSATIONS:
                return getAllMpesaTransactionsReport(filters);
            case PTLCAPPROVALPENDING:
                return getMpesaUploadsReport(filters);
            case ROLE:
                return getRolesReport(filters);
            case AUDITTRAIL:
                return getAuditTrailReport(filters);
            case APISETTINGS:
                return getApiSettingReport(filters);
            case SYSTEMPARAMS:
                return getSystemParamReport(filters);
            case FINANCEAPPROVALPENDING:
                return getFinancePendingReport(filters);
            case PERMISSIONS:
                return getPermissionsReport(filters);
            case FINANCEPROGRAMOFFICER:
                return getFinanceProgramReport(filters);
            case PROGRAMOFFICER:
                return getProgramOfficerReport(filters);
            case PROGRAMMANAGERAPPROVAL:
                return getProgramManagerReport(filters);
            case SYNCEDPAYMENTS:
                return getSyncedPaymentsReport(filters);
            case MDRSURVEILLANCEPAYMENTS:
                return getMdrSurveillancePaymentReport(filters);
            case MDRDOTPAYMENTS:
                return getMDRDotPaymentsReport(filters);
            case MDRPATIENTSANDCOSTPERCOUNTY:
                return getMdrPerCountyReport(filters);
            case PATIENTPAYMENTOVERTIME:
                return getPatientPaymentOverTime(filters);
            case PATIENTCOMPLETEDTREATMENT:
                return getPatientCompletedTreatment(filters);
            case MDRDOTPAYMENTSSUMMARY:
                return getMdrDotPaymentSummary(filters);
            case SUPERVISIONPAYMENTS:
                return getSupervisionPayments(filters);
            case DOWNLOADBATCHUNVERFIED:
                return getUnverfiriedRecipient(filters);
            case TRANSACTIONCHARGES:
                return getTransactionChargesReport(filters);
            case FUNDSTRANSFER:
                return getFundsTRansferView(filters);
            case STATEMENT:
                return getPaymentStatementReport(filters);
            case BUDGETARY:
                return  getBudgetaryReport(filters);
            case DETAILEDSUMMARY:
                return getDetailedSummaryReport(filters);
            case EXPECTEDVSACTUAL:
                return  getExpectedVsActualReport(filters);
            case GATEWAY:
                return  getGatewayReport(filters);
            case MPESA:
                return getMpesaRerport(filters);
            case GATEWAYNOTMPESA:
                return  geGatwayNotMpesaReport(filters);
            case MPESANOTGATEWAY:
                return geyMpesaNotGatewayReport(filters);






            // Add more report types as needed
            default:
                throw new IllegalArgumentException("Unsupported report type: " + reportName);
        }
    }


    public List<Integer> getDistinctDays(String meetingCode,Boolean isLatest) {
        if (isLatest) return eventCodesLatestRepo.getDistinctDaysForLatestCodes(meetingCode);
        else return eventCodesViewRepo.getDistinctDaysForAllCodes(meetingCode);
    }

    public List<EventCodesDTO> getEventCodes(String meetingCode,Integer day,Boolean isLatest) {
        List<EventCodesDTO> eventCodes = new ArrayList<>();
        if (isLatest) {
            List<Object[]> latestEventCodes = eventCodesLatestRepo.getLatestEventCodesForDay(meetingCode,String.valueOf(day));

            for (Object[] row : latestEventCodes) {
                 EventCodesDTO event = EventCodesDTO.builder()
                        .columnFour((String) row[0])
                        .columnThree((String) row[1])
                        .columnTwo((String) row[2])
                        .columnOne((String) row[3])
                        .day((Integer) row[4])
                        .meetingCode((String) row[5])
                        .build();
                eventCodes.add(event);
            }
            return eventCodes;
        } else {
            List<Object[]> latestEventCodes = eventCodesViewRepo.getAllEventCodesForDay(meetingCode,String.valueOf(day));
            for (Object[] row : latestEventCodes) {
                EventCodesDTO event = EventCodesDTO.builder()
                        .columnFour((String) row[0])
                        .columnThree((String) row[1])
                        .columnTwo((String) row[2])
                        .columnOne((String) row[3])
                        .day(Integer.parseInt((String) row[4]))
                        .meetingCode((String) row[5])
                        .build();
                eventCodes.add(event);
            }
            return eventCodes;
        }
    }

    /**
     * Get AddressBook report data
     */
    private List<AddressBookDTO> getAddressBookReport(Map<String, Object> filters) {
        AddressBookRequest request = new AddressBookRequest();

        // Map generic filters to specific request object
//        if (filters != null) {
//            if (filters.containsKey("organization")) {
//                request.setOrganization((String) filters.get("organization"));
//            }
//            if (filters.containsKey("addressType")) {
//                request.setAddressType((String) filters.get("addressType"));
//            }
//            if (filters.containsKey("page")) {
//                request.setPage((Integer) filters.get("page"));
//            }
//            if (filters.containsKey("size")) {
//                request.setSize((Integer) filters.get("size"));
//            }
            // Add more filter mappings as needed
//        }

        Page<AddressBookDTO> page = addressBookService.getFilteredBooks(request);
        return page.getContent();
    }
    private List<?> getBudgetReport(Map<String, Object> filters) {

        // Map generic filters to specific request object
//        if (filters != null) {
//            if (filters.containsKey("organization")) {
//                request.setOrganization((String) filters.get("organization"));
//            }
//            if (filters.containsKey("addressType")) {
//                request.setAddressType((String) filters.get("addressType"));
//            }
//            if (filters.containsKey("page")) {
//                request.setPage((Integer) filters.get("page"));
//            }
//            if (filters.containsKey("size")) {
//                request.setSize((Integer) filters.get("size"));
//            }
        // Add more filter mappings as needed
//        }

        List<Budget> page =  budgetRepo.findAll();
        return page;
    }

    /**
     * Get User report data - placeholder implementation
     */
    private List<?> getUserReport(Map<String, Object> filters) {
        // TODO: Implement user report logic
        // Example:
        // UserRequest request = mapFiltersToUserRequest(filters);
        // return userService.getFilteredUsers(request);
        try {
            Specification<UserView> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("organisation") && filters.get("organisation") != null && !filters.get("organisation").toString().isBlank()) {
                    String transactionID = filters.get("organisation").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("organisation")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("username")) {
                    try {
                        String orgId = filters.get("username").toString();
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("username"), orgId));
                    } catch (NumberFormatException ignored) {}
                }
                if (filters.containsKey("organizationId")) {
                    try {
                        String orgId = filters.get("organizationId").toString();
                        Long org = Long.valueOf(orgId);
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("organizationId"), org));
                    } catch (NumberFormatException ignored) {}
                }




                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        if (filters.containsKey("dateFrom") ) {
                            String fromStr = filters.get("dateFrom").toString();
                            LocalDateTime from = LocalDate.parse(fromStr)
                                    .atStartOfDay(); // e.g. 2023-11-22T00:00
                            predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("dateCreated"), from));
                        }

                        if (filters.containsKey("dateTo")) {
                            String toStr = filters.get("dateTo").toString();
                            LocalDateTime to = LocalDate.parse(toStr)
                                    .plusDays(1)
                                    .atStartOfDay(); // e.g. 2025-06-29T00:00 (exclusive)
                            predicate = cb.and(predicate, cb.lessThan(root.get("dateCreated"), to));
                        }

                        return predicate;
                    });
                }
                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 10000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<UserView> pageResult = userViewRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
//        throw new UnsupportedOperationException("User report not yet implemented");
    }

    private List<?> getOrderTypeReport(Map<String, Object> filters) {
        Specification<OrderType> spec = Specification.where(null);

        if (filters != null) {
            if (filters.containsKey("title")) {
                String title = filters.get("title").toString();
                spec = spec.and((root, query, cb) -> cb.like(cb.lower(root.get("title")), "%" + title.toLowerCase() + "%"));
            }

            if (filters.containsKey("inTrash")) {
                String inTrash = filters.get("inTrash").toString();
                spec = spec.and((root, query, cb) -> cb.equal(root.get("inTrash"), OrderType.TrashStatus.valueOf(inTrash)));
            }

            // Add more filter mappings as needed...
        }
        // Handle pagination
        int limit = 1000;
int page = 0;

if (filters != null) {
    Object limitObj = filters.get("limit");
    if (limitObj instanceof Integer) {
        limit = (Integer) limitObj;
    }

    Object pageObj = filters.get("page");
    if (pageObj instanceof Integer) {
        page = (Integer) pageObj;
    }
}

        PageRequest pageRequest = PageRequest.of(page, limit);

        Page<OrderType> pageResult = orderTypeRepo.findAll(spec, pageRequest);
        return pageResult.getContent();
    }

    private List<?> getProvinceReport(Map<String, Object> filters) {
        Specification<Province> spec = Specification.where(null);

        if (filters != null) {
            if (filters.containsKey("title")) {
                String title = filters.get("title").toString();
                spec = spec.and((root, query, cb) -> cb.like(cb.lower(root.get("title")), "%" + title.toLowerCase() + "%"));
            }

            if (filters.containsKey("inTrash")) {
                String inTrash = filters.get("inTrash").toString();
                spec = spec.and((root, query, cb) -> cb.equal(root.get("inTrash"), OrderType.TrashStatus.valueOf(inTrash)));
            }
            if (filters.containsKey("creationTime")) {
                String inTrash = filters.get("creationTime").toString();
                spec = spec.and((root, query, cb) -> cb.equal(root.get("inTrash"), OrderType.TrashStatus.valueOf(inTrash)));
            }


            // Add more filter mappings as needed...
        }

        // Handle pagination
        int limit = 1000;
int page = 0;

if (filters != null) {
    Object limitObj = filters.get("limit");
    if (limitObj instanceof Integer) {
        limit = (Integer) limitObj;
    }

    Object pageObj = filters.get("page");
    if (pageObj instanceof Integer) {
        page = (Integer) pageObj;
    }
}

        PageRequest pageRequest = PageRequest.of(page, limit);

        Page<Province> pageResult = provinceRepo.findAll(spec, pageRequest);
        return pageResult.getContent();
    }
    private List<?> getCountyReport(Map<String, Object> filters) {
        Specification<County> spec = Specification.where(null);
        Page<County> pageResult = null;
        try {


            if (filters != null && !filters.isEmpty()) {
                if (filters.containsKey("title")) {
                    String title = filters.get("title").toString();
                    spec = spec.and((root, query, cb) -> cb.like(cb.lower(root.get("title")), "%" + title.toLowerCase() + "%"));
                }

                if (filters.containsKey("inTrash")) {
                    String inTrash = filters.get("inTrash").toString();
                    spec = spec.and((root, query, cb) -> cb.equal(root.get("inTrash"), OrderType.TrashStatus.valueOf(inTrash)));
                }
                if (filters.containsKey("creationTime")) {
                    String inTrash = filters.get("creationTime").toString();
                    spec = spec.and((root, query, cb) -> cb.equal(root.get("inTrash"), OrderType.TrashStatus.valueOf(inTrash)));
                }


                // Add more filter mappings as needed...
            }

            // Handle pagination
            int limit = 1000;
            int page = 0;
            
            if (filters != null) {
                Object limitObj = filters.get("limit");
                if (limitObj instanceof Integer) {
                    limit = (Integer) limitObj;
                }
            
                Object pageObj = filters.get("page");
                if (pageObj instanceof Integer) {
                    page = (Integer) pageObj;
                }
            }

            PageRequest pageRequest = PageRequest.of(page, limit);

             pageResult = countyRepo.findAll(spec, pageRequest);
        }catch (Exception e){
            e.printStackTrace();
        }
        return pageResult.getContent();
    }
    private List<?> getBeneficiaryTypeReport(Map<String, Object> filters) {
        Specification<BeneficiaryType> spec = Specification.where(null);

        if (filters != null) {
            if (filters.containsKey("title")) {
                String title = filters.get("title").toString();
                spec = spec.and((root, query, cb) -> cb.like(cb.lower(root.get("title")), "%" + title.toLowerCase() + "%"));
            }

            if (filters.containsKey("inTrash")) {
                String inTrash = filters.get("inTrash").toString();
                spec = spec.and((root, query, cb) -> cb.equal(root.get("inTrash"), OrderType.TrashStatus.valueOf(inTrash)));
            }
            if (filters.containsKey("creationTime")) {
                String inTrash = filters.get("creationTime").toString();
                spec = spec.and((root, query, cb) -> cb.equal(root.get("inTrash"), OrderType.TrashStatus.valueOf(inTrash)));
            }


            // Add more filter mappings as needed...
        }

        // Handle pagination
        int limit = 1000;
        int page = 0;

        if (filters != null) {
            Object limitObj = filters.get("limit");
            if (limitObj instanceof Integer) {
                limit = (Integer) limitObj;
            }

            Object pageObj = filters.get("page");
            if (pageObj instanceof Integer) {
                page = (Integer) pageObj;
            }
        }

        PageRequest pageRequest = PageRequest.of(page, limit);

        Page<BeneficiaryType> pageResult = beneficiaryTypeRepo.findAll(spec, pageRequest);
        return pageResult.getContent();
    }
    private List<?> getDistrictReport(Map<String, Object> filters) {
        Specification<ViewDistrictInfo> spec = Specification.where(null);

        if (filters != null) {
            if (filters.containsKey("sub_county")) {
                String title = filters.get("sub_county").toString();
                spec = spec.and((root, query, cb) -> cb.like(cb.lower(root.get("title")), "%" + title.toLowerCase() + "%"));
            }

            if (filters.containsKey("region")) {
                String inTrash = filters.get("region").toString();
                spec = spec.and((root, query, cb) -> cb.equal(root.get("inTrash"), OrderType.TrashStatus.valueOf(inTrash)));
            }
            if (filters.containsKey("time_created")) {
                String inTrash = filters.get("time_created").toString();
                spec = spec.and((root, query, cb) -> cb.equal(root.get("inTrash"), OrderType.TrashStatus.valueOf(inTrash)));
            }


            // Add more filter mappings as needed...
        }

        // Handle pagination
        int limit = 1000;
        int page = 0;

        if (filters != null) {
            Object limitObj = filters.get("limit");
            if (limitObj instanceof Integer) {
                limit = (Integer) limitObj;
            }

            Object pageObj = filters.get("page");
            if (pageObj instanceof Integer) {
                page = (Integer) pageObj;
            }
        }

        PageRequest pageRequest = PageRequest.of(page, limit);

        Page<ViewDistrictInfo> pageResult = districtRepo.findAll(spec, pageRequest);
        return pageResult.getContent();
    }
    private List<?> getPerdiemReport(Map<String, Object> filters) {
        Specification<Perdiem> spec = Specification.where(null);

        if (filters != null) {
            if (filters.containsKey("Designation")) {
                String title = filters.get("Designation").toString();
                spec = spec.and((root, query, cb) -> cb.like(cb.lower(root.get("title")), "%" + title.toLowerCase() + "%"));
            }

            if (filters.containsKey("org_id")) {
                String inTrash = filters.get("org_id").toString();
                spec = spec.and((root, query, cb) -> cb.equal(root.get("inTrash"), OrderType.TrashStatus.valueOf(inTrash)));
            }
            if (filters.containsKey("inTrash")) {
                String inTrash = filters.get("inTrash").toString();
                spec = spec.and((root, query, cb) -> cb.equal(root.get("inTrash"), OrderType.TrashStatus.valueOf(inTrash)));
            }


            // Add more filter mappings as needed...
        }

        // Handle pagination
        int limit = 1000;
        int page = 0;

        if (filters != null) {
            Object limitObj = filters.get("limit");
            if (limitObj instanceof Integer) {
                limit = (Integer) limitObj;
            }

            Object pageObj = filters.get("page");
            if (pageObj instanceof Integer) {
                page = (Integer) pageObj;
            }
        }

        PageRequest pageRequest = PageRequest.of(page, limit);

        Page<Perdiem> pageResult = perdiemRepo.findAll(spec, pageRequest);
        return pageResult.getContent();
    }
    private List<?> getJobGroupReport(Map<String, Object> filters) {
        Specification<JobGroup> spec = Specification.where(null);

        if (filters != null) {
            if (filters.containsKey("title")) {
                String title = filters.get("title").toString();
                spec = spec.and((root, query, cb) -> cb.like(cb.lower(root.get("title")), "%" + title.toLowerCase() + "%"));
            }

            if (filters.containsKey("createdBy")) {
                String inTrash = filters.get("createdBy").toString();
                spec = spec.and((root, query, cb) -> cb.equal(root.get("inTrash"), OrderType.TrashStatus.valueOf(inTrash)));
            }
            if (filters.containsKey("inTrash")) {
                String inTrash = filters.get("inTrash").toString();
                spec = spec.and((root, query, cb) -> cb.equal(root.get("inTrash"), OrderType.TrashStatus.valueOf(inTrash)));
            }


            // Add more filter mappings as needed...
        }

        // Handle pagination
        int limit = 1000;
        int page = 0;

        if (filters != null) {
            Object limitObj = filters.get("limit");
            if (limitObj instanceof Integer) {
                limit = (Integer) limitObj;
            }

            Object pageObj = filters.get("page");
            if (pageObj instanceof Integer) {
                page = (Integer) pageObj;
            }
        }

        PageRequest pageRequest = PageRequest.of(page, limit);

        Page<JobGroup> pageResult = jobGroupRepo.findAll(spec, pageRequest);
        return pageResult.getContent();
    }
    private List<?> getRecipientReport(Map<String, Object> filters) {
        Specification<Recipient> spec = Specification.where(null);

        if (filters != null) {
            if (filters.containsKey("msisdn")) {
                String msisdn = filters.get("msisdn").toString();
                spec = spec.and((root, query, cb) -> cb.like(cb.lower(root.get("msisdn")), "%" + msisdn.toLowerCase() + "%"));
            }

            if (filters.containsKey("emailAddress")) {
                String email = filters.get("emailAddress").toString();
                spec = spec.and((root, query, cb) -> cb.equal(cb.lower(root.get("emailAddress")), email.toLowerCase()));
            }

            // Date range filtering
            if (filters.containsKey("creationTimeFrom") || filters.containsKey("creationTimeTo")) {
                spec = spec.and((root, query, cb) -> {
                    Predicate predicate = cb.conjunction();

                    if (filters.containsKey("creationTimeFrom")) {
                        long from = Long.parseLong(filters.get("creationTimeFrom").toString());
                        predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("creationTime"), new Date(from * 1000)));
                    }

                    if (filters.containsKey("creationTimeTo")) {
                        long to = Long.parseLong(filters.get("creationTimeTo").toString());
                        predicate = cb.and(predicate, cb.lessThanOrEqualTo(root.get("creationTime"), new Date(to * 1000)));
                    }
                    if (filters.containsKey("dateFrom")) {
                        String fromStr = filters.get("dateFrom").toString();
                        LocalDate localFrom = LocalDate.parse(fromStr); // Format: yyyy-MM-dd
                        Date fromDate = Date.from(localFrom.atStartOfDay(ZoneId.systemDefault()).toInstant());
                        predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("creationTime"), fromDate));
                    }

                    if (filters.containsKey("dateTo")) {
                        String toStr = filters.get("dateTo").toString();
                        LocalDate localTo = LocalDate.parse(toStr);
                        // Add 1 day to include the entire day
                        Date toDate = Date.from(localTo.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
                        predicate = cb.and(predicate, cb.lessThan(root.get("creationTime"), toDate));
                    }

                    return predicate;
                });
            }
        }

        int limit = 1000;
int page = 0;

        if (filters != null) {
            Object limitObj = filters.get("limit");
            if (limitObj instanceof Integer) {
                limit = (Integer) limitObj;
            }

            Object pageObj = filters.get("page");
            if (pageObj instanceof Integer) {
                page = (Integer) pageObj;
            }
        }
        PageRequest pageRequest = PageRequest.of(page, limit);

        Page<Recipient> pageResult = recipientRepo.findAll(spec, pageRequest);
        return pageResult.getContent();
    }
    private List<?> getOrganisationReport(Map<String, Object> filters) {
        Specification<Organisation> spec = Specification.where(null);

        if (filters != null) {
            if (filters.containsKey("phoneNumber")) {
                String msisdn = filters.get("phoneNumber").toString();
                spec = spec.and((root, query, cb) -> cb.like(cb.lower(root.get("phoneNumber")), "%" + msisdn.toLowerCase() + "%"));
            }

            if (filters.containsKey("email")) {
                String email = filters.get("email").toString();
                spec = spec.and((root, query, cb) -> cb.equal(cb.lower(root.get("email")), email.toLowerCase()));
            }
            if (filters.containsKey("shortCode")) {
                String email = filters.get("shortCode").toString();
                spec = spec.and((root, query, cb) -> cb.equal(cb.lower(root.get("shortCode")), email.toLowerCase()));
            }

            // Date range filtering
            if (filters.containsKey("dateCreated") || filters.containsKey("dateCreated")) {
                spec = spec.and((root, query, cb) -> {
                    Predicate predicate = cb.conjunction();

                    if (filters.containsKey("creationTimeFrom")) {
                        long from = Long.parseLong(filters.get("creationTimeFrom").toString());
                        predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("dateCreated"), new Date(from * 1000)));
                    }

                    if (filters.containsKey("creationTimeTo")) {
                        long to = Long.parseLong(filters.get("creationTimeTo").toString());
                        predicate = cb.and(predicate, cb.lessThanOrEqualTo(root.get("dateCreated"), new Date(to * 1000)));
                    }
                    if (filters.containsKey("dateFrom")) {
                        String fromStr = filters.get("dateFrom").toString();
                        LocalDate localFrom = LocalDate.parse(fromStr); // Format: yyyy-MM-dd
                        Date fromDate = Date.from(localFrom.atStartOfDay(ZoneId.systemDefault()).toInstant());
                        predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("dateCreated"), fromDate));
                    }

                    if (filters.containsKey("dateTo")) {
                        String toStr = filters.get("dateTo").toString();
                        LocalDate localTo = LocalDate.parse(toStr);
                        // Add 1 day to include the entire day
                        Date toDate = Date.from(localTo.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
                        predicate = cb.and(predicate, cb.lessThan(root.get("dateCreated"), toDate));
                    }

                    return predicate;
                });
            }
        }

        int limit = 1000;
        int page = 0;

        if (filters != null) {
            Object limitObj = filters.get("limit");
            if (limitObj instanceof Integer) {
                limit = (Integer) limitObj;
            }

            Object pageObj = filters.get("page");
            if (pageObj instanceof Integer) {
                page = (Integer) pageObj;
            }
        }
        PageRequest pageRequest = PageRequest.of(page, limit);

        Page<Organisation> pageResult = organizationRepo.findAll(spec, pageRequest);
        return pageResult.getContent();
    }
    private List<?> getOrganisationTypeReport(Map<String, Object> filters) {
        Specification<OrgOrderType> spec = Specification.where(null);

        if (filters != null) {
            if (filters.containsKey("orgId")) {
                String orgIdStr = filters.get("orgId").toString();
                if (!orgIdStr.isBlank()) {
                    Integer orgId = Integer.parseInt(orgIdStr);
                    spec = spec.and((root, query, cb) -> cb.equal(root.get("orgId"), orgId));
                }
            }

            if (filters.containsKey("inTrash")) {
                String inTrash = filters.get("inTrash").toString();
                spec = spec.and((root, query, cb) -> cb.equal(cb.lower(root.get("inTrash")), inTrash.toLowerCase()));
            }

            if (filters.containsKey("creationTimeFrom") || filters.containsKey("creationTimeTo") ||
                    filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {

                spec = spec.and((root, query, cb) -> {
                    Predicate predicate = cb.conjunction();

                    if (filters.containsKey("creationTimeFrom")) {
                        long from = Long.parseLong(filters.get("creationTimeFrom").toString());
                        predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("creationTime"), from));
                    }

                    if (filters.containsKey("creationTimeTo")) {
                        long to = Long.parseLong(filters.get("creationTimeTo").toString());
                        predicate = cb.and(predicate, cb.lessThanOrEqualTo(root.get("creationTime"), to));
                    }

                    if (filters.containsKey("dateFrom")) {
                        String fromStr = filters.get("dateFrom").toString();
                        long fromEpoch = LocalDate.parse(fromStr)
                                .atStartOfDay(ZoneId.systemDefault())
                                .toEpochSecond();
                        predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("creationTime"), fromEpoch));
                    }

                    if (filters.containsKey("dateTo")) {
                        String toStr = filters.get("dateTo").toString();
                        long toEpoch = LocalDate.parse(toStr)
                                .plusDays(1)
                                .atStartOfDay(ZoneId.systemDefault())
                                .toEpochSecond();
                        predicate = cb.and(predicate, cb.lessThan(root.get("creationTime"), toEpoch));
                    }

                    return predicate;
                });
            }
        }

        int limit = 1000;
        int page = 0;

        if (filters != null) {
            Object limitObj = filters.get("limit");
            if (limitObj instanceof Integer) {
                limit = (Integer) limitObj;
            }

            Object pageObj = filters.get("page");
            if (pageObj instanceof Integer) {
                page = (Integer) pageObj;
            }
        }

        PageRequest pageRequest = PageRequest.of(page, limit);
        Page<OrgOrderType> pageResult = orgOrderTypeRepo.findAll(spec, pageRequest);
        return pageResult.getContent();
    }

    private List<?> getLineItemReport(Map<String, Object> filters) {
        Specification<LineItem> spec = Specification.where(null);

        if (filters != null) {
            if (filters.containsKey("itemName")) {
                String lineItem = filters.get("itemName").toString();
                spec = spec.and((root, query, cb) -> cb.like(cb.lower(root.get("itemName")), "%" + lineItem.toLowerCase() + "%"));
            }

            if (filters.containsKey("inTrash")) {
                String inTrash = filters.get("inTrash").toString();
                spec = spec.and((root, query, cb) -> cb.equal(cb.lower(root.get("inTrash")), inTrash.toLowerCase()));
            }

            // Date range filtering
            if (filters.containsKey("creationTimeFrom") || filters.containsKey("creationTimeTo") || filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                spec = spec.and((root, query, cb) -> {
                    Predicate predicate = cb.conjunction();

                    if (filters.containsKey("creationTimeFrom")) {
                        long from = Long.parseLong(filters.get("creationTimeFrom").toString());
                        predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("creationTime"), new Date(from * 1000)));
                    }

                    if (filters.containsKey("creationTimeTo")) {
                        long to = Long.parseLong(filters.get("creationTimeTo").toString());
                        predicate = cb.and(predicate, cb.lessThanOrEqualTo(root.get("creationTime"), new Date(to * 1000)));
                    }
                    if (filters.containsKey("dateFrom")) {
                        String fromStr = filters.get("dateFrom").toString();
                        LocalDate localFrom = LocalDate.parse(fromStr); // Format: yyyy-MM-dd
                        Date fromDate = Date.from(localFrom.atStartOfDay(ZoneId.systemDefault()).toInstant());
                        predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("creationTime"), fromDate));
                    }

                    if (filters.containsKey("dateTo")) {
                        String toStr = filters.get("dateTo").toString();
                        LocalDate localTo = LocalDate.parse(toStr);
                        // Add 1 day to include the entire day
                        Date toDate = Date.from(localTo.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
                        predicate = cb.and(predicate, cb.lessThan(root.get("creationTime"), toDate));
                    }

                    return predicate;
                });
            }
        }

        int limit = 1000;
        int page = 0;

        if (filters != null) {
            Object limitObj = filters.get("limit");
            if (limitObj instanceof Integer) {
                limit = (Integer) limitObj;
            }

            Object pageObj = filters.get("page");
            if (pageObj instanceof Integer) {
                page = (Integer) pageObj;
            }
        }
        PageRequest pageRequest = PageRequest.of(page, limit);

        Page<LineItem> pageResult = lineItemRepo.findAll(spec, pageRequest);
        return pageResult.getContent();
    }
    private List<?> getFacilityReport(Map<String, Object> filters) {
        Specification<FacilityView> spec = Specification.where(null);

        if (filters != null) {
            if (filters.containsKey("facility")) {
                String lineItem = filters.get("facility").toString();
                spec = spec.and((root, query, cb) -> cb.like(cb.lower(root.get("facility")), "%" + lineItem.toLowerCase() + "%"));
            }

            if (filters.containsKey("province")) {
                String inTrash = filters.get("province").toString();
                spec = spec.and((root, query, cb) -> cb.equal(cb.lower(root.get("province")), inTrash.toLowerCase()));
            }

            // Date range filtering
            if (filters.containsKey("creationTimeFrom") || filters.containsKey("creationTimeTo") || filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                spec = spec.and((root, query, cb) -> {
                    Predicate predicate = cb.conjunction();

                    if (filters.containsKey("creationTimeFrom")) {
                        long from = Long.parseLong(filters.get("creationTimeFrom").toString());
                        predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("timeCreated"), new Date(from * 1000)));
                    }

                    if (filters.containsKey("creationTimeTo")) {
                        long to = Long.parseLong(filters.get("creationTimeTo").toString());
                        predicate = cb.and(predicate, cb.lessThanOrEqualTo(root.get("timeCreated"), new Date(to * 1000)));
                    }
                    if (filters.containsKey("dateFrom")) {
                        String fromStr = filters.get("dateFrom").toString();
                        LocalDate localFrom = LocalDate.parse(fromStr); // Format: yyyy-MM-dd
                        Date fromDate = Date.from(localFrom.atStartOfDay(ZoneId.systemDefault()).toInstant());
                        predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("timeCreated"), fromDate));
                    }

                    if (filters.containsKey("dateTo")) {
                        String toStr = filters.get("dateTo").toString();
                        LocalDate localTo = LocalDate.parse(toStr);
                        // Add 1 day to include the entire day
                        Date toDate = Date.from(localTo.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
                        predicate = cb.and(predicate, cb.lessThan(root.get("timeCreated"), toDate));
                    }

                    return predicate;
                });
            }
        }

        int limit = 1000;
        int page = 0;

        if (filters != null) {
            Object limitObj = filters.get("limit");
            if (limitObj instanceof Integer) {
                limit = (Integer) limitObj;
            }

            Object pageObj = filters.get("page");
            if (pageObj instanceof Integer) {
                page = (Integer) pageObj;
            }
        }
        PageRequest pageRequest = PageRequest.of(page, limit);

        Page<FacilityView> pageResult = facilityViewRepo.findAll(spec, pageRequest);
        return pageResult.getContent();
    }
    private List<?> getMdrPatientReport(Map<String, Object> filters) {
        Specification<MdrPatient> spec = Specification.where(null);

        if (filters != null) {
            if (filters.containsKey("firstname")) {
                String firstname = filters.get("firstname").toString();
                spec = spec.and((root, query, cb) ->
                        cb.like(cb.lower(root.get("firstname")), "%" + firstname.toLowerCase() + "%"));
            }

            if (filters.containsKey("patientRegistrationNumber")) {
                String regNo = filters.get("patientRegistrationNumber").toString();
                spec = spec.and((root, query, cb) ->
                        cb.equal(cb.lower(root.get("patientRegistrationNumber")), regNo.toLowerCase()));
            }

            // Date range filtering for LocalDateTime
            if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                spec = spec.and((root, query, cb) -> {
                    Predicate predicate = cb.conjunction();

                    if (filters.containsKey("dateFrom")) {
                        String fromStr = filters.get("dateFrom").toString();
                        LocalDateTime from = LocalDate.parse(fromStr)
                                .atStartOfDay(); // e.g. 2023-11-22T00:00
                        predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("creationTime"), from));
                    }

                    if (filters.containsKey("dateTo")) {
                        String toStr = filters.get("dateTo").toString();
                        LocalDateTime to = LocalDate.parse(toStr)
                                .plusDays(1)
                                .atStartOfDay(); // e.g. 2025-06-29T00:00 (exclusive)
                        predicate = cb.and(predicate, cb.lessThan(root.get("creationTime"), to));
                    }

                    return predicate;
                });
            }
        }

        int limit = 1000;
int page = 0;

        if (filters != null) {
            Object limitObj = filters.get("limit");
            if (limitObj instanceof Integer) {
                limit = (Integer) limitObj;
            }

            Object pageObj = filters.get("page");
            if (pageObj instanceof Integer) {
                page = (Integer) pageObj;
            }
        }
        PageRequest pageRequest = PageRequest.of(page, limit);

        Page<MdrPatient> pageResult = mdrPatientRepo.findAll(spec, pageRequest);
        return pageResult.getContent();
    }
    private List<?> getCTLCReport(Map<String, Object> filters) {
        try {


            Specification<ViewCtclPendingApiOrders> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("patientNumber")) {
                    String firstname = filters.get("patientNumber").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("patientNumber")), "%" + firstname.toLowerCase() + "%"));
                }

                if (filters.containsKey("county")) {
                    String regNo = filters.get("county").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.equal(cb.lower(root.get("county")), regNo.toLowerCase()));
                }
                if (filters.containsKey("month")) {
                    String regNo = filters.get("month").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.equal(cb.lower(root.get("month")), regNo.toLowerCase()));
                }
                if (filters.containsKey("year")) {
                    String regNo = filters.get("year").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.equal(cb.lower(root.get("year")), regNo.toLowerCase()));
                }

                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        if (filters.containsKey("dateFrom")) {
                            String fromStr = filters.get("dateFrom").toString();
                            LocalDateTime from = LocalDate.parse(fromStr)
                                    .atStartOfDay(); // e.g. 2023-11-22T00:00
                            predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("requestTime"), from));
                        }

                        if (filters.containsKey("dateTo")) {
                            String toStr = filters.get("dateTo").toString();
                            LocalDateTime to = LocalDate.parse(toStr)
                                    .plusDays(1)
                                    .atStartOfDay(); // e.g. 2025-06-29T00:00 (exclusive)
                            predicate = cb.and(predicate, cb.lessThan(root.get("requestTime"), to));
                        }

                        return predicate;
                    });
                }
                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<ViewCtclPendingApiOrders> pageResult = viewCtclPendingApiOrdersRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;


    }
    private List<?> getFinanceBatchReport(Map<String, Object> filters) {
        try {
            Specification<BatchApprovalView> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("batchno")) {
                    String firstname = filters.get("batchno").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("batchno")), "%" + firstname.toLowerCase() + "%"));
                }

                if (filters.containsKey("approvalLevel")) {
                    try {
                        Integer approvalLevel = Integer.parseInt(filters.get("approvalLevel").toString());
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("approvalLevel"), approvalLevel));
                    } catch (NumberFormatException ignored) {}
                }

                if (filters.containsKey("ordertype")) {
                    try {
                        Integer ordertype = Integer.parseInt(filters.get("ordertype").toString());
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("ordertype"), ordertype));
                    } catch (NumberFormatException ignored) {}
                }

                if (filters.containsKey("county")) {
                    String county = filters.get("county").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("county")), "%" + county.toLowerCase() + "%"));
                }

                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        if (filters.containsKey("dateFrom")) {
                            String fromStr = filters.get("dateFrom").toString();
                            LocalDateTime from = LocalDate.parse(fromStr)
                                    .atStartOfDay(); // e.g. 2023-11-22T00:00
                            predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("requestTime"), from));
                        }

                        if (filters.containsKey("dateTo")) {
                            String toStr = filters.get("dateTo").toString();
                            LocalDateTime to = LocalDate.parse(toStr)
                                    .plusDays(1)
                                    .atStartOfDay(); // e.g. 2025-06-29T00:00 (exclusive)
                            predicate = cb.and(predicate, cb.lessThan(root.get("requestTime"), to));
                        }

                        return predicate;
                    });
                }
                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<BatchApprovalView> pageResult = batchApprovalViewRepo.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;


    }
    private List<?> getFundAllocationReport(Map<String, Object> filters) {
        try {
            Specification<FundAllocationView> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("budgetLine")) {
                    String firstname = filters.get("budgetLine").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("budgetLine")), "%" + firstname.toLowerCase() + "%"));
                }

                if (filters.containsKey("financier")) {
                    try {
                        Integer approvalLevel = Integer.parseInt(filters.get("financier").toString());
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("financier"), approvalLevel));
                    } catch (NumberFormatException ignored) {}
                }

                if (filters.containsKey("description")) {
                    try {
                        Integer ordertype = Integer.parseInt(filters.get("description").toString());
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("description"), ordertype));
                    } catch (NumberFormatException ignored) {}
                }

                if (filters.containsKey("county")) {
                    String county = filters.get("county").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("county")), "%" + county.toLowerCase() + "%"));
                }

                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();


                        if (filters.containsKey("dateFrom")) {
                            String fromStr = filters.get("dateFrom").toString();
                            long fromEpoch = LocalDate.parse(fromStr)
                                    .atStartOfDay(ZoneId.systemDefault())
                                    .toEpochSecond();
                            predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("timeCreated"), fromEpoch));
                        }

                        if (filters.containsKey("dateTo")) {
                            String toStr = filters.get("dateTo").toString();
                            long toEpoch = LocalDate.parse(toStr)
                                    .plusDays(1)
                                    .atStartOfDay(ZoneId.systemDefault())
                                    .toEpochSecond();
                            predicate = cb.and(predicate, cb.lessThan(root.get("timeCreated"), toEpoch));
                        }

                        return predicate;
                    });
                }
                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<FundAllocationView> pageResult = fundAllocationViewRepo.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getSupervisionBeneficiaries(Map<String, Object> filters) {
        try {
            Specification<ApiOrderRequestView> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("name")) {
                    String firstname = filters.get("name").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("name")), "%" + firstname.toLowerCase() + "%"));
                }

                if (filters.containsKey("station")) {
                    try {
                        Integer approvalLevel = Integer.parseInt(filters.get("station").toString());
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("station"), approvalLevel));
                    } catch (NumberFormatException ignored) {}
                }

                if (filters.containsKey("msisdn")) {
                    try {
                        Integer ordertype = Integer.parseInt(filters.get("msisdn").toString());
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("msisdn"), ordertype));
                    } catch (NumberFormatException ignored) {}
                }

                if (filters.containsKey("county")) {
                    String county = filters.get("county").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("county")), "%" + county.toLowerCase() + "%"));
                }


                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        if (filters.containsKey("dateFrom")) {
                            String fromStr = filters.get("dateFrom").toString();
                            LocalDateTime from = LocalDate.parse(fromStr)
                                    .atStartOfDay(); // e.g. 2023-11-22T00:00
                            predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("requestTime"), from));
                        }

                        if (filters.containsKey("dateTo")) {
                            String toStr = filters.get("dateTo").toString();
                            LocalDateTime to = LocalDate.parse(toStr)
                                    .plusDays(1)
                                    .atStartOfDay(); // e.g. 2025-06-29T00:00 (exclusive)
                            predicate = cb.and(predicate, cb.lessThan(root.get("requestTime"), to));
                        }

                        return predicate;
                    });
                }
                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<ApiOrderRequestView> pageResult = apiOrderRequestViewRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getApprovalTurnAround(Map<String, Object> filters) {
        try {
            Specification<ApiOrderApprovalTimelineView> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("ordertype")) {
                    String firstname = filters.get("ordertype").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("ordertype")), "%" + firstname.toLowerCase() + "%"));
                }

                if (filters.containsKey("province")) {
                    try {
                        Integer approvalLevel = Integer.parseInt(filters.get("province").toString());
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("province"), approvalLevel));
                    } catch (NumberFormatException ignored) {}
                }

                if (filters.containsKey("organisation")) {
                    try {
                        Integer ordertype = Integer.parseInt(filters.get("organisation").toString());
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("organisation"), ordertype));
                    } catch (NumberFormatException ignored) {}
                }

                if (filters.containsKey("county")) {
                    String county = filters.get("county").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("county")), "%" + county.toLowerCase() + "%"));
                }


                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        if (filters.containsKey("dateFrom")) {
                            String fromStr = filters.get("dateFrom").toString();
                            LocalDateTime from = LocalDate.parse(fromStr)
                                    .atStartOfDay(); // e.g. 2023-11-22T00:00
                            predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("requestTime"), from));
                        }

                        if (filters.containsKey("dateTo")) {
                            String toStr = filters.get("dateTo").toString();
                            LocalDateTime to = LocalDate.parse(toStr)
                                    .plusDays(1)
                                    .atStartOfDay(); // e.g. 2025-06-29T00:00 (exclusive)
                            predicate = cb.and(predicate, cb.lessThan(root.get("requestTime"), to));
                        }

                        return predicate;
                    });
                }
                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<ApiOrderApprovalTimelineView> pageResult = apiOrderApprovalTimelineViewRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getEventsReport(Map<String, Object> filters) {
        try {
            Specification<EventSummaryView> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("activityName")) {
                    String firstname = filters.get("activityName").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("activityName")), "%" + firstname.toLowerCase() + "%"));
                }

                if (filters.containsKey("budget")) {
                    try {
                        Integer approvalLevel = Integer.parseInt(filters.get("budget").toString());
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("budget"), approvalLevel));
                    } catch (NumberFormatException ignored) {}
                }

                if (filters.containsKey("venue")) {
                    try {
                        Integer ordertype = Integer.parseInt(filters.get("venue").toString());
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("venue"), ordertype));
                    } catch (NumberFormatException ignored) {}
                }

                if (filters.containsKey("county")) {
                    String county = filters.get("county").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("county")), "%" + county.toLowerCase() + "%"));
                }
                if (filters.containsKey("status")) {
                    String county = filters.get("status").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("status")), "%" + county.toLowerCase() + "%"));
                }


                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        if (filters.containsKey("dateFrom")) {
                            String fromStr = filters.get("dateFrom").toString();
                            LocalDateTime from = LocalDate.parse(fromStr)
                                    .atStartOfDay(); // e.g. 2023-11-22T00:00
                            predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("startDate"), from));
                        }

                        if (filters.containsKey("dateTo")) {
                            String toStr = filters.get("dateTo").toString();
                            LocalDateTime to = LocalDate.parse(toStr)
                                    .plusDays(1)
                                    .atStartOfDay(); // e.g. 2025-06-29T00:00 (exclusive)
                            predicate = cb.and(predicate, cb.lessThan(root.get("endDate"), to));
                        }

                        return predicate;
                    });
                }
                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<EventSummaryView> pageResult = eventSummaryViewRepo.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getMessageOutbox(Map<String, Object> filters) {
        try {
            Specification<MessageOutbox> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("smsBatchNo")) {
                    String firstname = filters.get("smsBatchNo").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("smsBatchNo")), "%" + firstname.toLowerCase() + "%"));
                }

                if (filters.containsKey("phoneNumber")) {
                    try {
                        String phoneNumber = filters.get("phoneNumber").toString();
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("phoneNumber"), phoneNumber));
                    } catch (NumberFormatException ignored) {}
                }




                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        if (filters.containsKey("dateFrom") ) {
                            String fromStr = filters.get("dateFrom").toString();
                            LocalDateTime from = LocalDate.parse(fromStr)
                                    .atStartOfDay(); // e.g. 2023-11-22T00:00
                            predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("createdAt"), from));
                        }

                        if (filters.containsKey("dateTo")) {
                            String toStr = filters.get("dateTo").toString();
                            LocalDateTime to = LocalDate.parse(toStr)
                                    .plusDays(1)
                                    .atStartOfDay(); // e.g. 2025-06-29T00:00 (exclusive)
                            predicate = cb.and(predicate, cb.lessThan(root.get("createdAt"), to));
                        }

                        return predicate;
                    });
                }
                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<MessageOutbox> pageResult = messageOutboxRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getMessageTemplateReport(Map<String, Object> filters) {
        try {
            Specification<MessageTemplate> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("paymentType") && filters.get("paymentType") != null && !filters.get("paymentType").toString().isBlank()) {
                    String paymentTypeStr = filters.get("paymentType").toString();
                    Long paymentType = Long.valueOf(paymentTypeStr);
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("paymentType")), "%" + paymentType + "%"));
                }

                if (filters.containsKey("inTrash")) {
                    try {
                        String inTrash = filters.get("inTrash").toString();
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("inTrash"), inTrash));
                    } catch (NumberFormatException ignored) {}
                }




                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        if (filters.containsKey("dateFrom") ) {
                            String fromStr = filters.get("dateFrom").toString();
                            LocalDateTime from = LocalDate.parse(fromStr)
                                    .atStartOfDay(); // e.g. 2023-11-22T00:00
                            predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("createdAt"), from));
                        }

                        if (filters.containsKey("dateTo")) {
                            String toStr = filters.get("dateTo").toString();
                            LocalDateTime to = LocalDate.parse(toStr)
                                    .plusDays(1)
                                    .atStartOfDay(); // e.g. 2025-06-29T00:00 (exclusive)
                            predicate = cb.and(predicate, cb.lessThan(root.get("createdAt"), to));
                        }

                        return predicate;
                    });
                }
                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<MessageTemplate> pageResult = messageTemplateRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getAllMpesaTransactionsReport(Map<String, Object> filters) {
        try {
            Specification<MpesaUploads> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("transactionID") && filters.get("transactionID") != null && !filters.get("transactionID").toString().isBlank()) {
                    String transactionID = filters.get("transactionID").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("transactionID")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("orgId")) {
                    try {
                        String orgId = filters.get("orgId").toString();
                        Integer org = Integer.valueOf(orgId);
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("orgId"), orgId));
                    } catch (NumberFormatException ignored) {}
                }




                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        if (filters.containsKey("dateFrom") ) {
                            String fromStr = filters.get("dateFrom").toString();
                            LocalDateTime from = LocalDate.parse(fromStr)
                                    .atStartOfDay(); // e.g. 2023-11-22T00:00
                            predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("date"), from));
                        }

                        if (filters.containsKey("dateTo")) {
                            String toStr = filters.get("dateTo").toString();
                            LocalDateTime to = LocalDate.parse(toStr)
                                    .plusDays(1)
                                    .atStartOfDay(); // e.g. 2025-06-29T00:00 (exclusive)
                            predicate = cb.and(predicate, cb.lessThan(root.get("date"), to));
                        }

                        return predicate;
                    });
                }
                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<MpesaUploads> pageResult = mpesaUploadsRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getMpesaUploadsReport(Map<String, Object> filters) {
        try {
            Specification<BatchFileProcess> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("fileName") && filters.get("fileName") != null && !filters.get("fileName").toString().isBlank()) {
                    String transactionID = filters.get("fileName").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("fileName")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("status")) {
                    try {
                        String orgId = filters.get("status").toString();
                        Integer org = Integer.valueOf(orgId);
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("status"), orgId));
                    } catch (NumberFormatException ignored) {}
                }




                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        if (filters.containsKey("dateFrom") ) {
                            String fromStr = filters.get("dateFrom").toString();
                            LocalDateTime from = LocalDate.parse(fromStr)
                                    .atStartOfDay(); // e.g. 2023-11-22T00:00
                            predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("dateCreated"), from));
                        }

                        if (filters.containsKey("dateTo")) {
                            String toStr = filters.get("dateTo").toString();
                            LocalDateTime to = LocalDate.parse(toStr)
                                    .plusDays(1)
                                    .atStartOfDay(); // e.g. 2025-06-29T00:00 (exclusive)
                            predicate = cb.and(predicate, cb.lessThan(root.get("dateCreated"), to));
                        }

                        return predicate;
                    });
                }
                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<BatchFileProcess> pageResult = batchFileProcessRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getRolesReport(Map<String, Object> filters) {
        try {
            Specification<Role> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("title") && filters.get("title") != null && !filters.get("title").toString().isBlank()) {
                    String transactionID = filters.get("title").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("title")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("inTrash")) {
                    try {
                        String orgId = filters.get("inTrash").toString();
                        Integer org = Integer.valueOf(orgId);
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("inTrash"), orgId));
                    } catch (NumberFormatException ignored) {}
                }




                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        if (filters.containsKey("dateFrom")) {
                            String fromStr = filters.get("dateFrom").toString();
                            LocalDateTime fromDateTime = LocalDate.parse(fromStr).atStartOfDay();
                            long fromEpochMillis = fromDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                            predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("creationTime"), fromEpochMillis));
                        }

                        if (filters.containsKey("dateTo")) {
                            String toStr = filters.get("dateTo").toString();
                            LocalDateTime toDateTime = LocalDate.parse(toStr).plusDays(1).atStartOfDay();
                            long toEpochMillis = toDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                            predicate = cb.and(predicate, cb.lessThan(root.get("creationTime"), toEpochMillis));
                        }


                        return predicate;
                    });
                }
                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<Role> pageResult = roleRepo.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getAuditTrailReport(Map<String, Object> filters) {
        try {
            Specification<ActivityLogView> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("activity") && filters.get("activity") != null && !filters.get("activity").toString().isBlank()) {
                    String transactionID = filters.get("activity").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("activity")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("actionBy")) {
                    try {
                        String orgId = filters.get("actionBy").toString();
                        Integer org = Integer.valueOf(orgId);
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("actionBy"), orgId));
                    } catch (NumberFormatException ignored) {}
                }




                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        if (filters.containsKey("dateFrom") ) {
                            String fromStr = filters.get("dateFrom").toString();
                            LocalDateTime from = LocalDate.parse(fromStr)
                                    .atStartOfDay(); // e.g. 2023-11-22T00:00
                            predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("timeCreated"), from));
                        }

                        if (filters.containsKey("dateTo")) {
                            String toStr = filters.get("dateTo").toString();
                            LocalDateTime to = LocalDate.parse(toStr)
                                    .plusDays(1)
                                    .atStartOfDay(); // e.g. 2025-06-29T00:00 (exclusive)
                            predicate = cb.and(predicate, cb.lessThan(root.get("timeCreated"), to));
                        }


                        return predicate;
                    });
                }
                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<ActivityLogView> pageResult = activityLogViewRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getApiSettingReport(Map<String, Object> filters) {
        try {
            Specification<ApiParams> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("serviceId") && filters.get("serviceId") != null && !filters.get("serviceId").toString().isBlank()) {
                    String transactionID = filters.get("serviceId").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("serviceId")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("shortcode")) {
                    try {
                        String orgId = filters.get("shortcode").toString();
                        Integer org = Integer.valueOf(orgId);
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("shortcode"), orgId));
                    } catch (NumberFormatException ignored) {
                    }
                }

                // Date range filtering for LocalDateTime
//                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
//                    spec = spec.and((root, query, cb) -> {
//                        Predicate predicate = cb.conjunction();
//
//                        if (filters.containsKey("dateFrom") ) {
//                            String fromStr = filters.get("dateFrom").toString();
//                            LocalDateTime from = LocalDate.parse(fromStr)
//                                    .atStartOfDay(); // e.g. 2023-11-22T00:00
//                            predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("timeCreated"), from));
//                        }
//
//                        if (filters.containsKey("dateTo")) {
//                            String toStr = filters.get("dateTo").toString();
//                            LocalDateTime to = LocalDate.parse(toStr)
//                                    .plusDays(1)
//                                    .atStartOfDay(); // e.g. 2025-06-29T00:00 (exclusive)
//                            predicate = cb.and(predicate, cb.lessThan(root.get("timeCreated"), to));
//                        }
//
//
//                        return predicate;
//                    });
//                }
                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<ApiParams> pageResult = apiParamsRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getSystemParamReport(Map<String, Object> filters) {
        try {
            Specification<com.tangazoletu.tibuPortalEngine.entities.Param> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("parameter") && filters.get("parameter") != null && !filters.get("parameter").toString().isBlank()) {
                    String transactionID = filters.get("parameter").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("parameter")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("value")) {
                    try {
                        String orgId = filters.get("value").toString();
                        Integer org = Integer.valueOf(orgId);
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("value"), orgId));
                    } catch (NumberFormatException ignored) {
                    }
                }

                // Date range filtering for LocalDateTime
//                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
//                    spec = spec.and((root, query, cb) -> {
//                        Predicate predicate = cb.conjunction();
//
//                        if (filters.containsKey("dateFrom") ) {
//                            String fromStr = filters.get("dateFrom").toString();
//                            LocalDateTime from = LocalDate.parse(fromStr)
//                                    .atStartOfDay(); // e.g. 2023-11-22T00:00
//                            predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("timeCreated"), from));
//                        }
//
//                        if (filters.containsKey("dateTo")) {
//                            String toStr = filters.get("dateTo").toString();
//                            LocalDateTime to = LocalDate.parse(toStr)
//                                    .plusDays(1)
//                                    .atStartOfDay(); // e.g. 2025-06-29T00:00 (exclusive)
//                            predicate = cb.and(predicate, cb.lessThan(root.get("timeCreated"), to));
//                        }
//
//
//                        return predicate;
//                    });
//                }
                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<com.tangazoletu.tibuPortalEngine.entities.Param> pageResult = paramRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getFinancePendingReport(Map<String, Object> filters) {
        try {
            Specification<FinanceViewPending> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("batchNumber") && filters.get("batchNumber") != null && !filters.get("batchNumber").toString().isBlank()) {
                    String transactionID = filters.get("batchNumber").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("batchNumber")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("phoneNumber")) {
                    try {
                        String orgId = filters.get("phoneNumber").toString();
                        Integer org = Integer.valueOf(orgId);
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("phoneNumber"), orgId));
                    } catch (NumberFormatException ignored) {
                    }
                }

                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        if (filters.containsKey("dateFrom") ) {
                            String fromStr = filters.get("dateFrom").toString();
                            LocalDateTime from = LocalDate.parse(fromStr)
                                    .atStartOfDay(); // e.g. 2023-11-22T00:00
                            predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("requestTime"), from));
                        }

                        if (filters.containsKey("dateTo")) {
                            String toStr = filters.get("dateTo").toString();
                            LocalDateTime to = LocalDate.parse(toStr)
                                    .plusDays(1)
                                    .atStartOfDay(); // e.g. 2025-06-29T00:00 (exclusive)
                            predicate = cb.and(predicate, cb.lessThan(root.get("requestTime"), to));
                        }


                        return predicate;
                    });
                }
                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<FinanceViewPending> pageResult = financeViewPendingRepo.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getPermissionsReport(Map<String, Object> filters) {
        try {
            Specification<Permission> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("title") && filters.get("title") != null && !filters.get("title").toString().isBlank()) {
                    String transactionID = filters.get("title").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("title")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("description")) {
                    try {
                        String orgId = filters.get("description").toString();
                        Integer org = Integer.valueOf(orgId);
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("description"), orgId));
                    } catch (NumberFormatException ignored) {
                    }
                }

                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        if (filters.containsKey("dateFrom")) {
                            String fromStr = filters.get("dateFrom").toString();
                            LocalDateTime fromDateTime = LocalDate.parse(fromStr).atStartOfDay();
                            long fromEpochMillis = fromDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                            predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("creationTime"), fromEpochMillis));
                        }

                        if (filters.containsKey("dateTo")) {
                            String toStr = filters.get("dateTo").toString();
                            LocalDateTime toDateTime = LocalDate.parse(toStr).plusDays(1).atStartOfDay();
                            long toEpochMillis = toDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                            predicate = cb.and(predicate, cb.lessThan(root.get("creationTime"), toEpochMillis));
                        }


                        return predicate;
                    });
                }
                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<Permission> pageResult = permissionRepo.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getFinanceProgramReport(Map<String, Object> filters) {
        try {
            Specification<ViewFinanceOfficerApproval> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("batchNumber") && filters.get("batchNumber") != null && !filters.get("batchNumber").toString().isBlank()) {
                    String transactionID = filters.get("batchNumber").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("batchNumber")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("phoneNumber")) {
                    try {
                        String orgId = filters.get("phoneNumber").toString();
                        Integer org = Integer.valueOf(orgId);
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("phoneNumber"), orgId));
                    } catch (NumberFormatException ignored) {
                    }
                }

                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();
                        if (filters.containsKey("dateFrom") ) {
                            String fromStr = filters.get("dateFrom").toString();
                            LocalDateTime from = LocalDate.parse(fromStr)
                                    .atStartOfDay(); // e.g. 2023-11-22T00:00
                            predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("requestTime"), from));
                        }

                        if (filters.containsKey("dateTo")) {
                            String toStr = filters.get("dateTo").toString();
                            LocalDateTime to = LocalDate.parse(toStr)
                                    .plusDays(1)
                                    .atStartOfDay(); // e.g. 2025-06-29T00:00 (exclusive)
                            predicate = cb.and(predicate, cb.lessThan(root.get("requestTime"), to));
                        }


                        return predicate;
                    });
                }
                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<ViewFinanceOfficerApproval> pageResult = viewFinanceOfficerApprovalRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getProgramOfficerReport(Map<String, Object> filters) {
        try {
            Specification<ViewProgramAccountApproval> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("batchNumber") && filters.get("batchNumber") != null && !filters.get("batchNumber").toString().isBlank()) {
                    String transactionID = filters.get("batchNumber").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("batchNumber")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("phoneNumber")) {
                    try {
                        String orgId = filters.get("phoneNumber").toString();
                        Integer org = Integer.valueOf(orgId);
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("phoneNumber"), orgId));
                    } catch (NumberFormatException ignored) {
                    }
                }

                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        try {
                            if (filters.containsKey("dateFrom")) {
                                String fromStr = filters.get("dateFrom").toString();
                                if (!fromStr.isBlank()) {
                                    LocalDateTime from = LocalDate.parse(fromStr).atStartOfDay();
                                    log.info("Applying filter: requestTime >= {}", from);
                                    predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("requestTime"), from));
                                }
                            }

                            if (filters.containsKey("dateTo")) {
                                String toStr = filters.get("dateTo").toString();
                                if (!toStr.isBlank()) {
                                    LocalDateTime to = LocalDate.parse(toStr).plusDays(1).atStartOfDay(); // exclusive
                                    log.info("Applying filter: requestTime < {}", to);
                                    predicate = cb.and(predicate, cb.lessThan(root.get("requestTime"), to));
                                }
                            }
                        } catch (Exception e) {
                            log.error("Error parsing dateFrom/dateTo filter: {}", e.getMessage(), e);
                        }

                        return predicate;
                    });
                }

                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<ViewProgramAccountApproval> pageResult = viewProgramAccountApprovalRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getProgramManagerReport(Map<String, Object> filters) {
        try {
            Specification<com.tangazoletu.tibuPortalEngine.views.ViewProgramManagerApproval> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("batchNumber") && filters.get("batchNumber") != null && !filters.get("batchNumber").toString().isBlank()) {
                    String transactionID = filters.get("batchNumber").toString();
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("batchNumber")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("phoneNumber")) {
                    try {
                        String orgId = filters.get("phoneNumber").toString();
                        Integer org = Integer.valueOf(orgId);
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("phoneNumber"), orgId));
                    } catch (NumberFormatException ignored) {
                    }
                }

                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        try {
                            if (filters.containsKey("dateFrom")) {
                                String fromStr = filters.get("dateFrom").toString();
                                if (!fromStr.isBlank()) {
                                    LocalDateTime from = LocalDate.parse(fromStr).atStartOfDay();
                                    log.info("Applying filter: requestTime >= {}", from);
                                    predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("requestTime"), from));
                                }
                            }

                            if (filters.containsKey("dateTo")) {
                                String toStr = filters.get("dateTo").toString();
                                if (!toStr.isBlank()) {
                                    LocalDateTime to = LocalDate.parse(toStr).plusDays(1).atStartOfDay(); // exclusive
                                    log.info("Applying filter: requestTime < {}", to);
                                    predicate = cb.and(predicate, cb.lessThan(root.get("requestTime"), to));
                                }
                            }
                        } catch (Exception e) {
                            log.error("Error parsing dateFrom/dateTo filter: {}", e.getMessage(), e);
                        }

                        return predicate;
                    });
                }

                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<com.tangazoletu.tibuPortalEngine.views.ViewProgramManagerApproval> pageResult = viewProgramManagerApprovalRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getSyncedPaymentsReport(Map<String, Object> filters) {
        try {
            Specification<SyncedPayment> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("pmsId") && filters.get("pmsId") != null && !filters.get("pmsId").toString().isBlank()) {
                    String transactionID = filters.get("pmsId").toString();
                    Integer pmsId = Integer.parseInt(transactionID);
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("pmsId")), "%" + pmsId + "%"));
                }

                if (filters.containsKey("county")) {
                    try {
                        String orgId = filters.get("county").toString();
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("county"), orgId));
                    } catch (NumberFormatException ignored) {
                    }
                }

                // Date range filtering for LocalDateTime


                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<SyncedPayment> pageResult = syncedPaymentRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getMdrSurveillancePaymentReport(Map<String, Object> filters) {
        try {
            Specification<ViewMdrSctlcSummary> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("initiatorId") && filters.get("initiatorId") != null && !filters.get("initiatorId").toString().isBlank()) {
                    String transactionID = filters.get("initiatorId").toString();
                    Integer pmsId = Integer.parseInt(transactionID);
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("initiatorId")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("sctlcName")) {
                    try {
                        String orgId = filters.get("sctlcName").toString();
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("sctlcName"), orgId));
                    } catch (NumberFormatException ignored) {
                    }
                }

                // Date range filtering for LocalDateTime
//                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
//                    spec = spec.and((root, query, cb) -> {
//                        Predicate predicate = cb.conjunction();
//
//                        try {
//                            if (filters.containsKey("dateFrom")) {
//                                String fromStr = filters.get("dateFrom").toString();
//                                if (!fromStr.isBlank()) {
//                                    LocalDateTime from = LocalDate.parse(fromStr).atStartOfDay();
//                                    log.info("Applying filter: requestTime >= {}", from);
//                                    predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("requestTime"), from));
//                                }
//                            }
//
//                            if (filters.containsKey("dateTo")) {
//                                String toStr = filters.get("dateTo").toString();
//                                if (!toStr.isBlank()) {
//                                    LocalDateTime to = LocalDate.parse(toStr).plusDays(1).atStartOfDay(); // exclusive
//                                    log.info("Applying filter: requestTime < {}", to);
//                                    predicate = cb.and(predicate, cb.lessThan(root.get("requestTime"), to));
//                                }
//                            }
//                        } catch (Exception e) {
//                            log.error("Error parsing dateFrom/dateTo filter: {}", e.getMessage(), e);
//                        }
//
//                        return predicate;
//                    });
//                }


                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<ViewMdrSctlcSummary> pageResult = viewMdrSctlcSummaryRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getMDRDotPaymentsReport(Map<String, Object> filters) {
        try {
            Specification<ViewDotPaymentReport> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("county") && filters.get("county") != null && !filters.get("county").toString().isBlank()) {
                    String transactionID = filters.get("county").toString();
                    Integer pmsId = Integer.parseInt(transactionID);
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("county")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("requestType")) {
                    try {
                        String orgId = filters.get("requestType").toString();
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("requestType"), orgId));
                    } catch (NumberFormatException ignored) {
                    }
                }

                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        try {
                            if (filters.containsKey("dateFrom")) {
                                String fromStr = filters.get("dateFrom").toString();
                                if (!fromStr.isBlank()) {
                                    LocalDateTime from = LocalDate.parse(fromStr).atStartOfDay();
                                    log.info("Applying filter: requestTime >= {}", from);
                                    predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("requestTime"), from));
                                }
                            }

                            if (filters.containsKey("dateTo")) {
                                String toStr = filters.get("dateTo").toString();
                                if (!toStr.isBlank()) {
                                    LocalDateTime to = LocalDate.parse(toStr).plusDays(1).atStartOfDay(); // exclusive
                                    log.info("Applying filter: requestTime < {}", to);
                                    predicate = cb.and(predicate, cb.lessThan(root.get("requestTime"), to));
                                }
                            }
                        } catch (Exception e) {
                            log.error("Error parsing dateFrom/dateTo filter: {}", e.getMessage(), e);
                        }

                        return predicate;
                    });
                }


                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<ViewDotPaymentReport> pageResult = viewDotPaymentReportRepo.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getMdrPerCountyReport(Map<String, Object> filters) {
        try {
            Specification<MDRpatientsandcostpercounty> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("county") && filters.get("county") != null && !filters.get("county").toString().isBlank()) {
                    String transactionID = filters.get("county").toString();
                    Integer pmsId = Integer.parseInt(transactionID);
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("county")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("patientsRequests")) {
                    try {
                        String orgId = filters.get("patientsRequests").toString();
                        Integer id = Integer.parseInt(orgId);
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("patientsRequests"), id));
                    } catch (NumberFormatException ignored) {
                    }
                }

                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        try {
                            if (filters.containsKey("dateFrom")) {
                                String fromStr = filters.get("dateFrom").toString();
                                if (!fromStr.isBlank()) {
                                    LocalDateTime from = LocalDate.parse(fromStr).atStartOfDay();
                                    log.info("Applying filter: requestTime >= {}", from);
                                    predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("requestTime"), from));
                                }
                            }

                            if (filters.containsKey("dateTo")) {
                                String toStr = filters.get("dateTo").toString();
                                if (!toStr.isBlank()) {
                                    LocalDateTime to = LocalDate.parse(toStr).plusDays(1).atStartOfDay(); // exclusive
                                    log.info("Applying filter: requestTime < {}", to);
                                    predicate = cb.and(predicate, cb.lessThan(root.get("requestTime"), to));
                                }
                            }
                        } catch (Exception e) {
                            log.error("Error parsing dateFrom/dateTo filter: {}", e.getMessage(), e);
                        }

                        return predicate;
                    });
                }


                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<MDRpatientsandcostpercounty> pageResult = mdRpatientsandcostpercountyRepo.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getPatientPaymentOverTime(Map<String, Object> filters) {
        try {
            Specification<ViewMonthlyPatientSummary> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("patientRegistrationNumber") && filters.get("patientRegistrationNumber") != null && !filters.get("patientRegistrationNumber").toString().isBlank()) {
                    String transactionID = filters.get("patientRegistrationNumber").toString();
                    Integer pmsId = Integer.parseInt(transactionID);
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("patientRegistrationNumber")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("telephoneNumber")) {
                    try {
                        String orgId = filters.get("telephoneNumber").toString();
                        Integer id = Integer.parseInt(orgId);
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("telephoneNumber"), id));
                    } catch (NumberFormatException ignored) {
                    }
                }

                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        try {
                            if (filters.containsKey("dateFrom")) {
                                String fromStr = filters.get("dateFrom").toString();
                                if (!fromStr.isBlank()) {
                                    LocalDateTime from = LocalDate.parse(fromStr).atStartOfDay();
                                    log.info("Applying filter: requestTime >= {}", from);
                                    predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("requestTime"), from));
                                }
                            }

                            if (filters.containsKey("dateTo")) {
                                String toStr = filters.get("dateTo").toString();
                                if (!toStr.isBlank()) {
                                    LocalDateTime to = LocalDate.parse(toStr).plusDays(1).atStartOfDay(); // exclusive
                                    log.info("Applying filter: requestTime < {}", to);
                                    predicate = cb.and(predicate, cb.lessThan(root.get("requestTime"), to));
                                }
                            }
                        } catch (Exception e) {
                            log.error("Error parsing dateFrom/dateTo filter: {}", e.getMessage(), e);
                        }

                        return predicate;
                    });
                }


                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<ViewMonthlyPatientSummary> pageResult = viewMonthlyPatientSummaryRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getPatientCompletedTreatment(Map<String, Object> filters) {
        try {
            Specification<ViewPatientCompletedPatientReport> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("batchno") && filters.get("batchno") != null && !filters.get("batchno").toString().isBlank()) {
                    String transactionID = filters.get("batchno").toString();
                    Integer pmsId = Integer.parseInt(transactionID);
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("batchno")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("county")) {
                    try {
                        String orgId = filters.get("county").toString();
                        Integer id = Integer.parseInt(orgId);
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("county"), id));
                    } catch (NumberFormatException ignored) {
                    }
                }

                if (filters.containsKey("phoneNo")) {
                    try {
                        String phoneNo = filters.get("phoneNo").toString();
                        //Integer id = Integer.parseInt(orgId);
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("phoneNo"), phoneNo));
                    } catch (NumberFormatException ignored) {
                    }
                }

                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        try {
                            if (filters.containsKey("dateFrom")) {
                                String fromStr = filters.get("dateFrom").toString();
                                if (!fromStr.isBlank()) {
                                    LocalDateTime from = LocalDate.parse(fromStr).atStartOfDay();
                                    log.info("Applying filter: requestTime >= {}", from);
                                    predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("requestTime"), from));
                                }
                            }

                            if (filters.containsKey("dateTo")) {
                                String toStr = filters.get("dateTo").toString();
                                if (!toStr.isBlank()) {
                                    LocalDateTime to = LocalDate.parse(toStr).plusDays(1).atStartOfDay(); // exclusive
                                    log.info("Applying filter: requestTime < {}", to);
                                    predicate = cb.and(predicate, cb.lessThan(root.get("requestTime"), to));
                                }
                            }
                        } catch (Exception e) {
                            log.error("Error parsing dateFrom/dateTo filter: {}", e.getMessage(), e);
                        }

                        return predicate;
                    });
                }


                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<ViewPatientCompletedPatientReport> pageResult = viewPatientCompletedPatientReportRepo.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getMdrDotPaymentSummary(Map<String, Object> filters) {
        try {
            Specification<MdrdotPaymentsSummary> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("dotName") && filters.get("dotName") != null && !filters.get("dotName").toString().isBlank()) {
                    String transactionID = filters.get("dotName").toString();
                    Integer pmsId = Integer.parseInt(transactionID);
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("dotName")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("year")) {
                    try {
                        String orgId = filters.get("year").toString();
                        Integer id = Integer.parseInt(orgId);
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("year"), id));
                    } catch (NumberFormatException ignored) {
                    }
                }

                if (filters.containsKey("phoneNo")) {
                    try {
                        String phoneNo = filters.get("phoneNo").toString();
                        //Integer id = Integer.parseInt(orgId);
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("phoneNo"), phoneNo));
                    } catch (NumberFormatException ignored) {
                    }
                }

                // Date range filtering for LocalDateTime
//                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
//                    spec = spec.and((root, query, cb) -> {
//                        Predicate predicate = cb.conjunction();
//
//                        try {
//                            if (filters.containsKey("dateFrom")) {
//                                String fromStr = filters.get("dateFrom").toString();
//                                if (!fromStr.isBlank()) {
//                                    LocalDateTime from = LocalDate.parse(fromStr).atStartOfDay();
//                                    log.info("Applying filter: requestTime >= {}", from);
//                                    predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("requestTime"), from));
//                                }
//                            }
//
//                            if (filters.containsKey("dateTo")) {
//                                String toStr = filters.get("dateTo").toString();
//                                if (!toStr.isBlank()) {
//                                    LocalDateTime to = LocalDate.parse(toStr).plusDays(1).atStartOfDay(); // exclusive
//                                    log.info("Applying filter: requestTime < {}", to);
//                                    predicate = cb.and(predicate, cb.lessThan(root.get("requestTime"), to));
//                                }
//                            }
//                        } catch (Exception e) {
//                            log.error("Error parsing dateFrom/dateTo filter: {}", e.getMessage(), e);
//                        }
//
//                        return predicate;
//                    });
//                }


                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<MdrdotPaymentsSummary> pageResult = mdrdotPaymentsSummaryRepo.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getSupervisionPayments(Map<String, Object> filters) {
        try {
            Specification<ViewSupervisionPayments> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("initiatorId") && filters.get("initiatorId") != null && !filters.get("initiatorId").toString().isBlank()) {
                    String transactionID = filters.get("initiatorId").toString();
                    Integer pmsId = Integer.parseInt(transactionID);
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("initiatorId")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("sctlcName")) {
                    try {
                        String orgId = filters.get("sctlcName").toString();
                        Integer id = Integer.parseInt(orgId);
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("sctlcName"), id));
                    } catch (NumberFormatException ignored) {
                    }
                }

                if (filters.containsKey("unpaidAmt")) {
                    try {
                        String phoneNo = filters.get("unpaidAmt").toString();
                        //Integer id = Integer.parseInt(orgId);
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("unpaidAmt"), phoneNo));
                    } catch (NumberFormatException ignored) {
                    }
                }

                // Date range filtering for LocalDateTime
//                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
//                    spec = spec.and((root, query, cb) -> {
//                        Predicate predicate = cb.conjunction();
//
//                        try {
//                            if (filters.containsKey("dateFrom")) {
//                                String fromStr = filters.get("dateFrom").toString();
//                                if (!fromStr.isBlank()) {
//                                    LocalDateTime from = LocalDate.parse(fromStr).atStartOfDay();
//                                    log.info("Applying filter: requestTime >= {}", from);
//                                    predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("requestTime"), from));
//                                }
//                            }
//
//                            if (filters.containsKey("dateTo")) {
//                                String toStr = filters.get("dateTo").toString();
//                                if (!toStr.isBlank()) {
//                                    LocalDateTime to = LocalDate.parse(toStr).plusDays(1).atStartOfDay(); // exclusive
//                                    log.info("Applying filter: requestTime < {}", to);
//                                    predicate = cb.and(predicate, cb.lessThan(root.get("requestTime"), to));
//                                }
//                            }
//                        } catch (Exception e) {
//                            log.error("Error parsing dateFrom/dateTo filter: {}", e.getMessage(), e);
//                        }
//
//                        return predicate;
//                    });
//                }


                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<ViewSupervisionPayments> pageResult = viewSupervisionPaymentsRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getUnverfiriedRecipient(Map<String, Object> filters) {
        try {
            Specification<BatchDownloadExcelUnverified> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("phoneNo") && filters.get("phoneNo") != null && !filters.get("phoneNo").toString().isBlank()) {
                    String transactionID = filters.get("phoneNo").toString();
                    Integer pmsId = Integer.parseInt(transactionID);
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("phoneNo")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("beneficiary")) {
                    try {
                        String orgId = filters.get("beneficiary").toString();
                        Integer id = Integer.parseInt(orgId);
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("beneficiary"), id));
                    } catch (NumberFormatException ignored) {
                    }
                }


                // Date range filtering for LocalDateTime
//                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
//                    spec = spec.and((root, query, cb) -> {
//                        Predicate predicate = cb.conjunction();
//
//                        try {
//                            if (filters.containsKey("dateFrom")) {
//                                String fromStr = filters.get("dateFrom").toString();
//                                if (!fromStr.isBlank()) {
//                                    LocalDateTime from = LocalDate.parse(fromStr).atStartOfDay();
//                                    log.info("Applying filter: requestTime >= {}", from);
//                                    predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("requestTime"), from));
//                                }
//                            }
//
//                            if (filters.containsKey("dateTo")) {
//                                String toStr = filters.get("dateTo").toString();
//                                if (!toStr.isBlank()) {
//                                    LocalDateTime to = LocalDate.parse(toStr).plusDays(1).atStartOfDay(); // exclusive
//                                    log.info("Applying filter: requestTime < {}", to);
//                                    predicate = cb.and(predicate, cb.lessThan(root.get("requestTime"), to));
//                                }
//                            }
//                        } catch (Exception e) {
//                            log.error("Error parsing dateFrom/dateTo filter: {}", e.getMessage(), e);
//                        }
//
//                        return predicate;
//                    });
//                }


                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<BatchDownloadExcelUnverified> pageResult = batchDownloadExcelUnverifiedRepo.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getTransactionChargesReport(Map<String, Object> filters) {
        try {
            Specification<TransactionChargesView> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("paymentType") && filters.get("paymentType") != null && !filters.get("paymentType").toString().isBlank()) {
                    String transactionID = filters.get("paymentType").toString();
                    Integer pmsId = Integer.parseInt(transactionID);
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("paymentType")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("name")) {
                    try {
                        String orgId = filters.get("name").toString();
                        Integer id = Integer.parseInt(orgId);
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("name"), id));
                    } catch (NumberFormatException ignored) {
                    }
                }

                if (filters.containsKey("phoneNo")) {
                    try {
                        String phoneNo = filters.get("phoneNo").toString();
                        //Integer id = Integer.parseInt(orgId);
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("phoneNo"), phoneNo));
                    } catch (NumberFormatException ignored) {
                    }
                }

                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        try {
                            if (filters.containsKey("dateFrom")) {
                                String fromStr = filters.get("dateFrom").toString();
                                if (!fromStr.isBlank()) {
                                    LocalDateTime from = LocalDate.parse(fromStr).atStartOfDay();
                                    log.info("Applying filter: requestTime >= {}", from);
                                    predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("transactionDate"), from));
                                }
                            }

                            if (filters.containsKey("dateTo")) {
                                String toStr = filters.get("dateTo").toString();
                                if (!toStr.isBlank()) {
                                    LocalDateTime to = LocalDate.parse(toStr).plusDays(1).atStartOfDay(); // exclusive
                                    log.info("Applying filter: requestTime < {}", to);
                                    predicate = cb.and(predicate, cb.lessThan(root.get("transactionDate"), to));
                                }
                            }
                        } catch (Exception e) {
                            log.error("Error parsing dateFrom/dateTo filter: {}", e.getMessage(), e);
                        }

                        return predicate;
                    });
                }


                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<TransactionChargesView> pageResult = transactionChargesViewRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getFundsTRansferView(Map<String, Object> filters) {
        try {
            Specification<FundsTransferView> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("receiptNumber") && filters.get("receiptNumber") != null && !filters.get("receiptNumber").toString().isBlank()) {
                    String transactionID = filters.get("receiptNumber").toString();
                    Integer pmsId = Integer.parseInt(transactionID);
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("receiptNumber")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("transactionType")) {
                    try {
                        String transactionType = filters.get("transactionType").toString();
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("transactionType"), transactionType));
                    } catch (NumberFormatException ignored) {
                    }
                }

                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        try {
                            if (filters.containsKey("dateFrom")) {
                                String fromStr = filters.get("dateFrom").toString();
                                if (!fromStr.isBlank()) {
                                    LocalDateTime from = LocalDate.parse(fromStr).atStartOfDay();
                                    log.info("Applying filter: requestTime >= {}", from);
                                    predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("transactionDate"), from));
                                }
                            }

                            if (filters.containsKey("dateTo")) {
                                String toStr = filters.get("dateTo").toString();
                                if (!toStr.isBlank()) {
                                    LocalDateTime to = LocalDate.parse(toStr).plusDays(1).atStartOfDay(); // exclusive
                                    log.info("Applying filter: requestTime < {}", to);
                                    predicate = cb.and(predicate, cb.lessThan(root.get("transactionDate"), to));
                                }
                            }
                        } catch (Exception e) {
                            log.error("Error parsing dateFrom/dateTo filter: {}", e.getMessage(), e);
                        }

                        return predicate;
                    });
                }


                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<FundsTransferView> pageResult = fundsTransferViewRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getPaymentStatementReport(Map<String, Object> filters) {
        try {
            Specification<StatementView> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("transactionId") && filters.get("transactionId") != null && !filters.get("transactionId").toString().isBlank()) {
                    String transactionID = filters.get("transactionId").toString();
                    Integer pmsId = Integer.parseInt(transactionID);
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("transactionId")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("transactionDetails")) {
                    try {
                        String orgId = filters.get("transactionDetails").toString();
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("transactionDetails"), orgId));
                    } catch (NumberFormatException ignored) {
                    }
                }

                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        try {
                            if (filters.containsKey("dateFrom")) {
                                String fromStr = filters.get("dateFrom").toString();
                                if (!fromStr.isBlank()) {
                                    LocalDateTime from = LocalDate.parse(fromStr).atStartOfDay();
                                    log.info("Applying filter: requestTime >= {}", from);
                                    predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("dateTime"), from));
                                }
                            }

                            if (filters.containsKey("dateTo")) {
                                String toStr = filters.get("dateTo").toString();
                                if (!toStr.isBlank()) {
                                    LocalDateTime to = LocalDate.parse(toStr).plusDays(1).atStartOfDay(); // exclusive
                                    log.info("Applying filter: requestTime < {}", to);
                                    predicate = cb.and(predicate, cb.lessThan(root.get("dateTime"), to));
                                }
                            }
                        } catch (Exception e) {
                            log.error("Error parsing dateFrom/dateTo filter: {}", e.getMessage(), e);
                        }

                        return predicate;
                    });
                }


                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<StatementView> pageResult = statementViewRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getBudgetaryReport(Map<String, Object> filters) {
        try {
            Specification<BudgetaryView> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("month") && filters.get("month") != null && !filters.get("month").toString().isBlank()) {
                    String transactionID = filters.get("month").toString();
                    Integer pmsId = Integer.parseInt(transactionID);
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("month")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("year")) {
                    try {
                        String year = filters.get("year").toString();
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("year"), year));
                    } catch (NumberFormatException ignored) {
                    }
                }


                // Date range filtering for LocalDateTime



                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<BudgetaryView> pageResult = budgetaryViewRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getDetailedSummaryReport(Map<String, Object> filters) {
        try {
            Specification<DetailedSummaryView> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("orgId") && filters.get("orgId") != null && !filters.get("orgId").toString().isBlank()) {
                    String transactionID = filters.get("orgId").toString();
                    Integer pmsId = Integer.parseInt(transactionID);
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("orgId")), "%" + pmsId + "%"));
                }

                if (filters.containsKey("fundsAllocated")) {
                    try {
                        String year = filters.get("fundsAllocated").toString();
                        BigDecimal fundAllocated = BigDecimal.valueOf(year.equals("0") ? -1 : Integer.parseInt(year));
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("year"), fundAllocated));
                    } catch (NumberFormatException ignored) {
                    }
                }


                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        try {
                            if (filters.containsKey("dateFrom")) {
                                String fromStr = filters.get("dateFrom").toString();
                                if (!fromStr.isBlank()) {
                                    LocalDateTime from = LocalDate.parse(fromStr).atStartOfDay();
                                    log.info("Applying filter: requestTime >= {}", from);
                                    predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("dateFrom"), from));
                                }
                            }

                            if (filters.containsKey("dateTo")) {
                                String toStr = filters.get("dateTo").toString();
                                if (!toStr.isBlank()) {
                                    LocalDateTime to = LocalDate.parse(toStr).plusDays(1).atStartOfDay(); // exclusive
                                    log.info("Applying filter: requestTime < {}", to);
                                    predicate = cb.and(predicate, cb.lessThan(root.get("dateTo"), to));
                                }
                            }
                        } catch (Exception e) {
                            log.error("Error parsing dateFrom/dateTo filter: {}", e.getMessage(), e);
                        }

                        return predicate;
                    });
                }



                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<DetailedSummaryView> pageResult = detailedSummaryViewRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getExpectedVsActualReport(Map<String, Object> filters) {
        try {
            Specification<ExpectedVsActualView> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("numberOfTransactions") && filters.get("numberOfTransactions") != null && !filters.get("numberOfTransactions").toString().isBlank()) {
                    String transactionID = filters.get("numberOfTransactions").toString();
                    Integer pmsId = Integer.parseInt(transactionID);
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("numberOfTransactions")), "%" + pmsId + "%"));
                }

                if (filters.containsKey("msisdn")) {
                    try {
                        String year = filters.get("msisdn").toString();
                        BigDecimal fundAllocated = BigDecimal.valueOf(year.equals("0") ? -1 : Integer.parseInt(year));
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("msisdn"), fundAllocated));
                    } catch (NumberFormatException ignored) {
                    }
                }


                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        try {
                            if (filters.containsKey("dateFrom")) {
                                String fromStr = filters.get("dateFrom").toString();
                                if (!fromStr.isBlank()) {
                                    LocalDateTime from = LocalDate.parse(fromStr).atStartOfDay();
                                    log.info("Applying filter: requestTime >= {}", from);
                                    predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("trxDate"), from));
                                }
                            }

                            if (filters.containsKey("dateTo")) {
                                String toStr = filters.get("dateTo").toString();
                                if (!toStr.isBlank()) {
                                    LocalDateTime to = LocalDate.parse(toStr).plusDays(1).atStartOfDay(); // exclusive
                                    log.info("Applying filter: requestTime < {}", to);
                                    predicate = cb.and(predicate, cb.lessThan(root.get("trxDate"), to));
                                }
                            }
                        } catch (Exception e) {
                            log.error("Error parsing dateFrom/dateTo filter: {}", e.getMessage(), e);
                        }

                        return predicate;
                    });
                }



                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<ExpectedVsActualView> pageResult = expectedVsActualViewRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getGatewayReport(Map<String, Object> filters) {
        try {
            Specification<GatewayView> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("organisation") && filters.get("organisation") != null && !filters.get("organisation").toString().isBlank()) {
                    String transactionID = filters.get("organisation").toString();
                    Integer pmsId = Integer.parseInt(transactionID);
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("organisation")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("mpesaId")) {
                    try {
                        String year = filters.get("mpesaId").toString();
                        BigDecimal fundAllocated = BigDecimal.valueOf(year.equals("0") ? -1 : Integer.parseInt(year));
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("mpesaId"), fundAllocated));
                    } catch (NumberFormatException ignored) {
                    }
                }


                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        try {
                            if (filters.containsKey("dateFrom")) {
                                String fromStr = filters.get("dateFrom").toString();
                                if (!fromStr.isBlank()) {
                                    LocalDateTime from = LocalDate.parse(fromStr).atStartOfDay();
                                    log.info("Applying filter: requestTime >= {}", from);
                                    predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("date"), from));
                                }
                            }

                            if (filters.containsKey("dateTo")) {
                                String toStr = filters.get("dateTo").toString();
                                if (!toStr.isBlank()) {
                                    LocalDateTime to = LocalDate.parse(toStr).plusDays(1).atStartOfDay(); // exclusive
                                    log.info("Applying filter: requestTime < {}", to);
                                    predicate = cb.and(predicate, cb.lessThan(root.get("date"), to));
                                }
                            }
                        } catch (Exception e) {
                            log.error("Error parsing dateFrom/dateTo filter: {}", e.getMessage(), e);
                        }

                        return predicate;
                    });
                }



                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<GatewayView> pageResult = gatewayViewRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> getMpesaRerport(Map<String, Object> filters) {
        try {
            Specification<MpesaReportView> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("beneficiary") && filters.get("beneficiary") != null && !filters.get("beneficiary").toString().isBlank()) {
                    String transactionID = filters.get("beneficiary").toString();
                    Integer pmsId = Integer.parseInt(transactionID);
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("beneficiary")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("contactNumber")) {
                    try {
                        String year = filters.get("contactNumber").toString();
                        BigDecimal fundAllocated = BigDecimal.valueOf(year.equals("0") ? -1 : Integer.parseInt(year));
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("contactNumber"), year));
                    } catch (NumberFormatException ignored) {
                    }
                }


                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        try {
                            if (filters.containsKey("dateFrom")) {
                                String fromStr = filters.get("dateFrom").toString();
                                if (!fromStr.isBlank()) {
                                    LocalDateTime from = LocalDate.parse(fromStr).atStartOfDay();
                                    log.info("Applying filter: requestTime >= {}", from);
                                    predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("date"), from));
                                }
                            }

                            if (filters.containsKey("dateTo")) {
                                String toStr = filters.get("dateTo").toString();
                                if (!toStr.isBlank()) {
                                    LocalDateTime to = LocalDate.parse(toStr).plusDays(1).atStartOfDay(); // exclusive
                                    log.info("Applying filter: requestTime < {}", to);
                                    predicate = cb.and(predicate, cb.lessThan(root.get("date"), to));
                                }
                            }
                        } catch (Exception e) {
                            log.error("Error parsing dateFrom/dateTo filter: {}", e.getMessage(), e);
                        }

                        return predicate;
                    });
                }



                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<MpesaReportView> pageResult = mpesaReportViewRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> geGatwayNotMpesaReport(Map<String, Object> filters) {
        try {
            Specification<GatewayNotMpesaView> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("recipientName") && filters.get("recipientName") != null && !filters.get("recipientName").toString().isBlank()) {
                    String transactionID = filters.get("recipientName").toString();
                    Integer pmsId = Integer.parseInt(transactionID);
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("recipientName")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("trxId")) {
                    try {
                        String year = filters.get("trxId").toString();
                        BigDecimal fundAllocated = BigDecimal.valueOf(year.equals("0") ? -1 : Integer.parseInt(year));
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("trxId"), year));
                    } catch (NumberFormatException ignored) {
                    }
                }


                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        try {
                            if (filters.containsKey("dateFrom")) {
                                String fromStr = filters.get("dateFrom").toString();
                                if (!fromStr.isBlank()) {
                                    LocalDateTime from = LocalDate.parse(fromStr).atStartOfDay();
                                    log.info("Applying filter: requestTime >= {}", from);
                                    predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("transactionDate"), from));
                                }
                            }

                            if (filters.containsKey("dateTo")) {
                                String toStr = filters.get("dateTo").toString();
                                if (!toStr.isBlank()) {
                                    LocalDateTime to = LocalDate.parse(toStr).plusDays(1).atStartOfDay(); // exclusive
                                    log.info("Applying filter: requestTime < {}", to);
                                    predicate = cb.and(predicate, cb.lessThan(root.get("transactionDate"), to));
                                }
                            }
                        } catch (Exception e) {
                            log.error("Error parsing dateFrom/dateTo filter: {}", e.getMessage(), e);
                        }

                        return predicate;
                    });
                }



                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<GatewayNotMpesaView> pageResult = gatewayNotMpesaViewRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }
    private List<?> geyMpesaNotGatewayReport(Map<String, Object> filters) {
        try {
            Specification<MpesaNotGatewayView> spec = Specification.where(null);

            if (filters != null) {
                if (filters.containsKey("beneficiary") && filters.get("beneficiary") != null && !filters.get("beneficiary").toString().isBlank()) {
                    String transactionID = filters.get("beneficiary").toString();
                    Integer pmsId = Integer.parseInt(transactionID);
                    spec = spec.and((root, query, cb) ->
                            cb.like(cb.lower(root.get("beneficiary")), "%" + transactionID + "%"));
                }

                if (filters.containsKey("contactNumber")) {
                    try {
                        String year = filters.get("contactNumber").toString();
                        BigDecimal fundAllocated = BigDecimal.valueOf(year.equals("0") ? -1 : Integer.parseInt(year));
                        spec = spec.and((root, query, cb) ->
                                cb.equal(root.get("contactNumber"), year));
                    } catch (NumberFormatException ignored) {
                    }
                }


                // Date range filtering for LocalDateTime
                if (filters.containsKey("dateFrom") || filters.containsKey("dateTo")) {
                    spec = spec.and((root, query, cb) -> {
                        Predicate predicate = cb.conjunction();

                        try {
                            if (filters.containsKey("dateFrom")) {
                                String fromStr = filters.get("dateFrom").toString();
                                if (!fromStr.isBlank()) {
                                    LocalDateTime from = LocalDate.parse(fromStr).atStartOfDay();
                                    log.info("Applying filter: requestTime >= {}", from);
                                    predicate = cb.and(predicate, cb.greaterThanOrEqualTo(root.get("trxDate"), from));
                                }
                            }

                            if (filters.containsKey("dateTo")) {
                                String toStr = filters.get("dateTo").toString();
                                if (!toStr.isBlank()) {
                                    LocalDateTime to = LocalDate.parse(toStr).plusDays(1).atStartOfDay(); // exclusive
                                    log.info("Applying filter: requestTime < {}", to);
                                    predicate = cb.and(predicate, cb.lessThan(root.get("trxDate"), to));
                                }
                            }
                        } catch (Exception e) {
                            log.error("Error parsing dateFrom/dateTo filter: {}", e.getMessage(), e);
                        }

                        return predicate;
                    });
                }



                Object limitObj = filters.getOrDefault("limit", 1000);
                int limit = (limitObj instanceof Integer) ? (Integer) limitObj : 1000;
                Object pageObj = filters.get("page");
                int page;
                try {
                    if (pageObj instanceof Number) {
                        page = ((Number) pageObj).intValue();
                    } else if (pageObj instanceof String) {
                        page = Integer.parseInt((String) pageObj);
                    } else {
                        page = 0;
                    }
                } catch (Exception e) {
                    page = 0;
                }
                PageRequest pageRequest = PageRequest.of(page, limit);

                Page<MpesaNotGatewayView> pageResult = mpesaNotGatewayViewRepository.findAll(spec, pageRequest);
                return pageResult.getContent();

            }
        } catch (Exception e) {
            log.error("An error occurred"+ e.getCause());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return null;
    }







    /**
     * Get Order report data - placeholder implementation
     */
    private List<?> getOrderReport(Map<String, Object> filters) {
        // TODO: Implement order report logic
        // Example:
        // OrderRequest request = mapFiltersToOrderRequest(filters);
        // return orderService.getFilteredOrders(request);
        throw new UnsupportedOperationException("Order report not yet implemented");
    }

    /**
     * Get Contact report data - placeholder implementation
     */
    private List<?> getContactReport(Map<String, Object> filters) {
        // TODO: Implement contact report logic
        throw new UnsupportedOperationException("Contact report not yet implemented");
    }

    /**
     * Get available report types
     */
    public List<String> getAvailableReports() {
        return List.of(
                "addressbook",
                "users",
                "orders",
                "contacts",
                "budget"
        );
    }

    /**
     * Validate if report type is supported
     */
    public boolean isReportSupported(String reportName) {
        return getAvailableReports().contains(reportName.toLowerCase());
    }
}