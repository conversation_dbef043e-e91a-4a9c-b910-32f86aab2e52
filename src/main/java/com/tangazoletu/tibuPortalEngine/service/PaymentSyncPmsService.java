package com.tangazoletu.tibuPortalEngine.service;

import com.tangazoletu.tibuPortalEngine.dto.paymentapidto.PaymentSyncResponse;
import com.tangazoletu.tibuPortalEngine.entities.ApiOrder;
import com.tangazoletu.tibuPortalEngine.entities.Param;
import com.tangazoletu.tibuPortalEngine.entities.PaymentApproval;
import com.tangazoletu.tibuPortalEngine.entities.PmsApprovalSummary;
import com.tangazoletu.tibuPortalEngine.enums.ApiResponse;
import com.tangazoletu.tibuPortalEngine.repositories.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@Slf4j
@RequiredArgsConstructor
public class PaymentSyncPmsService {

    private RestTemplate restTemplate;
    private final PaymentApprovalRepository paymentApprovalRepository;
    private final ParamRepository paramRepository;
    private final PmsApprovalSummaryRepo pmsApprovalSummaryRepo;
    private final SupervisionDetailRepository supervisionDetailRepository;
    private final ApiOrderRepo apiOrderRepo;
    @Value("${pms.apiKey}")
      String apiKey;
    @Value("${pms.authKey}")
     String authKey;
    public PaymentSyncResponse sendPaymentSyncToPms(Long orderId, String paymentMode) {
        log.info(" Starting payment sync to PMS | Order ID: {}, Mode: {}", orderId, paymentMode);

        try {
            //  Fetch API key and PMS domain from config
            Optional<Param> param = paramRepository.findByParameter("NEW_PMS_API_DOMAIN");
            String pmsLinkValue = null;
            if (param.isPresent()) {
                 pmsLinkValue = param.get().getValue();
            }


            log.info(" PMS endpoint base: {}", pmsLinkValue);

            Optional<PmsApprovalSummary> orderDetailsOpt = pmsApprovalSummaryRepo.findFirstByPaymentId(orderId);
            Optional<PaymentApproval> levelDetailsOpt = paymentApprovalRepository.findTopByApiOrderIdOrderByIdDesc(orderId);

            if (orderDetailsOpt.isEmpty() || levelDetailsOpt.isEmpty()) {
                log.warn("❌ Order or approval level not found for order ID {}", orderId);
                return PaymentSyncResponse.failed("Order or approval level not found");
            }

            PmsApprovalSummary orderDetails = orderDetailsOpt.get();
            PaymentApproval levelDetails = levelDetailsOpt.get();

            HttpHeaders headers = new HttpHeaders();
            headers.set("X-Api-Key", apiKey);
            headers.setContentType(MediaType.APPLICATION_JSON);

            Map<String, Object> payload;

            if ("mdr".equalsIgnoreCase(paymentMode)) {
                payload = Map.of(
                        "Payment_Id", orderDetails.getPaymentId(),
                        "Payment_Status", orderDetails.getApprovalStatus(),
                        "Payment_Status_desc", orderDetails.getApprovalNotes() + " (" + levelDetails.getApprovalStatus() + " at level " + levelDetails.getApprovalLevelName() + ")",
                        "Approved_Amount", orderDetails.getCredit(),
                        "Authorisation_key", authKey
                );

                String endpoint = pmsLinkValue + "api/ClientPayments/sendMDRTBPayment_status_to_TIBU";
                log.info("📡 Sending MDR payment sync request to: {}", endpoint);
                log.debug("📦 Payload: {}", payload);

                ResponseEntity<Map> response = restTemplate.postForEntity(endpoint, new HttpEntity<>(payload, headers), Map.class);

                log.info("✅ Received MDR response: {}", response.getBody());

                return handlePmsResponse(response.getBody(), orderId);
            }

            if ("supervision".equalsIgnoreCase(paymentMode)) {
                List<Map<String, Object>> items = supervisionDetailRepository.fetchSupervisionExpensesByOrderId(orderId.intValue());

                payload = Map.of(
                        "Payment_Id", orderDetails.getPaymentId(),
                        "Payment_Status", orderDetails.getApprovalStatus(),
                        "Payment_Status_desc", orderDetails.getApprovalNotes() + " (" + levelDetails.getApprovalStatus() + " at level " + levelDetails.getApprovalLevelName() + ")",
                        "Items", items,
                        "Approval_Level", levelDetails.getApprovalLevel(),
                        "Authorisation_Key", authKey
                );

                String endpoint = pmsLinkValue + "api/ClientPayments/sendPayment_status_to_TIBU";
                log.info("📡 Sending Supervision payment sync request to: {}", endpoint);
                log.debug("📦 Payload: {}", payload);

                ResponseEntity<Map> response = restTemplate.postForEntity(endpoint, new HttpEntity<>(payload, headers), Map.class);

                log.info("✅ Received Supervision response: {}", response.getBody());

                return handlePmsResponse(response.getBody(), orderId);
            }

            log.warn("❌ Unsupported payment mode: {}", paymentMode);
            return PaymentSyncResponse.failed("Invalid payment mode");

        } catch (Exception e) {
            log.error("🔥 Exception during payment sync: {}", e.getMessage(), e);
            return PaymentSyncResponse.failed("Exception: " + e.getMessage());
        }
    }

    private PaymentSyncResponse handlePmsResponse(Map<?, ?> response, Long orderId) {
        if (response == null || response.isEmpty()) {
            log.warn("❌ Empty response from PMS");
            return PaymentSyncResponse.failed("Empty response from PMS");
        }

        Integer isPaymentUpdated = Integer.parseInt(String.valueOf(response.get("isPayment_Updated")));
        String responseCode = String.valueOf(response.get("Response_Code"));
        String responseValue = String.valueOf(response.get("Response_Value"));

        log.info("🔍 PMS Response Details => Updated: {}, Code: {}, Message: {}", isPaymentUpdated, responseCode, responseValue);

        if ("200".equals(responseCode) && isPaymentUpdated == 1) {
            // ✅ Optional: Update apiorder.status_sent = 'Yes'
            Optional<ApiOrder> apiOrder = apiOrderRepo.findById(orderId);
            if (apiOrder.isPresent()) {
                apiOrder.get().setStatusSent(ApiOrder.StatusSent.Yes);
                apiOrderRepo.save(apiOrder.get());
            }
            log.info("✅ Payment status synced successfully for Order ID {}: {}", orderId, responseValue);
            return PaymentSyncResponse.success("Payment  successfully Updated: " + responseValue);
        } else {
            log.warn("❌ Payment sync failed for Order ID {}: {}", orderId, responseValue);
            return PaymentSyncResponse.failed("Failed to update payment:: " + responseValue);
        }
    }






}
