package com.tangazoletu.tibuPortalEngine.service;

import com.tangazoletu.tibuPortalEngine.dto.MasterRecordRequest;
import com.tangazoletu.tibuPortalEngine.dto.MasterRecordsResponse;
import com.tangazoletu.tibuPortalEngine.dto.OrderTypeRequest;
import com.tangazoletu.tibuPortalEngine.dto.PerDiemRequest;
import com.tangazoletu.tibuPortalEngine.entities.*;
import com.tangazoletu.tibuPortalEngine.enums.ApiResponse;
import com.tangazoletu.tibuPortalEngine.repositories.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor

public class MasterRecordsService {

    private final OrderTypeRepo orderTypeRepo;
    private final ProvinceRepo provinceRepo;
    private final BeneficiaryTypeRepo beneficiaryTypeRepo;
    private final CountyRepo countyRepo;
    private final PerdiemRepo perdiemRepo;
    private final JobGroupRepo jobGroupRepo;
    private final RecipientRepo recipientRepo;
    private final OrganizationRepository organizationRepo;
    private final OrgOrderTypeRepo orgOrderTypeRepo;
    private final LineItemRepo lineItemRepo;
    private final DistrictRepository districtRepo;
    private final FacilityViewRepository facilityViewRepo;
    private final BudgetViewRepository budgetViewRepo;
    private final MdrPatientRepository mdrPatientRepo;
    private final BeneficiarySummaryViewRepo beneficiarySummaryViewRepo;
    private final BudgetRepo budgetRepo;
    private final OrderTypeViewRepo orderTypeViewRepo;


    public MasterRecordsResponse getMasterRecords(OrderTypeRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {

        MasterRecordsResponse masterRecordsResponse = MasterRecordsResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();
        try{
            String keyword = request.getKeyword();

            int page = request.getPage();
            int pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);

            Long id = request.getId();

            if (id != null) {

                Page<Map<String, Object>> orderTypes = orderTypeRepo.findById(id, pageable);
                masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                masterRecordsResponse.setResponseMessage("Success");
                masterRecordsResponse.setData(orderTypes);
            }else {

                Specification<OrderTypeView> spec = Specification.where(null);

                if (keyword != null && !keyword.isBlank()) {
                    spec = spec.and((root, query, cb) -> {
                        String likeKeyword = "%" + keyword.toLowerCase() + "%";
                        return cb.or(
                                cb.like(cb.lower(root.get("organisation")), likeKeyword),
                                cb.like(cb.lower(root.get("paymentType")), likeKeyword),
                                cb.like(cb.lower(root.get("timeCreated")), likeKeyword)
                        );
                    });
                }

                Page<?> orderTypes = orderTypeViewRepo.findAll(spec, pageable);
                if (!orderTypes.isEmpty()) {
                    masterRecordsResponse.setData(orderTypes);
                    masterRecordsResponse.setResponseMessage("Success");
                    masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                }
            }

        }catch (Exception e){
            log.error("an error occured", e.getMessage());
            masterRecordsResponse.setResponseCode(ApiResponse.FAIL.getCode());
            masterRecordsResponse.setResponseMessage("An error occured");
            e.printStackTrace();
        }
        return masterRecordsResponse;
    }

    public MasterRecordsResponse getProvince(MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        MasterRecordsResponse masterRecordsResponse = MasterRecordsResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();
        try {
            String keyword = request.getKeyword();
            Long id = request.getId();
            Integer page = request.getPage();
            Integer pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);

            if (id != null) {
                Page<Map<String, Object>> provinces = provinceRepo.findProvinces(id, pageable);
                masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                masterRecordsResponse.setResponseMessage("Success");
                masterRecordsResponse.setData(provinces);
            }else {
                Page<Map<String, Object>> province = provinceRepo.findProvincesWithKeyword(keyword, pageable);
                if (!province.isEmpty()) {
                    masterRecordsResponse.setData(province);
                    masterRecordsResponse.setResponseMessage("Success");
                    masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                }else {
                    masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    masterRecordsResponse.setResponseMessage("No province found");
                    masterRecordsResponse.setData(province);
                }
            }

        }catch (Exception e){
            masterRecordsResponse.setResponseMessage("An error occured");
            masterRecordsResponse.setResponseCode(ApiResponse.FAIL.getCode());
            log.error("an error occured", e.getMessage());
            e.printStackTrace();
        }
        return masterRecordsResponse;
    }

    public MasterRecordsResponse getBeneficiaryType(MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        MasterRecordsResponse masterRecordsResponse = MasterRecordsResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();
        try {
            String keyword = request.getKeyword();
            Long id = request.getId();
            int page = request.getPage() != null ? request.getPage() : 1;
            int pageSize = request.getPageSize() != null ? request.getPageSize() : 10;
            Pageable pageable = PageRequest.of(page, pageSize);
            if (id != null) {
                Page<BeneficiaryType> beneficiaryTypes = beneficiaryTypeRepo.findById(id, pageable);
                masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                masterRecordsResponse.setResponseMessage("Success");
                masterRecordsResponse.setData(beneficiaryTypes);
            }else {
                Page<BeneficiaryType> beneficiaryTypes = beneficiaryTypeRepo.findByName(keyword, pageable);
                if (!beneficiaryTypes.isEmpty()) {
                    masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    masterRecordsResponse.setResponseMessage("Success");
                    masterRecordsResponse.setData(beneficiaryTypes);
                }else {
                    masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    masterRecordsResponse.setResponseMessage("No BeneficiaryType found");
                }
            }

        }catch (Exception e){
            masterRecordsResponse.setResponseMessage("An error occured");
            masterRecordsResponse.setResponseCode(ApiResponse.FAIL.getCode());
            log.error("an error occured", e.getMessage());
            e.printStackTrace();
        }
        return masterRecordsResponse;

    }
    public MasterRecordsResponse getCounty(MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        MasterRecordsResponse masterRecordsResponse = MasterRecordsResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();
        try {
            String keyword = request.getKeyword();
            Long id = request.getId();
            Integer page = request.getPage();
            Integer pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);
            if (id != null) {
                Page<County> counties = countyRepo.findById(id, pageable);
                masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                masterRecordsResponse.setResponseMessage("Success");
                masterRecordsResponse.setData(counties);
            }else {
                Page<County> counties = countyRepo.findByName(keyword, pageable);
                if (!counties.isEmpty()) {
                    masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    masterRecordsResponse.setResponseMessage("Success");
                    masterRecordsResponse.setData(counties);
                }else {
                    masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    masterRecordsResponse.setResponseMessage("No Counties found");
                }
            }

        }catch (Exception e){
            masterRecordsResponse.setResponseMessage("An error occurred");
            masterRecordsResponse.setResponseCode(ApiResponse.FAIL.getCode());
            log.error("an error occured", e.getMessage());
            e.printStackTrace();
        }
        return masterRecordsResponse;

    }

    public MasterRecordsResponse getPerDiem(PerDiemRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        MasterRecordsResponse masterRecordsResponse = MasterRecordsResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();
        try {

            String keyword = request.getKeyword();
            String province = request.getProvince();
            String orgFilter = request.getOrgFilter();
            Long id = request.getId();
            int page = request.getPage();
            int pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);

            if (id != null) {
                Page<Perdiem> perdiems = perdiemRepo.findById(id, pageable);
                masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                masterRecordsResponse.setResponseMessage("Success");
                masterRecordsResponse.setData(perdiems);
            }else {
                Page<Map<String, Object>> perdiems = perdiemRepo.findFilteredPerDiem(keyword, province, orgFilter, pageable);
                if (!perdiems.isEmpty()) {
                    masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    masterRecordsResponse.setResponseMessage("Success");
                    masterRecordsResponse.setData(perdiems);
                }else {
                    masterRecordsResponse.setResponseMessage("No Perdiems found");
                    masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    masterRecordsResponse.setData(perdiems);
                }
            }

        }catch (Exception e){
            log.error("an error occured", e.getMessage());
            masterRecordsResponse.setResponseMessage("An error occured");
            masterRecordsResponse.setResponseCode(ApiResponse.FAIL.getCode());
            httpServletResponse.setStatus(500);
        }
        return masterRecordsResponse;
    }
    public MasterRecordsResponse getJobGroup(MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        MasterRecordsResponse masterRecordsResponse = MasterRecordsResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();
        try {
            String keyword = request.getKeyword();
            Long id = request.getId();
            Integer page = request.getPage();
            Integer pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);
            if (id != null) {
                Page<JobGroup> jobGroups = jobGroupRepo.findById(id, pageable);
                masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                masterRecordsResponse.setResponseMessage("Success");
                masterRecordsResponse.setData(jobGroups);
            }else {
                Page<JobGroup> jobGroups = jobGroupRepo.findByKeyword(keyword, pageable);
                if (!jobGroups.isEmpty()) {
                    masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    masterRecordsResponse.setResponseMessage("Success");
                    masterRecordsResponse.setData(jobGroups);
                }else {
                    masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    masterRecordsResponse.setResponseMessage("No JobGroups found");
                }
            }

        }catch (Exception e){
            masterRecordsResponse.setResponseMessage("An error occurred");
            masterRecordsResponse.setResponseCode(ApiResponse.FAIL.getCode());
            log.error("an error occured", e.getMessage());
            e.printStackTrace();
        }
        return masterRecordsResponse;

    }
    public MasterRecordsResponse getRecipient(MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        MasterRecordsResponse masterRecordsResponse = MasterRecordsResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();
        try {
            String keyword = request.getKeyword();
            Long id = request.getId();
            Integer page = request.getPage();
            Integer pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);
            if (id != null) {
                Page<Recipient> recipients = recipientRepo.findById(id, pageable);
                masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                masterRecordsResponse.setResponseMessage("Success");
                masterRecordsResponse.setData(recipients);
            }else {
                Page<Recipient> recipients = recipientRepo.findByKeyword(keyword, pageable);
                if (!recipients.isEmpty()) {
                    masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    masterRecordsResponse.setResponseMessage("Success");
                    masterRecordsResponse.setData(recipients);
                }else {
                    masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    masterRecordsResponse.setResponseMessage("No Recipient found");
                }
            }

        }catch (Exception e){
            masterRecordsResponse.setResponseMessage("An error occurred");
            masterRecordsResponse.setResponseCode(ApiResponse.FAIL.getCode());
            log.error("an error occured", e.getMessage());
            e.printStackTrace();
        }
        return masterRecordsResponse;

    }
    public MasterRecordsResponse getOrganization(MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        MasterRecordsResponse masterRecordsResponse = MasterRecordsResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();
        try {
            String keyword = request.getKeyword();
            Long id = request.getId();
            Integer page = request.getPage();
            Integer pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);
            Integer orgId = request.getOrgId();
            if (id != null) {
                Page<Organisation> organisations = organizationRepo.findById(id, pageable);
                masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                masterRecordsResponse.setResponseMessage("Success");
                masterRecordsResponse.setData(organisations);
            }else {
                Page<Organisation> organisations = organizationRepo.findFiltered(orgId,keyword, pageable);
                if (!organisations.isEmpty()) {
                    masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    masterRecordsResponse.setResponseMessage("Success");
                    masterRecordsResponse.setData(organisations);
                }else {
                    masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    masterRecordsResponse.setResponseMessage("No Organizations found");
                }
            }

        }catch (Exception e){
            masterRecordsResponse.setResponseMessage("An error occurred");
            masterRecordsResponse.setResponseCode(ApiResponse.FAIL.getCode());
            log.error("an error occured", e.getMessage());
            e.printStackTrace();
        }
        return masterRecordsResponse;

    }
    public MasterRecordsResponse getOrganizationOrderType(MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        MasterRecordsResponse masterRecordsResponse = MasterRecordsResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();
        try {
            String keyword = request.getKeyword();
            Long id = request.getId();
            Integer page = request.getPage();
            Integer pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);
            Integer orgId = request.getOrgId();
            if (id != null) {
                Page<OrgOrderType> organisations = orgOrderTypeRepo.findById(id, pageable);
                masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                masterRecordsResponse.setResponseMessage("Success");
                masterRecordsResponse.setData(organisations);
            }else {
                Page<Map<String, Object>> orgOrdertypeData = orgOrderTypeRepo.findOrgOrdertypeData(String.valueOf(orgId),keyword, pageable);
                if (!orgOrdertypeData.isEmpty()) {
                    masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    masterRecordsResponse.setResponseMessage("Success");
                    masterRecordsResponse.setData(orgOrdertypeData);
                }else {
                    masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    masterRecordsResponse.setResponseMessage("No Organizations found");
                }
            }

        }catch (Exception e){
            masterRecordsResponse.setResponseMessage("An error occurred");
            masterRecordsResponse.setResponseCode(ApiResponse.FAIL.getCode());
            log.error("an error occured", e.getMessage());
            e.printStackTrace();
        }
        return masterRecordsResponse;

    }
    public MasterRecordsResponse getLineItem(MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        MasterRecordsResponse masterRecordsResponse = MasterRecordsResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();
        try {
            String keyword = request.getKeyword();
            Long id = request.getId();
            Integer page = request.getPage();
            Integer pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);
            if (id != null) {
                Page<LineItem> lineItems = lineItemRepo.findById(id, pageable);
                masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                masterRecordsResponse.setResponseMessage("Success");
                masterRecordsResponse.setData(lineItems);
            }else {
                Page<LineItem> lineItems = lineItemRepo.searchByKeyword(keyword, pageable);
                if (!lineItems.isEmpty()) {
                    masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    masterRecordsResponse.setResponseMessage("Success");
                    masterRecordsResponse.setData(lineItems);
                }else {
                    masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    masterRecordsResponse.setResponseMessage("No Line Items found");
                }
            }

        }catch (Exception e){
            masterRecordsResponse.setResponseMessage("An error occurred");
            masterRecordsResponse.setResponseCode(ApiResponse.FAIL.getCode());
            log.error("an error occured", e.getMessage());
            e.printStackTrace();
        }
        return masterRecordsResponse;

    }
    public MasterRecordsResponse getDistrict(MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        MasterRecordsResponse masterRecordsResponse = MasterRecordsResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();
        try{

            String keyword = request.getKeyword();
            int page = request.getPage();
            int pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);

            Page<ViewDistrictInfo> districts = districtRepo.searchByKeyword(keyword,  pageable);
            if (!districts.isEmpty()) {
                masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                masterRecordsResponse.setResponseMessage("Success");
                masterRecordsResponse.setData(districts);
            }



        }catch (Exception e){

            masterRecordsResponse.setResponseMessage("An error occurred");
            masterRecordsResponse.setResponseCode(ApiResponse.FAIL.getCode());
            log.error("an error occured", e.getMessage());
            e.printStackTrace();
        }
        return masterRecordsResponse;
    }

    public MasterRecordsResponse getFacility(MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        MasterRecordsResponse masterRecordsResponse = MasterRecordsResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();
        try {
            String keyword = request.getKeyword();
            int page = request.getPage();
            int pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);

            Page<FacilityView> facilityViews = facilityViewRepo.searchFacilities(keyword, pageable);
            if (!facilityViews.isEmpty()) {
                masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                masterRecordsResponse.setResponseMessage("Success");
                masterRecordsResponse.setData(facilityViews);
            }

        }catch (Exception e){
            masterRecordsResponse.setResponseMessage("An error occurred");
            masterRecordsResponse.setResponseCode(ApiResponse.FAIL.getCode());
            log.error("an error occured", e.getMessage());
            e.printStackTrace();
        }
        return masterRecordsResponse;
    }

    public MasterRecordsResponse getBudget(MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        MasterRecordsResponse masterRecordsResponse = MasterRecordsResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();

        try {
            String keyword = request.getKeyword();
            int page = request.getPage();
            int pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);

            // If ID is provided, fetch specific Budget
            if (request.getId() != null) {
                Page<Budget> views = budgetRepo.findTopById(Math.toIntExact(request.getId()), PageRequest.of(0, 1));
                if (!views.isEmpty()) {
                    masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                    masterRecordsResponse.setResponseMessage("Success");
                    masterRecordsResponse.setData(views);
                    return masterRecordsResponse; // Early return
                }
            }

            // Otherwise, search budget view
            Page<BudgetView> views1 = budgetViewRepo.searchBudget(keyword, pageable);
            if (!views1.isEmpty()) {
                masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                masterRecordsResponse.setResponseMessage("Success");
                masterRecordsResponse.setData(views1);
            }

        } catch (Exception e) {
            masterRecordsResponse.setResponseMessage("An error occurred");
            masterRecordsResponse.setResponseCode(ApiResponse.FAIL.getCode());
            log.error("An error occurred", e);
        }

        return masterRecordsResponse;
    }

    public MasterRecordsResponse getMdrPatient(MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        MasterRecordsResponse masterRecordsResponse = MasterRecordsResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();
        try {
            String keyword = request.getKeyword();
            int page = request.getPage();
            int pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);
            Integer countyId = request.getCountyId();

            Integer beneficiaryType = request.getBeneficiaryId();
            Integer confimed = request.getConfirmed();

            String startDate = request.getStartDate(); // e.g., "2024-06-18T10:30"
            String endDate = request.getEndDate();     // e.g., "2024-06-18T15:00"

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm");
            LocalDateTime startDateTime= null;
            LocalDateTime endDateTime = null;

            if (startDate != null && endDate != null) {

                 startDateTime = LocalDateTime.parse(startDate, formatter);
                 endDateTime = LocalDateTime.parse(endDate, formatter);
            }


            Page<MdrPatient> mdrPatients = mdrPatientRepo.searchPatients(keyword, countyId,beneficiaryType,startDateTime,endDateTime,confimed, pageable);
            if (!mdrPatients.isEmpty()) {
                masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                masterRecordsResponse.setResponseMessage("Success");
                masterRecordsResponse.setData(mdrPatients);
            }


        }catch (Exception e){
            masterRecordsResponse.setResponseMessage("An error occurred");
            masterRecordsResponse.setResponseCode(ApiResponse.FAIL.getCode());
            log.error("an error occured", e.getMessage());
            e.printStackTrace();
        }
        return masterRecordsResponse;
    }
    public MasterRecordsResponse getBeneficiary(MasterRecordRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        MasterRecordsResponse masterRecordsResponse = MasterRecordsResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();
        try {

            String keyword = request.getKeyword();
            int page = request.getPage();
            int pageSize = request.getPageSize();
            String province = request.getProvince();
            Pageable pageable = PageRequest.of(page, pageSize);
            String county = String.valueOf(request.getCountyId());

            //Page<BeneficiarySummaryView> beneficiarySummaryViews = beneficiarySummaryViewRepo.searchFiltered(province,county,keyword,pageable);
            Page<BeneficiarySummaryView> beneficiarySummaryViews1 = beneficiarySummaryViewRepo.findAll(pageable);
            if (beneficiarySummaryViews1 != null){
                masterRecordsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                masterRecordsResponse.setResponseMessage("Success");
                masterRecordsResponse.setData(beneficiarySummaryViews1);
            }



        }catch (Exception e){
            masterRecordsResponse.setResponseMessage("An error occurred");
            masterRecordsResponse.setResponseCode(ApiResponse.FAIL.getCode());
            log.error("an error occured", e.getMessage());

        }
        return masterRecordsResponse;
    }

}
