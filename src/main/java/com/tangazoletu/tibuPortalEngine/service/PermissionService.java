package com.tangazoletu.tibuPortalEngine.service;

import com.tangazoletu.tibuPortalEngine.controllers.PermissionsController;
import com.tangazoletu.tibuPortalEngine.dto.ChildPermissionDto;
import com.tangazoletu.tibuPortalEngine.dto.ChildPermissionRequest;
import com.tangazoletu.tibuPortalEngine.dto.MenuItemDto;
import com.tangazoletu.tibuPortalEngine.dto.MenuPermissionDto;
import com.tangazoletu.tibuPortalEngine.repositories.MenuPermissionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class PermissionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PermissionService.class);

    @Autowired
    private MenuPermissionRepository menuPermissionRepository;

    public List<MenuPermissionDto> getMenuPermissions(String userId) {
        List<String> modules = menuPermissionRepository.findDistinctModules();
        List<MenuPermissionDto> menuPermissions = new ArrayList<>();

        for (String module : modules) {
            String displayName = formatEnumValue(module);
            List<MenuItemDto> menuItems = getMenuItemsForModule(userId, displayName);
            if (!menuItems.isEmpty()) {
                menuPermissions.add(new MenuPermissionDto(displayName, menuItems));
            }
        }

        LOGGER.info("Check menu permissions > {}", menuPermissions);

        return menuPermissions;
    }
    public static String formatEnumValue(String value) {
        return value.replaceAll("([a-z])([A-Z])", "$1 $2");
    }

    private List<MenuItemDto> getMenuItemsForModule(String userId, String module) {
        // Get level 1 menu items
        List<Object[]> level1Results = menuPermissionRepository.findMenuItemsByUserAndModule(
                userId, module, 1, null
        );

        List<MenuItemDto> level1Items = level1Results.stream()
                .map(this::mapToMenuItemDto)
                .collect(Collectors.toList());

        // For each level 1 item, get its children (level 2)
        for (MenuItemDto level1Item : level1Items) {
            List<Object[]> level2Results = menuPermissionRepository.findMenuItemsByUserAndModule(
                    userId, module, 2, level1Item.getId()
            );

            List<MenuItemDto> children = level2Results.stream()
                    .map(this::mapToMenuItemDto)
                    .collect(Collectors.toList());

            level1Item.setChildren(children);
        }

        return level1Items;
    }

    public List<ChildPermissionDto> getChildPermissions(ChildPermissionRequest request) {
        // Using native query approach
        List<Object[]> results = menuPermissionRepository.findChildPermissionsByModule(request.getUserId(),request.getModule());

        return results.stream()
                .map(row -> new ChildPermissionDto(
                        ((Number) row[0]).longValue(),
                        (String) row[1],
                        (String) row[2]
                ))
                .collect(Collectors.toList());
    }

    private MenuItemDto mapToMenuItemDto(Object[] result) {
        MenuItemDto dto = new MenuItemDto();
        dto.setMenuName((String) result[0]);
        dto.setUrl((String) result[1]);
        dto.setId(((Number) result[2]).longValue());
        dto.setMenuLevel((Integer) result[3]);
        dto.setMenuPos((Integer) result[4]);
        // child_of is at index 5 if needed
        return dto;
    }

    public String processUrl(String url, String baseUrl) {
        if ("/".equals(url)) {
            return baseUrl + "/tibu/";
        }
        return url;
    }
}
