package com.tangazoletu.tibuPortalEngine.service;

import com.tangazoletu.tibuPortalEngine.dto.ApiResponse;
import com.tangazoletu.tibuPortalEngine.dto.ProcessRequest;
import com.tangazoletu.tibuPortalEngine.dto.ReportRequest;
import com.tangazoletu.tibuPortalEngine.dto.StatusRequest;
import com.tangazoletu.tibuPortalEngine.entities.ApiOrderResubmissionsView;
import com.tangazoletu.tibuPortalEngine.entities.PaymentApproval;
import com.tangazoletu.tibuPortalEngine.dto.ReportResponse;
import com.tangazoletu.tibuPortalEngine.dto.ReportResponse;
import com.tangazoletu.tibuPortalEngine.entities.ApiOrderReport;
import com.tangazoletu.tibuPortalEngine.entities.MpesaUploads;
import com.tangazoletu.tibuPortalEngine.dto.StatusRequest;
import com.tangazoletu.tibuPortalEngine.entities.ApiOrderResubmissionsView;
import com.tangazoletu.tibuPortalEngine.entities.PaymentApproval;
import com.tangazoletu.tibuPortalEngine.entities.MpesaUploads;
import com.tangazoletu.tibuPortalEngine.dto.*;
import com.tangazoletu.tibuPortalEngine.entities.*;
import com.tangazoletu.tibuPortalEngine.enums.ApiResponseCode;
import com.tangazoletu.tibuPortalEngine.model.EligibleForResubmissionDTO;
import com.tangazoletu.tibuPortalEngine.model.PaymentDetailsDTO;
import com.tangazoletu.tibuPortalEngine.model.ReturnCodeDTO;
import com.tangazoletu.tibuPortalEngine.model.supervisionDetails;
import com.tangazoletu.tibuPortalEngine.repositories.*;
import com.tangazoletu.tibuPortalEngine.repositories.MpesaUploadsRepository;
import com.tangazoletu.tibuPortalEngine.repositories.MpesaUploadsRepository;
import com.tangazoletu.tibuPortalEngine.repositories.PaymentReportRepository;
import com.tangazoletu.tibuPortalEngine.util.SharedFunctions;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import com.tangazoletu.tibuPortalEngine.repositories.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.PageRequest;

import org.springframework.data.domain.Sort;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;


import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

@Service
@Data
@RequiredArgsConstructor
@Slf4j
public class PaymentReportService {

    private final PaymentReportRepository paymentReportRepository;
    private final MpesaUploadsRepository mpesaUploadsRepository;
    private final PaymentApprovalRepository paymentApprovalRepository;
    private  final ApiOrderResubmissionsViewRepository apiOrderResubmissionsViewRepository;

    private final ApprovalProcessingRepo approvalProcessingRepo;
    private final PaymentStatusViewRepository paymentStatusViewRepository;
    private final CompletedPaymentViewRepository completedPaymentViewRepository;
    private final PaymentsRepo paymentsRepo;
    private final TempPaymentRepository tmpPaymentRepository;
    private final FailedPaymentRepository failedPaymentRepository;
    private final AllApiPaymentsRepo allApiPaymentsRepo;
    private final IntraTransferRepo intraTransferRepo;
    private final MpesaResubmissionRepository mpesaResubmissionRepository;
    private final BatchReferenceSummaryRepository batchReferenceSummaryRepository;
    private final MonthlyOrderSummaryRepo monthlyOrderSummaryRepo;
    private final PendingBatchCountyOrderViewRepository pendingBatchCountyOrderViewRepository;
    private final ApiOrderCommunityViewRepository aOrderCommunityViewRepository;
    private final PaymentRequestDetailsRepo paymentRequestDetailsRepo;
    private final ApiOrderRepo apiOrderRepo;
    private final SharedFunctions sharedFunctions;
    private final PaymentTransactionViewRepository paymentTransactionViewRepository;
    private final PendingLevel2OrderViewRepository pendingLevel2OrderViewRepository;
    private final PaymentApprovalRejectedViewRepository paymentApprovalRejectedViewRepository;
    private final PendingApprovedReportRepository pendingApprovedReportRepository;
    private final ApprovalReportRepository approvalReportRepository;
    private final BulkApprovalReportRepository bulkApprovalReportRepository;
    private final ReservedApprovalViewRepository reservedApprovalViewRepository;

    public ReportResponseView getReport(ReportRequest request) {
        ReportResponseView response = ReportResponseView.builder()
                .responseCode("01")
                .responseMessage("An error occured")
                .build();
        try {


            Pageable pageable = PageRequest.of(request.getPageNumber(), request.getPageSize());

            String keyword = request.getKeyword();

            //  Query with organization name filter
            Page<?> page = pendingApprovedReportRepository.fetchPendingApprovedReportsFiltered(
                    request.getApprovalLevel(), keyword, pageable);

            if (page.isEmpty()){
                response.setResponseMessage("No records ffetchPendingApprovedReportsFilteredByOrgound");
                response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
            }else {
                response.setResponseMessage("Success");
                response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                response.setData(page);
            }


            return response;

        } catch (Exception ex) {
            log.error("Error while fetching filtered payment reports", ex);
            return ReportResponseView.builder()
                    .responseCode(String.valueOf(ApiResponseCode.FAIL))
                    .responseMessage("Error while fetching reports.")
                    .build();
        }


    }


    public ApiResponse getLatestApproved(Long apiorderId) {
        try {
            Object[] raw = paymentReportRepository.findLatestApprovedRaw(apiorderId);
            if (raw == null || raw.length == 0) {
                return ApiResponse.builder()
                        .responseCode(ApiResponseCode.FAIL)
                        .responseMessage("No data found.")
                        .build();
            }

            Object[] row = (Object[]) raw[0];

            PaymentApprovalReportDTO dto = PaymentApprovalReportDTO.builder()
                    .id(row[0] != null ? ((Number) row[0]).longValue() : null)
                    .approvalLevel(row[1] != null ? ((Number) row[1]).intValue() : null)
                    .approvalLevelName((String) row[2])
                    .approvalStatus((String) row[3])
                    .approvedAmount(row[4] != null ? ((Number) row[4]).doubleValue() : null)
                    .approver((String) row[5])
                    .approvalTime(row[6] != null ? row[6].toString() : null)
                    .approvalNotes((String) row[7])
                    .initiator((String) row[8])
                    .build();

            return ApiResponse.builder()
                    .responseCode(ApiResponseCode.SUCCESS)
                    .responseMessage("Filtered report fetched successfully.")
                    .jdata(dto)
                    .build();

        } catch (Exception e) {
            e.printStackTrace();
            return ApiResponse.builder()
                    .responseCode(ApiResponseCode.FAIL)
                    .responseMessage("Error fetching approval report.")
                    .build();
        }
    }




    public ApprovalReportResponse fetchReports(StatusRequest statusRequest) {
        ApprovalReportResponse approvalReportResponse = ApprovalReportResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL.getCode())
                .build();
        try {
            // Get authenticated user
//            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
//            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            //String organizationName = userDetails.getOrganizationName();

            Pageable pageable = PageRequest.of(statusRequest.getPage(), statusRequest.getPageSize());

            //  Query with organization name filter
            Page<ApprovalReport> page = approvalReportRepository.fetchApprovalReportsFilteredByStatus(
                    statusRequest.getApprovalStatus(), pageable);
            if (page.isEmpty()){
                approvalReportResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                approvalReportResponse.setResponseMessage("No records");
            }else {
                approvalReportResponse.setData(page);
                approvalReportResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                approvalReportResponse.setResponseMessage("Success");
            }

            //  Build paginated response

            return approvalReportResponse;

        } catch (Exception ex) {
            log.error("Error while fetching filtered payment reports", ex);
            return approvalReportResponse.builder()
                    .responseCode(ApiResponseCode.FAIL.getCode())
                    .responseMessage("Error while fetching reports.")
                    .build();
        }


    }

    private String trimOrNull(String value) {
        return (value == null || value.trim().isEmpty()) ? null : value.trim();
    }

    public ReportResponse getBulkApprovals(ProcessRequest processRequest) {
        ReportResponse response = ReportResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL.getCode())
                .build();
        try {
            Pageable pageable = PageRequest.of(processRequest.getPageNumber(), processRequest.getPageSize());

            Page<?> page = bulkApprovalReportRepository.fetchApiBulkApprovals(
                    processRequest.getOrgId(), pageable);

            if (page.isEmpty()){
                response.setResponseMessage("No records found for orgId: " + processRequest.getOrgId());
                response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
            }else {
                response.setResponseMessage("Success");
                response.setData(page);
                response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
            }

            return response;

        } catch (Exception ex) {
            log.error("Error while fetching approval processing reports", ex);
            return response.builder()
                    .responseCode(ApiResponseCode.FAIL.getCode())
                    .responseMessage("Error while fetching approval processing reports.")
                    .build();
        }
    }

    public ReportResponse getReversedApprovals(Integer countyId, int page, int size) {
        ReportResponse reportResponse = ReportResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL.getCode())
                .build();
        try {
            PageRequest pageable = PageRequest.of(page, size);
            Page<?> pageResult = reservedApprovalViewRepository.fetchReversedApprovals(countyId, pageable);

            if (pageResult.isEmpty()) {
                reportResponse.setResponseMessage("No records found for countyId: " + countyId);
                reportResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
            }else {
                reportResponse.setResponseMessage("Success");
                reportResponse.setData(pageResult);
                reportResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
            }



            return reportResponse;

        } catch (Exception e) {
            log.error("Error fetching reversed approvals", e);
            return reportResponse.builder()
                    .responseCode(ApiResponseCode.FAIL.getCode())
                    .responseMessage("Failed to fetch approvals.")
                    .build();
        }
    }
    public ReportResponse getResubmissions(Integer countyId, int page, int size) {
        ReportResponse reportResponse = ReportResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL.getCode())
                .build();
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "primarykey"));

            Page<ApiOrderResubmissionsView> pageResult =
                    apiOrderResubmissionsViewRepository.fetchFilteredResubmissions(countyId, pageable);
            if (pageResult.isEmpty()) {
                reportResponse.setResponseMessage("No records found for countyId: " + countyId);
                reportResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
            }else {
                reportResponse.setResponseMessage("Success");
                reportResponse.setData(pageResult);
                reportResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
            }


            return reportResponse;

        } catch (Exception e) {
            log.error("Error fetching resubmissions", e);
            return reportResponse.builder()
                    .responseCode(ApiResponseCode.FAIL.getCode())
                    .responseMessage("Failed to fetch resubmissions.")
                    .build();
        }
    }

    public ReportResponse getMpesaRecords(ReportRequest reportRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse){
        ReportResponse response = ReportResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL.getCode())
                .build();
        try {
            Integer orgId = reportRequest.getOrgId();
            int page = reportRequest.getPage();
            int pageSize = reportRequest.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);

            Page<MpesaUploads> mpesaUploads = mpesaUploadsRepository.findByOrgId(orgId, pageable);
            if (mpesaUploads.getTotalElements() == 0) {
                response.setResponseMessage("No records found for orgId: " + orgId);
                response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
            }else {
                response.setResponseMessage("Success");
                response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                response.setData(mpesaUploads);
            }


        }catch (Exception ex) {
            log.error("Error while fetching mpesa records", ex);
            response.setResponseCode(ApiResponseCode.FAIL.getCode());
            response.setResponseMessage("Error while fetching mpesa records.");
            ex.printStackTrace();
        }
        return response;
    }
    public ReportResponse getPendingMpesa(ReportRequest reportRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        ReportResponse response = ReportResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL.getCode())
                .build();

        try {
            Integer orgId = reportRequest.getOrgId();
            int page = reportRequest.getPage();
            int pageSize = reportRequest.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);

            // Normalize filters: empty string → null
            String keyword = hasText(reportRequest.getKeyword()) ? reportRequest.getKeyword().trim() : null;
            String province = hasText(reportRequest.getProvince()) ? reportRequest.getProvince().trim() : null;
            String budget = hasText(reportRequest.getBudgetLine()) ? reportRequest.getBudgetLine().trim() : null;
            String paymentType = hasText(reportRequest.getPaymentType()) ? reportRequest.getPaymentType().trim() : null;
            List<String> county = (reportRequest.getCounties() != null && !reportRequest.getCounties().isEmpty())
                    ? reportRequest.getCounties()
                    : null;

            LocalDateTime dateFrom = null;
            LocalDateTime dateTo = null;

            // Parse dateFrom
            if (hasText(reportRequest.getDateFrom())) {
                try {
                    dateFrom = Instant.ofEpochMilli(Long.parseLong(reportRequest.getDateFrom().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateFrom: {}", reportRequest.getDateFrom());
                }
            }

            // Parse dateTo
            if (hasText(reportRequest.getDateTo())) {
                try {
                    dateTo = Instant.ofEpochMilli(Long.parseLong(reportRequest.getDateTo().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateTo: {}", reportRequest.getDateTo());
                }
            }

            Page<PaymentStatusView> mpesaUploads = paymentStatusViewRepository
                    .filterPayments(keyword, province, budget, county, paymentType, dateFrom, dateTo, pageable);

            if (mpesaUploads.isEmpty()) {
                response.setResponseMessage("No records found for orgId: " + orgId);
            } else {
                response.setResponseMessage("Success");
                response.setData(mpesaUploads);
            }

            response.setResponseCode(ApiResponseCode.SUCCESS.getCode());

        } catch (Exception ex) {
            log.error("Error while fetching mpesa records", ex);
            response.setResponseMessage("Error while fetching mpesa records.");
            response.setResponseCode(ApiResponseCode.FAIL.getCode());
        }

        return response;
    }
    public ReportResponse getCompletedMpesaRecords(ReportRequest reportRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        ReportResponse response = ReportResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL.getCode())
                .build();

        try {
            Integer orgId = reportRequest.getOrgId();
            int page = reportRequest.getPage();
            int pageSize = reportRequest.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);

            // Normalize filters: empty string → null
            String keyword = hasText(reportRequest.getKeyword()) ? reportRequest.getKeyword().trim() : null;
            String province = hasText(reportRequest.getProvince()) ? reportRequest.getProvince().trim() : null;
            String budget = hasText(reportRequest.getBudgetLine()) ? reportRequest.getBudgetLine().trim() : null;
            String paymentType = hasText(reportRequest.getPaymentType()) ? reportRequest.getPaymentType().trim() : null;
            List<String> county = (reportRequest.getCounties() != null && !reportRequest.getCounties().isEmpty())
                    ? reportRequest.getCounties()
                    : null;

            LocalDateTime dateFrom = null;
            LocalDateTime dateTo = null;

            // Parse dateFrom
            if (hasText(reportRequest.getDateFrom())) {
                try {
                    dateFrom = Instant.ofEpochMilli(Long.parseLong(reportRequest.getDateFrom().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateFrom: {}", reportRequest.getDateFrom());
                }
            }

            // Parse dateTo
            if (hasText(reportRequest.getDateTo())) {
                try {
                    dateTo = Instant.ofEpochMilli(Long.parseLong(reportRequest.getDateTo().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateTo: {}", reportRequest.getDateTo());
                }
            }
            if (hasText(reportRequest.getId())){
                Optional<Payment> completedPaymentViews = paymentsRepo.findById(Long.valueOf(reportRequest.getId()));
                if (completedPaymentViews.isPresent()) {
                    response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                    response.setResponseMessage("Success");
                    response.setPayment(completedPaymentViews);
                }
                List<PaymentDetailsDTO> paymentDetailsDTO = completedPaymentViewRepository.getPaymentDetailsById(Long.valueOf(reportRequest.getId()));
                if (paymentDetailsDTO.size() > 0) {
                log.info("paymentDetailsDTO : {}", paymentDetailsDTO.get(0).toString());
                log.info("orderid : {}", paymentDetailsDTO.get(0).getOrderId());
                response.setPaymentDetailsDTO(paymentDetailsDTO.get(0));
                }
                Optional<String> title = completedPaymentViewRepository.getTitle(paymentDetailsDTO.get(0).getOrderId());
                if (title.isPresent()) {
                    if (title.get().equalsIgnoreCase("SUPERVISION") || title.get().equalsIgnoreCase("MDR OTHER PAYMENTS")){
                        Optional<supervisionDetails> supervisionDetails = completedPaymentViewRepository.getSupervisionDetails(paymentDetailsDTO.get(0).getOrderId());
                        response.setSupervisionDetails(supervisionDetails.get());
                    }
                }
                Optional<PaymentApprovalDTO> paymentApprovalDTOS = completedPaymentViewRepository.getPaymentApprovalsByOrderId(paymentDetailsDTO.get(0).getOrderId());
                if (paymentApprovalDTOS.isPresent()) {
                    response.setPaymentApprovalDTO(paymentApprovalDTOS.get());
                }
                if (reportRequest.getId() !=null) {
                    Optional<TempPayment> tempPayment = tmpPaymentRepository.findById(Integer.valueOf(reportRequest.getId()));
                    response.setTempPayment(tempPayment);
                }
                if (paymentDetailsDTO.get(0).getTrxStatus() != null && paymentDetailsDTO.get(0).getTrxStatus().equalsIgnoreCase("FAILED")) {

                    ReturnCodeDTO returnCodeDTO = completedPaymentViewRepository.findReturnCodeForFailedPayment(Long.valueOf(reportRequest.getId()));
                    response.setReturnCodeDTO(returnCodeDTO);
                    EligibleForResubmissionDTO eligibleForResubmissionDTO = completedPaymentViewRepository.checkResubmissionEligibility(Long.valueOf(reportRequest.getId()));
                    response.setEligibleForResubmissionDTO(eligibleForResubmissionDTO);


                }
            }else {

                Page<CompletedPaymentView> completedPaymentViews = completedPaymentViewRepository
                        .filterCompletedPayments(keyword, province, budget, county, paymentType, dateFrom, dateTo, pageable);

                if (completedPaymentViews.isEmpty()) {
                    response.setResponseMessage("No records found for orgId: " + orgId);
                } else {
                    response.setResponseMessage("Success");
                    response.setData(completedPaymentViews);
                }

                response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
            }

        } catch (Exception ex) {
            log.error("Error while fetching mpesa records", ex);
            response.setResponseMessage("Error while fetching mpesa records.");
            response.setResponseCode(ApiResponseCode.FAIL.getCode());
        }

        return response;
    }
    public ReportResponse getFailedMpesaRecords(ReportRequest reportRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        ReportResponse response = ReportResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL.getCode())
                .build();

        try {
            Integer orgId = reportRequest.getOrgId();
            int page = reportRequest.getPage();
            int pageSize = reportRequest.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);

            // Normalize filters: empty string → null
            String keyword = hasText(reportRequest.getKeyword()) ? reportRequest.getKeyword().trim() : null;
            String province = hasText(reportRequest.getProvince()) ? reportRequest.getProvince().trim() : null;
            String budget = hasText(reportRequest.getBudgetLine()) ? reportRequest.getBudgetLine().trim() : null;
            String paymentType = hasText(reportRequest.getPaymentType()) ? reportRequest.getPaymentType().trim() : null;
            List<String> county = (reportRequest.getCounties() != null && !reportRequest.getCounties().isEmpty())
                    ? reportRequest.getCounties()
                    : null;

            LocalDateTime dateFrom = null;
            LocalDateTime dateTo = null;

            // Parse dateFrom
            if (hasText(reportRequest.getDateFrom())) {
                try {
                    dateFrom = Instant.ofEpochMilli(Long.parseLong(reportRequest.getDateFrom().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateFrom: {}", reportRequest.getDateFrom());
                }
            }

            // Parse dateTo
            if (hasText(reportRequest.getDateTo())) {
                try {
                    dateTo = Instant.ofEpochMilli(Long.parseLong(reportRequest.getDateTo().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateTo: {}", reportRequest.getDateTo());
                }
            }
            if (hasText(reportRequest.getId())){
                //resubmit
                Optional<FailedPaymentEntity> resubmit = failedPaymentRepository.findById(Long.valueOf(reportRequest.getId()));
                if (resubmit.isPresent()) {
                    response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                    response.setResponseMessage("Success");
                    response.setPayment(resubmit);
                }
                //TODO view how to handle this
                List<PaymentDetailsDTO> paymentDetailsDTO = completedPaymentViewRepository.getPaymentDetailsById(Long.valueOf(reportRequest.getId()));
                if (paymentDetailsDTO.size() > 0) {
                    log.info("paymentDetailsDTO : {}", paymentDetailsDTO.get(0).toString());
                    log.info("orderid : {}", paymentDetailsDTO.get(0).getOrderId());
                    response.setPaymentDetailsDTO(paymentDetailsDTO.get(0));
                }
                Optional<String> title = completedPaymentViewRepository.getTitle(paymentDetailsDTO.get(0).getOrderId());
                if (title.isPresent()) {
                    if (title.get().equalsIgnoreCase("SUPERVISION") || title.get().equalsIgnoreCase("MDR OTHER PAYMENTS")){
                        Optional<supervisionDetails> supervisionDetails = completedPaymentViewRepository.getSupervisionDetails(paymentDetailsDTO.get(0).getOrderId());
                        response.setSupervisionDetails(supervisionDetails.get());
                    }
                }
                Optional<PaymentApprovalDTO> paymentApprovalDTOS = completedPaymentViewRepository.getPaymentApprovalsByOrderId(paymentDetailsDTO.get(0).getOrderId());
                if (paymentApprovalDTOS.isPresent()) {
                    response.setPaymentApprovalDTO(paymentApprovalDTOS.get());
                }
                if (reportRequest.getId() !=null) {
                    Optional<TempPayment> tempPayment = tmpPaymentRepository.findById(Integer.valueOf(reportRequest.getId()));
                    response.setTempPayment(tempPayment);
                }
                if (paymentDetailsDTO.get(0).getTrxStatus() != null && paymentDetailsDTO.get(0).getTrxStatus().equalsIgnoreCase("FAILED")) {

                    ReturnCodeDTO returnCodeDTO = completedPaymentViewRepository.findReturnCodeForFailedPayment(Long.valueOf(reportRequest.getId()));
                    response.setReturnCodeDTO(returnCodeDTO);
                    EligibleForResubmissionDTO eligibleForResubmissionDTO = completedPaymentViewRepository.checkResubmissionEligibility(Long.valueOf(reportRequest.getId()));
                    response.setEligibleForResubmissionDTO(eligibleForResubmissionDTO);


                }

            }else {

                Page<FailedPaymentEntity> failedPaymentEntities = failedPaymentRepository.filterFailedPayments(keyword, province, budget,  paymentType, dateFrom, dateTo, pageable);

                if (failedPaymentEntities.isEmpty()) {
                    response.setResponseMessage("No records found for orgId: " + orgId);
                } else {
                    response.setResponseMessage("Success");
                    response.setData(failedPaymentEntities);
                }

                response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
            }

        } catch (Exception ex) {
            log.error("Error while fetching mpesa records", ex);
            response.setResponseMessage("Error while fetching mpesa records.");
            response.setResponseCode(ApiResponseCode.FAIL.getCode());
        }

        return response;
    }
    public ReportResponse getReversalMpesaRecords(ReportRequest reportRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        ReportResponse response = ReportResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL.getCode())
                .build();

        try {
            Integer orgId = reportRequest.getOrgId();
            int page = reportRequest.getPage();
            int pageSize = reportRequest.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);

            // Normalize filters: empty string → null
            String keyword = hasText(reportRequest.getKeyword()) ? reportRequest.getKeyword().trim() : null;
            String province = hasText(reportRequest.getProvince()) ? reportRequest.getProvince().trim() : null;
            String budget = hasText(reportRequest.getBudgetLine()) ? reportRequest.getBudgetLine().trim() : null;
            String paymentType = hasText(reportRequest.getPaymentType()) ? reportRequest.getPaymentType().trim() : null;
            List<String> county = (reportRequest.getCounties() != null && !reportRequest.getCounties().isEmpty())
                    ? reportRequest.getCounties()
                    : null;

            LocalDateTime dateFrom = null;
            LocalDateTime dateTo = null;

            Page<Payment> reverse = paymentReportRepository.findReversalsByKeyword(keyword, pageable);
            if (reverse.isEmpty()) {
                response.setResponseMessage("No records found for orgId: " + orgId);
                response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
            }else {
                response.setResponseMessage("Success");
                response.setData(reverse);
                response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
            }


        } catch (Exception e) {

            response.setResponseMessage("Failed");
            response.setResponseCode(ApiResponseCode.FAIL.getCode());
            e.printStackTrace();


        }
        return response;
    }
    public ReportResponse getAllApiPayments(ReportRequest reportRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        ReportResponse response = ReportResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL.getCode())
                .build();

        try {
            Integer orgId = reportRequest.getOrgId();
            int page = reportRequest.getPage();
            int pageSize = reportRequest.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);

            // Normalize filters: empty string → null
            String keyword = hasText(reportRequest.getKeyword()) ? reportRequest.getKeyword().trim() : null;
            String province = hasText(reportRequest.getProvince()) ? reportRequest.getProvince().trim() : null;
            String budget = hasText(reportRequest.getBudgetLine()) ? reportRequest.getBudgetLine().trim() : null;
            String paymentType = hasText(reportRequest.getPaymentType()) ? reportRequest.getPaymentType().trim() : null;
            List<String> rawCounties = reportRequest.getCounties();
            String county1 = null;
            List<String> rawCounties1 = reportRequest.getCounties();

            List<String> county = (rawCounties1 != null)
                    ? rawCounties1.stream().filter(c -> c != null && !c.trim().isEmpty()).toList()
                    : null;

            if (county != null && !county.isEmpty()) {
                county1 = county.get(0);
            } else {
                county = null;
                county1 = null;
            }
            LocalDateTime dateFrom = null;
            LocalDateTime dateTo = null;

            // Parse dateFrom
            if (hasText(reportRequest.getDateFrom())) {
                try {
                    dateFrom = Instant.ofEpochMilli(Long.parseLong(reportRequest.getDateFrom().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateFrom: {}", reportRequest.getDateFrom());
                }
            }

            // Parse dateTo
            if (hasText(reportRequest.getDateTo())) {
                try {
                    dateTo = Instant.ofEpochMilli(Long.parseLong(reportRequest.getDateTo().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateTo: {}", reportRequest.getDateTo());
                }
            }
            if (hasText(reportRequest.getId())){
                //resubmit
                Optional<FailedPaymentEntity> resubmit = failedPaymentRepository.findById(Long.valueOf(reportRequest.getId()));
                if (resubmit.isPresent()) {
                    response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                    response.setResponseMessage("Success");
                    response.setPayment(resubmit);
                }
                //TODO view how to handle this
                List<PaymentDetailsDTO> paymentDetailsDTO = completedPaymentViewRepository.getPaymentDetailsById(Long.valueOf(reportRequest.getId()));
                if (paymentDetailsDTO.size() > 0) {
                    log.info("paymentDetailsDTO : {}", paymentDetailsDTO.get(0).toString());
                    log.info("orderid : {}", paymentDetailsDTO.get(0).getOrderId());
                    response.setPaymentDetailsDTO(paymentDetailsDTO.get(0));
                }
                Optional<String> title = completedPaymentViewRepository.getTitle(paymentDetailsDTO.get(0).getOrderId());
                if (title.isPresent()) {
                    if (title.get().equalsIgnoreCase("SUPERVISION") || title.get().equalsIgnoreCase("MDR OTHER PAYMENTS")){
                        Optional<supervisionDetails> supervisionDetails = completedPaymentViewRepository.getSupervisionDetails(paymentDetailsDTO.get(0).getOrderId());
                        response.setSupervisionDetails(supervisionDetails.get());
                    }
                }
                Optional<PaymentApprovalDTO> paymentApprovalDTOS = completedPaymentViewRepository.getPaymentApprovalsByOrderId(paymentDetailsDTO.get(0).getOrderId());
                if (paymentApprovalDTOS.isPresent()) {
                    response.setPaymentApprovalDTO(paymentApprovalDTOS.get());
                }
                if (reportRequest.getId() !=null) {
                    Optional<TempPayment> tempPayment = tmpPaymentRepository.findById(Integer.valueOf(reportRequest.getId()));
                    response.setTempPayment(tempPayment);
                }
                if (paymentDetailsDTO.get(0).getTrxStatus() != null && paymentDetailsDTO.get(0).getTrxStatus().equalsIgnoreCase("FAILED")) {

                    ReturnCodeDTO returnCodeDTO = completedPaymentViewRepository.findReturnCodeForFailedPayment(Long.valueOf(reportRequest.getId()));
                    response.setReturnCodeDTO(returnCodeDTO);
                    EligibleForResubmissionDTO eligibleForResubmissionDTO = completedPaymentViewRepository.checkResubmissionEligibility(Long.valueOf(reportRequest.getId()));
                    response.setEligibleForResubmissionDTO(eligibleForResubmissionDTO);


                }

            }else {
                province = (province != null && province.trim().isEmpty()) ? null : province;
                paymentType = (paymentType != null && paymentType.trim().isEmpty()) ? null : paymentType;
                keyword = (keyword != null && keyword.trim().isEmpty()) ? null : keyword;

                log.info("Filter Parameters => province: {}, county: {}, paymentType: {}, keyword: {}, dateFrom: {}, dateTo: {}",
                        province, county, paymentType, keyword, dateFrom, dateTo);
                Page<AllApiPayments> allApiPayments = allApiPaymentsRepo.filterReport(
                        keyword, province, county1, paymentType, dateFrom, dateTo, pageable);

                if (allApiPayments.isEmpty()) {
                    response.setResponseMessage("No records found for orgId: " + orgId);
                } else {
                    response.setResponseMessage("Success");
                    response.setData(allApiPayments);
                }

                response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
            }

        } catch (Exception ex) {
            log.error("Error while fetching mpesa records", ex);
            response.setResponseMessage("Error while fetching mpesa records.");
            response.setResponseCode(ApiResponseCode.FAIL.getCode());
        }

        return response;
    }
    public ReportResponse getIntraTransfers(ReportRequest reportRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        ReportResponse response = ReportResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL.getCode())
                .build();

        try {
            Integer orgId = reportRequest.getOrgId();
            int page = reportRequest.getPage();
            int pageSize = reportRequest.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);

            // Normalize filters: empty string → null
            String keyword = hasText(reportRequest.getKeyword()) ? reportRequest.getKeyword().trim() : null;
            String province = hasText(reportRequest.getProvince()) ? reportRequest.getProvince().trim() : null;
            String budget = hasText(reportRequest.getBudgetLine()) ? reportRequest.getBudgetLine().trim() : null;
            String paymentType = hasText(reportRequest.getPaymentType()) ? reportRequest.getPaymentType().trim() : null;
            List<String> rawCounties = reportRequest.getCounties();
            String county1 = null;
            List<String> rawCounties1 = reportRequest.getCounties();

            List<String> county = (rawCounties1 != null)
                    ? rawCounties1.stream().filter(c -> c != null && !c.trim().isEmpty()).toList()
                    : null;

            if (county != null && !county.isEmpty()) {
                county1 = county.get(0);
            } else {
                county = null;
                county1 = null;
            }
            LocalDateTime dateFrom = null;
            LocalDateTime dateTo = null;

            // Parse dateFrom
            if (hasText(reportRequest.getDateFrom())) {
                try {
                    dateFrom = Instant.ofEpochMilli(Long.parseLong(reportRequest.getDateFrom().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateFrom: {}", reportRequest.getDateFrom());
                }
            }

            // Parse dateTo
            if (hasText(reportRequest.getDateTo())) {
                try {
                    dateTo = Instant.ofEpochMilli(Long.parseLong(reportRequest.getDateTo().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateTo: {}", reportRequest.getDateTo());
                }
            }
            if (hasText(reportRequest.getId())){

            }else {
                province = (province != null && province.trim().isEmpty()) ? null : province;
                paymentType = (paymentType != null && paymentType.trim().isEmpty()) ? null : paymentType;
                keyword = (keyword != null && keyword.trim().isEmpty()) ? null : keyword;

                log.info("Filter Parameters => province: {}, county: {}, paymentType: {}, keyword: {}, dateFrom: {}, dateTo: {}",
                        province, county, paymentType, keyword, dateFrom, dateTo);
                Page<IntraTransfers> intraTransfers = intraTransferRepo.searchCompletedPayouts(
                        keyword, province, county1, paymentType, dateFrom, dateTo, pageable);

                if (intraTransfers.isEmpty()) {
                    response.setResponseMessage("No records found for orgId: " + orgId);
                } else {
                    response.setResponseMessage("Success");
                    response.setData(intraTransfers);
                }

                response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
            }

        } catch (Exception ex) {
            log.error("Error while fetching mpesa records", ex);
            response.setResponseMessage("Error while fetching mpesa records.");
            response.setResponseCode(ApiResponseCode.FAIL.getCode());
        }

        return response;
    }
    public ReportResponse getResubmissionMpesaRecords(ReportRequest reportRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        ReportResponse response = ReportResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL.getCode())
                .build();

        try {
            Integer orgId = reportRequest.getOrgId();
            int page = reportRequest.getPage();
            int pageSize = reportRequest.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);

            // Normalize filters: empty string → null
            String keyword = hasText(reportRequest.getKeyword()) ? reportRequest.getKeyword().trim() : null;
            String province = hasText(reportRequest.getProvince()) ? reportRequest.getProvince().trim() : null;
            String budget = hasText(reportRequest.getBudgetLine()) ? reportRequest.getBudgetLine().trim() : null;
            String paymentType = hasText(reportRequest.getPaymentType()) ? reportRequest.getPaymentType().trim() : null;
            List<String> rawCounties = reportRequest.getCounties();
            String county1 = null;
            List<String> rawCounties1 = reportRequest.getCounties();

            List<String> county = (rawCounties1 != null)
                    ? rawCounties1.stream().filter(c -> c != null && !c.trim().isEmpty()).toList()
                    : null;

            if (county != null && !county.isEmpty()) {
                county1 = county.get(0);
            } else {
                county = null;
                county1 = null;
            }
            LocalDateTime dateFrom = null;
            LocalDateTime dateTo = null;

            // Parse dateFrom
            if (hasText(reportRequest.getDateFrom())) {
                try {
                    dateFrom = Instant.ofEpochMilli(Long.parseLong(reportRequest.getDateFrom().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateFrom: {}", reportRequest.getDateFrom());
                }
            }

            // Parse dateTo
            if (hasText(reportRequest.getDateTo())) {
                try {
                    dateTo = Instant.ofEpochMilli(Long.parseLong(reportRequest.getDateTo().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateTo: {}", reportRequest.getDateTo());
                }
            }
            if (hasText(reportRequest.getId())){

            }else {
                province = (province != null && province.trim().isEmpty()) ? null : province;
                paymentType = (paymentType != null && paymentType.trim().isEmpty()) ? null : paymentType;
                keyword = (keyword != null && keyword.trim().isEmpty()) ? null : keyword;

                log.info("Filter Parameters => province: {}, county: {}, paymentType: {}, keyword: {}, dateFrom: {}, dateTo: {}",
                        province, county, paymentType, keyword, dateFrom, dateTo);
                Page<MpesaResubmissionView> mpesaResubmissionViews = mpesaResubmissionRepository.searchResubmissions(
                        keyword, province, county1, paymentType, dateFrom, dateTo, pageable);

                if (mpesaResubmissionViews.isEmpty()) {
                    response.setResponseMessage("No records found for orgId: " + orgId);
                } else {
                    response.setResponseMessage("Success");
                    response.setData(mpesaResubmissionViews);
                }

                response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
            }

        } catch (Exception ex) {
            log.error("Error while fetching mpesa records", ex);
            response.setResponseMessage("Error while fetching mpesa records.");
            response.setResponseCode(ApiResponseCode.FAIL.getCode());
        }

        return response;
    }
    public ReportResponse getBatch(ReportRequest reportRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        ReportResponse response = ReportResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL.getCode())
                .build();

        try {
            int page = reportRequest.getPage();
            int pageSize = reportRequest.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);

            // Normalize filters: empty string → null
            String keyword = hasText(reportRequest.getKeyword()) ? reportRequest.getKeyword().trim() : null;
            String province = hasText(reportRequest.getProvince()) ? reportRequest.getProvince().trim() : null;
            String budget = hasText(reportRequest.getBudgetLine()) ? reportRequest.getBudgetLine().trim() : null;
            String paymentType = hasText(reportRequest.getPaymentType()) ? reportRequest.getPaymentType().trim() : null;
            List<String> rawCounties = reportRequest.getCounties();
            String county1 = null;
            List<String> rawCounties1 = reportRequest.getCounties();

            List<String> county = (rawCounties1 != null)
                    ? rawCounties1.stream().filter(c -> c != null && !c.trim().isEmpty()).toList()
                    : null;

            if (county != null && !county.isEmpty()) {
                county1 = county.get(0);
            } else {
                county = null;
                county1 = null;
            }
            LocalDateTime dateFrom = null;
            LocalDateTime dateTo = null;

            // Parse dateFrom
            if (hasText(reportRequest.getDateFrom())) {
                try {
                    dateFrom = Instant.ofEpochMilli(Long.parseLong(reportRequest.getDateFrom().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateFrom: {}", reportRequest.getDateFrom());
                }
            }

            // Parse dateTo
            if (hasText(reportRequest.getDateTo())) {
                try {
                    dateTo = Instant.ofEpochMilli(Long.parseLong(reportRequest.getDateTo().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateTo: {}", reportRequest.getDateTo());
                }
            }
            if (hasText(reportRequest.getId())){

            }else {
                Long orgId = null;
                if (reportRequest.getOrgId() != null) {
                    orgId = Long.valueOf(reportRequest.getOrgId());
               }
                Page<BatchReferenceSummary> batchReferenceSummaries = batchReferenceSummaryRepository.searchByKeywordAndOrgId(
                        keyword,orgId, pageable);

                if (batchReferenceSummaries.isEmpty()) {
                    response.setResponseMessage("No records found for orgId: " + orgId);
                } else {
                    response.setResponseMessage("Success");
                    response.setData(batchReferenceSummaries);
                }

                response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
            }

        } catch (Exception ex) {
            log.error("Error while fetching mpesa records", ex);
            response.setResponseMessage("Error while fetching mpesa records.");
            response.setResponseCode(ApiResponseCode.FAIL.getCode());
        }

        return response;
    }
    public ReportResponse getTotalexpenditurecounyperiod(ReportRequest reportRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        ReportResponse response = ReportResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL.getCode())
                .build();

        try {
            int page = reportRequest.getPage();
            int pageSize = reportRequest.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);

            // Normalize filters: empty string → null
            String keyword = hasText(reportRequest.getKeyword()) ? reportRequest.getKeyword().trim() : null;
            String province = hasText(reportRequest.getProvince()) ? reportRequest.getProvince().trim() : null;
            String budget = hasText(reportRequest.getBudgetLine()) ? reportRequest.getBudgetLine().trim() : null;
            String paymentType = hasText(reportRequest.getPaymentType()) ? reportRequest.getPaymentType().trim() : null;
            List<String> rawCounties = reportRequest.getCounties();
            String county1 = null;
            List<String> rawCounties1 = reportRequest.getCounties();

            List<String> county = (rawCounties1 != null)
                    ? rawCounties1.stream().filter(c -> c != null && !c.trim().isEmpty()).toList()
                    : null;

            if (county != null && !county.isEmpty()) {
                county1 = county.get(0);
            } else {
                county = null;
                county1 = null;
            }
            LocalDateTime dateFrom = null;
            LocalDateTime dateTo = null;
            String batch = reportRequest.getBatchNumber();

            // Parse dateFrom
            if (hasText(reportRequest.getDateFrom())) {
                try {
                    dateFrom = Instant.ofEpochMilli(Long.parseLong(reportRequest.getDateFrom().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateFrom: {}", reportRequest.getDateFrom());
                }
            }

            // Parse dateTo
            if (hasText(reportRequest.getDateTo())) {
                try {
                    dateTo = Instant.ofEpochMilli(Long.parseLong(reportRequest.getDateTo().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateTo: {}", reportRequest.getDateTo());
                }
            }
            if (hasText(reportRequest.getId())){

            }else {
                Long orgId = null;
                if (reportRequest.getOrgId() != null) {
                    orgId = Long.valueOf(reportRequest.getOrgId());
                }
                province = (province != null && province.trim().isEmpty()) ? null : province;
                paymentType = (paymentType != null && paymentType.trim().isEmpty()) ? null : paymentType;
                keyword = (keyword != null && keyword.trim().isEmpty()) ? null : keyword;

                log.info("Filter Parameters => province: {}, county: {}, paymentType: {}, keyword: {}, dateFrom: {}, dateTo: {}",
                        province, county, paymentType, keyword, dateFrom, dateTo);
                Long budget1 = null;
                if (budget != null) {
                    budget1 = Long.valueOf(budget);

                }
                Page<MonthlyOrderSummary> result = monthlyOrderSummaryRepo.searchSummary(
                        province,county1, budget1,batch,dateFrom,dateTo, pageable);



                if (result.isEmpty()) {
                    response.setResponseMessage("No records found for orgId: " + orgId);
                } else {
                    response.setResponseMessage("Success");
                    response.setData(result);
                }

                response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
            }

        } catch (Exception ex) {
            log.error("Error while fetching mpesa records", ex);
            response.setResponseMessage("Error while fetching mpesa records.");
            response.setResponseCode(ApiResponseCode.FAIL.getCode());
        }

        return response;
    }
    public ReportResponse getCountyBatch(ReportRequest reportRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        ReportResponse response = ReportResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL.getCode())
                .build();

        try {
            int page = reportRequest.getPage();
            int pageSize = reportRequest.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);

            // Normalize filters: empty string → null
            String keyword = hasText(reportRequest.getKeyword()) ? reportRequest.getKeyword().trim() : null;
            String province = hasText(reportRequest.getProvince()) ? reportRequest.getProvince().trim() : null;
            String budget = hasText(reportRequest.getBudgetLine()) ? reportRequest.getBudgetLine().trim() : null;
            String paymentType = hasText(reportRequest.getPaymentType()) ? reportRequest.getPaymentType().trim() : null;
            List<String> rawCounties = reportRequest.getCounties();
            String county1 = null;
            List<String> rawCounties1 = reportRequest.getCounties();

            List<String> county = (rawCounties1 != null)
                    ? rawCounties1.stream().filter(c -> c != null && !c.trim().isEmpty()).toList()
                    : null;

            if (county != null && !county.isEmpty()) {
                county1 = county.get(0);
            } else {
                county = null;
                county1 = null;
            }
            LocalDateTime dateFrom = null;
            LocalDateTime dateTo = null;
            String batch = reportRequest.getBatchNumber();

            // Parse dateFrom
            if (hasText(reportRequest.getDateFrom())) {
                try {
                    dateFrom = Instant.ofEpochMilli(Long.parseLong(reportRequest.getDateFrom().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateFrom: {}", reportRequest.getDateFrom());
                }
            }

            // Parse dateTo
            if (hasText(reportRequest.getDateTo())) {
                try {
                    dateTo = Instant.ofEpochMilli(Long.parseLong(reportRequest.getDateTo().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateTo: {}", reportRequest.getDateTo());
                }
            }
            if (hasText(reportRequest.getId())){

            }else {
                Long orgId = null;
                if (reportRequest.getOrgId() != null) {
                    orgId = Long.valueOf(reportRequest.getOrgId());
                }
                province = (province != null && province.trim().isEmpty()) ? null : province;
                paymentType = (paymentType != null && paymentType.trim().isEmpty()) ? null : paymentType;
                keyword = (keyword != null && keyword.trim().isEmpty()) ? null : keyword;

                log.info("Filter Parameters => province: {}, county: {}, paymentType: {}, keyword: {}, dateFrom: {}, dateTo: {}",
                        province, county, paymentType, keyword, dateFrom, dateTo);
                Long budget1 = null;
                if (budget != null) {
                    budget1 = Long.valueOf(budget);

                }
                Page<PendingBatchCountyOrderView> result = pendingBatchCountyOrderViewRepository.searchPendingBatches(
                        batch,orgId,county1,keyword, pageable);



                if (result.isEmpty()) {
                    response.setResponseMessage("No records found for orgId: " + orgId);
                } else {
                    response.setResponseMessage("Success");
                    response.setData(result);
                }

                response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
            }

        } catch (Exception ex) {
            log.error("Error while fetching mpesa records", ex);
            response.setResponseMessage("Error while fetching mpesa records.");
            response.setResponseCode(ApiResponseCode.FAIL.getCode());
        }

        return response;
    }
    public ReportResponse getHcwPayments(ReportRequest reportRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        ReportResponse response = ReportResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL.getCode())
                .build();

        try {
            int page = reportRequest.getPage();
            int pageSize = reportRequest.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);

            // Normalize filters: empty string → null
            String keyword = hasText(reportRequest.getKeyword()) ? reportRequest.getKeyword().trim() : null;
            String province = hasText(reportRequest.getProvince()) ? reportRequest.getProvince().trim() : null;
            String budget = hasText(reportRequest.getBudgetLine()) ? reportRequest.getBudgetLine().trim() : null;
            String paymentType = hasText(reportRequest.getPaymentType()) ? reportRequest.getPaymentType().trim() : null;
            List<String> rawCounties = reportRequest.getCounties();
            String county1 = null;
            List<String> rawCounties1 = reportRequest.getCounties();

            List<String> county = (rawCounties1 != null)
                    ? rawCounties1.stream().filter(c -> c != null && !c.trim().isEmpty()).toList()
                    : null;

            if (county != null && !county.isEmpty()) {
                county1 = county.get(0);
            } else {
                county = null;
                county1 = null;
            }
            LocalDateTime dateFrom = null;
            LocalDateTime dateTo = null;
            String batch = reportRequest.getBatchNumber();

            // Parse dateFrom
            if (hasText(reportRequest.getDateFrom())) {
                try {
                    dateFrom = Instant.ofEpochMilli(Long.parseLong(reportRequest.getDateFrom().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateFrom: {}", reportRequest.getDateFrom());
                }
            }

            // Parse dateTo
            if (hasText(reportRequest.getDateTo())) {
                try {
                    dateTo = Instant.ofEpochMilli(Long.parseLong(reportRequest.getDateTo().trim()))
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException e) {
                    log.warn("Invalid dateTo: {}", reportRequest.getDateTo());
                }
            }
            if (hasText(reportRequest.getId())){
                Optional<PaymentRequestDetails> paymentRequestDetails = paymentRequestDetailsRepo.findById(Long.valueOf(reportRequest.getId()));
                if (paymentRequestDetails.isPresent()) {
                    response.setPaymentRequestDetails(paymentRequestDetails);
                }
                Optional<ApiOrder> apiOrder = apiOrderRepo.findById(Long.valueOf(reportRequest.getId()));
                if (apiOrder.isPresent()){
                    response.setApiOrder(apiOrder);
                }
                Optional<String> title = completedPaymentViewRepository.getTitle(Long.valueOf(reportRequest.getId()));
                if (title.isPresent()) {
                    if (title.get().equalsIgnoreCase("SUPERVISION") || title.get().equalsIgnoreCase("MDR OTHER PAYMENTS")){
                        List<supervisionDetails> supervisionDetails = completedPaymentViewRepository.getSupervisionDetailsReport(Long.valueOf(reportRequest.getId()));
                        response.setSupervisionDetailsReport(supervisionDetails);
                    }
                }
                List<PaymentApprovalDTO> paymentApprovalDTOS = completedPaymentViewRepository.getPaymentApprovalsByOrderIdReport(Long.valueOf(reportRequest.getId()));
                if (paymentApprovalDTOS.size() >0) {
                    response.setPaymentApprovalDTOReport(paymentApprovalDTOS);
                }
                boolean permission = false;
                Integer userId = 0;
                userId = 222268;
                if (apiOrder.isPresent()){
                    String approvalStatus = String.valueOf(apiOrder.get().getApprovalStatus());
                    if (approvalStatus.equalsIgnoreCase("Rejected")) {
                        permission = sharedFunctions.hasPermission("reverserejection", 3, userId);

                    }else if (approvalStatus.equalsIgnoreCase("Pending")) {
                        permission = sharedFunctions.hasPermission("reverseapproval", 3, userId);

                    }
                }
                List<PaymentTransactionView> paymentTransactionViews = paymentTransactionViewRepository.findAll();
                if (paymentTransactionViews.size() >0){
                    response.setPaymentTransactionViews(paymentTransactionViews);
                }
                response.setPermission(permission);




            }else {
                Long orgId = null;
                if (reportRequest.getOrgId() != null) {
                    orgId = Long.valueOf(reportRequest.getOrgId());
                }
                province = (province != null && province.trim().isEmpty()) ? null : province;
                paymentType = (paymentType != null && paymentType.trim().isEmpty()) ? null : paymentType;
                keyword = (keyword != null && keyword.trim().isEmpty()) ? null : keyword;

                log.info("Filter Parameters => province: {}, county: {}, paymentType: {}, keyword: {}, dateFrom: {}, dateTo: {}",
                        province, county, paymentType, keyword, dateFrom, dateTo);
                Long budget1 = null;
                if (budget != null) {
                    budget1 = Long.valueOf(budget);

                }
                String month = reportRequest.getMonth();
                String year = reportRequest.getYear();
                String status = reportRequest.getStatus();
                Page<ApiOrderCommunityView> result = aOrderCommunityViewRepository.searchCommunityOrders(
                        keyword,province,county1,budget,month, year, status,dateFrom,dateTo, pageable);



                if (result.isEmpty()) {
                    response.setResponseMessage("No records found for orgId: " + orgId);
                } else {
                    response.setResponseMessage("Success");
                    response.setData(result);
                }

                response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
            }

        } catch (Exception ex) {
            log.error("Error while fetching mpesa records", ex);
            response.setResponseMessage("Error while fetching mpesa records.");
            response.setResponseCode(ApiResponseCode.FAIL.getCode());
        }

        return response;
    }
    public ReverseRejectedReportRepose getReverseRejected(ReportRequest reportRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        ReverseRejectedReportRepose response = ReverseRejectedReportRepose.builder()
                .responseCode("01")
                .responseMessage("Failed")
                .build();
        try {
            String id = reportRequest.getId();
            if (id == null) {
                log.info("no id is passed");
                response.setResponseMessage("No id is saved");
                response.setResponseCode(ApiResponseCode.FAIL.getCode());
           }else {
                Integer page = 0;
                Integer pageSize = 100;
                Pageable pageable = PageRequest.of(page, pageSize);
                Page<PaymentApprovalRejectedView> paymentApprovalRejectedView=  paymentApprovalRejectedViewRepository.findAllByApiorderId(Long.valueOf(id),pageable);
                if (!paymentApprovalRejectedView.isEmpty()) {
                    response.setResponseMessage("Success");
                    response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                    response.setData(paymentApprovalRejectedView);
                }

           }

        }catch (Exception e){
            log.error("Error while fetching mpesa records", e);
            response.setResponseMessage("Failed");
            response.setResponseCode(ApiResponseCode.FAIL.getCode());
        }
        return response;
    }
    private boolean hasText(String str) {
        return str != null && !str.trim().isEmpty();
    }



}
