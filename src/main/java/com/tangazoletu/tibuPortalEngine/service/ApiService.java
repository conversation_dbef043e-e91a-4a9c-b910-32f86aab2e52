package com.tangazoletu.tibuPortalEngine.service;

import com.google.gson.Gson;
import com.tangazoletu.tibuPortalEngine.configurations.HttpClientConfig;
import com.tangazoletu.tibuPortalEngine.configurations.HttpClientConfig;
import com.tangazoletu.tibuPortalEngine.configurations.JwtUtils;
import com.tangazoletu.tibuPortalEngine.dto.*;
import com.tangazoletu.tibuPortalEngine.dto.paymentapidto.PaymentSyncResponse;
import com.tangazoletu.tibuPortalEngine.entities.User;
import com.tangazoletu.tibuPortalEngine.enums.ApiResponseCode;
import com.tangazoletu.tibuPortalEngine.enums.FormAction;
import com.tangazoletu.tibuPortalEngine.enums.FormName;
import com.tangazoletu.tibuPortalEngine.exceptions.EncryptionException;
import com.tangazoletu.tibuPortalEngine.repositories.CrudService;
import com.tangazoletu.tibuPortalEngine.util.SharedFunctions;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import lombok.RequiredArgsConstructor;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.SessionFactory;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.AdviceMode;
import org.springframework.core.env.Environment;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;


import java.math.BigInteger;
import java.security.SecureRandom;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
//@Slf4j
@Validated
@RequiredArgsConstructor
@EnableTransactionManagement(mode = AdviceMode.ASPECTJ)
public class ApiService {
    private static final org.slf4j.Logger log = LoggerFactory.getLogger(ApiService.class);
    private final CrudService crudService;
    private final JwtUtils jwtUtil;
    private final AesEncryptionService encryptionService;
    private final Environment environment;
    private final SharedFunctions sharedFunctions;
    private final HttpClientConfig httpClient;
    private final PaymentSyncPmsService paymentSyncPmsService;

    @Autowired
    private final RestTemplate restTemplate;

    private SessionFactory sessionFactory;

    private User apiUser;
    @Autowired
    private HttpServletRequest httpServletRequest;

    public ApiResponse processRequest(
            ApiDto encryptedRequestData,
            User apiUser,
            HttpServletRequest servletRequest,
            HttpServletResponse servletResponse
    ) throws EncryptionException {
        ApiResponse apiResponse = ApiResponse.builder().responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed to process request").build();

        //Get the dynamic encryptionKey form the token
        String dynamicEncryptionKey = jwtUtil.getDynamicEncryptionKey(
                servletRequest.getHeader("Authorization").split(" ")[1]
        );
        MultipartFile[] file = new MultipartFile[]{};
        byte[] decryptedFileBytes = null;

        try {
            //Decrypt the payload
            String encryptedPayload = encryptedRequestData.getPayload();
            String decryptedJson = encryptionService.decrypt(encryptedPayload);
            JSONObject jsonData = new JSONObject(decryptedJson);

            if(encryptedRequestData.getFile()!=null){
                //Decode the file and decrypt

                String originalFileName = jsonData.getString("fileName");
                String encryptedFile = encryptedRequestData.getFile();

                // Decrypt file bytes
                decryptedFileBytes = encryptionService.decryptFile(encryptedFile);
                if (decryptedFileBytes == null || decryptedFileBytes.length == 0) {
                    throw new IllegalArgumentException("Decrypted file bytes are null or empty");
                }

                // Optional: Save the decrypted file locally for debugging
                Path folder = Paths.get("Uploaded-files");
                Files.createDirectories(folder);
                Path filePath = folder.resolve(originalFileName);
                Files.write(filePath, decryptedFileBytes);
                System.out.println("File saved successfully at " + filePath.toString());

                // Create InputStream from decrypted bytes
                //putStream excelInputStream = new ByteArrayInputStream(decryptedFileBytes);
                // Remember to
                try (InputStream is = new ByteArrayInputStream(decryptedFileBytes)) {
                    Workbook workbook = WorkbookFactory.create(is); // Auto detects XLS/XLSX

                    // You can now read sheets, rows, cells from workbook
                    Sheet sheet = workbook.getSheetAt(0);
                    for (Row row : sheet) {
                        for (Cell cell : row) {
                            System.out.print(cell.toString() + "\t");
                        }
                        System.out.println();
                    }
                    workbook.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    // Handle parsing errors (invalid format, corrupted file, etc)
                }
                // Extract addressBookId from JSON (or provide fallback)
                String addressBookId = jsonData.optString("addressbook", null);
                String fileName = originalFileName;


                // Decrypt file bytes as before:

                if (decryptedFileBytes == null || decryptedFileBytes.length == 0) {
                    throw new IllegalArgumentException("Decrypted file bytes are null or empty");
                }



                Files.write(Path.of("Uploaded-files/" + originalFileName), decryptedFileBytes);
                System.out.println("File Saved Successfully: "+ originalFileName);
            };



            log.info("RECEIVED REQUEST: {}", jsonData);

            if (jsonData.has("f") && !jsonData.isNull("f") && !ObjectUtils.isEmpty(jsonData.optString("f", ""))) {

                String recordPrimaryKey = "";
                if (jsonData.has("cell") && !jsonData.isNull("cell")) {
                    recordPrimaryKey = jsonData.optString("cell", "");
                }
                //If cell is not empty and the action is not provided, the action becomes update
                FormName formName = null;
                try {
                    formName = FormName.valueOf(jsonData.getString("f").trim().toUpperCase());
                } catch (IllegalArgumentException e) {
                    e.printStackTrace();
                }

                FormAction formAction = null;
                try {
                    formAction = !jsonData.has("action") ? FormAction.__NOT_SET :
                            !ObjectUtils.isEmpty(jsonData.getString("action"))
                                    ? FormAction.valueOf(jsonData.getString("action").trim().toUpperCase())
                                    : !ObjectUtils.isEmpty(recordPrimaryKey) && ObjectUtils.isEmpty(jsonData.getString("action"))
                                    ? FormAction.UPDATE : FormAction.CREATE;
                } catch (Exception e) {
                    e.printStackTrace();
                }

                if (formName != null) {
                    //Sanitize the forName and action so that they can be converted properly to enums
                    jsonData.put("formName", formName.name());
                    jsonData.put("action", formAction.name());

                    switch (formName) {
                        case USER: {
                            //user module goes here
                            apiResponse = processUserManagement(new Gson().fromJson(jsonData.toString(), UserRequest.class), apiUser, servletRequest, servletResponse);
                            break;
                        }
                        case ROLE: {

                            apiResponse = processRoles(new Gson().fromJson(jsonData.toString(), RoleRequest.class), apiUser, servletRequest, servletResponse);
                            break;

                        }
                        case B2CACCOUNTSETTINGS: {
                            apiResponse = processb2cAccountSettings(new Gson().fromJson(jsonData.toString(), B2CAccountSettingsRequest.class), apiUser, servletRequest, servletResponse);
                            break;
                        }
                        case PARAMETER:{
                            apiResponse = processParameter(new Gson().fromJson(jsonData.toString(), ParameterRequest.class), apiUser, servletRequest, servletResponse);
                            break;
                        }
                        case EXCEMPT_USER: {
                            apiResponse = processExcemptUsers(new Gson().fromJson(jsonData.toString(), ExcemptUserRequest.class ), apiUser, servletRequest,servletResponse);
                            break;
                        }
                        case DEACTIVATE_USER: {
                            apiResponse = processadeactivatedUserUsers(new Gson().fromJson(jsonData.toString(), deactivateUserRequest.class ), apiUser, servletRequest,servletResponse);
                            break;
                        }
                        case MEETING:{
                            apiResponse = processMeeting(new Gson().fromJson(jsonData.toString(), MeetingRequest.class), jsonData, apiUser, servletRequest, servletResponse);
                            break;
                        }
                        case APPROVAL:
                        case APPROVALTOP:{
                            apiResponse = processApproval(jsonData, apiUser, servletRequest, servletResponse);
                            break;
                        }
                        case CHANGEPHONENUMBER: {
                            apiResponse = processChangePhone(jsonData, apiUser, servletRequest, servletResponse);
                            break;
                        }
                        case CONFIRMRECIPIENT: {
                            apiResponse = processConfirmRecipient(jsonData, file, apiUser, servletRequest, servletResponse);
                            break;
                        }
                        case ATTENDANCEPROCESSINGWITHEXCEL:{
                            apiResponse = processAttendanceProcessingWithExcel(jsonData, file, apiUser, servletRequest, servletResponse);
                            break;
                        }
                        case ATTENDANCEAPPROVAL:{
                            apiResponse = processAttendanceApproval(jsonData, file, apiUser, servletRequest,servletResponse);
                            break;
                        }
                        case ATTENDANCEPROCESSING:{
                            apiResponse = processAttendanceProcessing(jsonData, file, apiUser, servletRequest, servletResponse);
                            break;

                        }
//                        case BENEFICIARYTYPE:
//                        {
//                            apiResponse = processBeneficiaryType( jsonData,servletRequest, servletResponse);
//                            break;
//
//                        }
                        case COUNTY:
                        {
                            apiResponse = processCounty( jsonData,servletRequest, servletResponse);
                            break;

                        }
                        case DISTRICT:
                        {
                            apiResponse = processDistrict( jsonData,servletRequest, servletResponse);
                            break;

                        }
                        case FACILITY:
                        {
                            apiResponse = processFacility( jsonData,servletRequest, servletResponse);
                            break;

                        }
                        case FINANCIER:
                        {
                            apiResponse = processFinancier( jsonData,servletRequest, servletResponse);
                            break;

                        }
                        case ORDERTYPE:
                        {
                            apiResponse = processOrderType( jsonData,servletRequest, servletResponse);
                            break;

                        }
                        case PROVINCE:
                        {
                            apiResponse = processProvince( jsonData,servletRequest, servletResponse);
                            break;

                        }
                        case BUDGET:
                        {
                            apiResponse = processBudget( jsonData,servletRequest, servletResponse);
                            break;

                        }
                        case PERDIEM:
                        {
                            apiResponse = processPerdiem( jsonData,servletRequest, servletResponse);
                            break;

                        }
                        case JOBGROUP:
                        {
                            apiResponse = processJobGroup( jsonData,servletRequest, servletResponse);
                            break;

                        }
                        case RECIPIENT:
                        {
                            apiResponse = processRecipient( jsonData,servletRequest, servletResponse);
                            break;

                        }
                        case ORGANISATION:
                        {
                            apiResponse = processOrganisation( jsonData,servletRequest, servletResponse);
                            break;

                        }
                        case ORG_PAYMENT_TYPES:
                        {
                            apiResponse = processOrgOrderType( jsonData,servletRequest, servletResponse);
                            break;

                        }
                        case LINEITEM:
                        {
                            apiResponse = processLineItem( jsonData,servletRequest, servletResponse);
                            break;

                        }
                        case ATTENDANCEAPPROVALEXCEL: {
                            apiResponse = processAttendanceApprovalExcel(jsonData, file, apiUser, servletRequest, servletResponse);
                            break;
                        }
                        case PAYMENTS_INBUILD_PAYMENT: {
                            apiResponse = processPaymentsInbuildPayment(jsonData,file, apiUser, servletRequest, servletResponse);
                            break;
                        }
                        case STAGEAPPROVAL: {
                            apiResponse = processStageApproval(jsonData, file, apiUser, servletRequest, servletResponse);
                            break;
                        }
                        case ORDER: {
                            apiResponse = processOrder(jsonData, decryptedFileBytes, apiUser, servletRequest, servletResponse);
                            break;
                        }
                        case BENEFICIARYTYPE: {
                            apiResponse = processBeneficiaryType(jsonData, file, apiUser, servletRequest, servletResponse);
                            break;
                        }
                        case TRANS_ADD: {
                            apiResponse = processTransAdd(jsonData, file, apiUser, servletRequest, servletResponse);
                            break;
                        }
                        case PAYMENTSYNC: {
                            apiResponse = processPaymentSync(jsonData, file, apiUser, servletRequest, servletResponse);
                            break;
                        }
                        case RESUBMIT: {
                            apiResponse = processResubmit(jsonData, file, apiUser, servletRequest, servletResponse);
                            break;

                        }


                        case BULKSMS:
                        {
                            apiResponse = processSmsBroadcast( jsonData,servletRequest, servletResponse);
                            break;

                        }
                        case SMSTEMPLATES:
                        {
                            apiResponse = processMessageTemplate( jsonData,servletRequest, servletResponse);
                            break;

                        }
                        case ADDRESSBK:
                        {
                            apiResponse=processAddressBook(jsonData,servletRequest,servletResponse);
                            break;
                        }
                        case CONTACTUPLOAD: {
                            MultipartFile fileContact = extractFileFromRequest(servletRequest);
                            String addressBookId = jsonData.optString("addressbook");
                            String fileName = fileContact.getOriginalFilename();

                            InputStream excelInputStream = fileContact.getInputStream();
                            byte[] fileBytes = excelInputStream.readAllBytes();  // Java 9+ method to read all bytes

                            apiResponse = processContactExcelUpload(fileBytes, addressBookId, fileName, servletRequest);
                            break;
                        }
                        case FUNDALLOCATION:
                            apiResponse = processFundAllocation(new Gson().fromJson(jsonData.toString(), FundsAllocationFormRequest.class),servletRequest,servletResponse);
                            break;
                        case REVERSEAPPROVAL:
                            apiResponse = processReverseApproval(new Gson().fromJson(jsonData.toString(), ReverseApprovalRequest.class),servletRequest,servletResponse);
                            break;
                        case REVERSEREJECTION:
                            apiResponse = processReverseRejection(new Gson().fromJson(jsonData.toString(),ReversalRejectionRequest.class ), servletRequest,servletResponse);





                    }
                }

                // Remove this once the cases are added.
                // This just allows you to visualize the request sent
                servletResponse.setStatus(javax.servlet.http.HttpServletResponse.SC_OK);
//                apiResponse.setResponseMessage("Request received and decrypted. You should switch me off if you are seeing this with the switch cases.");
//                apiResponse.setData(decryptedJson);
            }
            else {
                servletResponse.setStatus(javax.servlet.http.HttpServletResponse.SC_BAD_REQUEST);
                apiResponse.setResponseMessage("Not all the relevant details were provided to process the request.");
                apiResponse.setData(decryptedJson);
            }
        } catch (Exception e) {
            servletResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            log.error("ERROR OCCURRED ON PROCESSING THE REQUEST. ERROR: {}", e.getMessage());
            e.printStackTrace();
            if (e.getClass().equals(EncryptionException.class)) throw new EncryptionException(e.getMessage(), e);

        }

        log.info("RESPONSE: {}", new Gson().toJson(apiResponse));
        return apiResponse;
    }




    private ApiResponse processMeeting(MeetingRequest meetingRequest, JSONObject postData, User apiUser, HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed an error occurred")
                .build();
        try {
            UserDetailsImpl userDetails = (UserDetailsImpl) SecurityContextHolder.getContext()
                    .getAuthentication()
                    .getPrincipal();
            int userId = Math.toIntExact(userDetails.getId());
            String recordPrimaryKey = meetingRequest.getFormCell();
            String action = meetingRequest.getAction();
            if (action.equalsIgnoreCase("DELETE")){
                String deleteSql = "UPDATE event SET intrash = 'Yes' WHERE id = :id";
                HashMap<String, Object> params = new HashMap<>();
                params.put("id", recordPrimaryKey);
                crudService.executeNativeQuery(deleteSql, params);
                apiResponse.setResponseMessage("Activity has been deleted successfully");
                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                return apiResponse;
            }else {
                if (recordPrimaryKey != null && recordPrimaryKey.length() > 0) {
                    String sql ="SELECT datediff(end_date,start_date)day,day(start_date),meeting_code,number_Expected,day(end_date) FROM event where id= :id";
                    HashMap<String, Object> params = new HashMap<>();
                    params.put("id", recordPrimaryKey);
                    List<Object[]> result = crudService.fetchWithNativeQuery(sql,params,0,1);
                    String startDateIso = postData.optString("startdate");
                    LocalDate startDate = null;
                    String endDateIso = postData.optString("enddate");
                    LocalDate endDate = null;
                    if (startDateIso != null && !startDateIso.isEmpty()) {
                        LocalDateTime startDateTime = LocalDateTime.parse(startDateIso, DateTimeFormatter.ISO_DATE_TIME);
                        startDate = startDateTime.toLocalDate();
                    }
                    if (endDateIso != null && !endDateIso.isEmpty()) {
                        LocalDateTime endDateTime = LocalDateTime.parse(endDateIso, DateTimeFormatter.ISO_DATE_TIME);
                        endDate = endDateTime.toLocalDate();
                    }

                    // Update event
                    String updateSql = "UPDATE event SET number_Expected = :expected, county_id = :county, venue = :venue, status = :status, start_date = :start, budget_id = :budget, end_date = :end, meeting_type = :meetingType, event_name = :eventName, travelling_days = :travelDays, payment_times = :paymentTimes WHERE id = :id";
                    Map<String, Object> updateParams = new HashMap<>();
                    updateParams.put("expected", Integer.parseInt((String) postData.get("numberExpected")));
                    updateParams.put("county", postData.get("county"));
                    updateParams.put("venue", postData.get("venue"));
                    updateParams.put("status", postData.get("status"));
                    updateParams.put("start", startDate);
                    updateParams.put("budget", postData.get("budget"));
                    updateParams.put("end", endDate);
                    updateParams.put("meetingType", postData.get("meetingtype"));
                    updateParams.put("eventName", postData.get("meetingname"));
                    updateParams.put("travelDays", Integer.parseInt((String) postData.get("paybletravellingdays")));
                    updateParams.put("paymentTimes", postData.get("paymenttimes"));
                    updateParams.put("id", recordPrimaryKey);

                    crudService.executeNativeQuery(updateSql, updateParams);

                    Object[] row1 = result.get(0);
                    int originalDays = Integer.parseInt(row1[0].toString()) + 1;
                    int startDay = Integer.parseInt(row1[1].toString());
                    int endDay = Integer.parseInt(row1[4].toString());
                    String meetingCode = row1[2].toString();
                    int originalExpected = Integer.parseInt(row1[3].toString());
                    int numberExpected = meetingRequest.getNumberExpected();
                    int additionalCodes = numberExpected - originalExpected;

                    // Calculate updated number of days
                    long date1 = startDate.toEpochDay();
                    long date2 = endDate.toEpochDay();
                    int updatedDays = (int) Math.abs(date2 - date1) + 1;

                    // === LOGGING (replaces $_SESSION['testing']) ===
                    StringBuilder debugLog = new StringBuilder();
                    debugLog.append("Original No of Days: ").append(originalDays)
                            .append(" | Updated no of days: ").append(updatedDays)
                            .append(" | No Expected: ").append(numberExpected)
                            .append(" | Original Expected: ").append(originalExpected)
                            .append(" | Additional Added: ").append(additionalCodes).append("\n");

                    // === Insert codes for additional participants for existing days ===
                    for (int i = 1; i <= originalDays; i++) {
                        for (int j = 1; j <= additionalCodes; j++) {
                            String code = sharedFunctions.generateRandomNumber(4);
                            String insertSql = "INSERT INTO event_codes(meeting_code, verification_code, day, last_inserted) " +
                                    "VALUES (:meeting_code, :code, :day, '1')";
                            Map<String, Object> params2 = new HashMap<>();
                            params2.put("meeting_code", meetingCode);
                            params2.put("code", code);
                            params2.put("day", i);
                            crudService.executeNativeQuery(insertSql, params2);

                            debugLog.append(" IN day ").append(i).append(" participant ").append(j)
                                    .append(" code: ").append(code).append("\n");
                        }
                        debugLog.append(" OUT day ").append(i).append("\n");
                        startDay++;
                    }

                    // === Insert codes for newly added days ===
                    int newStartDay = endDay + 1;
                    int newDays = updatedDays - originalDays;

                    debugLog.append("Start New Day From: ").append(newStartDay)
                            .append(" | Days Added: ").append(newDays).append("\n");

                    for (int i = originalDays + 1; i <= updatedDays; i++) {
                        for (int j = 1; j <= numberExpected; j++) {
                            String code = sharedFunctions.mtgrandomNumber(4);
                            String insertSql = "INSERT INTO event_codes(meeting_code, verification_code, day, last_inserted) " +
                                    "VALUES (:meeting_code, :code, :day, '1')";
                            Map<String, Object> params1 = new HashMap<>();
                            params1.put("meeting_code", meetingCode);
                            params1.put("code", code);
                            params1.put("day", i);
                            crudService.executeNativeQuery(insertSql, params1);

                            debugLog.append(" IN new day ").append(i).append(" participant ").append(j)
                                    .append(" code: ").append(code).append("\n");
                        }
                        debugLog.append(" OUT new day ").append(i).append("\n");
                        newStartDay++;
                    }

                    // Print or log the debugging info (replace with logger if available)
                    apiResponse.setResponseMessage(debugLog.toString());
                    // Step 1: Delete existing view entries
                    String deleteSql = "DELETE FROM event_codes_view WHERE meetingCode = :meetingCode";
                    Map<String, Object> deleteParams = new HashMap<>();
                    deleteParams.put("meetingCode", meetingCode);
                    crudService.executeNativeQuery(deleteSql, deleteParams);

                    // Step 2: Get distinct days
                    String daysSql = "SELECT DISTINCT day FROM event_codes WHERE meeting_code = :meetingCode";
                    Map<String, Object> daysParams = new HashMap<>();
                    daysParams.put("meetingCode", meetingCode);
                    List<Object[]> daysList = crudService.fetchWithNativeQuery(daysSql, daysParams, 0, 0);

                    // Step 3: Loop through each day
                    for (Object[] dayRow : daysList) {
                        int day = Integer.parseInt(dayRow[0].toString());

                        // Step 3a: Fetch detailed registration codes for this day
                        String query = """
                            SELECT ID AS primarykey,
                                   CONCAT(
                                       (SELECT DATE_FORMAT(DATE_ADD(start_date, INTERVAL day-1 DAY), '%d/%m/%Y')
                                        FROM event WHERE meeting_code = :meetingCode),
                                       '<BR/>Dial *645*70# to register ',
                                       '.<br/>Meeting code: </b>', :meetingCode,
                                       '<br/><b>Registration Code: </b>', verification_code
                                   ) AS `Registration Code`,
                                   day
                            FROM event_codes
                            WHERE meeting_code = :meetingCode AND day = :day
                            ORDER BY ID ASC
                        """;

                        Map<String, Object> mainParams = new HashMap<>();
                        mainParams.put("meetingCode", meetingCode);
                        mainParams.put("day", day);
                        List<Object[]> mainData = crudService.fetchWithNativeQuery(query, mainParams, 0, 0);

                        // Step 3b: Prepare for inserting in groups of 4
                        String columnOne = "", columnTwo = "", columnThree = "", columnFour = "";
                        int d = 1;
                        int inserted_ok = 0;
                        int prevDay = day;

                        for (Object[] row : mainData) {
                            String registrationCode = row[1].toString();
                            int currentDay = Integer.parseInt(row[2].toString());

                            switch (d) {
                                case 1 -> {
                                    columnOne = registrationCode;
                                    prevDay = currentDay;
                                }
                                case 2 -> {
                                    columnTwo = registrationCode;
                                    if (currentDay != prevDay) {
                                        sharedFunctions.insertEventCodesView(meetingCode, columnOne, "", columnThree, columnFour, prevDay);
                                        columnOne = columnTwo;
                                        columnTwo = columnThree = columnFour = "";
                                        d = 0;
                                        inserted_ok = 1;
                                    }
                                }
                                case 3 -> {
                                    columnThree = registrationCode;
                                    if (currentDay != prevDay) {
                                        sharedFunctions.insertEventCodesView(meetingCode, columnOne, columnTwo, "", columnFour, prevDay);
                                        columnOne = columnThree;
                                        columnTwo = columnThree = columnFour = "";
                                        d = 0;
                                        inserted_ok = 1;
                                    }
                                }
                                default -> { // case 4 and above
                                    columnFour = registrationCode;
                                    if (currentDay != prevDay) {
                                        sharedFunctions.insertEventCodesView(meetingCode, columnOne, columnTwo, columnThree, "", prevDay);
                                    } else {
                                        sharedFunctions.insertEventCodesView(meetingCode, columnOne, columnTwo, columnThree, columnFour, currentDay);
                                    }
                                    columnOne = columnTwo = columnThree = columnFour = "";
                                    d = 0;
                                    inserted_ok = 1;
                                }
                            }
                            d++;
                            prevDay = currentDay;
                        }

                        // Final insert if needed
                        if (inserted_ok == 0 && (!columnOne.isEmpty() || !columnTwo.isEmpty() || !columnThree.isEmpty() || !columnFour.isEmpty())) {
                            sharedFunctions.insertEventCodesView(meetingCode, columnOne, columnTwo, columnThree, columnFour, day);
                        }
                    }
                    HashMap<String, Object> params2 = new HashMap<>();
                    params2.put("meetingCode", meetingCode);

                    // Step 1: Delete from event_codes_latest
                    String deleteSql1 = "DELETE FROM event_codes_latest WHERE meetingCode = :meetingCode";
                    crudService.executeNativeQuery(deleteSql1, params2);

                    // Step 2: Get distinct days
                    String selectDaysSql = "SELECT DISTINCT day FROM event_codes WHERE meeting_code = :meetingCode";
                    Map<String, Object> params3 = new HashMap<>();
                    params3.put("meetingCode", meetingCode);
                    List<Object> daysList2 = crudService.fetchWithNativeQuery(selectDaysSql, params3, 0, 10);

                    for (Object dayRow : daysList2) {
                        int day = ((Number) dayRow).intValue();

                        HashMap<String, Object> queryParams = new HashMap<>();
                        queryParams.put("meetingCode", meetingCode);
                        queryParams.put("day", day);

                        // Step 3: Fetch mainData for that day
                        String mainQuery = """
                                SELECT ID as primarykey,
                                CONCAT(
                                    (SELECT DATE_FORMAT(DATE_ADD(start_date, INTERVAL day-1 DAY),'%d/%m/%Y') FROM event WHERE meeting_code = :meetingCode),
                                    '<BR/>Dial *645*70# to register ', '.<br/>Meeting code: </b>', :meetingCode,
                                    '<br/><b>Registration Code: </b>', verification_code
                                ) AS `Registration Code`,
                                (SELECT DATE_FORMAT(DATE_ADD(start_date, INTERVAL day-1 DAY),'%d/%m/%Y') 
                                 FROM event WHERE meeting_code = :meetingCode) AS `Code for Date`,
                                day 
                                FROM event_codes 
                                WHERE meeting_code = :meetingCode AND day = :day AND last_inserted = 1 
                                ORDER BY id ASC
                            """;

                        List<Object[]> mainData = crudService.fetchWithNativeQuery(mainQuery, queryParams, 0, 0);

                        int d = 1;
                        int insertedOk = 0;
                        String columnOne = "", columnTwo = "", columnThree = "", columnFour = "";
                        int dayOne = 0, dayTwo = 0, dayThree = 0, dayFour = 0;

                        for (Object[] row : mainData) {
                            String code = (String) row[1];
                            int codeDay = (int) row[3];

                            switch (d) {
                                case 1 -> {
                                    columnOne = code;
                                    dayOne = codeDay;
                                }
                                case 2 -> {
                                    columnTwo = code;
                                    dayTwo = codeDay;
                                    if (dayOne != dayTwo) {
                                        sharedFunctions.insertIntoLatest(meetingCode, columnOne, "", columnThree, columnFour, dayOne);
                                        columnOne = columnTwo;
                                        columnTwo = columnThree = columnFour = "";
                                        d = 0;
                                        insertedOk = 1;
                                    }
                                }
                                case 3 -> {
                                    columnThree = code;
                                    dayThree = codeDay;
                                    if (dayTwo != dayThree) {
                                        sharedFunctions.insertIntoLatest(meetingCode, columnOne, columnTwo, "", columnFour, dayTwo);
                                        columnOne = columnThree;
                                        columnTwo = columnThree = columnFour = "";
                                        d = 0;
                                        insertedOk = 1;
                                    }
                                }
                                default -> {
                                    columnFour = code;
                                    dayFour = codeDay;
                                    if (dayThree != dayFour) {
                                        sharedFunctions.insertIntoLatest(meetingCode, columnOne, columnTwo, columnThree, "", dayThree);
                                        columnOne = columnFour;
                                        columnTwo = columnThree = columnFour = "";
                                        d = 0;
                                        insertedOk = 1;
                                    } else {
                                        sharedFunctions.insertIntoLatest(meetingCode, columnOne, columnTwo, columnThree, columnFour, dayFour);
                                        columnOne = columnTwo = columnThree = columnFour = "";
                                        d = 0;
                                        insertedOk = 1;
                                    }
                                }
                            }
                            d++;
                        }

                        // Final insert if not already done
                        if (insertedOk == 0 && (!columnOne.isEmpty() || !columnTwo.isEmpty() || !columnThree.isEmpty() || !columnFour.isEmpty())) {
                            sharedFunctions.insertIntoLatest(meetingCode, columnOne, columnTwo, columnThree, columnFour, day);
                        }
                    }
                    apiResponse.setResponseMessage("Meeting Updated Successfully.");
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);



                }else {//insert new meeting
                    // Java version of PHP logic for event creation and code generation

                    String numberExpected = postData.getString("numberExpected");
                    String county = postData.get("county").toString();
                    String venue = postData.getString("venue");
                    String startDate = ZonedDateTime.parse(postData.getString("startdate"), DateTimeFormatter.ISO_DATE_TIME).toLocalDate().toString();
                    String endDate = ZonedDateTime.parse(postData.getString("enddate"), DateTimeFormatter.ISO_DATE_TIME).toLocalDate().toString();
                    String budget = postData.get("budget").toString();
                    String meetingType = postData.getString("meetingtype");
                    String meetingName = postData.getString("meetingname");
                    String paymentTimes = postData.getString("paymenttimes");
                    String travellingDays = postData.getString("paybletravellingdays");
                    String orgId = postData.optString("org_o_id", "1");

                    // Get next event ID
                    String maxIdSql = "SELECT MAX(ID) + 1 as nextId FROM event";
                    List<Object> maxIdResult = crudService.fetchWithNativeQuery(maxIdSql, new HashMap<>(), 0, 1);
                    int nextId = (maxIdResult.isEmpty() || maxIdResult.get(0) == null) ? 1 : ((Number) maxIdResult.get(0)).intValue();


                    // Generate meeting code
                    String dateConcat = LocalDate.now().format(DateTimeFormatter.ofPattern("ddMMyy"));
                    int codeSum = Integer.parseInt(dateConcat) + nextId;
                    String orgPrefixSql = "SELECT org_prefix FROM organisation WHERE id = :orgId";
                    Map<String, Object> orgParams = new HashMap<>();
                    orgParams.put("orgId", orgId);

                    List<Object> orgPrefixResult = crudService.fetchWithNativeQuery(orgPrefixSql, orgParams, 0, 1);
                    String orgPrefix = (orgPrefixResult.isEmpty() || orgPrefixResult.get(0) == null) ? "" : orgPrefixResult.get(0).toString().trim();

                    String meetingCode = orgPrefix + codeSum;

                    // Insert event
                    String insertEventSql = "INSERT INTO event (number_Expected, county_id, meeting_code, venue, status, start_date, end_date, budget_id, meeting_type, event_name, payment_times, travelling_days, org_id) VALUES (:numExpected, :county, :meetingCode, :venue, '1', :startDate, :endDate, :budget, :meetingType, :meetingName, :paymentTimes, :travellingDays, :orgId)";
                    Map<String, Object> eventParams = new HashMap<>();
                    eventParams.put("numExpected", numberExpected);
                    eventParams.put("county", county);
                    eventParams.put("meetingCode", meetingCode);
                    eventParams.put("venue", venue);
                    eventParams.put("startDate", startDate);
                    eventParams.put("endDate", endDate);
                    eventParams.put("budget", budget);
                    eventParams.put("meetingType", meetingType);
                    eventParams.put("meetingName", meetingName);
                    eventParams.put("paymentTimes", paymentTimes);
                    eventParams.put("travellingDays", travellingDays);
                    eventParams.put("orgId", orgId);

                    crudService.executeNativeQuery(insertEventSql, eventParams);

                    // Get event days
                    String daysSql = "SELECT DATEDIFF(end_date, start_date), DAY(start_date) FROM event WHERE meeting_code = :meetingCode";
                    Map<String, Object> daysParams = new HashMap<>();
                    daysParams.put("meetingCode", meetingCode);

                    List<Object[]> daysResult = crudService.fetchWithNativeQuery(daysSql, daysParams, 0, 1);
                    int numberOfDays = (daysResult.isEmpty() || daysResult.get(0)[0] == null) ? 1 : ((Number) daysResult.get(0)[0]).intValue() + 1;
                    int startDay = (daysResult.isEmpty() || daysResult.get(0)[1] == null) ? 1 : ((Number) daysResult.get(0)[1]).intValue();


                    // Insert codes
                    for (int i = 1; i <= numberOfDays; i++) {
                        for (int j = 1; j <= Integer.parseInt(numberExpected); j++) {
                            String code = sharedFunctions.mtgrandomNumber(4);
                            String codeSql = "INSERT INTO event_codes (meeting_code, verification_code, day) VALUES (:meetingCode, :code, :day)";
                            Map<String, Object> codeParams = Map.of("meetingCode", meetingCode, "code", code, "day", i);
                            crudService.executeNativeQuery(codeSql, codeParams);
                        }
                    }

                    // Insert into event_codes_view
                    String getDaysSql = "SELECT DISTINCT day FROM event_codes WHERE meeting_code = :meetingCode";
                    List<Integer> dayList = crudService.fetchWithNativeQuery(getDaysSql, Map.of("meetingCode", meetingCode), 0, 100)
                            .stream()
                            .map(row -> ((Number) row).intValue())
                            .toList();

                    for (Integer day : dayList) {
                        String codeQuery = "SELECT ID, CONCAT((SELECT DATE_FORMAT(DATE_ADD(start_date, INTERVAL day - 1 DAY), '%d/%m/%Y') FROM event WHERE meeting_code = :meetingCode), '<BR/>Dial *645*70# to register .<br/>Meeting code: </b>', :meetingCode, '<br/><b>Registration Code: </b>', verification_code), day FROM event_codes WHERE meeting_code = :meetingCode AND day = :day ORDER BY ID ASC";
                        List<Object[]> codeRows = crudService.fetchWithNativeQuery(codeQuery, Map.of("meetingCode", meetingCode, "day", day), 0, 100);

                        String col1 = "", col2 = "", col3 = "", col4 = "";
                        String day1 = "", day2 = "", day3 = "", day4 = "";
                        int d = 1;
                        int insertedOk = 0;

                        for (Object[] row : codeRows) {
                            String code = row[1].toString();
                            String dDay = row[2].toString();

                            switch (d) {
                                case 1 -> {
                                    col1 = code; day1 = dDay;
                                }
                                case 2 -> {
                                    col2 = code; day2 = dDay;
                                    if (!day1.equals(day2)) {
                                        sharedFunctions.insertIntoEventCodesView(meetingCode, col1, "", col3, col4, day1);
                                        col1 = col2; col2 = col3 = col4 = ""; d = 1; insertedOk = 1; continue;
                                    }
                                }
                                case 3 -> {
                                    col3 = code; day3 = dDay;
                                    if (!day2.equals(day3)) {
                                        sharedFunctions.insertIntoEventCodesView(meetingCode, col1, col2, "", col4, day2);
                                        col1 = col3; col2 = col3 = col4 = ""; d = 1; insertedOk = 1; continue;
                                    }
                                }
                                default -> {
                                    col4 = code; day4 = dDay;
                                    if (!day3.equals(day4)) {
                                        sharedFunctions.insertIntoEventCodesView(meetingCode, col1, col2, col3, "", day3);
                                        col1 = col4; col2 = col3 = col4 = ""; d = 1;
                                    } else {
                                        sharedFunctions.insertIntoEventCodesView(meetingCode, col1, col2, col3, col4, day4);
                                        col1 = col2 = col3 = col4 = ""; d = 0;
                                    }
                                    insertedOk = 1;
                                }
                            }
                            d++;
                        }

                        if (insertedOk == 0) {
                            sharedFunctions.insertIntoEventCodesView(meetingCode, col1, col2, col3, col4, String.valueOf(day));
                        }
                    }

                    apiResponse.setResponseMessage("Meeting Added Successfully.");
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);


                }

            }

        }catch (Exception e){
            log.error(e.getMessage());
            e.printStackTrace();
        }
        return apiResponse;
    }

    private ApiResponse processExcemptUsers(ExcemptUserRequest excemptUserRequest, User apiUser, HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed an error occurred")
                .build();
        try {
            String excemptUser = excemptUserRequest.getExcempt();
            if (excemptUser != null) {
                String update = "UPDATE permission SET EXCEMPTED_USERS = CONCAT(EXCEMPTED_USERS, ',', :excempt)";
                Map<String, Object> params = new HashMap<>();
                params.put("excempt", excemptUser); // excemptUser should be something like "222236"
                crudService.executeNativeQuery(update, params);
                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                apiResponse.setResponseMessage("User excempted from permission successfully");
            }

        }catch (Exception e){
            log.error(e.getMessage());
            e.printStackTrace();
        }
        return apiResponse;
    }


    private ApiResponse processadeactivatedUserUsers(deactivateUserRequest request, User apiUser, HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed an error occurred")
                .build();
        try {
            String status = request.getIntrash();
            Long id = Long.valueOf(request.getFormCell());
            if (status != null) {
                String update = "UPDATE user SET inTrash = :inTrash WHERE id = :id";
                Map<String, Object> params = new HashMap<>();
                params.put("inTrash", status);
                params.put("id", id); // excemptUser should be something like "222236"
                crudService.executeNativeQuery(update, params);
                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                apiResponse.setResponseMessage("User Activated successfully");
            }

        }catch (Exception e){
            log.error(e.getMessage());
            e.printStackTrace();
        }
        return apiResponse;
    }

    private ApiResponse processParameter(ParameterRequest parameterRequest, User apiUser, HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed: An error occurred")
                .build();

        try {
            Long cell = Long.valueOf(parameterRequest.getFormCell()); // equivalent to $_POST['cell']
            String description = parameterRequest.getMessage();
            String parmValue = parameterRequest.getParmValue();
            String parameter = parameterRequest.getParameter();// equivalent to $parmvalue
            String sourceIp = sharedFunctions.getSourceIp(servletRequest);
            UserDetailsImpl userDetails = (UserDetailsImpl) SecurityContextHolder.getContext()
                    .getAuthentication()
                    .getPrincipal();
            int userId = Math.toIntExact(userDetails.getId());
            if (cell != null) {
                Map<String, Object> paramsq = Map.of("id", new BigInteger(String.valueOf(cell)));
                String record1 = sharedFunctions.getRecordById("PARAM", "ID = :id", paramsq);

                // Fetch old record
                //String record1 = sharedFunctions.getRecordById("PARAM", "POSITION = '" + cell + "'",new HashMap<>());

                // Update PARAM set VALUE
                String updateSql = "UPDATE PARAM SET VALUE = :value, POSITION = :position, PARAMETER = :parameter, DESCRIPTION = :description WHERE ID = :id";
                Map<String, Object> updateParams = new HashMap<>();
                updateParams.put("value", parmValue);
                updateParams.put("position", cell);
                updateParams.put("parameter", parameter);
                updateParams.put("description", description);
                updateParams.put("id", cell);
                crudService.executeNativeQuery(updateSql, updateParams);

                Map<String, Object> params3 = Map.of("id", new BigInteger(String.valueOf(cell)));
                String record2 = sharedFunctions.getRecordById("PARAM", "ID = :id", params3);


                // Fetch updated record
                //String record2 = sharedFunctions.getRecordById("PARAM", "POSITION = '" + cell + "'",new HashMap<>());

                // Generate change log string
                String logString = sharedFunctions.returnLogString(record1, record2);

                // Audit the update
                sharedFunctions.auditAction("System params Update",
                        "Updated System params " + parmValue + " (" + logString + ")",
                        sourceIp,
                        parameterRequest.toString(), null,userId, null);

                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                apiResponse.setResponseMessage("Parameter updated successfully");
            }

        } catch (Exception e) {
            log.error("Error in processParameter: " + e.getMessage(), e);
        }

        return apiResponse;
    }



    private ApiResponse processUserManagement(UserRequest request, User apiUser, HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed to process request")
                .build();
        try {
            String recordPrimaryKey = request.getFormCell();
            String sourceIp = sharedFunctions.getSourceIp(servletRequest);
            String organisation = request.getOrganisation();
            String[] permissions;
            String record1 = "";
            UserDetailsImpl userDetails = (UserDetailsImpl) SecurityContextHolder.getContext()
                    .getAuthentication()
                    .getPrincipal();
            int userId = Math.toIntExact(userDetails.getId());
            //int orgId = Math.toIntExact(Long.valueOf(userDetails.getOrganizationName()));
            if (request.getFormAction().equalsIgnoreCase("DELETE")) {
                String moveUserToTrashQuery = "update user set intrash ='yes' where id =:id";
                HashMap<String, Object> trashPrams = new HashMap<>();
                trashPrams.put("id", recordPrimaryKey);
                crudService.executeNativeQuery(moveUserToTrashQuery, trashPrams);
                sharedFunctions.auditAction("DELETE", "DELETED USER ID: "+ recordPrimaryKey, sourceIp, request.toString(),null, userId, null);
                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                apiResponse.setResponseMessage("Deleted Successfully");

            } else  {//check permissions selected
                permissions = request.getPermission();
                if (permissions != null && permissions.length > 0) {
                    if (permissions.length == 1 && permissions[0].contains(",")) {
                        permissions = permissions[0].split(",");
                    }else {
                    }
                }else {
                    permissions = null;
                }
                if (recordPrimaryKey != null && !recordPrimaryKey.isEmpty()){//update user

                    //get provinces
                    String provinces = request.getProvinces();
                    String loginCheckSql ="SELECT * FROM user WHERE login = :username  AND id  !=:recordPrimaryKey";
                    HashMap<String, Object> loginPrams = new HashMap<>();
                    loginPrams.put("username", request.getUsername());
                    loginPrams.put("recordPrimaryKey", recordPrimaryKey);
                    List<String> checkUsernameQuery =crudService.fetchWithNativeQuery(loginCheckSql, loginPrams, 0, 1);
                    if (checkUsernameQuery == null && checkUsernameQuery.size() < 0) {
                        apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                        apiResponse.setResponseMessage("Login name already doesn't exists. Please use a different login.");
                    }else {
                        record1 = sharedFunctions.getRecordId("user", String.valueOf(userId));
                        String email = request.getEmail();
                        if (sharedFunctions.isEmailValid(email)) {
                            apiResponse.setResponseCode(ApiResponseCode.FAIL);
                            apiResponse.setResponseMessage("Invalid email address. Please try again.");
                            return apiResponse;
                        }
                        StringBuilder updateUserSql = new StringBuilder("UPDATE user SET ");
                        updateUserSql.append("login = :login, firstname = :firstname, lastname = :lastname, email = :email ");
                        HashMap<String, Object> updatePrams = new HashMap<>();
                        updatePrams.put("login", request.getUsername());
                        updatePrams.put("firstname", request.getFirstName());
                        updatePrams.put("lastname", request.getLastName());
                        updatePrams.put("email", request.getEmail());
                        if (request.getProvince() != null && request.getProvinces() != null && !request.getProvince().isEmpty()) {
                            updateUserSql.append(", province = :province,  provinces= :provinces");
                            updatePrams.put("province", request.getProvince());
                            updatePrams.put("provinces", request.getProvinces());
                        }
                        if (sharedFunctions.hasPermission("organization", 3, userId)) {
                            updateUserSql.append(", org_id = :orgId");
                            updatePrams.put("orgId", Integer.parseInt(request.getOrganisation()));
                        }
                        updateUserSql.append(" WHERE id = :id");
                        updatePrams.put("id", recordPrimaryKey);
                        crudService.executeNativeQuery(updateUserSql.toString(), updatePrams);
                        //unlock account if required
                        if ("0".equals(request.getAccountLocked())) {
                            String unlockSql = "UPDATE user SET status = 1, login_attempts= 0, reset_attempts=0 WHERE id = :userId";
                            Map<String, Object> unlockPrams = new HashMap<>();
                            unlockPrams.put("userId", recordPrimaryKey);
                            crudService.executeNativeQuery(unlockSql, unlockPrams);
                            sharedFunctions.auditAction("UNLOCKED", "Unlocked user id: " + recordPrimaryKey, sourceIp, request.toString(), null, null, Integer.valueOf(organisation));
                            apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                            apiResponse.setResponseMessage("Unlocked Successfully");

                        }
                        //update roles
                        String deleteRole = "DELETE FROM rolemap WHERE user = :user";
                        HashMap<String, Object> deletePrams = new HashMap<>();
                        deletePrams.put("user", recordPrimaryKey);
                        int createdTime1 = (int) (Instant.now().toEpochMilli() / 1000);
                        crudService.executeNativeQuery(deleteRole, deletePrams);
                        for (String permission : permissions) {
                            String sqlInsertRole = "INSERT INTO rolemap (user, role, creationTime) VALUES (:user, :role, :creationTime)";
                            HashMap<String, Object> insertPrams = new HashMap<>();
                            insertPrams.put("user", recordPrimaryKey);
                            insertPrams.put("role", permission);
                            insertPrams.put("creationTime", createdTime1);
                            crudService.executeNativeQuery(sqlInsertRole, insertPrams);
                        }
                        String record = sharedFunctions.getRecordId("user", String.valueOf(userId));
                        String changesMade = sharedFunctions.returnLogString(record1, record);
                        sharedFunctions.auditAction("UPDATED", "Updated user: " + request.getUsername() + "id :" + recordPrimaryKey + "(" + changesMade + ")", sourceIp, request.toString(), null, null, Integer.valueOf(organisation));
                        apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                        apiResponse.setResponseMessage("Updated Successfully");


                    }

                }else {//insert of new user

                    String province = request.getProvince();
                    String checkUserQuery = "SELECT id FROM user WHERE login = :username";
                    HashMap<String, Object> checkPrams = new HashMap<>();
                    checkPrams.put("username", request.getUsername());
                    Object row = sharedFunctions.getItems(List.of("id"), "user", "", "WHERE login = '"+ request.getUsername() +"'", "", false);
                    if (row == null) {
                        // 1. Generate random password
                        SecureRandom random = new SecureRandom();
                        byte[] bytes = new byte[4]; // 4 bytes = 8 hex characters
                        random.nextBytes(bytes);
                        String autogeneratedPassword = sharedFunctions.bytesToHex(bytes);

                        // 2. Hash with SHA-512
                        String hashedPassword = sharedFunctions.hashSha512(autogeneratedPassword);

                        // 3. Get creation timestamp
                        String createdTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        int createdTime1 = (int) (Instant.now().toEpochMilli() / 1000);

                        // 4. Prepare SQL
                        // 4. Prepare SQL
                        HashMap<String, Object> insertParams = new HashMap<>();

                        insertParams.put("login", request.getUsername());
                        insertParams.put("firstname", request.getFirstName());
                        insertParams.put("lastname", request.getLastName());
                        insertParams.put("email", request.getEmail());
                        insertParams.put("shaPassword", hashedPassword);

                        StringBuilder sqlBuilder = new StringBuilder("INSERT INTO user(login, firstname, lastname, email, shaPassword");

                        // Add optional fields if present
                        if (request.getProvince() != null && !request.getProvince().isEmpty()) {
                            sqlBuilder.append(", province, provinces");
                            insertParams.put("province", request.getProvince());
                            insertParams.put("provinces", request.getProvince());
                        }

                        sqlBuilder.append(", creationtime, org_id)");
                        insertParams.put("creationtime", createdTime1);
                        insertParams.put("org_id", request.getOrganisation());

                        // Add placeholders
                        StringBuilder valuesBuilder = new StringBuilder(" VALUES(:login, :firstname, :lastname, :email, :shaPassword");

                        if (insertParams.containsKey("province")) {
                            valuesBuilder.append(", :province, :provinces");
                        }
                        valuesBuilder.append(", :creationtime, :org_id)");

                        String insertUserSql = sqlBuilder.toString() + valuesBuilder.toString();

                        // Execute using native query method that supports named parameters
                        crudService.executeNativeQuery(insertUserSql, insertParams);
                        // 5. Fetch newly inserted user ID
                        String fetchSql = "SELECT id FROM user WHERE creationtime = '" + createdTime1 + "'";
                        List<Object> result = crudService.fetchWithNativeQuery(fetchSql, new HashMap<>(), 0, 1);

                        if (!result.isEmpty()) {
                            int userId1 = (int) result.get(0);


                            // 6. Insert roles
                            if (permissions != null) {
                                for (String roleId : permissions) {
                                    String roleSql = "INSERT INTO rolemap (user, role, creationtime) VALUES (:userId , :roleId, :createdTime)";
                                    HashMap<String, Object> roleParams = new HashMap<>();
                                    roleParams.put("userId", userId1);
                                    int roleId1 = Integer.parseInt(roleId);
                                    roleParams.put("roleId", roleId1);
                                    roleParams.put("createdTime", createdTime1);
                                    crudService.executeNativeQuery(roleSql, roleParams);
                                }
                            }
                            String homeLink = "SELECT VALUE FROM PARAM WHERE PARAMETER = 'SYSTEM_HOME_LINK'";
                            String resetLink = homeLink + "reset-password?reset=" + hashedPassword;

                            // 7. Audit log
                            sharedFunctions.auditAction("CREATED", "Created user id: " + userId, sourceIp, request.toString(), null, null, Integer.valueOf(request.getOrganisation()));
                            boolean sendEmail = sharedFunctions.sendEmail(request.getFirstName(), request.getLastName(), request.getUsername(),autogeneratedPassword, request.getEmail(),resetLink );
                            if (sendEmail) {
                                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                                apiResponse.setResponseMessage("User created successfully. The autogenerated password has been sent to their email.");
                            }else {
                                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                                apiResponse.setResponseMessage("User created successfully, but the email could not be sent.");
                            }
                        }else {
                            apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                            apiResponse.setResponseMessage("Username is already in use.");
                        }

                    }

                }
            }



        }catch (Exception e){
            e.printStackTrace();
            log.error("An error occurred on user management " + e.getMessage());
        }

        return apiResponse;
    }

    private ApiResponse processRoles(RoleRequest roleRequest, User apiUser, HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("An error occurred")
                .build();

        try {
            String recordPrimaryKey = roleRequest.getFormCell() != null ? roleRequest.getFormCell() : null;
            String organisation = roleRequest.getOrganisation();
            String sourceIp = sharedFunctions.getSourceIp(servletRequest);
            String[] permissions = roleRequest.getPermissions();

            // Normalize permissions array
            if (permissions != null && permissions.length == 1 && permissions[0].contains(",")) {
                permissions = permissions[0].split(",");
            }
            UserDetailsImpl userDetails = (UserDetailsImpl) SecurityContextHolder.getContext()
                    .getAuthentication()
                    .getPrincipal();
            int userId = Math.toIntExact(userDetails.getId());

            if (roleRequest.getAction().equalsIgnoreCase("DELETE")) {
                // DELETE Action
                String deleteRoleSql = "UPDATE role SET intrash = 'Yes' WHERE id = :id";
                Map<String, Object> trashParams = Map.of("id", recordPrimaryKey);
                crudService.executeNativeQuery(deleteRoleSql, trashParams);
                sharedFunctions.auditAction("DELETE", "Deleted Role ID: " + recordPrimaryKey, sourceIp, roleRequest.toString(), null, userId, null);

                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                apiResponse.setResponseMessage("Deleted Successfully");
                return apiResponse;
            }

            if (recordPrimaryKey != null && !recordPrimaryKey.isEmpty()) {
                // UPDATE role title
                String updateRoleSql = "UPDATE role SET title = :title WHERE id = :id";
                Map<String, Object> updateParams = new HashMap<>();
                updateParams.put("title", roleRequest.getTitle());
                updateParams.put("id", recordPrimaryKey);
                crudService.executeNativeQuery(updateRoleSql, updateParams);

                // DELETE old permissions
                String deletePermSql = "DELETE FROM permissionmap WHERE role = :role";
                Map<String, Object> deleteParams = Map.of("role", recordPrimaryKey);
                crudService.executeNativeQuery(deletePermSql, deleteParams);

                // INSERT new permissions
                if (permissions != null) {
                    for (String perm : permissions) {
                        String insertPermSql = "INSERT INTO permissionmap (role, permission, creationtime, site) VALUES (:role, :permission, :creationtime, :site)";
                        Map<String, Object> insertPermParams = new HashMap<>();
                        insertPermParams.put("role", recordPrimaryKey);
                        insertPermParams.put("permission", perm);
                        insertPermParams.put("creationtime", System.currentTimeMillis() / 1000); // Unix timestamp
                        insertPermParams.put("site", 0);
                        crudService.executeNativeQuery(insertPermSql, insertPermParams);
                    }
                }

                sharedFunctions.auditAction("UPDATED", "Updated Role ID: " + recordPrimaryKey, sourceIp, roleRequest.toString(), null, userId, Integer.parseInt(organisation));
                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                apiResponse.setResponseMessage("Updated role successfully");

            } else {
                // INSERT new role
                String createdTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                int createdTime1 = (int) (Instant.now().toEpochMilli() / 1000);

                String insertRoleSql = "INSERT INTO role (title, creationtime, org_id) VALUES (:title, :creationtime, :org_id)";
                Map<String, Object> insertRoleParams = new HashMap<>();
                insertRoleParams.put("title", roleRequest.getTitle());
                insertRoleParams.put("creationtime", createdTime1);
                insertRoleParams.put("org_id", organisation);
                crudService.executeNativeQuery(insertRoleSql, insertRoleParams);

                // Fetch inserted role ID
                String fetchRoleIdSql = "SELECT id FROM role WHERE creationtime = :creationtime";
                Map<String, Object> fetchParams = Map.of("creationtime", createdTime1);
                List<Object> insertedRoleIdList = crudService.fetchWithNativeQuery(fetchRoleIdSql, fetchParams, 0, 1);

                if (insertedRoleIdList.isEmpty()) {
                    throw new RuntimeException("Failed to fetch newly created role ID");
                }

                BigInteger newRoleId = new BigInteger(insertedRoleIdList.get(0).toString());

                // INSERT permissions
                if (permissions != null) {
                    for (String perm : permissions) {
                        String insertPermSql = "INSERT INTO permissionmap (role, permission, creationtime, site) VALUES (:role, :permission, :creationtime, :site)";
                        Map<String, Object> insertPermParams = new HashMap<>();
                        insertPermParams.put("role", newRoleId);
                        insertPermParams.put("permission", perm);
                        insertPermParams.put("creationtime", System.currentTimeMillis() / 1000);
                        insertPermParams.put("site", 0);
                        crudService.executeNativeQuery(insertPermSql, insertPermParams);
                    }
                }

                sharedFunctions.auditAction("CREATED", "Created role: " + roleRequest.getTitle(), sourceIp, roleRequest.toString(), null, null, Integer.parseInt(organisation));
                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                apiResponse.setResponseMessage("Created role successfully");
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.error("An error occurred in role management: " + e.getMessage());
        }

        return apiResponse;
    }
    private ApiResponse processb2cAccountSettings(B2CAccountSettingsRequest b2CAccountSettingsRequest, User apiUser, HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Error occurred")
                .build();

        try {
            String sourceIp = sharedFunctions.getSourceIp(servletRequest);
            String cell = b2CAccountSettingsRequest.getFormCell();
            String serviceProviderId = b2CAccountSettingsRequest.getSpid();
            String serviceId = b2CAccountSettingsRequest.getServiceId();
            String password = b2CAccountSettingsRequest.getPassword();
            String initiatorPassword = b2CAccountSettingsRequest.getInitiatorPwd();
            String initiatorUsername = b2CAccountSettingsRequest.getInitiatorUsername();
            String scode = b2CAccountSettingsRequest.getShortcode();
            String commandId = b2CAccountSettingsRequest.getCommandId();
            UserDetailsImpl userDetails = (UserDetailsImpl) SecurityContextHolder.getContext()
                    .getAuthentication()
                    .getPrincipal();
            int userId = Math.toIntExact(userDetails.getId());
            String orderTypeName = "";


            // Normalize permissions
            String[] permissions = b2CAccountSettingsRequest.getPermissions();
            if (permissions != null && permissions.length == 1 && permissions[0].contains(",")) {
                permissions = permissions[0].split(",");
            }

            // DELETE logic
            if (FormAction.DELETE.equals(b2CAccountSettingsRequest.getAction())) {
                String deleteSql = "UPDATE api_params SET INTRASH = 'YES' WHERE ID = :id";
                crudService.executeHibernateQuery(deleteSql, Map.of("id", new BigInteger(cell)));

                String username = (String) crudService.fetchWithNativeQuery("SELECT INITIATOR_USERNAME FROM api_params WHERE ID = :id", Map.of("id", new BigInteger(cell)), 0, 1)
                        .stream().findFirst().orElse("UNKNOWN");

                sharedFunctions.auditAction("B2C Account Setting Deletion", "Deleted B2C Account Setting: " + username, sourceIp, b2CAccountSettingsRequest.toString(),null,userId,null);
                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                apiResponse.setResponseMessage("Deleted successfully");
                return apiResponse;
            }

            // Check service provider ID
            if (serviceProviderId == null || !serviceProviderId.matches("\\d+")) {
                log.warn("No valid service provider ID provided");
            }

            // UPDATE logic
            if (cell != null && !cell.isBlank()) {
                Map<String, Object> params = Map.of("id", cell);
                String oldRecord = sharedFunctions.getRecordById("api_params", "id = :id", params);

                StringBuilder updateSql = new StringBuilder("UPDATE api_params SET ");
                updateSql.append("SPID = :spid, SERVICEID = :serviceid, INITIATOR_USERNAME = :initiatorUsername, SCODE = :scode, COMMAND_ID = :commandId");

                Map<String, Object> updateParams = new HashMap<>();
                updateParams.put("spid", serviceProviderId);
                updateParams.put("serviceid", serviceId);
                updateParams.put("initiatorUsername", initiatorUsername);
                updateParams.put("scode", scode);
                updateParams.put("commandId", commandId);
                updateParams.put("id", new BigInteger(cell));

                if (!"changeme".equalsIgnoreCase(password)) {
                    updateSql.append(", PASSWORD = :password");
                    updateParams.put("password", password);
                }

                if (!"changeme".equalsIgnoreCase(initiatorPassword)) {
                    updateSql.append(", INITIATOR_PWD = :initiatorPassword, change_password = 1");
                    updateParams.put("initiatorPassword", initiatorPassword);
                }

                updateSql.append(" WHERE ID = :id");
                crudService.executeNativeQuery(updateSql.toString(), updateParams);
                Map<String, Object> params1 = Map.of("id", new BigInteger(cell));
                String newRecord = sharedFunctions.getRecordById("api_params", "id = :id", params1);


               // String newRecord = sharedFunctions.getRecordById("api_params", "id = "+ Map.of("id", new BigInteger(cell)), new HashMap<>());
                String logString = sharedFunctions.returnLogString(oldRecord, newRecord);

                sharedFunctions.auditAction("B2C Account Setting Update", "Updated B2C Account " + scode + " (" + logString + ")", sourceIp, b2CAccountSettingsRequest.toString(), null, userId, null);

                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                apiResponse.setResponseMessage("Updated B2C Account Setting successfully");

            } else {
                // INSERT logic
                List<Object> exists = crudService.fetchWithNativeQuery("SELECT SPID FROM api_params WHERE SCODE = :scode", Map.of("scode", scode), 0, 1);
                if (exists.isEmpty()) {
                    String insertSql = "INSERT INTO api_params (SPID, SERVICEID, PASSWORD, INITIATOR_PWD, INITIATOR_USERNAME, SCODE, COMMAND_ID) " +
                            "VALUES (:spid, :serviceid, :password, :initiatorPassword, :initiatorUsername, :scode, :commandId)";

                    Map<String, Object> insertParams = new HashMap<>();
                    insertParams.put("spid", serviceProviderId);
                    insertParams.put("serviceid", serviceId);
                    insertParams.put("password", password);
                    insertParams.put("initiatorPassword", initiatorPassword);
                    insertParams.put("initiatorUsername", initiatorUsername);
                    insertParams.put("shortcode", scode);
                    insertParams.put("commandId", commandId);

                    crudService.executeNativeQuery(insertSql, insertParams);

                    sharedFunctions.auditAction("B2C Account Setting Creation", "Created B2C Account " + scode, sourceIp, b2CAccountSettingsRequest.toString(),null, userId, null);

                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Inserted B2C Account Setting successfully");
                } else {
                    sharedFunctions.auditAction("B2C Account Setting Creation", "Failed to create B2C Account " + scode, sourceIp, b2CAccountSettingsRequest.toString(), null, userId, null );
                    apiResponse.setResponseMessage("B2C Account already exists");
                }
            }

        } catch (Exception e) {
            log.error("An error occurred in processb2cAccountSettings: " + e.getMessage(), e);
        }

        return apiResponse;
    }

    private ApiResponse processApproval(JSONObject jsonData, User apiUser, HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseMessage("An error occurred during approval")
                .responseCode(ApiResponseCode.FAIL)
                .build();
        try {
            String primaryKeyRaw = jsonData.has("primarykey") && !jsonData.isNull("primarykey")
                    ? jsonData.optString("primarykey", "")
                    : jsonData.optString("cell", "");
            String[] batchNos = primaryKeyRaw.split(",");
            StringBuilder batchNoTest = new StringBuilder();
            StringBuilder batchNoIds = new StringBuilder();
            String currentId;
            String orderTypeName;
            String delimiter = "_";
            UserDetailsImpl userDetails = (UserDetailsImpl) SecurityContextHolder.getContext()
                    .getAuthentication()
                    .getPrincipal();
            int userId = Math.toIntExact(userDetails.getId());
            String sourceIp = sharedFunctions.getSourceIp(servletRequest);
            Integer orgId = Math.toIntExact(userDetails.getOrganizationId());
            // Construct the IN clause string (e.g., 'B001','B002')
            for (int i = 0; i < batchNos.length; i++) {
                batchNoTest.append("'").append(batchNos[i].trim()).append("'");
                if (i < batchNos.length - 1) {
                    batchNoTest.append(",");
                }
            }
            HttpSession session = servletRequest.getSession();
            String approvalLevel = (String) session.getAttribute("approval");
            if (approvalLevel == null ){
                approvalLevel = jsonData.optString("batch_approval_level");
            }
            String sql =  "SELECT id FROM apiorder WHERE batchno IN (" + batchNoTest + ") " +
                    "AND approval_level = :approvalLevel " +
                    "AND approval_status = 'Pending' " +
                    "AND request_src = 'api'";
            Map<String, Object> selectParams = new HashMap<>();
            selectParams.put("approvalLevel", approvalLevel);

            List<Integer> result = crudService.fetchWithNativeQuery(sql, selectParams, 0, 1);

            //concatenate IDs into a comma seperated
            for (Integer row : result) {
                Object id = row;
                if (id != null) {
                    batchNoIds.append(id.toString()).append(",");
                }
            }
            //remove trailing comma if needed
            String batchNoIdsStr = batchNoIds.length() >0 ? batchNoIds.substring(0, batchNoIds.length() - 1) : "";
            // Optionally, update the jsonData map or use batchNoIdsStr as needed
            //jsonData.put("primarykey", batchNoIdsStr);
            String provinces = jsonData.optString("provinces") != null ? jsonData.optString("provinces") : "";

            if (provinces != null && !provinces.isEmpty()){
                String batchNo = "TIBU" + System.currentTimeMillis();
                String region = "";
                String ordertype = "";
                String budget = "";
                String treatmentModelFilter = "";

                // Optional filters
                if (jsonData.optString("provinces") != null && !jsonData.optString("provinces").toString().isEmpty()) {
                    region = " AND province = '" + jsonData.optString("provinces") + "' ";
                }
                if (jsonData.optString("ordertypes") != null && !jsonData.optString("ordertypes").toString().isEmpty()) {
                    ordertype = " AND ordertype = " + jsonData.optString("ordertypes");
                }
                if (jsonData.optString("budgets") != null && !jsonData.optString("budgets").toString().isEmpty()) {
                    budget = " AND budget = " + jsonData.optString("budgets");
                }

                String sqlQuery;
                Object treatmentModel = jsonData.optString("treatment_model");

                if (treatmentModel != null && (treatmentModel.equals("3") || treatmentModel.equals("5"))) {
                    treatmentModelFilter = " AND treatment_model = '" + treatmentModel + "'";

                    sqlQuery = "UPDATE apiorder SET batchno = (CONCAT(" +
                            "(SELECT REPLACE(TITLE,' ','') FROM county WHERE ID=apiorder.county), " +
                            "CASE(treatment_model) " +
                            "   WHEN 4 THEN 'SUP' " +
                            "   WHEN 1 THEN 'MDR' " +
                            "   WHEN 2 THEN 'MDR' " +
                            "   WHEN 3 THEN 'WEB' " +
                            "   WHEN 5 THEN 'MTNG' " +
                            "   ELSE 'MDR' END, " +
                            "IF((SELECT value FROM PARAM WHERE parameter='Batch Expiry Day of Month') > DAY(requesttime) AND MONTH(requesttime)=1, " +
                            "   YEAR(requesttime)-1, YEAR(requesttime)), " +
                            "IF((SELECT value FROM PARAM WHERE parameter='Batch Expiry Day of Month') > DAY(requesttime), " +
                            "   LPAD(MONTH(requesttime)-1, 2, '0'), " +
                            "   LPAD(MONTH(requesttime), 2, '0')))) " +
                            "WHERE (batchno = '0' OR batchno IS NULL) " +
                            "AND approval_status = 'Pending' AND approval_level = '0' AND intrash = 'No' " +
                            region + ordertype + budget + treatmentModelFilter;

                    session.setAttribute("testingpoo", sqlQuery);
                } else {
                    sqlQuery = "UPDATE apiorder SET batchno = '" + batchNo + "' " +
                            "WHERE approval_status = 'Pending' AND approval_level = '0' " +
                            "AND batchno = '0' AND intrash = 'No' " +
                            region + ordertype + budget;
                    session.setAttribute("testing2", "no 3 or 5 ");
                }

                // Execute update query for batch update
                crudService.executeNativeQuery(sqlQuery, new HashMap<>());

                // Insert into batchreference
                String insertSql = "INSERT INTO batchreference(batch_no, batch_count, region, budget, ordertype, notes, createdby, org_id) " +
                        "VALUES (:batchNo, :payments, :region, :budget, :ordertype, :notes, :createdBy, :orgId)";

                Map<String, Object> insertParams = new HashMap<>();
                insertParams.put("batchNo", batchNo);
                insertParams.put("payments", jsonData.optInt("payments"));
                insertParams.put("region", 0);//TODO provinces
                insertParams.put("budget", 129);
                insertParams.put("ordertype", 12);
                insertParams.put("notes", jsonData.optString("notes"));
                insertParams.put("createdBy", userId);
                insertParams.put("orgId", 1);

                crudService.executeNativeQuery(insertSql, insertParams);

                // Audit and session message
                String ipAddress = servletRequest.getRemoteAddr();
                String auditNote = "Batch No. " + batchNo + " has been created for " + jsonData.optString("payments") + " payments.";
                sharedFunctions.auditAction(
                        "PAYMENTBATCH",
                        "Batch No. " + batchNo + " has been created for " + jsonData.optString("payments") + " payments.",
                        ipAddress,
                        jsonData.toString(), // POST data
                        null, // GET data
                        userId, // or whatever user ID session key you use
                        orgId // organization ID
                );

                apiResponse.setResponseMessage("notes"+ auditNote);

            }else {
                // missed some logic here
                String currentLevel = jsonData.optString("batch_approval_level");
                List<String> IDs = new ArrayList<>();
                int i = 0;
                List<Integer> result1;

                String primaryKeyStr = jsonData.optString("primarykey", "").trim();
                String cell = jsonData.optString("cell", "").trim();

                if (primaryKeyStr.isEmpty()) {
                    primaryKeyStr = cell;
                }

                String[] keys = primaryKeyStr.split(",");

                // Bulk Approval
                if (jsonData.optString("primarykey") !=null) {
                    IDs.addAll(Arrays.asList(keys));
                    if (IDs.get(0).equalsIgnoreCase("all")) {
                        i = 1;
                    }

                    if ("YES".equalsIgnoreCase(jsonData.optString("batchapprove").toString())) {
                        StringBuilder batchnosBuilder = new StringBuilder();
                        for (int x = i; x < IDs.size(); x++) {
                            batchnosBuilder.append("'").append(IDs.get(x)).append("',");
                        }

                        String batchnos = batchnosBuilder.toString().replaceAll(",$", "");
                        String batchApprovalLevel = jsonData.optString("batch_approval_level").toString();

                        String sql2 = "SELECT id FROM apiorder WHERE batchno IN (" + batchnos + ") " +
                                "AND approval_status='Pending' AND approval_level='" + batchApprovalLevel + "' ORDER BY id DESC";
                        result1 = crudService.fetchWithNativeQuery(sql2, new HashMap<>(), 0, 1);

                        // Reset IDs list with only selected results
                        IDs.clear();
                        for (Integer row : result1) {
                            IDs.add(row.toString());
                        }
                        i = 0;
                    }
                } else {
                    // Single approval
                    IDs.add(jsonData.get("cell").toString());
                }

                // Permission check
                boolean hasPermission = false;
                switch (Integer.parseInt(currentLevel)) {
                    case 1 -> hasPermission = sharedFunctions.hasPermission("approvalone", 3, userId);
                    case 2 -> hasPermission = sharedFunctions.hasPermission("approvaltwo", 3, userId);
                    case 3 -> hasPermission = sharedFunctions.hasPermission("approvalthree", 3, userId);
                    case 4 -> hasPermission = sharedFunctions.hasPermission("approvalfour", 3, userId);
                    case 5 -> hasPermission = sharedFunctions.hasPermission("approvalfive", 3, userId);
                    case 6 -> hasPermission = sharedFunctions.hasPermission("approvalsix", 3, userId);
                    case 7 -> hasPermission = sharedFunctions.hasPermission("approvalseven", 3, userId);
                    case 8 -> hasPermission = sharedFunctions.hasPermission("approvaleight", 3, userId);
                    case 9 -> hasPermission = sharedFunctions.hasPermission("approvalnine", 3, userId);
                    case 10 -> hasPermission = sharedFunctions.hasPermission("approvalten", 3, userId);
                    default -> hasPermission = false;
                }

                // TODO -> to be replaced
                String processViaBackend = "YES";

//                // bulk approval logic missing here - TODO
                if (jsonData.has("primarykey") && !jsonData.optString("primarykey").isEmpty() &&
                        "YES".equalsIgnoreCase(processViaBackend) &&
                        !jsonData.has("approvalOneAmount") && !jsonData.has("approvalOneAmount1")) {

                    // Get permission from session or service
                    Boolean permission = true;
                    if (permission == null) {
                        permission = false; // Default to false if not set
                    }

                    if (permission) {
                        try {
                            // Get IDs list (assuming it's stored in session or passed as parameter)
                            String idString = String.join(",", IDs);

                            // Insert into approval_processing table
                            Map<String, Object> approvalProcessingParams = new HashMap<>();
                            approvalProcessingParams.put("approver_id", userId);
                            approvalProcessingParams.put("approval_level", currentLevel);
                            approvalProcessingParams.put("ids_string", idString);
                            approvalProcessingParams.put("status", "0");
                            approvalProcessingParams.put("postdata", jsonData.toString());
                            approvalProcessingParams.put("remoteAddress", servletRequest.getRemoteAddr());
                            approvalProcessingParams.put("org_id", userDetails.getOrganizationId());

                            String approvalProcessingSql = "INSERT INTO approval_processing(approver_id, approval_level, ids_string, status, postdata, TIME_INITIATED, remoteAddress, org_id) VALUES (:approver_id, :approval_level, :ids_string, :status, :postdata, CURRENT_TIMESTAMP, :remoteAddress, :org_id)";

                            crudService.executeNativeQuery(approvalProcessingSql, approvalProcessingParams);

                            // Update apiorder status to 'Processing'
                            String idStringFinal = idString.replaceFirst("^all,", "");

                            Map<String, Object> updateParams = new HashMap<>();
                            String updateSql = "UPDATE apiorder SET approval_status = 'Processing' WHERE id IN (" + idStringFinal + ")";

                            crudService.executeNativeQuery(updateSql, updateParams);

                            // Audit action
                            sharedFunctions.auditAction("APPROVED", "Approved payment for processing: ", servletRequest.getRemoteAddr(), jsonData.toString(),null,userId,orgId);
                            apiResponse.setResponseMessage("Your approval has been received.");
                            apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                            return apiResponse;
                        } catch(Exception ex) {
                            apiResponse.setResponseMessage("An error occurred approving batch.");
                            apiResponse.setResponseCode(ApiResponseCode.FAIL);
                            return apiResponse;
                        }
                    } else {
                        apiResponse.setResponseMessage("You dont have permission to approve.");
                        apiResponse.setResponseCode(ApiResponseCode.FAIL);
                        return apiResponse;
                    }
                }

                // Loop through IDs for approval processing
                while (i < IDs.size()) {
                     currentId = IDs.get(i);
                    i++;

                    String sql1 = "SELECT ao.approval_level, ao.ordertype, ao.credit, ot.title, ao.recipient2credit, ao.budget, ao.org_id " +
                            "FROM apiorder ao LEFT JOIN ordertype ot ON ao.orderType = ot.id WHERE ao.id = :id";

                    HashMap<String, Object> params = new HashMap<>();
                    params.put("id", currentId);
                    List<Object[]> rows = crudService.fetchWithNativeQuery(sql1, params, 0, 1);
                    if (rows.isEmpty()) continue;

                    Object[] row = rows.get(0);

//                    String currentLevel = row[0].toString();
                    String orderTypeId = row[1].toString();
                    String originalAmount = row[2].toString();
                    String originalAmount2 = row[4].toString();
                    orderTypeName = row[3].toString();
                    String budgetId = row[5].toString();
                     orgId = Integer.parseInt(row[6].toString());

                    String approvalStatus = "";
                    String notes = "";

                    List<List<Object>> items = new ArrayList<>();
                    List<List<String>> items1 = new ArrayList<>();

                    String approveStatus = jsonData.optString("status");

                    if (approveStatus.isEmpty()) {
                        // Individual approval
                        if (orderTypeName.equalsIgnoreCase("MDR OTHER PAYMENTS") || orderTypeName.equalsIgnoreCase("SUPERVISION")) {
                            String supervisionSql = "SELECT id FROM supervision_details WHERE apiorder_id = :apiorder_id";
                            HashMap<String, Object> paramSupervision = new HashMap<>();
                            paramSupervision.put("apiorder_id", currentId);
                            List<Integer>  supervisionDetails = crudService.fetchWithNativeQuery(supervisionSql, paramSupervision, 0, 50);
                            approvalStatus = "Rejected";

                            for (Integer supervisionRow : supervisionDetails) {
                                String sid = supervisionRow.toString();
                                String statusKey = "approvalOne_" + sid;
                                if (("Approved".equalsIgnoreCase(jsonData.getString(statusKey))) || ("Approve".equalsIgnoreCase(jsonData.getString(statusKey)))) {
                                    approvalStatus = "Approved";
                                }
                                items.add(Arrays.asList(
                                        sid,
                                        jsonData.optString("approvalOneAmount_" + sid),
                                        jsonData.get(statusKey),
                                        jsonData.getString("approvalOneNotes_" + sid)
                                ));
                            }
                        } else {
                            approvalStatus = jsonData.optString("approvalOne").toString();
                        }
                    } else {
                        // Bulk approval
                        if (orderTypeName.equalsIgnoreCase("MDR OTHER PAYMENTS") || orderTypeName.equalsIgnoreCase("SUPERVISION")) {
                            String supervisionSql = "SELECT id, credit, approval_status FROM supervision_details WHERE apiorder_id = :apiorder_id";
                            HashMap<String, Object> paramSupervision = new HashMap<>();
                            paramSupervision.put("apiorder_id", currentId);
                            List<Object[]> supervisionDetails = crudService.fetchWithNativeQuery(supervisionSql, paramSupervision, 0,1);

                            for (Object[] supervisionRow : supervisionDetails) {
                                String status = "Rejected".equalsIgnoreCase(String.valueOf(supervisionRow[2]))
                                        ? "Rejected"
                                        : String.valueOf(jsonData.optString("status"));

                                items1.add(Arrays.asList(
                                        String.valueOf(supervisionRow[0]),
                                        String.valueOf(supervisionRow[1]),
                                        status,
                                        "" // optional: add notes or any other value needed
                                ));
                            }
                            if (!items1.isEmpty()) {
                                log.info("line items " + items1.getFirst());
                            }
                        }
                        List<String> approvalOneValues = new ArrayList<>();

                        Iterator<String> keys1 = jsonData.keys();
                        while (keys1.hasNext()) {
                            String key1 = keys1.next();
                            if (key1.startsWith("approvalOne_")) {
                                String value = jsonData.optString(key1);
                                approvalOneValues.add(value);
                            }else if (key1.startsWith("approvalOne")){
                                String value2 = jsonData.optString("approvalOne");
                                approvalOneValues.add(value2);
                            }else {
                                String value3 = jsonData.optString("status");
                                approvalOneValues.add(value3);
                            }
                        }
                        log.info("line approvalOneValues " + approvalOneValues.get(0));


                        approvalStatus = approvalOneValues.get(0);
                    }

                    //TODO -> Recheck this
                    notes = jsonData.equals("approvalOneNotes")
                            ? jsonData.optString("approvalOneNotes")
                            : jsonData.optString("Notes");

                    String approvalAmount = "";
                    String approvalAmount2 = "";

                    if (!jsonData.optString("approvalOneAmount").isEmpty()) {
                        approvalAmount = jsonData.optString("approvalOneAmount");
                    } else if (!jsonData.optString("approvalOneAmount1").isEmpty()) {
                        approvalAmount = jsonData.optString("approvalOneAmount1");
                        approvalAmount2 = jsonData.optString("approvalTwoAmount");
                    } else if (!jsonData.optString("approvalOneAmount1").isEmpty()) {
                        approvalAmount = jsonData.optString("approvalOneAmount1");
                        approvalAmount2 = jsonData.optString("approvalTwoAmount");
                    } else {
                        approvalAmount = originalAmount;
                        approvalAmount2 = originalAmount2;
                    }

                    if (jsonData.optString("approvalTwo") != null && jsonData.optString("approvalTwo").equalsIgnoreCase("REJECTED")) {
                        approvalAmount2 = "0";
                    }

                    if (jsonData.optString("approvalTwo") != null && jsonData.optString("approvalTwo").equalsIgnoreCase("Approved") && approvalStatus.equalsIgnoreCase("REJECTED")) {
                        approvalStatus = "Approved";
                        approvalAmount = "0";
                    }

                    // Fetch next level
                    String approvalOrderQuery = "SELECT approvalOrder FROM org_ordertype WHERE ordertypeId=:ordertypeId AND org_id=:orgId";
                    HashMap<String, Object> approvalOrderParams = new HashMap<>();
                    approvalOrderParams.put("ordertypeId", orderTypeId);
                    approvalOrderParams.put("orgId", orgId);

                    List<String> approvalOrderRows = crudService.fetchWithNativeQuery(approvalOrderQuery, approvalOrderParams, 0,1);
                    String approvalOrder = approvalOrderRows.isEmpty() ? "" : approvalOrderRows.get(0).toString();

                    int nextLevel = Integer.parseInt(currentLevel);
                    String apiAppStatus = "Pending";

                    List<String> levels = Arrays.asList(approvalOrder.split(","));
                    int curIndex = levels.indexOf(currentLevel);
                    if (curIndex < levels.size() - 1) {
                        nextLevel = Integer.parseInt(levels.get(curIndex + 1));
                    } else {
                        apiAppStatus = approvalStatus;
                    }

                    if ("REJECTED".equalsIgnoreCase(approvalStatus)) {
                        nextLevel = Integer.parseInt(currentLevel);
                        apiAppStatus = "Rejected";
                    }
                    //int userId = Integer.parseInt(apiUser.getId().toString());


                    // You can now continue to update the apiorder and log audits...
                    if (hasPermission) {
                        String updateQuery = "";
                        Map<String, Object> updateParams = new HashMap<>();
                        if (approvalAmount2 != null && !approvalAmount2.isEmpty()) {
                            updateQuery = "UPDATE apiorder SET credit = :credit, recipient2credit = :recipient2credit, approval_level = :nextLevel, approval_status = :approvalStatus, status_sent = 'No' WHERE id = :id";
                            updateParams.put("recipient2credit", approvalAmount2);
                            //update apiorder set credit = '$approvalAmount',recipient2credit= '$approvalAmount2',approval_level = '$nextLevel',approval_status='$apiAppStatus',status_sent = 'No' where id='$IDs[$i]'
                        } else {
                            updateQuery = "UPDATE apiorder SET credit = :credit, approval_level = :nextLevel, approval_status = :approvalStatus, status_sent = 'No' WHERE id = :id";
                        }
                        updateParams.put("credit", approvalAmount);
                        updateParams.put("nextLevel", nextLevel);
                        updateParams.put("approvalStatus", apiAppStatus);
                        updateParams.put("id", currentId);
                        crudService.executeNativeQuery(updateQuery, updateParams);

                        Map<String, Object> levelRow = crudService.getSingleResult("SELECT approval_level_name FROM approval_levels WHERE approval_level = :currentLevel", Map.of("currentLevel", currentLevel));
                        String levelName = levelRow != null ? (String) levelRow.get("approval_level_name") : "";

                        // Step 1: Fetch existing T_VALUE
                        Map<String, Object> trxValRow = crudService.getSingleResult(
                                "SELECT T_VALUE FROM TRX_VALUES WHERE ORDER_ID = :id",
                                Map.of("id", currentId)
                        );

                        if (trxValRow != null && trxValRow.get("T_VALUE") != null) {

                            // Step 2: Fetch transaction details from apiorder
                            Map<String, Object> row4 = crudService.getSingleResult(
                                    "SELECT id, msisdn, credit, driver_amount, driver_phone, recipient2credit, recipient2msisdn " +
                                            "FROM apiorder WHERE id = :id",
                                    Map.of("id", currentId)
                            );

                            if (row4 != null) {

                                // Step 3: Convert to string-based map for updateChainTrx
                                // ✅ Manually create chainData map (like PHP version)
                                HashMap<String, String> chainData = new HashMap<>();
                                chainData.put("id", row4.get("id").toString());
                                chainData.put("msisdn", row4.get("msisdn") != null ? row4.get("msisdn").toString() : "");
                                chainData.put("credit", row4.get("credit") != null ? row4.get("credit").toString() : "0");
                                chainData.put("driver_amount", row4.get("driver_amount") != null ? row4.get("driver_amount").toString() : "0");
                                chainData.put("driver_phone", row4.get("driver_phone") != null ? row4.get("driver_phone").toString() : "");
                                chainData.put("recipient2credit", row4.get("recipient2credit") != null ? row4.get("recipient2credit").toString() : "");
                                chainData.put("recipient2msisdn", row4.get("recipient2msisdn") != null ? row4.get("recipient2msisdn").toString() : "");

                                // Update the chain
                                String trxChain = sharedFunctions.updateChainTrx(
                                        trxValRow.get("T_VALUE").toString(),
                                        chainData,
                                        row4.get("id").toString(),
                                        null
                                );

                                // Save updated chain
                                crudService.executeNativeQuery(
                                        "UPDATE TRX_VALUES SET T_VALUE = :trxChain WHERE ORDER_ID = :id",
                                        Map.of("trxChain", trxChain, "id", currentId)
                                );


                                // Step 6: Audit the update
                                sharedFunctions.auditAction(
                                        "UPDATED",
                                        "Updated chain for trx id",
                                        row4.get("id").toString(),
                                        servletRequest.getRemoteAddr(),
                                        null,
                                        userId,
                                        orgId
                                );
                            }
                        }

                            Map<String, Object> phoneRow = crudService.getSingleResult("SELECT CONCAT('(', msisdn, '', IFNULL(CONCAT(' DOT:', recipient2msisdn), '')) as phone FROM apiorder WHERE id = :id", Map.of("id", currentId));
                            String requestPhoneNumber = phoneRow != null ? (String) phoneRow.get("phone") : "";
                            //String ApprovalStatus = jsonData.optString("status");
//                            if (ApprovalStatus != null && !ApprovalStatus.isEmpty()) {
//                                 ApprovalStatus =  approvalStatus;
//                            }
                            Map<String, Object> approvalInsertParams = new HashMap<>();
                            approvalInsertParams.put("apiorder_id", currentId);
                            approvalInsertParams.put("approver_id", servletRequest.getSession().getAttribute("user"));
                            approvalInsertParams.put("approval_level", currentLevel);
                            approvalInsertParams.put("approval_level_name", levelName);
                            approvalInsertParams.put("approval_notes", requestPhoneNumber + notes);
                            approvalInsertParams.put("approved_amount", Double.parseDouble(approvalAmount));
                            approvalInsertParams.put("original_amount", Double.parseDouble(originalAmount));
                            approvalInsertParams.put("approval_status", approvalStatus);
                            approvalInsertParams.put("approved_recipient2amount", !approvalAmount2.isEmpty() ? Double.parseDouble(approvalAmount2) : 0.0);
                            approvalInsertParams.put("original_recipient2amount", !originalAmount2.isEmpty() ? Double.parseDouble(originalAmount2) : originalAmount2);

                            crudService.executeNativeQuery("""
                                INSERT INTO payment_approval(apiorder_id, approver_id, approval_level, approval_level_name, approval_notes, approved_amount, original_amount, approval_time, approval_status, approved_recipient2amount, original_recipient2amount)
                                VALUES (:apiorder_id, :approver_id, :approval_level, :approval_level_name, :approval_notes, :approved_amount, :original_amount, CURRENT_TIMESTAMP, :approval_status, :approved_recipient2amount, :original_recipient2amount)
                            """, approvalInsertParams);

                            Map<String, Object> firstApproval = crudService.getSingleResult("""
                                SELECT MIN(p.approval_time) AS batchDate, 
                                       (SELECT COUNT(id) FROM payment_approval p2 WHERE p2.apiorder_id = :id) AS approvalCount 
                                FROM payment_approval p WHERE p.apiorder_id = :id
                            """, Map.of("id", currentId));

                            String batchDate = String.valueOf(firstApproval.get("batchDate"));
                            int approvalCount = Integer.parseInt(String.valueOf(firstApproval.get("approvalCount")));

                            if (batchDate != null && approvalCount == 1) {
                                Map<String, Object> batchRow = crudService.getSingleResult("SELECT batchno FROM apiorder WHERE id = :id", Map.of("id", currentId));
                                String batchNo = batchRow != null ? String.valueOf(batchRow.get("batchno")) : "";

                                if (batchNo == null || batchNo.length() < 2) {
                                    String updateBatchNoQuery = """
                                        UPDATE apiorder SET batchno = (
                                            CONCAT(
                                                (SELECT REPLACE(TITLE, ' ', '') FROM county WHERE ID = apiorder.county),
                                                CASE treatment_model 
                                                    WHEN 4 THEN 'SUP' 
                                                    WHEN 1 THEN 'MDR' 
                                                    WHEN 2 THEN 'MDR' 
                                                    WHEN 3 THEN 'WEB' 
                                                    ELSE 'MDR' 
                                                END,
                                                CASE 
                                                    WHEN (SELECT value FROM PARAM WHERE parameter = 'Batch Expiry Day of Month') < DAY(:batchDate) AND MONTH(:batchDate) = 1 
                                                    THEN YEAR(:batchDate) + 1 
                                                    ELSE YEAR(:batchDate) 
                                                END,
                                                CASE 
                                                    WHEN (SELECT value FROM PARAM WHERE parameter = 'Batch Expiry Day of Month') < DAY(:batchDate)
                                                    THEN LPAD(MONTH(:batchDate) + 1, 2, '0') 
                                                    ELSE LPAD(MONTH(:batchDate), 2, '0') 
                                                END
                                            )
                                        ) WHERE (batchno = 0 OR batchno IS NULL) AND id = :id
                                    """;
                                    crudService.executeNativeQuery(updateBatchNoQuery, Map.of("batchDate", batchDate, "id", currentId));
                                }
                            }

                            servletRequest.getSession().setAttribute("BATCHSESSION", "Debug info here if needed");

                            for (List<String> item : items1) {
                                Map<String, Object> supervisionUpdate = Map.of(
                                        "credit", item.get(1),
                                        "approval_status", "Rejected".equalsIgnoreCase(item.get(2)) ? "Rejected" : apiAppStatus,
                                        "id", item.get(0)
                                );
                                crudService.executeNativeQuery("UPDATE supervision_details SET credit = :credit, approval_status = :approval_status WHERE id = :id", supervisionUpdate);

                                Map<String, Object> approvalMap = Map.of(
                                        "supdetails_id", item.get(0),
                                        "apiorder_id", currentId,
                                        "approver_id", userId,
                                        "approval_level", currentLevel,
                                        "amount", item.get(1),
                                        "status", apiAppStatus,
                                        "notes", jsonData.optString("approvalOneNotes")
                                );
                                crudService.executeNativeQuery("""
                                    INSERT INTO supervision_details_approval(supdetails_id, apiorder_id, approver_id, approval_level, amount, status, notes)
                                    VALUES (:supdetails_id, :apiorder_id, :approver_id, :approval_level, :amount, :status, :notes)
                                """, approvalMap);
                            }

                            List<Map<String, Object>> driverCredits = crudService.fetchWithNativeQuery("SELECT credit FROM supervision_details WHERE apiorder_id = :id AND (title='Driver - Lunch' OR title='Driver - Per Diem') ORDER BY id DESC", Map.of("id", currentId),0,100);
                            List<Object> driverIds = crudService.fetchWithNativeQuery(
                                    "SELECT id FROM drivers_details WHERE apiorder_id = :id ORDER BY id DESC",
                                    Map.of("id", currentId),
                                    0,
                                    100
                            );
                            int driverAmt = 0;
                            int amountDriver = 0;
                            for (int j = 0; j < driverCredits.size(); j++) {
                                BigDecimal credit = (BigDecimal) driverCredits.get(j);
                                 amountDriver = credit.intValue();  // or credit.doubleValue() if needed

                            }
                            for (Object obj : driverIds) {
                                int driverId = Integer.parseInt(obj.toString()); // or just (Integer) obj if you're sure
                                crudService.executeNativeQuery(
                                        "UPDATE drivers_details SET amount = :amount WHERE id = :id",
                                        Map.of("amount", amountDriver, "id", driverId)
                                );
                                driverAmt += amountDriver;
                            }

                            if (!driverCredits.isEmpty()) {
                                crudService.executeNativeQuery("UPDATE apiorder SET driver_amount = :amount WHERE id = :id", Map.of("amount", driverAmt, "id", currentId));
                            }

                            String systemUrl = crudService.getSingleResult("SELECT VALUE FROM PARAM WHERE PARAMETER = 'SYSTEM_HOME_LINK'", Map.of()).get("VALUE").toString();
                           PaymentSyncResponse paymentSyncResponse = paymentSyncPmsService.sendPaymentSyncToPms(Long.valueOf(currentId),orderTypeName);
                           if (paymentSyncResponse != null){
                               String message = paymentSyncResponse.getMessage();
                               log.info("message for payment sync {}" , message);
                           }
//                            switch (orderTypeName.toUpperCase()) {
//                                case "MDR OTHER PAYMENTS":
//                                case "SUPERVISION":
//                                    //httpClient.sendGet(systemUrl + "/api/v1/sendPaymentStatus.php?order_id=" + currentId + "&payment_mode=supervision");
//                                    break;
//                                case "PATIENT SUPPORT":
//                                    //httpClient.sendGet(systemUrl + "/api/v1/sendPaymentStatus.php?order_id=" + currentId + "&payment_mode=mdr");
//                                    break;
//                                default:
//                                    break;
//                            }

                            sharedFunctions.auditAction("APPROVED", approvalStatus + " payment id: " + curIndex + " of Amount " + approvalAmount + " " + systemUrl, servletRequest.getRemoteAddr(), jsonData.toString(), null, userId, orgId);
                            apiResponse.setResponseMessage(notes);
                            apiResponse.setResponseCode(ApiResponseCode.SUCCESS);


                        apiResponse.setResponseMessage(notes);
                        apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    }

                }

            }



        }catch (Exception e){
            e.printStackTrace();
            log.error("An error occurred during approval");
        }
        return apiResponse;
    }

    private ApiResponse processChangePhone(JSONObject jsonData, User apiUser, HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed to change phone number")
                .build();
        try {
            String cell = jsonData.getString("cell");
            if (cell != null){
                String editPhoneNumbercomments = jsonData.getString("editPhoneNumbercomments").trim();
                BigInteger id = new BigInteger(cell);
                String record1 = sharedFunctions.getRecordId("apiorder", String.valueOf(id));
                String update = "UPDATE apiorder SET editedMSISDNComments = :editedMSISDNComments, msisdn= :rphonenumber, recipient2msisdn=:dotphonenumber,driver_phone=:driverphonenumber where ID =:id";
                Map<String, Object> params = new HashMap<>();
                params.put("id", id);
                params.put("editedMSISDNComments", editPhoneNumbercomments);
                params.put("rphonenumber",jsonData.getString("rphonenumber"));
                params.put("dotphonenumber",jsonData.getString("dotphonenumber"));
                params.put("driverphonenumber",jsonData.getString("driverphonenumber"));
                params.put("msisdn", jsonData.getString("rphonenumber"));
                crudService.executeHibernateQuery(update,params);

                String sql1 = "UPDATE apiorder SET originalmsisdn=:msisdn WHERE ID=:id and originalmsisdn is null or originalmsisdn=''";
                Map<String, Object> params2 = new HashMap<>();
                params2.put("id", id);
                params2.put("msisdn", jsonData.getString("msisdn"));
                crudService.executeHibernateQuery(sql1,params2);

                String record2 = sharedFunctions.getRecordId("apiorder", String.valueOf(id));
                String logString = sharedFunctions.returnLogString(record1, record2);
                sharedFunctions.auditAction("Request Phone Edit","Update phone number of order id = "+id +" ("+logString +" )", servletRequest.getRemoteAddr(),jsonData.toString(), null, apiUser.getId().intValue(), null);
                apiResponse.setResponseMessage("Updated Request Phone number successfully");
                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
            }else {
                sharedFunctions.auditAction("Request Phone Edit", "Failed to edit phone number of the request"+ cell, servletRequest.getRemoteAddr(), jsonData.toString(),null, apiUser.getId().intValue(), null );
                apiResponse.setResponseMessage("Failed to edit phone number of the request");
                apiResponse.setResponseCode(ApiResponseCode.FAIL);
            }

        }catch (Exception e){
            e.printStackTrace();
            log.error("An error occurred change phone number");
        }
        return apiResponse;
    }
    private ApiResponse processConfirmRecipient(JSONObject jsonData, MultipartFile[] files, User apiUser, HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseMessage("Error occurred while process the confirm recipient")
                .responseCode(ApiResponseCode.FAIL)
                .build();
        try{
            if (files ==null || files.length == 0){
                apiResponse.setResponseMessage("No records found");
                apiResponse.setResponseCode(ApiResponseCode.FAIL);
                return apiResponse;
            }
            MultipartFile file = files[0];
            String contentType = file.getContentType();

            List<String> allowedTypes = List.of(
                    "application/vnd.ms-excel",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "application/ms-excel",
                    "application/wps-office.xls",
                    "application/octet-stream"
            );
            if (!allowedTypes.contains(contentType)){
                apiResponse.setResponseMessage("File type not supported");
                apiResponse.setResponseCode(ApiResponseCode.FAIL);
                return apiResponse;
            }
            int insertedCount = 0;
            try(InputStream inputStream = file.getInputStream(); Workbook workbook= new XSSFWorkbook(inputStream)) {
                Sheet sheet = workbook.getSheetAt(0);
                int totalRecords = sheet.getPhysicalNumberOfRows() -1;

                for (int i=0; i<totalRecords; i++) {
                    Row row = sheet.getRow(i);
                    if (row == null) {
                        continue;
                    }
                    String msisdn = sharedFunctions.getCellValue(row.getCell(0));
                    String fullName = sharedFunctions.getCellValue(row.getCell(1));
                    String mpesaName = sharedFunctions.getCellValue(row.getCell(2));
                    if (msisdn == null || msisdn.isEmpty()) continue;
                    String checkSql = "SELECT ID FROM confirmed_recipient WHERE MSISDN = :msisdn";
                    Map<String, Object> params = new HashMap<>();
                    params.put("msisdn", msisdn);
                    List<Map<String, Object>> existing = crudService.fetchWithHibernateQuery(checkSql, params, 0,1);
                    if (existing.isEmpty()) {
                        String insertQuery = "INSERT INTO confirmed_recipient (Full_name, MSISDN, createdby, origin)\n" +
                                "                            VALUES (:Full_name, :MSISDN, :createdby, :origin)";
                        Map<String, Object> params2 = new HashMap<>();
                        params2.put("Full_name", fullName);
                        params2.put("MSISDN", msisdn);
                        params2.put("createdby", apiUser.getId());
                        params2.put("origin", "Mpesa upload Verification");
                        crudService.executeHibernateQuery(insertQuery,params);
                        insertedCount++;

                    }
                }
                apiResponse.setResponseMessage(insertedCount +"out of" +totalRecords + "items have been successfully uploaded.");
                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);

            }catch (Exception e){
                apiResponse.setResponseMessage("Sorry, there was a problem uploading your file.");
                apiResponse.setResponseCode(ApiResponseCode.FAIL);
            }

        }catch (Exception e){
            e.printStackTrace();
            log.error("An error occured while processing confirm recipient" +e.getMessage());
        }
        return apiResponse;
    }
    private ApiResponse processAttendanceProcessingWithExcel(JSONObject jsonData, MultipartFile[] files, User apiUser, HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse response = ApiResponse.builder().build();
        StringBuilder sessionTesting = new StringBuilder(jsonData.toString() + " ");
        String cell = servletRequest.getParameter("meeting_code"); // assuming passed as request param
        String clientIp = servletRequest.getRemoteAddr(); // or getClientIpAddr(servletRequest)
        List<List<Object>> dataArray = sharedFunctions.parseJsonArray(jsonData);
        int collcount = dataArray.get(0).size(); // assuming all rows same length

        for (List<Object> row : dataArray) {
            String telephone = String.valueOf(row.get(collcount - 9));
            String rate = String.valueOf(row.get(collcount - 8));
            double perdiem = sharedFunctions.parseDouble(row.get(collcount - 7));
            double lunch = sharedFunctions.parseDouble(row.get(collcount - 6));
            double transport = sharedFunctions.parseDouble(row.get(collcount - 5));
            double extraperdiem = sharedFunctions.parseDouble(row.get(collcount - 4));
            double others = sharedFunctions.parseDouble(row.get(collcount - 3));
            double total = sharedFunctions.parseDouble(row.get(collcount - 2));
            String processAction = String.valueOf(row.get(collcount - 1));
            String comments = String.valueOf(row.get(collcount - 0));

            if ("NO".equalsIgnoreCase(processAction)) {
                sessionTesting.append(" ").append(processAction).append(" IS ACTION : editing no in process ");
                String sql = "UPDATE attendance SET processing_status=3, processing_comments=:comments WHERE meeting_code=:cell AND attendance.msisdn=:telephone";
                Map<String, Object> params = Map.of("comments", comments, "cell", cell, "telephone", telephone);
                crudService.executeNativeQuery(sql, params);
                sharedFunctions.auditAction("UPDATED", "Attendance processing status", clientIp, jsonData.toString(), null, null, null);
            } else {
                sessionTesting.append(processAction).append(" IS ACTION : editing yes in process ");

                String query = """
                SELECT treatment_model, attendance.msisdn, CONCAT(firstname,' ',lastname) AS Name, COUNT(*) AS No,
                       (SELECT county_id FROM event WHERE meeting_code = :cell LIMIT 1) AS countyID,
                       requestTime, attendance.id
                FROM attendance
                INNER JOIN recipient ON recipient.id = attendance.recipient_id
                WHERE meeting_code = :cell AND processing_status = '0' AND attendance.msisdn = :telephone
            """;

                Map<String, Object> qParams = Map.of("cell", cell, "telephone", telephone);
                List<Object[]> result = crudService.fetchWithNativeQuery(query, qParams, 0,1);
                if (result.isEmpty()) continue;

                Object[] rowData = result.get(0);
                String treatmentModel = String.valueOf(rowData[0]);
                String msisdn = String.valueOf(rowData[1]);
                String fname = String.valueOf(rowData[2]);
                String countyId = String.valueOf(rowData[4]);
                Timestamp requestTime = (Timestamp) rowData[5];
                String attendanceId = String.valueOf(rowData[6]);

                int year = Calendar.getInstance().get(Calendar.YEAR);
                String month = new SimpleDateFormat("MMMM").format(requestTime);
                double totalAmount = perdiem + transport + extraperdiem + others - lunch;

                // Insert expense details
                sharedFunctions.insertTempExpense(attendanceId, "Per Diem", perdiem);
                sharedFunctions.insertTempExpense(attendanceId, "Transport/Fuel", transport);
                sharedFunctions.insertTempExpense(attendanceId, "Lunch", lunch);
                sharedFunctions.insertTempExpense(attendanceId, "Extra Per Diem", extraperdiem);
                sharedFunctions.insertTempExpense(attendanceId, "Others", others);

                String updateSql = """
                UPDATE attendance SET processing_status=1, processing_comments=:comments, payable_amount_1=:amount
                WHERE meeting_code=:cell AND processing_status='0' AND attendance.msisdn=:telephone
            """;
                Map<String, Object> updateParams = Map.of(
                        "comments", comments,
                        "amount", totalAmount,
                        "cell", cell,
                        "telephone", telephone
                );
                crudService.executeNativeQuery(updateSql, updateParams);
                sharedFunctions.auditAction("UPDATED", "Updated attendance processing status", clientIp, jsonData.toString(), null, null, null);
            }
        }

        servletRequest.getSession().setAttribute("testing", sessionTesting.toString());
        servletRequest.getSession().setAttribute("notes", "Successfully Processed!");
        response.setResponseMessage("Processed successfully.");
        response.setResponseCode(ApiResponseCode.SUCCESS);
        return response;
    }

    private ApiResponse processAttendanceApproval(JSONObject jsonData, MultipartFile[] files, User apiUser,
                                                  HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Error occurred during attendance approval")
                .build();

        try {
            String primaryKeyParam = jsonData.getString("primarykey");
            servletRequest.getSession().setAttribute("notes1", primaryKeyParam);
            log.info("Received primary keys for bulk approval: {}", primaryKeyParam);

            if (primaryKeyParam != null && !primaryKeyParam.isEmpty()) {
                String[] IDs = primaryKeyParam.split(",");
                int i = IDs[0].equalsIgnoreCase("all") ? 1 : 0;

                while (i < IDs.length) {
                    String[] arr = IDs[i].split("-");
                    String msisdn = arr[0];
                    String meetingCode = arr[1];
                    String clientIp = servletRequest.getRemoteAddr();

                    if ("reject".equalsIgnoreCase(jsonData.getString("action"))) {
                        String rejectSql = "UPDATE attendance SET processing_status=3 WHERE meeting_code=:meetingCode AND msisdn=:msisdn";
                        Map<String, Object> rejectParams = Map.of("meetingCode", meetingCode, "msisdn", msisdn);
                        crudService.executeNativeQuery(rejectSql, rejectParams);
                        servletRequest.getSession().setAttribute("notes", "Successfully Rejected!");
                        log.info("Rejected attendance for MSISDN: {} in meeting: {}", msisdn, meetingCode);
                    } else {
                        String query = """
                        SELECT treatment_model, a.msisdn, CONCAT(r.firstname, ' ', r.lastname) AS name,
                               (SELECT county_id FROM event WHERE meeting_code = :meetingCode LIMIT 1) AS county_id,
                               a.requestTime, a.id
                        FROM attendance a
                        JOIN recipient r ON r.id = a.recipient_id
                        WHERE a.meeting_code = :meetingCode AND a.msisdn = :msisdn
                    """;

                        Map<String, Object> queryParams = Map.of("meetingCode", meetingCode, "msisdn", msisdn);
                        List<Object[]> result = crudService.fetchWithNativeQuery(query, queryParams, 0,1);

                        if (result.isEmpty()) {
                            log.warn("No attendance record found for MSISDN: {}, meeting: {}", msisdn, meetingCode);
                            i++;
                            continue;
                        }

                        Object[] row = result.get(0);
                        String treatmentModel = String.valueOf(row[0]);
                        String fullName = String.valueOf(row[2]);
                        String countyId = String.valueOf(row[3]);
                        Timestamp requestTime = (Timestamp) row[4];
                        String attendanceId = String.valueOf(row[5]);

                        String supervisorName = "";
                        int currentLevel = 6;
                        int year = LocalDate.now().getYear();
                        String month = new SimpleDateFormat("MMMM").format(requestTime);
                        long paymentId = new Random().nextLong();
                        double totalAmount = 0.0; // TODO: Replace with actual amount logic
                        String orgId = String.valueOf(servletRequest.getSession().getAttribute("org_o_id"));
                        if (orgId == null || orgId.equals("null")) {
                            orgId = "1";
                        }
                        String latitude = ""; // TODO: Replace with actual values
                        String longitude = ""; // TODO: Replace with actual values
                        String provinceId = ""; // TODO: Replace with actual values
                        String districtId = ""; // TODO: Replace with actual values
                        String notes = ""; // TODO: Replace with actual values
                        String paymentIDs = ""; // TODO: Replace with actual values

                        Map<String, Object> budgetParams = new HashMap<>();
                        budgetParams.put("meetingCode", meetingCode);

                        List<Map<String, Object>> budgetResult = crudService.fetchWithNativeQuery(
                                "SELECT budget_id FROM event WHERE meeting_code = :meetingCode",
                                budgetParams, 0,1
                        );

                        String budget = null;
                        if (!budgetResult.isEmpty()) {
                            budget = String.valueOf(budgetResult.get(0).get("budget_id"));
                            log.info("Fetched budget_id: {}", budget);
                        } else {
                            log.error("No budget_id found for meeting_code: {}", meetingCode);
                        }

                        Map<String, Object> orderTypeParams = new HashMap<>();
                        orderTypeParams.put("budget", budget);
                        orderTypeParams.put("orgId", orgId);

                        String orderType = null;
                        if (budget != null) {
                            List<Map<String, Object>> orderTypeResult = crudService.fetchWithNativeQuery(
                                    "SELECT ordertypeId FROM org_ordertype WHERE budget_id = :budget AND org_id = :orgId",
                                    orderTypeParams,0,1
                            );

                            if (!orderTypeResult.isEmpty()) {
                                orderType = String.valueOf(orderTypeResult.get(0).get("ordertypeid")); // Be aware of column name case
                                log.info("Fetched ordertypeId: {}", orderType);
                            } else {
                                log.error("No ordertypeId found for budget_id: {} and org_id: {}", budget, orgId);
                            }
                        }

                        String insertOrderSql = """
                        INSERT INTO apiorder(treatment_model, initiator_username, firstname, middlename, lastname, ordertype, budget, credit,
                                             msisdn, notes, county, province, district, request_src, requesttime, approval_level,
                                             approval_status, sourceip, latitude, longitude, year, month, payment_id, attached_to,
                                             batchno, org_id)
                        VALUES(:treatmentModel, :supervisorName, :firstname, :middlename, :lastname, :orderType, :budget, :credit,
                               :msisdn, :notes, :county, :province, :district, 'api', NOW(), :level, 'Pending', :clientIp,
                               :latitude, :longitude, :year, :month, :paymentId, :attachedTo, :batchNo, :orgId)
                    """;

                        Map<String, Object> insertParams = new HashMap<>();
                        insertParams.put("treatmentModel", treatmentModel);
                        insertParams.put("supervisorName", supervisorName);
                        insertParams.put("firstname", fullName);
                        insertParams.put("middlename", "");
                        insertParams.put("lastname", "");
                        insertParams.put("orderType", orderType);
                        insertParams.put("budget", budget);
                        insertParams.put("credit", totalAmount);
                        insertParams.put("msisdn", msisdn);
                        insertParams.put("notes", notes);
                        insertParams.put("county", countyId);
                        insertParams.put("province", provinceId);
                        insertParams.put("district", districtId);
                        insertParams.put("level", currentLevel);
                        insertParams.put("clientIp", clientIp);
                        insertParams.put("latitude", latitude);
                        insertParams.put("longitude", longitude);
                        insertParams.put("year", year);
                        insertParams.put("month", month);
                        insertParams.put("paymentId", paymentId);
                        insertParams.put("attachedTo", paymentIDs);
                        insertParams.put("batchNo", meetingCode);
                        insertParams.put("orgId", orgId);

                        crudService.executeNativeQuery(insertOrderSql, insertParams);
                        log.info("Inserted apiorder for MSISDN: {}", msisdn);

                        // Get the inserted apiorder ID
                        String fetchOrderIdSql = "SELECT id FROM apiorder WHERE msisdn = :msisdn ORDER BY id DESC LIMIT 1";
                        Map<String, Object> params = new HashMap<>();
                        params.put("msisdn", msisdn);
                        List<Object[]> orderRow = crudService.fetchWithNativeQuery(fetchOrderIdSql, params, 0,1);

                        if (!orderRow.isEmpty()) {
                            String orderId = String.valueOf(orderRow.get(0)[0]);

                            // Fetch details for hash
                            String orderDetailsQuery = "SELECT id, msisdn, credit, driver_amount, driver_phone, recipient2credit, recipient2msisdn FROM apiorder WHERE id = :id";
                            Map<String, Object> params1 = new HashMap<>();
                            params1.put("id", orderId);
                            List<Object[]> trxData = crudService.fetchWithNativeQuery(orderDetailsQuery, params1, 0, 1);
                            Object[] trxRow = trxData.get(0);

                            HashMap<String, String> map = new HashMap<>();
                            map.put("id", trxRow[0].toString());
                            map.put("msisdn", trxRow[1].toString());
                            map.put("credit", trxRow[2].toString());
                            map.put("driver_amount", trxRow[3].toString());
                            map.put("driver_phone", trxRow[4].toString());
                            map.put("recipient2credit", trxRow[5].toString());
                            map.put("recipient2msisdn", trxRow[6].toString());

                            String trxChain =sharedFunctions.updateChainTrx(null, map, orderId, null);
                            String insertTrxSql = "INSERT INTO TRX_VALUES(ORDER_ID, T_VALUE) VALUES(:orderId, :value)";
                            crudService.executeNativeQuery(insertTrxSql, Map.of("orderId", orderId, "value", trxChain));
                            sharedFunctions.auditAction("CREATED", "Created new order transaction id", orderId, clientIp, null, null, null);
                            log.info("Inserted TRX_VALUES for Order ID: {}", orderId);

                            // Insert supervision details
                            List<String> expenseTitles = List.of("Per Diem", "Transport/Fuel", "Lunch");
                            for (String title : expenseTitles) {
                                String supervisionInsert = """
                                INSERT INTO supervision_details(apiorder_id, expense_id, title, credit, original_amount, url)
                                SELECT :orderId, expense_id, title, credit, original_amount, url
                                FROM temp_supervision_details
                                WHERE apiorder_id = :attendanceId AND title = :title
                            """;
                                crudService.executeNativeQuery(supervisionInsert, Map.of(
                                        "orderId", orderId,
                                        "attendanceId", attendanceId,
                                        "title", title
                                ));
                            }

                            String updateAttendanceSql = "UPDATE attendance SET processing_status = 4 WHERE meeting_code = :meetingCode AND msisdn = :msisdn";
                            crudService.executeNativeQuery(updateAttendanceSql, Map.of("meetingCode", meetingCode, "msisdn", msisdn));

                            log.info("Successfully processed and updated attendance for MSISDN: {}, meeting: {}", msisdn, meetingCode);
                            servletRequest.getSession().setAttribute("notes", "Successfully Processed");
                        } else {
                            log.error("Failed to retrieve inserted apiorder for MSISDN: {}", msisdn);
                        }
                    }
                    i++;
                }
            }

            apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
            apiResponse.setResponseMessage("Bulk approval processing completed successfully");
        } catch (Exception e) {
            log.error("Error in processAttendanceApproval: ", e);
            apiResponse.setResponseMessage("An error occurred during processing");
        }

        return apiResponse;
    }
    private ApiResponse processAttendanceProcessing(JSONObject postData, MultipartFile[] files, User apiUser,
                                                    HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseMessage("An error occurred")
                .responseCode(ApiResponseCode.FAIL)
                .build();

        try {
            String cell = postData.optString("cell");
            String action = postData.optString("action");

            // Parse cell to get msisdn and meetingCode (matching PHP explode logic)
            String[] arr = cell.split("-");
            String msisdn = postData.optString("telephoneNumber");
            String meetingCode = postData.optString("meetingCode");

            String clientIp = servletRequest.getRemoteAddr();

            if ("reject".equalsIgnoreCase(action)) {
                String sql = "UPDATE attendance SET processing_status=3 WHERE meeting_code='" + meetingCode + "' AND attendance.msisdn='" + msisdn + "'";
                crudService.executeNativeQuery(sql, new HashMap<>());
                servletRequest.getSession().setAttribute("notes", "Successfully Rejected!");
                log.info("Rejected attendance for MSISDN {} in meeting {}", msisdn, meetingCode);
            } else {
                String query = "SELECT treatment_model, attendance.msisdn, CONCAT(firstname, ' ', lastname) AS name, COUNT(*) AS no, " +
                        "(SELECT county_id FROM event WHERE meeting_code=:meetingCode LIMIT 1) AS countyID, " +
                        "requestTime, attendance.id " +
                        "FROM attendance " +
                        "INNER JOIN recipient ON recipient.id = attendance.recipient_id " +
                        "WHERE meeting_code = :meetingCode AND attendance.msisdn = :msisdn " +
                        "GROUP BY treatment_model, attendance.msisdn, firstname, lastname, requestTime, attendance.id";

                Map<String, Object> queryParams = new HashMap<>();
                queryParams.put("meetingCode", meetingCode);
                queryParams.put("msisdn", msisdn);

                List<Object[]> resultList = crudService.fetchWithNativeQuery(query, queryParams,0,1);

                if (resultList.isEmpty()) {
                    log.error("No attendance record found for MSISDN {} in meeting {}", msisdn, meetingCode);
                    return apiResponse;
                }

                Object[] row = resultList.get(0);

//                String treatmentModel = (String) row[0];
//                String fullName = (String) row[2];
//                String countyId = String.valueOf(row[4]);
                String requestTime = row[5].toString();
                Long attendanceId = Long.parseLong(row[6].toString());

                int currentLevel = 6;
                int year = Calendar.getInstance().get(Calendar.YEAR);
                String month = new SimpleDateFormat("MMMM").format(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(requestTime));
                long paymentId = new Random().nextLong();

                double perdiem = postData.getDouble("extraPerDiem");
                double transport = postData.getDouble("transport");
                double lunch = postData.getDouble("lessLunch");
                double totalAmount = perdiem + transport + lunch;

                String insertExpenseQuery = "INSERT INTO temp_supervision_details(apiorder_id, expense_id, title, credit, original_amount, url) " +
                        "VALUES (:apiorder_id, :expense_id, :title, :credit, :original_amount, '')";

                String[] titles = {"Per Diem", "Transport/Fuel", "Lunch"};
                double[] credits = {perdiem, transport, lunch};

                for (int i = 0; i < titles.length; i++) {
                    Map<String, Object> expenseParams = new HashMap<>();
                    expenseParams.put("apiorder_id", attendanceId);
                    expenseParams.put("expense_id", new Random().nextInt(100000)); // Use UUID/random ID generator if needed
                    expenseParams.put("title", titles[i]);
                    expenseParams.put("credit", credits[i]);
                    expenseParams.put("original_amount", credits[i]);
                    crudService.executeNativeQuery(insertExpenseQuery, expenseParams);
                }

                String updateSql = "UPDATE attendance SET processing_status=1 WHERE meeting_code=:meetingCode AND msisdn=:msisdn";
                Map<String, Object> updateParams = new HashMap<>();
                updateParams.put("meetingCode", meetingCode);
                updateParams.put("msisdn", msisdn);
                crudService.executeNativeQuery(updateSql, updateParams);

                servletRequest.getSession().setAttribute("notes1", "Successfully Processed");
                log.info("Successfully processed supervision setup for MSISDN {} in meeting {}", msisdn, meetingCode);
            }

            apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
            apiResponse.setResponseMessage("Processed successfully");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error in processAttendanceProcessing: {}", e.getMessage());
        }

        return apiResponse;
    }
    private ApiResponse processAttendanceApprovalExcel(JSONObject jsonData, MultipartFile[] files, User apiUser,
                                                       HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Error occurred")
                .build();

        try {
            // Extract parameters from JSON data only (no servletRequest session access)
            String cell = jsonData.optString("cell", "");
            String jsonDataString = jsonData.optString("jsondata", "");
            String editExcelStr = jsonData.optString("editexcel", "0");
            int collCount = jsonData.optInt("collcount", 0);
            String orgId = jsonData.optString("org_id", "1"); // Get from jsonData instead of session
            String clientIp = servletRequest.getRemoteAddr(); // Only get IP from servletRequest

            // Validate required parameters
            if (cell.isEmpty() || jsonDataString.isEmpty() || collCount == 0) {
                log.error("Missing required parameters: cell={}, jsondata length={}, collcount={}",
                         cell, jsonDataString.length(), collCount);
                apiResponse.setResponseMessage("Missing required parameters");
                return apiResponse;
            }

            // Parse the JSON array data (matching PHP: $arr = json_decode($jsondata))
            JSONArray dataArray;
            try {
                dataArray = new JSONArray(jsonDataString);
            } catch (Exception e) {
                log.error("Failed to parse JSON data: {}", e.getMessage());
                apiResponse.setResponseMessage("Invalid JSON data format");
                return apiResponse;
            }

            // Initialize debugging info (no session access)
            StringBuilder debugInfo = new StringBuilder();

            // Check if this is editing mode (matching PHP: if (isset($editexcel) && $editexcel == '1'))
            if ("1".equals(editExcelStr)) {
                // EDITING MODE - Update temp_supervision_details and processing_status=1
                log.info("Processing attendance approval in EDITING mode for cell: {}", cell);
                debugInfo.append("Editing");

                // Process each row in the data array (matching PHP: foreach ($arr as $row))
                for (int i = 0; i < dataArray.length(); i++) {
                    JSONArray row = dataArray.getJSONArray(i);

                    // Extract values from row using column positions (matching PHP logic exactly)
                    String telephone = safeGetString(row, collCount - 10);      // $telephone = $row[$collcount - 10]
                    String rate = safeGetString(row, collCount - 9);            // $rate = $row[$collcount - 9]
                    String perdiem = safeGetString(row, collCount - 8);         // $perdiem = $row[$collcount - 8]
                    String lunch = safeGetString(row, collCount - 7);           // $lunch = $row[$collcount - 7]
                    String transport = safeGetString(row, collCount - 6);       // $transport = $row[$collcount - 6]
                    String extraPerDiem = safeGetString(row, collCount - 5);    // $extraperdiem = $row[$collcount - 5]
                    String total = safeGetString(row, collCount - 3);           // $total = $row[$collcount - 3]
                    String action = safeGetString(row, collCount - 2);          // $action = $row[$collcount - 2]
                    String comments = safeGetString(row, collCount - 1);        // $comments = $row[$collcount - 1]
                    String attendanceIdsStr = safeGetString(row, collCount);    // $row[$collcount]

                    // Skip empty rows (null pointer protection)
                    if (telephone == null || telephone.trim().isEmpty()) {
                        continue;
                    }

                    // Extract attendance ID (matching PHP: $attendance_ids = explode(",", $row[$collcount]))
                    String attendanceId = "";
                    if (attendanceIdsStr != null && !attendanceIdsStr.isEmpty()) {
                        String[] attendanceIds = attendanceIdsStr.split(",");
                        if (attendanceIds.length > 0) {
                            attendanceId = attendanceIds[0].trim();
                        }
                    }

                    // Update debug info (matching PHP behavior)
                    debugInfo.append(attendanceIdsStr).append(" ").append(attendanceId).append(" ACTION IS ").append(action);
                    debugInfo.append(comments).append(" ").append(attendanceId).append(" comments are ").append(action);

                    // Process based on action (matching PHP: if (isset($action) && $action == 'NO'))
                    if ("NO".equals(action)) {
                        // REJECT - Update processing_status to 3 (matching PHP logic)
                        debugInfo.append(action).append(" IS ACTION : editing no in approve ");

                        String updateSql = "UPDATE attendance SET processing_status=3, processing_comments=:comments " +
                                         "WHERE meeting_code=:meetingCode AND attendance.msisdn=:telephone";

                        Map<String, Object> params = new HashMap<>();
                        params.put("comments", comments);
                        params.put("meetingCode", cell);
                        params.put("telephone", telephone);

                        crudService.executeNativeQuery(updateSql, params);

                        // Audit action (matching PHP: auditAction("UPDATED", "Updated attendance processing status"))
                        log.info("AUDIT: Updated attendance processing status to REJECTED for MSISDN: {} in meeting: {}",
                                telephone, cell);

                    } else if ("YES".equals(action)) {
                        // APPROVE - Process the approval (matching PHP logic)
                        debugInfo.append(action).append(" IS ACTION : editing yes in approve ");

                        processEditingApprovalExcel(cell, telephone, perdiem, transport, lunch, extraPerDiem,
                                                   total, comments, attendanceId, clientIp, debugInfo);
                    }
                }

            } else {
                // APPROVING MODE - Create apiorder and move to supervision_details, set processing_status=4
                log.info("Processing attendance approval in APPROVING mode for cell: {}", cell);
                debugInfo.append("Approving");

                // Process each row in the data array (matching PHP: foreach ($arr as $row))
                for (int i = 0; i < dataArray.length(); i++) {
                    JSONArray row = dataArray.getJSONArray(i);

                    // Extract values from row using column positions (matching PHP logic for approving mode)
                    // Note: Different column positions for approving mode vs editing mode
                    String telephone = safeGetString(row, collCount - 10);      // $telephone = $row[$collcount - 10]
                    String rate = safeGetString(row, collCount - 9);            // $rate = $row[$collcount - 9]
                    String perdiem = safeGetString(row, collCount - 8);         // $perdiem = $row[$collcount - 8]
                    String lunch = safeGetString(row, collCount - 7);           // $lunch = $row[$collcount - 7]
                    String transport = safeGetString(row, collCount - 6);       // $transport = $row[$collcount - 6]
                    String extraPerDiem = safeGetString(row, collCount - 5);    // $extraperdiem = $row[$collcount - 5]
                    String total = safeGetString(row, collCount - 4);           // $total = $row[$collcount - 4]
                    String action = safeGetString(row, collCount - 3);          // $action = $row[$collcount - 3]
                    String comments = safeGetString(row, collCount - 2);        // $comments = $row[$collcount - 2]
                    String attendanceIdsStr = safeGetString(row, collCount);    // $row[$collcount]

                    // Skip empty rows (matching PHP: && $telephone != '')
                    if (telephone == null || telephone.trim().isEmpty()) {
                        continue;
                    }

                    // Extract attendance ID (matching PHP: $attendance_ids = explode(",", $row[$collcount]))
                    String attendanceId = "";
                    if (attendanceIdsStr != null && !attendanceIdsStr.isEmpty()) {
                        String[] attendanceIds = attendanceIdsStr.split(",");
                        if (attendanceIds.length > 0) {
                            attendanceId = attendanceIds[0].trim();
                        }
                    }

                    // Update debug info (matching PHP behavior)
                    debugInfo.append(attendanceIdsStr).append(" ").append(attendanceId).append(" ACTION IS ").append(action);

                    // Process based on action (matching PHP: if (isset($action) && $action == 'NO' && $telephone != ''))
                    if ("NO".equals(action)) {
                        // REJECT - Update processing_status to 3 (matching PHP logic)
                        debugInfo.append(action).append(" IS ACTION : Rejecting");

                        String updateSql = "UPDATE attendance SET processing_status=3, approval_comments=:comments " +
                                         "WHERE meeting_code=:meetingCode AND attendance.msisdn=:telephone";

                        Map<String, Object> params = new HashMap<>();
                        params.put("comments", comments);
                        params.put("meetingCode", cell);
                        params.put("telephone", telephone);

                        crudService.executeNativeQuery(updateSql, params);

                        // Audit action (matching PHP: auditAction("UPDATED", "Updated attendance processing status"))
                        log.info("AUDIT: Updated attendance processing status to REJECTED for MSISDN: {} in meeting: {}",
                                telephone, cell);

                    } else if ("YES".equals(action)) {
                        // APPROVE - Create API order and finalize approval (matching PHP logic)
                        debugInfo.append(action).append(" IS ACTION : Approving");

                        processApprovingApprovalExcel(cell, telephone, perdiem, transport, lunch, extraPerDiem,
                                                    total, comments, attendanceId, clientIp, debugInfo, apiUser, orgId);
                    }
                }
            }

            // Set success response with debug info
            apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
            apiResponse.setResponseMessage("Successfully Processed");
            apiResponse.setData("Debug Info: " + debugInfo.toString() + " | Notes: Successfully Processed");

            log.info("Attendance approval processing completed successfully for cell: {}", cell);

        } catch (Exception e) {
            log.error("Error in processAttendanceApprovalExcel: {}", e.getMessage(), e);
            apiResponse.setResponseMessage("Error processing attendance approval: " + e.getMessage());
        }

        return apiResponse;
    }

    /**
     * Helper method to safely get string from JSONArray with null protection
     */
    private String safeGetString(JSONArray row, int index) {
        try {
            if (index >= 0 && index < row.length()) {
                Object value = row.get(index);
                return value != null ? value.toString().trim() : "";
            }
            return "";
        } catch (Exception e) {
            log.warn("Error getting value at index {}: {}", index, e.getMessage());
            return "";
        }
    }

    /**
     * Process editing approval for YES action (matching PHP logic)
     */
    private void processEditingApprovalExcel(String cell, String telephone, String perdiem, String transport,
                                           String lunch, String extraPerDiem, String total, String comments,
                                           String attendanceId, String clientIp, StringBuilder debugInfo) throws Exception {

        // Get attendance details (matching PHP query)
        String attendanceQuery = "SELECT treatment_model, attendance.msisdn, CONCAT(firstname, ' ', lastname) AS name, " +
                               "COUNT(*) AS no, (SELECT county_id FROM event WHERE meeting_code=:meetingCode LIMIT 1) AS countyID, " +
                               "requestTime, attendance.id " +
                               "FROM attendance " +
                               "INNER JOIN recipient ON recipient.id = attendance.recipient_id " +
                               "WHERE meeting_code=:meetingCode AND processing_status='1' AND attendance.msisdn=:telephone";

        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("meetingCode", cell);
        queryParams.put("telephone", telephone);

        // Update debug info
        debugInfo.append(attendanceQuery).append(" ");

        List<Object[]> attendanceResults = crudService.fetchWithNativeQuery(attendanceQuery, queryParams, 0, 1);

        if (attendanceResults.isEmpty()) {
            log.error("No attendance record found for MSISDN {} in meeting {}", telephone, cell);
            return;
        }

        Object[] attendanceRow = attendanceResults.get(0);
        String treatmentModel = (String) attendanceRow[0];
        String fullName = (String) attendanceRow[2];
        String countyId = String.valueOf(attendanceRow[4]);
        String requestTime = attendanceRow[5].toString();
        String actualAttendanceId = attendanceRow[6].toString();

        // Calculate total amount (matching PHP: $total_amount = $perdiem + $transport + $extraperdiem + $others - $lunch)
        double perdiemAmount = parseDoubleValue(perdiem);
        double transportAmount = parseDoubleValue(transport);
        double lunchAmount = parseDoubleValue(lunch);
        double extraPerDiemAmount = parseDoubleValue(extraPerDiem);
        double others = 0.0; // Default to 0 as in PHP
        double totalAmount = perdiemAmount + transportAmount + extraPerDiemAmount + others - lunchAmount;

        // Delete existing temp_supervision_details (matching PHP: DELETE FROM temp_supervision_details WHERE apiorder_id='$attendance_id')
        String deleteQuery = "DELETE FROM temp_supervision_details WHERE apiorder_id=:attendanceId";
        Map<String, Object> deleteParams = new HashMap<>();
        deleteParams.put("attendanceId", actualAttendanceId);

        debugInfo.append(deleteQuery).append(" ");

        crudService.executeNativeQuery(deleteQuery, deleteParams);

        // Insert new temp_supervision_details records (matching PHP logic)
        insertTempSupervisionDetailExcel(actualAttendanceId, "Per Diem", perdiemAmount, debugInfo);
        insertTempSupervisionDetailExcel(actualAttendanceId, "Transport/Fuel", transportAmount, debugInfo);
        insertTempSupervisionDetailExcel(actualAttendanceId, "Lunch", lunchAmount, debugInfo);
        insertTempSupervisionDetailExcel(actualAttendanceId, "Extra Per Diem", extraPerDiemAmount, debugInfo);
        insertTempSupervisionDetailExcel(actualAttendanceId, "Others", others, debugInfo);

        // Update attendance record (matching PHP: UPDATE attendance SET processing_status=1)
        String updateSql = "UPDATE attendance SET processing_status=1, payable_amount_1=:totalAmount, processing_comments=:comments " +
                          "WHERE meeting_code=:meetingCode AND processing_status='1' AND attendance.msisdn=:telephone";

        Map<String, Object> updateParams = new HashMap<>();
        updateParams.put("totalAmount", totalAmount);
        updateParams.put("comments", comments);
        updateParams.put("meetingCode", cell);
        updateParams.put("telephone", telephone);

        debugInfo.append(updateSql).append(" ");

        crudService.executeNativeQuery(updateSql, updateParams);

        // Audit action (matching PHP: auditAction("UPDATED", "Updated attendance processing status"))
        log.info("AUDIT: Updated attendance processing status for MSISDN: {} in meeting: {}", telephone, cell);
    }

    /**
     * Helper method to insert temp supervision detail record
     */
    private void insertTempSupervisionDetailExcel(String attendanceId, String title, double amount,
                                                 StringBuilder debugInfo) throws Exception {
        String insertQuery = "INSERT INTO temp_supervision_details(apiorder_id, expense_id, title, credit, original_amount, url) " +
                           "VALUES(:attendanceId, :expenseId, :title, :credit, :originalAmount, '')";

        Map<String, Object> params = new HashMap<>();
        params.put("attendanceId", attendanceId);
        params.put("expenseId", (int)(Math.random() * 100000)); // Random expense ID (matching PHP: rand())
        params.put("title", title);
        params.put("credit", amount);
        params.put("originalAmount", amount);

        // Update debug info
        debugInfo.append(insertQuery).append(" ");

        crudService.executeNativeQuery(insertQuery, params);

        // Audit action (matching PHP: auditAction("CREATED", "Temp supervision items"))
        log.info("AUDIT: Created temp supervision item: {} for attendance: {}", title, attendanceId);
    }

    /**
     * Helper method to safely parse double values
     */
    private double parseDoubleValue(String value) {
        try {
            return value != null && !value.trim().isEmpty() ? Double.parseDouble(value.trim()) : 0.0;
        } catch (NumberFormatException e) {
            log.warn("Failed to parse double value: {}", value);
            return 0.0;
        }
    }

    /**
     * Process approving approval for YES action (matching PHP logic for final approval)
     */
    private void processApprovingApprovalExcel(String cell, String telephone, String perdiem, String transport,
                                             String lunch, String extraPerDiem, String total, String comments,
                                             String attendanceId, String clientIp, StringBuilder debugInfo,
                                             User apiUser, String orgId) throws Exception {

        // Get attendance details with additional fields for approval (matching PHP query)
        String attendanceQuery = "SELECT treatment_model, attendance.msisdn, CONCAT(firstname, ' ', lastname) AS name, " +
                               "COUNT(*) AS no, (SELECT county_id FROM event WHERE meeting_code=:meetingCode LIMIT 1) AS countyID, " +
                               "requestTime, attendance.id, recipient.regionid, recipient.subcountyid " +
                               "FROM attendance " +
                               "INNER JOIN recipient ON recipient.id = attendance.recipient_id " +
                               "WHERE meeting_code=:meetingCode AND processing_status='1' AND attendance.msisdn=:telephone";

        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("meetingCode", cell);
        queryParams.put("telephone", telephone);

        // Update debug info
        debugInfo.append(attendanceQuery).append(" ");

        List<Object[]> attendanceResults = crudService.fetchWithNativeQuery(attendanceQuery, queryParams, 0, 1);

        if (attendanceResults.isEmpty()) {
            log.error("No attendance record found for MSISDN {} in meeting {}", telephone, cell);
            return;
        }

        Object[] attendanceRow = attendanceResults.get(0);
        String treatmentModel = (String) attendanceRow[0];
        String fullName = (String) attendanceRow[2];
        String countyId = String.valueOf(attendanceRow[4]);
        String requestTime = attendanceRow[5].toString();
        String actualAttendanceId = attendanceRow[6].toString();
        String provinceId = String.valueOf(attendanceRow[7]);
        String districtId = String.valueOf(attendanceRow[8]);

        // Calculate total amount (matching PHP: $total_amount = $perdiem + $transport + $extraperdiem + $others - $lunch)
        double perdiemAmount = parseDoubleValue(perdiem);
        double transportAmount = parseDoubleValue(transport);
        double lunchAmount = parseDoubleValue(lunch);
        double extraPerDiemAmount = parseDoubleValue(extraPerDiem);
        double others = 0.0; // Default to 0 as in PHP
        double totalAmount = perdiemAmount + transportAmount + extraPerDiemAmount + others - lunchAmount;

        // Get budget and order type (matching PHP logic)
        String budgetQuery = "SELECT budget_id FROM event WHERE meeting_code=:meetingCode";
        Map<String, Object> budgetParams = new HashMap<>();
        budgetParams.put("meetingCode", cell);

        List<Object[]> budgetResults = crudService.fetchWithNativeQuery(budgetQuery, budgetParams, 0, 1);
        String budget = budgetResults.isEmpty() ? "1" : String.valueOf(budgetResults.get(0)[0]);

        // Get order type (matching PHP logic)
        String orderTypeQuery = "SELECT ordertypeId FROM org_ordertype WHERE budget_id=:budget AND org_id=:orgId";
        Map<String, Object> orderTypeParams = new HashMap<>();
        orderTypeParams.put("budget", budget);
        orderTypeParams.put("orgId", orgId);

        List<Object[]> orderTypeResults = crudService.fetchWithNativeQuery(orderTypeQuery, orderTypeParams, 0, 1);
        String orderType = orderTypeResults.isEmpty() ? "1" : String.valueOf(orderTypeResults.get(0)[0]);

        // Generate payment ID (matching PHP: $payment_id = rand())
        long paymentId = (long)(Math.random() * 1000000);

        // Get current year and month (matching PHP logic)
        java.util.Calendar cal = java.util.Calendar.getInstance();
        int year = cal.get(java.util.Calendar.YEAR);
        String month = new java.text.SimpleDateFormat("MMMM").format(cal.getTime());

        // Parse name parts (matching PHP logic)
        String[] nameParts = fullName.split(" ");
        String firstname = nameParts.length > 0 ? nameParts[0] : "";
        String middlename = nameParts.length > 1 ? nameParts[1] : "";
        String lastname = nameParts.length > 2 ? nameParts[2] : "";

        // Create API order (matching PHP INSERT into apiorder)
        String apiOrderSql = "INSERT INTO apiorder(treatment_model, initiator_username, firstname, middlename, lastname, " +
                           "ordertype, budget, credit, msisdn, notes, county, province, district, request_src, requesttime, " +
                           "approval_level, approval_status, sourceip, latitude, longitude, year, month, payment_id, " +
                           "attached_to, batchno, org_id) " +
                           "VALUES(:treatmentModel, :initiatorUsername, :firstname, :middlename, :lastname, :ordertype, " +
                           ":budget, :credit, :msisdn, :notes, :county, :province, :district, 'api', NOW(), " +
                           "'9', 'Pending', :sourceip, :latitude, :longitude, :year, :month, :paymentId, " +
                           ":attachedTo, :batchno, :orgId)";

        Map<String, Object> apiOrderParams = new HashMap<>();
        apiOrderParams.put("treatmentModel", treatmentModel);
        apiOrderParams.put("initiatorUsername", ""); // Empty as in PHP
        apiOrderParams.put("firstname", firstname);
        apiOrderParams.put("middlename", middlename);
        apiOrderParams.put("lastname", lastname);
        apiOrderParams.put("ordertype", orderType);
        apiOrderParams.put("budget", budget);
        apiOrderParams.put("credit", totalAmount);
        apiOrderParams.put("msisdn", telephone);
        apiOrderParams.put("notes", ""); // Empty as in PHP
        apiOrderParams.put("county", countyId);
        apiOrderParams.put("province", provinceId);
        apiOrderParams.put("district", districtId);
        apiOrderParams.put("sourceip", clientIp);
        apiOrderParams.put("latitude", ""); // Empty as in PHP
        apiOrderParams.put("longitude", ""); // Empty as in PHP
        apiOrderParams.put("year", year);
        apiOrderParams.put("month", month);
        apiOrderParams.put("paymentId", paymentId);
        apiOrderParams.put("attachedTo", ""); // Empty as in PHP
        apiOrderParams.put("batchno", cell);
        apiOrderParams.put("orgId", orgId);

        debugInfo.append(apiOrderSql).append(" ");

        crudService.executeNativeQuery(apiOrderSql, apiOrderParams);

        // Audit action (matching PHP: auditAction("CREATED", "Api order item"))
        log.info("AUDIT: Created API order item for MSISDN: {} in meeting: {}", telephone, cell);

        // Get the newly created order ID (matching PHP: select id from apiorder where msisdn = '$msisdn' order by id desc limit 1)
        String getOrderIdQuery = "SELECT id FROM apiorder WHERE msisdn=:msisdn ORDER BY id DESC LIMIT 1";
        Map<String, Object> orderIdParams = new HashMap<>();
        orderIdParams.put("msisdn", telephone);

        List<Object[]> orderIdResults = crudService.fetchWithNativeQuery(getOrderIdQuery, orderIdParams, 0, 1);

        if (!orderIdResults.isEmpty()) {
            String newOrderId = String.valueOf(orderIdResults.get(0)[0]);

            // Create supervision details from temp_supervision_details (matching PHP logic)
            createSupervisionDetailsFromTempExcel(newOrderId, actualAttendanceId, debugInfo);

            // Update attendance to final approved status (matching PHP: UPDATE attendance SET processing_status=4)
            String updateSql = "UPDATE attendance SET processing_status=4, approval_comments=:comments " +
                              "WHERE meeting_code=:meetingCode AND processing_status='1' AND attendance.msisdn=:telephone";

            Map<String, Object> updateParams = new HashMap<>();
            updateParams.put("comments", comments);
            updateParams.put("meetingCode", cell);
            updateParams.put("telephone", telephone);

            crudService.executeNativeQuery(updateSql, updateParams);

            // Audit action (matching PHP: auditAction("UPDATED", "Updated attendance processing status"))
            log.info("AUDIT: Updated attendance processing status to APPROVED for MSISDN: {} in meeting: {}",
                    telephone, cell);

        } else {
            log.error("Failed to create API order for MSISDN: {} in meeting: {}", telephone, cell);
        }
    }

    /**
     * Create supervision details from temp_supervision_details (matching PHP logic)
     */
    private void createSupervisionDetailsFromTempExcel(String newOrderId, String attendanceId,
                                                      StringBuilder debugInfo) throws Exception {

        // List of titles to process (matching PHP logic)
        String[] titles = {"Per Diem", "Transport/Fuel", "Lunch", "Extra Per Diem", "Others"};

        for (String title : titles) {
            // Insert supervision_details from temp_supervision_details (matching PHP queries)
            String insertQuery = "INSERT INTO supervision_details(apiorder_id, expense_id, title, credit, original_amount, url) " +
                               "SELECT :newOrderId AS apiorder_id, expense_id, title, credit, original_amount, url " +
                               "FROM temp_supervision_details " +
                               "WHERE apiorder_id=:attendanceId AND title=:title";

            Map<String, Object> params = new HashMap<>();
            params.put("newOrderId", newOrderId);
            params.put("attendanceId", attendanceId);
            params.put("title", title);

            // Update debug info
            debugInfo.append(insertQuery).append(" ");

            crudService.executeNativeQuery(insertQuery, params);

            // Audit action (matching PHP: auditAction("CREATED", "Supervision details"))
            log.info("AUDIT: Created supervision detail: {} for order: {}", title, newOrderId);
        }
    }

    private ApiResponse processPaymentsInbuildPayment(JSONObject jsonData, MultipartFile[] files, User apiUser, HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseMessage("Error occurred")
                .responseCode(ApiResponseCode.FAIL)
                .build();

        try {
            // Parse the JSON data from POST
            String jsonString = jsonData.getString("jsondata");
            JSONArray arr = new JSONArray(jsonString);

            int countx = 0;
            String requestTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String batchNo = "TIBU" + System.currentTimeMillis();
            int orgId = (int) servletRequest.getSession().getAttribute("org_o_id");
            UserDetailsImpl userDetails = (UserDetailsImpl) SecurityContextHolder.getContext()
                    .getAuthentication()
                    .getPrincipal();
            int userId = Math.toIntExact(userDetails.getId());
            int initiatorId = userId; // assuming User has getId()
            String initiatorUsername = apiUser.getLogin(); // assuming User has getLogin()
            String sourceIp = servletRequest.getRemoteAddr();

            for (int i = 0; i < arr.length(); i++) {
                JSONArray x = arr.getJSONArray(i);

                String firstName = x.getString(0);
                String middleName = x.getString(1);
                String lastName = x.getString(2);
                String phoneNumber = x.getString(3).replaceAll("[ +]", "");
                String amount = x.getString(4);
                String jobGroup = x.getString(5);
                String province = x.getString(6);
                String county = x.getString(7);
                String district = x.getString(8);
                String paymentType = x.getString(9);
                String budget = x.getString(10);
                String notes = x.getString(11);
                int treatmentModel = 3;

                // Resolve FK values
                String ordertypeQuery = "SELECT id FROM ordertype WHERE title=:title LIMIT 1";
                String budgetQuery = "SELECT id FROM budget WHERE title=:title LIMIT 1";
                String districtQuery = "SELECT id FROM district WHERE title=:title LIMIT 1";
                String countyQuery = "SELECT id FROM county WHERE title=:title LIMIT 1";
                String provinceQuery = "SELECT id FROM province WHERE title=:title LIMIT 1";

                Map<String, Object> param = Map.of("title", paymentType);
                List<Object[]> ordertypeResult = crudService.fetchWithNativeQuery(ordertypeQuery, param, 0, 1);
                Integer ordertypeId = ordertypeResult.isEmpty() ? null : Integer.parseInt(ordertypeResult.get(0)[0].toString());

                param = Map.of("title", budget);
                List<Object[]> budgetResult = crudService.fetchWithNativeQuery(budgetQuery, param, 0, 1);
                Integer budgetId = budgetResult.isEmpty() ? null : Integer.parseInt(budgetResult.get(0)[0].toString());

                param = Map.of("title", district);
                List<Object[]> districtResult = crudService.fetchWithNativeQuery(districtQuery, param, 0, 1);
                Integer districtId = districtResult.isEmpty() ? null : Integer.parseInt(districtResult.get(0)[0].toString());

                param = Map.of("title", county);
                List<Object[]> countyResult = crudService.fetchWithNativeQuery(countyQuery, param, 0, 1);
                Integer countyId = countyResult.isEmpty() ? null : Integer.parseInt(countyResult.get(0)[0].toString());

                param = Map.of("title", province);
                List<Object[]> provinceResult = crudService.fetchWithNativeQuery(provinceQuery, param, 0, 1);
                Integer provinceId = provinceResult.isEmpty() ? null : Integer.parseInt(provinceResult.get(0)[0].toString());

                Map<String, Object> insertParams = new HashMap<>();
                insertParams.put("treatment_model", treatmentModel);
                insertParams.put("initiator_id", initiatorId);
                insertParams.put("initiator_username", initiatorUsername);
                insertParams.put("firstname", firstName);
                insertParams.put("middlename", middleName);
                insertParams.put("lastname", lastName);
                insertParams.put("ordertype", ordertypeId);
                insertParams.put("budget", budgetId);
                insertParams.put("credit", amount);
                insertParams.put("msisdn", phoneNumber);
                insertParams.put("notes", notes);
                insertParams.put("district", districtId);
                insertParams.put("county", countyId);
                insertParams.put("province", provinceId);
                insertParams.put("request_src", "web");
                insertParams.put("requesttime", requestTime);
                insertParams.put("approval_level", 0);
                insertParams.put("approval_status", "Pending");
                insertParams.put("sourceip", sourceIp);
                insertParams.put("batchno", batchNo);
                insertParams.put("org_id", orgId);

                String insertQuery = """
                INSERT INTO apiorder(treatment_model, initiator_id, initiator_username, firstname, middlename, lastname, ordertype, budget, credit, msisdn, notes, district, county, province, request_src, requesttime, approval_level, approval_status, sourceip, batchno, org_id)
                VALUES (:treatment_model, :initiator_id, :initiator_username, :firstname, :middlename, :lastname, :ordertype, :budget, :credit, :msisdn, :notes, :district, :county, :province, :request_src, :requesttime, :approval_level, :approval_status, :sourceip, :batchno, :org_id)
            """;

                crudService.executeNativeQuery(insertQuery, insertParams);

                // Get latest inserted order
                String orderQuery = "SELECT id, msisdn, credit, driver_amount, driver_phone, recipient2credit, recipient2msisdn FROM apiorder WHERE requesttime=:requesttime AND msisdn=:msisdn ORDER BY id DESC LIMIT 1";
                Map<String, Object> orderQueryParams = Map.of("requesttime", requestTime, "msisdn", phoneNumber);
                List<Object[]> orderRow = crudService.fetchWithNativeQuery(orderQuery, orderQueryParams, 0, 1);

                if (!orderRow.isEmpty()) {
                    Object[] row = orderRow.get(0);
                    HashMap<String, String> map = new HashMap<>();
                    map.put("id", row[0].toString());
                    map.put("msisdn", row[1].toString());
                    map.put("credit", row[2].toString());
                    map.put("driver_amount", row[3].toString());
                    map.put("driver_phone", row[4].toString());
                    map.put("recipient2credit", row[5].toString());
                    map.put("recipient2msisdn", row[6].toString());

                    // Generate transaction hash
                    //TODO chaining everywhere
                    String trxChain = sharedFunctions.updateChainTrx(null, map, (String) row[0], null);

                    // Insert into TRX_VALUES
                    Map<String, Object> trxParams = Map.of("order_id", row[0], "t_value", trxChain);
                    String trxInsert = "INSERT INTO TRX_VALUES(ORDER_ID, T_VALUE) VALUES(:order_id, :t_value)";
                    crudService.executeNativeQuery(trxInsert, trxParams);

                    sharedFunctions.auditAction("CREATED", "Created new order transaction id", String.valueOf(row[0]), sourceIp, null, null,null);
                }

                countx++;
            }

            apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
            apiResponse.setResponseMessage(countx + " Payments have been successfully uploaded.");

        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error in processPaymentsInbuildPayment: {}", e.getMessage());
        }

        return apiResponse;
    }
    private ApiResponse processStageApproval(JSONObject jsonData, MultipartFile[] files, User apiUser, HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseMessage("Error occurred")
                .responseCode(ApiResponseCode.FAIL)
                .build();
        try {
            // Get the cell ID (assumed to be passed in JSON under "cell")
            int cellId = jsonData.getInt("cell");

            // Optional: log to file (equivalent to file_put_contents("stage.txt", $cell, FILE_APPEND);)
//            try (FileWriter fw = new FileWriter("/tmp/stage.txt", true)) {
//                fw.write(cellId + "\n");
//            } catch (IOException ioe) {
//                log.warn("Unable to write to stage.txt: {}", ioe.getMessage());
//            }

            // Run the update query
            String updateQuery = "UPDATE apiorder SET picked_status = 0 WHERE ID = :id";
            Map<String, Object> params = new HashMap<>();
            params.put("id", cellId);
            crudService.executeNativeQuery(updateQuery, params);

            // Set session attribute
            servletRequest.getSession().setAttribute("notes", "Payment re-staged for approval");

            // Return success
            apiResponse.setResponseMessage("Payment re-staged for approval.");
            apiResponse.setResponseCode(ApiResponseCode.SUCCESS);

        } catch (Exception e) {
            e.printStackTrace();
            log.error("An error occurred: {}", e.getMessage());
        }
        return apiResponse;
    }

    private ApiResponse processOrder(JSONObject jsonData, byte[] decryptedFileBytes, User apiUser, HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseMessage("Error occurred")
                .responseCode(ApiResponseCode.FAIL)
                .build();
        try {
            String action = jsonData.getString("action");
            String cellId = null;
            if (jsonData.has("cell") && !jsonData.isNull("cell")) {
                cellId = jsonData.getString("cell");
            }
            String remoteIp = servletRequest.getRemoteAddr();
            UserDetailsImpl userDetails = (UserDetailsImpl) SecurityContextHolder.getContext()
                    .getAuthentication()
                    .getPrincipal();
            int userId = Math.toIntExact(userDetails.getId());
            //String remoteIp = servletRequest.getRemoteAddr();
            String batchNo = "TIBU" + System.currentTimeMillis();


            if ("delete".equalsIgnoreCase(action)) {
                // DELETE ACTION
                String deleteQuery = "UPDATE apiorder SET intrash = 'YES' WHERE id = :id";
                Map<String, Object> params = new HashMap<>();
                params.put("id", Integer.parseInt(cellId));
                crudService.executeNativeQuery(deleteQuery, params);

                sharedFunctions.auditAction("DELETE", "Deleted payment id: " + cellId, remoteIp, jsonData.toString(), null, null, null);
                log.info("Deleted payment with ID: {}", cellId);

                apiResponse.setResponseMessage("Payment deleted successfully");
                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                return apiResponse;

            }
            else if (cellId != null && !cellId.isEmpty()) {
                // EDIT ACTION
                String beneficiaryId = jsonData.getString("beneficiary");

                // 1. Fetch beneficiary details
                String benQuery = "SELECT b.firstname, b.middlename, b.lastname, CONCAT(b.firstname, ' ', b.middlename, ' ', b.lastname) AS full_names, " +
                        "b.msisdn, b.beneficiarytype, f.title AS facility, b.district, b.county, b.province " +
                        "FROM beneficiary b LEFT JOIN facility f ON f.id = b.facility WHERE b.id = :benId";
                Map<String, Object> benParams = new HashMap<>();
                benParams.put("benId", Integer.parseInt("22"));
                List<Object[]> benResult = crudService.fetchWithNativeQuery(benQuery, benParams, 0, 1);

                if (benResult.isEmpty()) {
                    log.error("Beneficiary not found for ID: {}", beneficiaryId);
                    return apiResponse;
                }

                Object[] ben = benResult.get(0);

                // 2. Get initiator info
                List<String> claimant = crudService.fetchWithNativeQuery("SELECT login FROM user WHERE id = :uid",
                        Map.of("uid", userId), 0, 1);

                String initiatorUsername = (claimant.isEmpty()) ? "unknown" : String.valueOf(claimant.get(0));

                // 3. Fetch record before update

//                List<Object[]> record1 = crudService.fetchWithNativeQuery(
//                        "SELECT * FROM apiorder WHERE id = :id", Map.of("id", Integer.parseInt(cellId)), 0, 1);
                String record1 = sharedFunctions.getRecordId("apiorder", String.valueOf(cellId));
                // 4. Perform the update
                String updateQuery = "UPDATE apiorder SET initiator_username = :username, firstname = :firstname, middlename = :middlename, lastname = :lastname, " +
                        "beneficiarytype = :beneficiarytype, ordertype = :ordertype, budget = :budget, credit = :credit, msisdn = :msisdn, notes = :notes, " +
                        "facility = :facility, district = :district, county = :county, province = :province, approval_level = '0', approval_status = 'Pending', " +
                        "sourceip = :ip WHERE id = :id";

                Map<String, Object> updateParams = new HashMap<>();
                updateParams.put("username", initiatorUsername);
                updateParams.put("firstname", ben[0]);
                updateParams.put("middlename", ben[1]);
                updateParams.put("lastname", ben[2]);
                updateParams.put("beneficiarytype", ben[5]);
                updateParams.put("ordertype", jsonData.getString("ordertype"));
                updateParams.put("budget", jsonData.getString("budget"));
                updateParams.put("credit", jsonData.getString("amount"));
                updateParams.put("msisdn", ben[4]);
                updateParams.put("notes", jsonData.getString("notes"));
                updateParams.put("facility", ben[6]);
                updateParams.put("district", ben[7]);
                updateParams.put("county", ben[8]);
                updateParams.put("province", ben[9]);
                updateParams.put("ip", remoteIp);
                updateParams.put("id", Integer.parseInt(cellId));

                crudService.executeNativeQuery(updateQuery, updateParams);

                // 5. Fetch record after update
//                List<Object[]> record2 = crudService.fetchWithNativeQuery(
//                        "SELECT * FROM apiorder WHERE id = :id", Map.of("id", Integer.parseInt(cellId)), 0, 1);
                String record2 = sharedFunctions.getRecordId("apiorder", String.valueOf(cellId));

                // 6. Generate change log
                String logString = sharedFunctions.returnLogString(record1, record2);

                // 7. Audit log
                String fullName = ben[0] + " " + ben[1] + " " + ben[2];
                sharedFunctions.auditAction("EDIT", "Updated payment id: " + cellId + " for " + fullName + " (" + logString + ")", remoteIp, jsonData.toString(), null, null, null);

                log.info("Successfully updated payment ID: {} for {}", cellId, fullName);

                apiResponse.setResponseMessage("Payment updated successfully");
                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
            }
            else {
                if (decryptedFileBytes == null || decryptedFileBytes.length == 0) {
                    apiResponse.setResponseMessage("No file uploaded or file decryption failed.");
                    return apiResponse;
                }
                String selectedProvince = jsonData.optString("province", "");
                String selectedCounty = jsonData.optString("county", "");
                String selectedConstituency = jsonData.optString("district", "");

                String provinceTitle = "";
                String countyTitle = "";
                String constituencyTitle = "";

                Map<String, Object> params = new HashMap<>();

                if (!selectedProvince.isEmpty()) {
                    String provinceQuery = "SELECT title FROM province WHERE id = :provinceId";
                    params.put("provinceId", selectedProvince);
                    List<?> result1 = crudService.fetchWithNativeQuery(provinceQuery, params, 0, 1);
                    if (!result1.isEmpty()) {
                        provinceTitle = result1.get(0).toString(); // fix here
                    }
                    params.clear();
                }

                if (!selectedCounty.isEmpty()) {
                    String countyQuery = "SELECT title FROM county WHERE id = :countyId";
                    params.put("countyId", selectedCounty);
                    List<?> result2 = crudService.fetchWithNativeQuery(countyQuery, params, 0, 1);
                    if (!result2.isEmpty()) {
                       // Object[] row = (Object[]) result2.get(0);
                        countyTitle = result2.get(0).toString();
                    }
                    params.clear();
                }

                if (!selectedConstituency.isEmpty()) {
                    String constituencyQuery = "SELECT title FROM district WHERE id = :constituencyId";
                    params.put("constituencyId", selectedConstituency);
                    List<?> result3 = crudService.fetchWithNativeQuery(constituencyQuery, params, 0, 1);
                    if (!result3.isEmpty()) {
                        //Object[] row = (Object[]) result3.get(0);
                        constituencyTitle = result3.get(0).toString();
                    }
                    params.clear();
                }


                // Read Excel content
                InputStream fis = new ByteArrayInputStream(decryptedFileBytes);
                Workbook workbook = WorkbookFactory.create(fis);
                Sheet sheet = workbook.getSheetAt(0);

                int totalRows = sheet.getPhysicalNumberOfRows();
                boolean containsDecimal = false;
                boolean containsInvalidPhone = false;

                for (int i = 1; i < totalRows; i++) {
                    Row row = sheet.getRow(i);
                    if (row == null) continue;

                    Cell amountCell = row.getCell(4);
                    if (amountCell != null && amountCell.getCellType() == CellType.NUMERIC) {
                        double amount = amountCell.getNumericCellValue();
                        if (amount % 1 != 0) {
                            containsDecimal = true;
                            break;
                        }
                    }

                    Cell phoneCell = row.getCell(3);
                    if (phoneCell != null) {
                        String phone = phoneCell.toString().trim().replace("+", "").replace(" ", "");
                        if (sharedFunctions.validatePhoneNumber(phone)) {
                            containsInvalidPhone = true;
                            break;
                        }
                    }
                }

                if (containsInvalidPhone) {
                    log.error("The file contains a record with an invalid phone number. The entire file has been rejected.");
                    apiResponse.setResponseMessage("The file contains an invalid phone number. Upload rejected.");
                    return apiResponse;
                }

                if (containsDecimal) {
                    log.error("The file contains an amount with a decimal point. The entire file has been rejected.");
                    apiResponse.setResponseMessage("Amount column has decimal values. Upload rejected.");
                    return apiResponse;
                }

                int successCount = 0;

                for (int i = 1; i < totalRows; i++) {
                    Row row = sheet.getRow(i);
                    if (row == null || row.getCell(0) == null || row.getCell(0).toString().trim().isEmpty()) break;

                    String firstName = row.getCell(0).toString().trim();
                    String middleName = row.getCell(1).toString().trim();
                    String lastName = row.getCell(2).toString().trim();
                    String phone = row.getCell(3).toString().trim().replace("+", "").replace(" ", "");
                    String amount = row.getCell(4).toString().trim();
                    String jobGroup = row.getCell(5) != null ? row.getCell(5).toString().trim() : "";

                    if (sharedFunctions.validatePhoneNumber(phone)) {
                        log.error("Invalid phone number: {} for {} {} {}", phone, firstName, middleName, lastName);
                        continue;
                    }
                    // Fetch the initiator_username (claimant login)
                    List<String> columnsToFetch = Arrays.asList("login", "org_id");
                    List<Object> claimantList = (List<Object>) sharedFunctions.getItems(
                            columnsToFetch,
                            "user",
                            "",
                            "WHERE id = " + userId,
                            "",
                            true
                    );
                    String claimantLogin = "";
                    Object claimantOrgId = null;

                    if (claimantList != null && claimantList.size() >= 2) {
                        claimantLogin = String.valueOf(claimantList.get(0));
                        claimantOrgId = claimantList.get(1);
                    }



                     claimantLogin = (claimantList != null && !claimantList.isEmpty()) ? String.valueOf(claimantList.get(0)) : "";

                    String treatmentModel = jsonData.optString("treatment_model", "");
                    String timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Timestamp(System.currentTimeMillis()));
                    Map<String, Object> orderParams = new HashMap<>();

                    orderParams.put("initiator_id", userId);
                    orderParams.put("initiator_username", claimantLogin);
                    orderParams.put("firstname", firstName);
                    orderParams.put("middlename", middleName);
                    orderParams.put("lastname", lastName);
                    orderParams.put("ordertype", jsonData.optString("ordertype"));
                    orderParams.put("budget", jsonData.optString("budget"));
                    orderParams.put("credit", amount);
                    orderParams.put("msisdn", phone);
                    orderParams.put("notes", jsonData.optString("notes"));
                    orderParams.put("district", jsonData.optString("district", "").isEmpty() ? constituencyTitle : jsonData.optString("district"));
                    orderParams.put("county", jsonData.optString("county", "").isEmpty() ? countyTitle : jsonData.optString("county"));
                    orderParams.put("province", jsonData.optString("province", "").isEmpty() ? provinceTitle : jsonData.optString("province"));
                    orderParams.put("request_src", "web");
                    orderParams.put("requesttime", timestamp);
                    orderParams.put("approval_level", 0);
                    orderParams.put("approval_status", "Pending");
                    orderParams.put("sourceip", remoteIp);
                    orderParams.put("batchno", batchNo);
                    orderParams.put("org_id", claimantOrgId);

                    String insertSql;

                    if (treatmentModel != null && !treatmentModel.isEmpty() && phone != null && !phone.isEmpty()) {
                        orderParams.put("treatment_model", treatmentModel);

                        insertSql = "INSERT INTO apiorder(treatment_model, initiator_id, initiator_username, firstname, middlename, lastname, ordertype, budget, credit, msisdn, notes, district, county, province, request_src, requesttime, approval_level, approval_status, sourceip, batchno, org_id) " +
                                "VALUES (:treatment_model, :initiator_id, :initiator_username, :firstname, :middlename, :lastname, :ordertype, :budget, :credit, :msisdn, :notes, :district, :county, :province, :request_src, :requesttime, :approval_level, :approval_status, :sourceip, :batchno, :org_id)";
                    } else {
                        insertSql = "INSERT INTO apiorder (initiator_id, initiator_username, firstname, middlename, lastname, ordertype, budget, credit, msisdn, notes, district, county, province, request_src, requesttime, approval_level, approval_status, sourceip, batchno, org_id) " +
                                "VALUES (:initiator_id, :initiator_username, :firstname, :middlename, :lastname, :ordertype, :budget, :credit, :msisdn, :notes, :district, :county, :province, :request_src, :requesttime, :approval_level, :approval_status, :sourceip, :batchno, :org_id)";

                        // Optionally log invalid phone or treatment model
                        log.warn("Invalid phone or missing treatment_model for {} {} {}", firstName, middleName, lastName);
                    }

                    crudService.executeNativeQuery(insertSql, orderParams);

                    sharedFunctions.auditAction("CREATE", "created new payment for " + firstName + " " + middleName + " " + lastName + " - Batch " + batchNo,
                            remoteIp, jsonData.toString(), null, userId, null);

                    // Fetch the last inserted order
                    Map<String, Object> fetchParams = new HashMap<>();
                    fetchParams.put("requesttime", timestamp);
                    fetchParams.put("msisdn", phone);

                    String fetchSql = "SELECT id, msisdn, credit, driver_amount, driver_phone, recipient2credit, recipient2msisdn " +
                            "FROM apiorder WHERE requesttime = :requesttime AND msisdn = :msisdn ORDER BY id DESC";

                    List<Object[]> orderResult = crudService.fetchWithNativeQuery(fetchSql, fetchParams, 0, 1);
                    if (!orderResult.isEmpty()) {
                        Object[] row3 = orderResult.get(0);

                        Integer orderId = (Integer) row3[0];

                        HashMap<String, String> trxMap = new HashMap<>();
                        trxMap.put("id", row3[0].toString());                 // id
                        trxMap.put("msisdn", row3[1].toString());             // msisdn
                        trxMap.put("credit", row3[2].toString());             // credit
                        trxMap.put("driver_amount", row3[3].toString());      // driver_amount
                        trxMap.put("id", row3[0] != null ? row3[0].toString() : "");
                        trxMap.put("msisdn", row3[1] != null ? row3[1].toString() : "");
                        trxMap.put("credit", row3[2] != null ? row3[2].toString() : "");
                        trxMap.put("driver_amount", row3[3] != null ? row3[3].toString() : "");

                        String trxChain = sharedFunctions.updateChainTrx(null, trxMap, String.valueOf(orderId), null);

                        // Save to TRX_VALUES
                        Map<String, Object> trxParams = new HashMap<>();
                        trxParams.put("ORDER_ID", orderId);
                        trxParams.put("T_VALUE", trxChain);

                        String trxInsert = "INSERT INTO TRX_VALUES (ORDER_ID, T_VALUE) VALUES (:ORDER_ID, :T_VALUE)";
                        crudService.executeNativeQuery(trxInsert, trxParams);


                    sharedFunctions.auditAction("CREATED", "Created new order transaction id"+ orderId, remoteIp, null, null, userId, null);
                    } else {
                        log.error("Failed to retrieve inserted order for msisdn: {} at time: {}", phone, timestamp);
                    }

                    successCount++;
                }

                Map<String, Object> params4 = new HashMap<>();
                params4.put("batchno", batchNo);

                String updateSql = """
                    UPDATE apiorder
                    SET 
                        Confirmed_Beneficairy = (
                            CASE 
                                WHEN (
                                    SELECT COUNT(*) 
                                    FROM confirmed_recipient 
                                    WHERE msisdn = apiorder.MSISDN OR msisdn = CONCAT('+', apiorder.MSISDN)
                                ) > 0 
                                THEN 'Beneficiary Confirmed' 
                                ELSE 'Beneficiary Not Confirmed' 
                            END
                        ),
                        Confirmed_Driver = (
                            CASE 
                                WHEN (
                                    SELECT COUNT(*) 
                                    FROM confirmed_recipient 
                                    WHERE msisdn = apiorder.driver_phone OR msisdn = CONCAT('+', apiorder.driver_phone)
                                ) > 0 
                                THEN CONCAT(
                                    'Confirmed Driver:(',
                                    (SELECT Full_name FROM confirmed_recipient WHERE msisdn = apiorder.driver_phone OR msisdn = CONCAT('+', apiorder.driver_phone) LIMIT 1),
                                    '(',
                                    (SELECT MSISDN FROM confirmed_recipient WHERE msisdn = apiorder.driver_phone OR msisdn = CONCAT('+', apiorder.driver_phone) LIMIT 1),
                                    '))'
                                )
                                ELSE 
                                    CASE 
                                        WHEN driver_phone IS NULL OR driver_phone = '' 
                                        THEN '' 
                                        ELSE 'Driver Not Confirmed' 
                                    END 
                            END
                        ),
                        Confirmed_DOT = (
                            CASE 
                                WHEN (
                                    SELECT COUNT(*) 
                                    FROM confirmed_recipient 
                                    WHERE msisdn = apiorder.recipient2msisdn OR msisdn = CONCAT('+', apiorder.recipient2msisdn)
                                ) > 0 
                                THEN CONCAT(
                                    'Confirmed DOT:(',
                                    (SELECT Full_name FROM confirmed_recipient WHERE msisdn = apiorder.recipient2msisdn OR msisdn = CONCAT('+', apiorder.recipient2msisdn) LIMIT 1),
                                    '(',
                                    (SELECT MSISDN FROM confirmed_recipient WHERE msisdn = apiorder.recipient2msisdn OR msisdn = CONCAT('+', apiorder.recipient2msisdn) LIMIT 1),
                                    '))'
                                )
                                ELSE 
                                    CASE 
                                        WHEN recipient2msisdn IS NULL OR recipient2msisdn = '' 
                                        THEN '' 
                                        ELSE 'DOT Not Confirmed' 
                                    END 
                            END
                        )
                    WHERE batchno = :batchno
                """;

                crudService.executeNativeQuery(updateSql, params4);


                workbook.close();
                apiResponse.setResponseMessage(successCount + " record(s) successfully imported.");
                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                return apiResponse;

            }

        }catch (Exception e){
            e.printStackTrace();
            log.error("An error occurred: {}", e.getMessage());

        }
        return apiResponse;
    }

    private ApiResponse processBeneficiaryType(JSONObject jsonData, MultipartFile[] files, User apiUser, HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseMessage("Error occurred")
                .responseCode(ApiResponseCode.FAIL)
                .build();

        try {
            String action = jsonData.optString("action", "").trim(); // e.g. "delete"
            String title = jsonData.optString("title", "").trim();
            String cell = jsonData.optString("cell", "").trim(); // equivalent to $_POST['cell']
            String remoteIp = servletRequest.getRemoteAddr();

            Map<String, Object> postData = new HashMap<>();
            // Optionally collect all jsonData keys for audit
            jsonData.keys().forEachRemaining(key -> postData.put(key, jsonData.opt(key)));

            if ("delete".equalsIgnoreCase(action)) {
                String sql = "UPDATE beneficiarytype SET intrash = 'YES' WHERE id = :id";
                Map<String, Object> params = new HashMap<>();
                params.put("id", cell);
                crudService.executeNativeQuery(sql, params);
                sharedFunctions.auditAction("DELETE", "Deleted beneficiary category id: " + cell, remoteIp, postData.toString(), null, null, null);

                apiResponse = ApiResponse.builder()
                        .responseMessage("Deleted successfully")
                        .responseCode(ApiResponseCode.SUCCESS)
                        .build();

            } else {
                if (!cell.isEmpty()) {
                    // Update existing record
                    Map<String, Object> params1 = new HashMap<>();
                    params1.put("cell", cell);
                    String record1 = sharedFunctions.getRecordById("beneficiarytype", "id = :id", params1);
                    String sql = "UPDATE beneficiarytype SET title = :title WHERE id = :id";
                    Map<String, Object> params = new HashMap<>();
                    params.put("title", title);
                    params.put("id", cell);
                    crudService.executeNativeQuery(sql, params);
                    Map<String, Object> params2 = new HashMap<>();
                    params2.put("cell", cell);
                    String record2 = sharedFunctions.getRecordById("beneficiarytype", "id = :id", params2);

                    String logString = sharedFunctions.returnLogString(record1, record2);
                    sharedFunctions.auditAction("UPDATED", "Updated beneficiary category " + title + " id: " + cell + " (" + logString + ")", remoteIp, postData.toString(), null, null, null);

                    apiResponse = ApiResponse.builder()
                            .responseMessage("Updated successfully")
                            .responseCode(ApiResponseCode.SUCCESS)
                            .build();

                } else {
                    // Insert new record
                    String sql = "INSERT INTO beneficiarytype (title, creationtime) VALUES (:title, :creationtime)";
                    Map<String, Object> params = new HashMap<>();
                    params.put("title", title);
                    params.put("creationtime", System.currentTimeMillis() / 1000); // Unix timestamp in seconds
                    crudService.executeNativeQuery(sql, params);

                    sharedFunctions.auditAction("CREATED", "Created beneficiary category " + title, remoteIp, postData.toString(), null,null, null);

                    apiResponse = ApiResponse.builder()
                            .responseMessage("Created successfully")
                            .responseCode(ApiResponseCode.SUCCESS)
                            .build();
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.error("An error occurred: {}", e.getMessage());
            apiResponse = ApiResponse.builder()
                    .responseMessage("Failed: " + e.getMessage())
                    .responseCode(ApiResponseCode.FAIL)
                    .build();
        }
        return apiResponse;
    }

    private ApiResponse processTransAdd(JSONObject jsonData, MultipartFile[] files, User apiUser, HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseMessage("An error occurred")
                .responseCode(ApiResponseCode.FAIL)
                .build();

        try {
            int rows = jsonData.optInt("rows", 0); // Equivalent to $_GET['rows']
            List<Map<String, Object>> vals = new ArrayList<>();

            for (int i = 0; i < rows; i++) {
                Map<String, Object> data = new HashMap<>();
                data.put("First_Name", "");
                data.put("Middle_Name", "");
                data.put("Last_Name", "");
                data.put("Phone_Number", "");
                data.put("Amount", 0);
                data.put("Job_Group", "");
                data.put("Meeting ID", "");
                data.put("Region", "Select Region");
                data.put("County", "Select County");
                data.put("Sub County", "Select Sub County");
                data.put("Payment_Type", "Select Payment Type");
                data.put("Budget_Line", "Select Budget Line");

                vals.add(data);
            }

            // Convert to JSON string if needed
            String dataJson = new JSONArray(vals).toString();
            log.info("data json", dataJson.toString());

            // You can store this in session if needed:
             servletRequest.getSession().setAttribute("datat2", dataJson);

            apiResponse = ApiResponse.builder()
                    .responseMessage("Data generated successfully")
                    .responseCode(ApiResponseCode.SUCCESS)
                    .jdata(vals) // <-- send the actual list, not a stringified version
                    .build();

        } catch (Exception e) {
            e.printStackTrace();
            log.error("An error occurred: {}", e.getMessage());
            apiResponse = ApiResponse.builder()
                    .responseMessage("Failed: " + e.getMessage())
                    .responseCode(ApiResponseCode.FAIL)
                    .build();
        }

        return apiResponse;
    }

    private ApiResponse processPaymentSync(JSONObject jsonData, MultipartFile[] files, User apiUser, HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("An error occurred")
                .build();

        try {
            HttpSession session = servletRequest.getSession();

            // Extract input values from jsonData
            String key = jsonData.optString("pms_id");
            String beneficiary = jsonData.optString("beneficiary");
            String initiator = jsonData.optString("initiator");
            String county = jsonData.optString("county");
            String treatmentModel = jsonData.optString("treatment_model");
            String paymentId = jsonData.optString("payment_id");
            String msisdn = jsonData.optString("msisdn");
            String budgetLine = jsonData.optString("budget_line");
            String amount = jsonData.optString("amount");
            String region = jsonData.optString("region");
            String comments = jsonData.optString("comments");

            // Check if payment already exists
            String checkSql = "SELECT * FROM synced_payments WHERE pms_id = :pms_id";
            Map<String, Object> checkParams = new HashMap<>();
            checkParams.put("pms_id", key);

            List<Map<String, Object>> result = crudService.fetchWithNativeQuery(checkSql, checkParams, 0,1);

            if (!result.isEmpty()) {
                session.setAttribute("notes", "Payment already queued for resync");
                apiResponse = ApiResponse.builder()
                        .responseCode(ApiResponseCode.SUCCESS)
                        .responseMessage("Payment already queued for resync")
                        .build();
            } else {
                // Insert new payment
                String insertSql = "INSERT INTO synced_payments (pms_id, beneficiary, initiator, county, treatment_model, payment_id, msisdn, budget_line, amount, region, status, comments) " +
                        "VALUES (:pms_id, :beneficiary, :initiator, :county, :treatment_model, :payment_id, :msisdn, :budget_line, :amount, :region, 'PENDING', :comments)";
                Map<String, Object> insertParams = new HashMap<>();
                insertParams.put("pms_id", key);
                insertParams.put("beneficiary", beneficiary);
                insertParams.put("initiator", initiator);
                insertParams.put("county", county);
                insertParams.put("treatment_model", treatmentModel);
                insertParams.put("payment_id", paymentId);
                insertParams.put("msisdn", msisdn);
                insertParams.put("budget_line", budgetLine);
                insertParams.put("amount", amount);
                insertParams.put("region", region);
                insertParams.put("comments", comments);

                crudService.executeNativeQuery(insertSql, insertParams);

                // Audit the action
                sharedFunctions.auditAction("CREATED", "Created synced payment records", servletRequest.getRemoteAddr(), jsonData.toString(), null, null,null);

                session.setAttribute("notes", "Payment successfully resynced to PMS");

                apiResponse = ApiResponse.builder()
                        .responseCode(ApiResponseCode.SUCCESS)
                        .responseMessage("Payment successfully resynced to PMS")
                        .build();
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.error("An error occurred while sync payment: {}", e.getMessage());

            apiResponse = ApiResponse.builder()
                    .responseCode(ApiResponseCode.FAIL)
                    .responseMessage("An error occurred: " + e.getMessage())
                    .build();
        }

        return apiResponse;
    }
    private ApiResponse processResubmit(JSONObject jsonData, MultipartFile[] files, User apiUser,
                                        HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseMessage("An error occurred on resubmit")
                .responseCode(ApiResponseCode.FAIL)
                .build();
        try {
            String proc = jsonData.optString("proc");
            String notes = jsonData.optString("notes", "").replace("'", "''");
            String mpesaTrxId = jsonData.optString("mpesa_trx_id", "").replace("'", "''");
            String cell = jsonData.optString("cell");
            String primaryKey = jsonData.optString("primarykey");

            if (proc.equals("purge") || proc.equals("Mark As Complete")) {
                if (!cell.isEmpty()) {
                    // Backup payment to payment_rslog
                    String insertBackupSql = "INSERT INTO payment_rslog (ID, order_id, budget_id, order_type, debit_amount, debit_commission, credit_amount, credit_commission, " +
                            "sending_charge, withdrawal_charge, MSISDN, trx_type, trx_status, trx_id, trx_date, trx_desc, trx_status_details, batch_file_path, " +
                            "recipient_name, parent_id, proposed_recipient_name, proposed_schedule_time, trx_schedule_time, batch_id, processing_mode, processed_status, " +
                            "service_provider_id, completion_date, fail_count, status, notes, sourceIP, request_date, verification_status, CONVERSATION_ID, " +
                            "ORIG_CONVERSATION_ID, INTERMEDIATE_STATUS, TIME_INITIATED, TIME_COMPLETED, RETURN_CODE, REFERENCE_ID, B2C_RETRIES, B2C_RECIPIENT_NAME, " +
                            "finalize_step, org_id, running_bal) " +
                            "SELECT ID, order_id, budget_id, order_type, debit_amount, debit_commission, credit_amount, credit_commission, sending_charge, " +
                            "withdrawal_charge, MSISDN, trx_type, trx_status, trx_id, trx_date, trx_desc, trx_status_details, batch_file_path, recipient_name, " +
                            "parent_id, proposed_recipient_name, proposed_schedule_time, trx_schedule_time, batch_id, processing_mode, processed_status, " +
                            "service_provider_id, completion_date, fail_count, status, notes, sourceIP, request_date, verification_status, CONVERSATION_ID, " +
                            "ORIG_CONVERSATION_ID, INTERMEDIATE_STATUS, TIME_INITIATED, TIME_COMPLETED, RETURN_CODE, REFERENCE_ID, B2C_RETRIES, B2C_RECIPIENT_NAME, " +
                            "finalize_step, org_id, running_bal FROM payment WHERE id = :cell";

                    Map<String, Object> paramMap = Map.of("cell", cell);
                    crudService.executeNativeQuery(insertBackupSql, paramMap);
                    String record1 = sharedFunctions.getRecordById("payment", "id = :cell", paramMap);



                    // Mark payment as completed
                    String updateSql = "UPDATE payment SET RETURN_CODE = '00', TRX_STATUS = '4', trx_status = 'Completed', " +
                            "trx_id = :trx_id, reference_id = :trx_id, TRX_DESC = :notes, TIME_COMPLETED = CURRENT_TIMESTAMP WHERE ID = :cell";

                    paramMap = Map.of("trx_id", mpesaTrxId, "notes", notes, "cell", cell);
                    crudService.executeNativeQuery(updateSql, paramMap);

                    String record2 = sharedFunctions.getRecordById("payment", "id = :cell", paramMap);

                    String logString = sharedFunctions.returnLogString(record1, record2);

                    sharedFunctions.auditAction("Transaction Diagnosis", "Transaction id " + cell + " marked as completed. (" + logString + ")",
                            servletRequest.getRemoteAddr(), jsonData.toString(), null, null, null);

                    apiResponse.setResponseMessage("Transaction has been successfully marked as completed");
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                }
            } else {
                List<String> ids = new ArrayList<>();
                if (!primaryKey.isEmpty()) {
                    ids.addAll(Arrays.asList(primaryKey.split(",")));
                    if (!ids.isEmpty() && ids.get(0).equals("all")) {
                        ids.remove(0); // remove "all"
                    }
                } else {
                    ids.add(cell);
                }

                for (String id : ids) {
                    if (id != null && !id.isEmpty()) {
                        // Backup to payment_rslog
                        String insertBackupSql = """
                                INSERT INTO payment_rslog (
                                    ID, order_id, budget_id, order_type, debit_amount, debit_commission,
                                    credit_amount, credit_commission, sending_charge, withdrawal_charge,
                                    MSISDN, trx_type, trx_status, trx_id, trx_date, trx_desc,
                                    trx_status_details, batch_file_path, recipient_name, parent_id,
                                    proposed_recipient_name, proposed_schedule_time, trx_schedule_time,
                                    batch_id, processing_mode, processed_status, service_provider_id,
                                    completion_date, fail_count, status, notes, sourceIP, request_date,
                                    verification_status, CONVERSATION_ID, ORIG_CONVERSATION_ID,
                                    INTERMEDIATE_STATUS, TIME_INITIATED, TIME_COMPLETED, RETURN_CODE,
                                    REFERENCE_ID, B2C_RETRIES, B2C_RECIPIENT_NAME, finalize_step,
                                    org_id, running_bal
                                )
                                SELECT 
                                    ID, order_id, budget_id, order_type, debit_amount, debit_commission,
                                    credit_amount, credit_commission, sending_charge, withdrawal_charge,
                                    MSISDN, trx_type, trx_status, trx_id, trx_date, trx_desc,
                                    trx_status_details, batch_file_path, recipient_name, parent_id,
                                    proposed_recipient_name, proposed_schedule_time, trx_schedule_time,
                                    batch_id, processing_mode, processed_status, service_provider_id,
                                    completion_date, fail_count, status, notes, sourceIP, request_date,
                                    verification_status, CONVERSATION_ID, ORIG_CONVERSATION_ID,
                                    INTERMEDIATE_STATUS, TIME_INITIATED, TIME_COMPLETED, RETURN_CODE,
                                    REFERENCE_ID, B2C_RETRIES, B2C_RECIPIENT_NAME, finalize_step,
                                    org_id, running_bal
                                FROM payment 
                                WHERE ID = :paymentId
                            """;
                        crudService.executeNativeQuery(insertBackupSql, Map.of("paymentId", id));

                        Map<String, Object> paramMap1 = new HashMap<>();
                        paramMap1.put("cell", id);

                        String record1 = sharedFunctions.getRecordById("payment", "id = :cell", paramMap1);

                        String updatePayment = "UPDATE payment SET TIME_INITIATED = CURRENT_TIMESTAMP, TRX_DESC = '', RETURN_CODE = '00', " +
                                "TRX_STATUS = '1', FAIL_COUNT = 0, B2C_RETRIES = (B2C_RETRIES + 1) WHERE ID = :id";
                        crudService.executeNativeQuery(updatePayment, Map.of("id", id));

                        String rollupSql = """
                                INSERT INTO temp_payment (
                                    REQUEST_ID, COMMAND_ID, RECEPIENT, SENDER, AMOUNT,
                                    TRX_STATUS, RETURN_CODE, TRX_DESC, TIME_INITIATED, TIME_COMPLETED,
                                    FAIL_COUNT, CONVERSATION_ID, ORIG_CONVERSATION_ID, SYNC_STATUS,
                                    WORKING_BAL, CHARGES_BAL, UTILITY_BAL, FLOAT_BAL, REFERENCE,
                                    STATUS_QUERY, RETRIES, TRX_TYPE, MSISDN, SYNC_STATUS_TEMP,
                                    MERCHANT_BAL, COMMISSIONS, SERVICE_CHARGE, MPESA_ID,
                                    B2C_RETRIES, B2C_RECIPIENT_NAME, RECIPIENT_CHARGES, PROGRAM_CHARGES
                                )
                                SELECT * FROM (
                                    SELECT 
                                        REQUEST_ID, COMMAND_ID, RECEPIENT, SENDER, AMOUNT,
                                        '0' AS TRX_STATUS, '0' AS RETURN_CODE, 'Resubmitted Successfully.' AS TRX_DESC,
                                        CURRENT_TIMESTAMP, NULL AS TIME_COMPLETED,
                                        FAIL_COUNT, CONVERSATION_ID, CONCAT(ORIG_CONVERSATION_ID, 'R') AS ORIG_CONVERSATION_ID,
                                        0 AS SYNC_STATUS, WORKING_BAL, CHARGES_BAL, UTILITY_BAL, FLOAT_BAL, REFERENCE,
                                        STATUS_QUERY, RETRIES, TRX_TYPE, MSISDN, SYNC_STATUS_TEMP,
                                        MERCHANT_BAL, COMMISSIONS, SERVICE_CHARGE, MPESA_ID,
                                        B2C_RETRIES, B2C_RECIPIENT_NAME, RECIPIENT_CHARGES, PROGRAM_CHARGES
                                    FROM temp_payment
                                    WHERE REQUEST_ID = :requestId
                                    ORDER BY TIME_COMPLETED DESC
                                ) t
                                LIMIT 1
                            """;

                        crudService.executeNativeQuery(rollupSql, Map.of("requestId", id));

                        Map<String, Object> paramMap2 = new HashMap<>();
                        paramMap2.put("cell", cell);
                        String record2 = sharedFunctions.getRecordById("payment", "id = :cell", paramMap2);


                        String logString = sharedFunctions.returnLogString(record1, record2);

                        sharedFunctions.auditAction("Transaction Resubmit", "Transaction id " + id + " resubmitted. (" + logString + ")",
                                servletRequest.getRemoteAddr(), jsonData.toString(), null, null, null);

                        apiResponse.setResponseMessage("Transaction has been successfully resubmitted");
                        apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    }
                }
            }

        } catch (Exception e) {
            log.error("An error occurred on resubmit: {}", e.getMessage(), e);
        }
        return apiResponse;
    }











    public ApiResponse processBeneficiaryType(JSONObject jsonData,
                                              HttpServletRequest request,
                                              HttpServletResponse response) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed to process beneficiary category")
                .build();

        try {
            String action = jsonData.optString("action", "").toLowerCase();
            String title = jsonData.optString("title", "");
            String cell = jsonData.optString("cell", "");
            String ipAddress = request.getRemoteAddr();
            String postdata = jsonData.toString();

            if ("delete".equals(action)) {
                String deleteQuery = "UPDATE beneficiarytype SET intrash = 'YES' WHERE id = :id";
                Map<String, Object> deleteParams = new HashMap<>();
                deleteParams.put("id", cell);

                int deleted = crudService.executeNativeQuery(deleteQuery, deleteParams);
                if (deleted > 0) {
                    auditAction("DELETE", "Deleted beneficiary category id: " + cell, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Beneficiary category deleted successfully");
                } else {
                    apiResponse.setResponseMessage("No record found with id: " + cell + " to delete");
                }

            } else if ("update".equals(action) && !cell.isEmpty()) {
                // UPDATE
                Map<String, Object> oldRecord = crudService.fetchRecordById("beneficiarytype", "id = '" + cell + "'");
                if (oldRecord == null) {
                    apiResponse.setResponseMessage("No record found with id: " + cell + " to update");
                    return apiResponse;
                }

                String updateQuery = "UPDATE beneficiarytype SET title = :title WHERE id = :id";
                Map<String, Object> updateParams = new HashMap<>();
                updateParams.put("title", title);
                updateParams.put("id", cell);

                int updated = crudService.executeNativeQuery(updateQuery, updateParams);
                if (updated > 0) {
                    Map<String, Object> newRecord = crudService.fetchRecordById("beneficiarytype", "id = '" + cell + "'");
                    String logString = returnLogString(oldRecord, newRecord);
                    auditAction("UPDATED", "Updated beneficiary category " + title + " id: " + cell + " (" + logString + ")", ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Beneficiary category updated successfully");
                } else {
                    apiResponse.setResponseMessage("Failed to update beneficiary category with id: " + cell);
                }

            } else {
                // Check if a record with the same title already exists
                String normalizedTitle = title.trim().toLowerCase();
                String checkQuery = "SELECT COUNT(*) FROM beneficiarytype WHERE TRIM(LOWER(title)) = :title AND (intrash IS NULL OR intrash = 'NO')";
                Map<String, Object> checkParams = new HashMap<>();
                checkParams.put("title", normalizedTitle);

                // Execute the query to check the count
                Integer count = crudService.executeNativeQueryForSingleResult(checkQuery, checkParams, Integer.class);

                if (count != null && count > 0) {
                    apiResponse.setResponseMessage("A beneficiary category with the same title already exists.");
                    return apiResponse;
                }

                // Safe to insert the new record
                String insertQuery = "INSERT INTO beneficiarytype (title, creationtime) VALUES (:title, :creationtime)";
                Map<String, Object> insertParams = new HashMap<>();
                insertParams.put("title", title);
                insertParams.put("creationtime", System.currentTimeMillis() / 1000); // UNIX time

                int inserted = crudService.executeNativeQuery(insertQuery, insertParams);
                if (inserted > 0) {
                    auditAction("CREATED", "Created beneficiary category " + title, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Beneficiary category created successfully");
                } else {
                    apiResponse.setResponseMessage("Failed to create beneficiary category");
                }
            }

        } catch (Exception e) {
            log.error("Error processing beneficiary category: {}", e.getMessage(), e);
            apiResponse.setResponseMessage("An error occurred while processing the request: " + e.getMessage());
        }

        return apiResponse;
    }

    public ApiResponse processCounty(JSONObject jsonData,
                                     HttpServletRequest request,
                                     HttpServletResponse response) {

        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed to process county")
                .build();

        try {
            String action = jsonData.optString("action", "").toLowerCase();
            String title = jsonData.optString("title", "").trim();
            String cell = jsonData.optString("cell", "").trim(); // Assuming this is the county ID
            String ipAddress = request.getRemoteAddr();
            String postData = jsonData.toString();

            if ("delete".equals(action) && !cell.isEmpty()) {
                // Soft delete
                String deleteQuery = "UPDATE county SET intrash = 'YES' WHERE id = :id";
                Map<String, Object> deleteParams = new HashMap<>();
                deleteParams.put("id", cell);

                int deleted = crudService.executeNativeQuery(deleteQuery, deleteParams);
                log.debug("Executing delete query: {}", deleteQuery);
                log.debug("With parameters: {}", deleteParams);

                if (deleted > 0) {
                    auditAction("DELETE", "Deleted county id: " + cell, ipAddress, postData);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("County deleted successfully");
                }
            } else if ("update".equals(action) && !cell.isEmpty()) {
                // Update
                Map<String, Object> oldRecord = crudService.fetchRecordById("county", "id = '" + cell + "'");

                // Check for duplicate title
                String checkUpdateQuery = "SELECT COUNT(*) FROM county WHERE title = :title AND id != :id AND intrash != 'YES'";
                Map<String, Object> checkUpdateParams = new HashMap<>();
                checkUpdateParams.put("title", title);
                checkUpdateParams.put("id", cell);

                Long count = crudService.executeCountQuery(checkUpdateQuery, checkUpdateParams);
                if (count != null && count > 0) {
                    apiResponse.setResponseCode(ApiResponseCode.FAIL);
                    apiResponse.setResponseMessage("County already exists with title: " + title);
                    return apiResponse;
                }

                String updateQuery = "UPDATE county SET title = :title WHERE id = :id";
                Map<String, Object> updateParams = new HashMap<>();
                updateParams.put("title", title);
                updateParams.put("id", cell);

                int updated = crudService.executeNativeQuery(updateQuery, updateParams);
                if (updated > 0) {
                    Map<String, Object> newRecord = crudService.fetchRecordById("county", "id = '" + cell + "'");
                    String logString = returnLogString(oldRecord, newRecord);
                    auditAction("UPDATE", "Updated county title to '" + title + "' (id: " + cell + "). Changes: " + logString, ipAddress, postData);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("County updated successfully");
                }
            }
 else {
                // Insert with duplicate check
                String checkQuery = "SELECT COUNT(*) FROM county WHERE title = :title AND intrash != 'YES'";
                Map<String, Object> checkParams = new HashMap<>();
                checkParams.put("title", title);

                Long count = crudService.executeCountQuery(checkQuery, checkParams);
                if (count != null && count > 0) {
                    apiResponse.setResponseCode(ApiResponseCode.FAIL);
                    apiResponse.setResponseMessage("County already exists with title: " + title);
                    return apiResponse;
                }

                // Insert new record
                String insertQuery = "INSERT INTO county (title, creationtime) VALUES (:title, :creationtime)";
                Map<String, Object> insertParams = new HashMap<>();
                insertParams.put("title", title);
                insertParams.put("creationtime", System.currentTimeMillis() / 1000);

                int inserted = crudService.executeNativeQuery(insertQuery, insertParams);
                if (inserted > 0) {
                    auditAction("CREATE", "Created county: " + title, ipAddress, postData);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("County created successfully");
                }
            }

        } catch (Exception e) {
            log.error("Error processing county: {}", e.getMessage(), e);
            apiResponse.setResponseMessage("An unexpected error occurred: " + e.getMessage());
        }

        return apiResponse;
    }

    public ApiResponse processDistrict(JSONObject jsonData,
                                       HttpServletRequest request,
                                       HttpServletResponse response) {

        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed to process Sub county")
                .build();

        try {
            String action = jsonData.optString("action", "").trim().toLowerCase();
            String title = jsonData.optString("title", "").trim();
            String province = jsonData.optString("province", "").trim();
            String countyId = jsonData.optString("county", "").trim();
            String cell = jsonData.optString("cell", "").trim(); // District ID if updating/deleting
            String ipAddress = request.getRemoteAddr();
            String postData = jsonData.toString();

            // DELETE (Skip validation)
            if ("delete".equals(action) && !cell.isEmpty()) {
                String deleteQuery = "UPDATE district SET intrash = 'YES' WHERE id = :id";
                Map<String, Object> deleteParams = new HashMap<>();
                deleteParams.put("id", cell);

                int deleted = crudService.executeNativeQuery(deleteQuery, deleteParams);
                if (deleted > 0) {
                    auditAction("DELETE", "Deleted district ID: " + cell, ipAddress, postData);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Sub County deleted successfully");
                }
                return apiResponse;
            }

            // Validate input only for insert/update
            if (title.isEmpty()) {
                apiResponse.setResponseMessage("Sub County title is required.");
                return apiResponse;
            }

            if (province.isEmpty() || !province.matches("\\d+")) {
                apiResponse.setResponseMessage("Valid Province ID is required.");
                return apiResponse;
            }

            if (!countyId.isEmpty() && !countyId.matches("\\d+")) {
                apiResponse.setResponseMessage("Invalid county ID.");
                return apiResponse;
            }

            // UPDATE
            if ("update".equals(action) && !cell.isEmpty()) {
                Map<String, Object> oldRecord = crudService.fetchRecordById("district", "id = '" + cell + "'");

                // Check for duplicate title (excluding self)
                String checkQuery = "SELECT COUNT(*) FROM district WHERE title = :title AND id != :id AND intrash != 'YES'";
                Map<String, Object> checkParams = new HashMap<>();
                checkParams.put("title", title);
                checkParams.put("id", cell);
                Long count = crudService.executeCountQuery(checkQuery, checkParams);
                if (count != null && count > 0) {
                    apiResponse.setResponseMessage("Sub County with this title already exists.");
                    return apiResponse;
                }

                String updateQuery = "UPDATE district SET title = :title, province = :province, county_id = :countyId WHERE id = :id";
                Map<String, Object> updateParams = new HashMap<>();
                updateParams.put("title", title);
                updateParams.put("province", province);
                updateParams.put("countyId", countyId.isEmpty() ? null : countyId);
                updateParams.put("id", cell);

                int updated = crudService.executeNativeQuery(updateQuery, updateParams);
                if (updated > 0) {
                    Map<String, Object> newRecord = crudService.fetchRecordById("district", "id = '" + cell + "'");
                    String logString = returnLogString(oldRecord, newRecord);
                    auditAction("UPDATE", "Updated sub county '" + title + "' (ID: " + cell + "). Changes: " + logString, ipAddress, postData);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Sub County updated successfully");
                }
            }
            // INSERT
            else {
                String checkQuery = "SELECT COUNT(*) FROM district WHERE title = :title AND intrash != 'YES'";
                Map<String, Object> checkParams = new HashMap<>();
                checkParams.put("title", title);
                Long count = crudService.executeCountQuery(checkQuery, checkParams);

                if (count != null && count > 0) {
                    apiResponse.setResponseMessage("District with this title already exists.");
                    return apiResponse;
                }

                String insertQuery = "INSERT INTO district (title, province, county_id, creationtime) " +
                        "VALUES (:title, :province, :countyId, :creationtime)";
                Map<String, Object> insertParams = new HashMap<>();
                insertParams.put("title", title);
                insertParams.put("province", province);
                insertParams.put("countyId", countyId.isEmpty() ? null : countyId);
                insertParams.put("creationtime", System.currentTimeMillis() / 1000);

                int inserted = crudService.executeNativeQuery(insertQuery, insertParams);
                if (inserted > 0) {
                    auditAction("CREATE", "Created district: " + title, ipAddress, postData);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("District created successfully");
                }
            }

        } catch (Exception e) {
            log.error("Error processing district: {}", e.getMessage(), e);
            apiResponse.setResponseMessage("An unexpected error occurred: " + e.getMessage());
        }

        return apiResponse;
    }


    public ApiResponse processFacility(JSONObject jsonData,
                                       HttpServletRequest request,
                                       HttpServletResponse response) {

        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed to process facility")
                .build();

        try {
            String action = jsonData.optString("action", "").toLowerCase();
            String title = jsonData.optString("title", "");
            String province = jsonData.optString("province", "");
            String district = jsonData.optString("district", "");
            String cell = jsonData.optString("cell", "");
            String ipAddress = request.getRemoteAddr();
            String postdata = jsonData.toString();

            if ("delete".equals(action)) {
                String deleteQuery = "UPDATE facility SET intrash = 'YES' WHERE id = :id";
                Map<String, Object> deleteParams = new HashMap<>();
                deleteParams.put("id", cell);

                int deleted = crudService.executeNativeQuery(deleteQuery, deleteParams);
                if (deleted > 0) {
                    auditAction("DELETE", "Deleted facility id: " + cell, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Facility deleted successfully");
                    return apiResponse;
                }
                apiResponse.setResponseCode(ApiResponseCode.FAIL);
                apiResponse.setResponseMessage("Facility could not be deleted");
                return apiResponse;

            }if ("update".equals(action) && !cell.isEmpty())  {
                // UPDATE
                Map<String, Object> oldRecord = crudService.fetchRecordById("facility", "id = '" + cell + "'");

                String updateQuery = "UPDATE facility SET title = :title, province = :province, district = :district WHERE id = :id";
                Map<String, Object> updateParams = new HashMap<>();
                updateParams.put("title", title);
                updateParams.put("province", province);
                updateParams.put("district", district);
                updateParams.put("id", cell);

                int updated = crudService.executeNativeQuery(updateQuery, updateParams);
                if (updated > 0) {
                    Map<String, Object> newRecord = crudService.fetchRecordById("facility", "id = '" + cell + "'");
                    String logString = returnLogString(oldRecord, newRecord);
                    auditAction("UPDATED", "Updated facility " + title + " id: " + cell + " (" + logString + ")", ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Facility updated successfully");
                    return apiResponse;
                }
                apiResponse.setResponseCode(ApiResponseCode.FAIL);
                apiResponse.setResponseMessage("Facility could not be updated");
                return apiResponse;

            } else {
                // INSERT
                String insertQuery = "INSERT INTO facility (title, province, district, creationtime) " +
                        "VALUES ( :title, :province, :district, :creationtime)";
                Map<String, Object> insertParams = new HashMap<>();
                insertParams.put("title", title);
                insertParams.put("province", province);
                insertParams.put("district", district);
                insertParams.put("creationtime", System.currentTimeMillis() / 1000); // Unix timestamp

                int inserted = crudService.executeNativeQuery(insertQuery, insertParams);
                if (inserted > 0) {
                    auditAction("CREATED", "Created facility " + title + " id: " + cell, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Facility created successfully");
                    return apiResponse;
                }
                apiResponse.setResponseCode(ApiResponseCode.FAIL);
                apiResponse.setResponseMessage("Facility could not be created");
                return apiResponse;

            }

        } catch (Exception e) {
            log.error("Error processing facility: {}", e.getMessage(), e);
        }

        return apiResponse;
    }
    public ApiResponse processFinancier(JSONObject jsonData,
                                        HttpServletRequest request,
                                        HttpServletResponse response) {

        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed to process financier")
                .build();

        try {
            String action = jsonData.optString("action", "").toLowerCase();
            String title = jsonData.optString("title", "").trim();
            String code = jsonData.optString("code", "").trim();
            String cell = jsonData.optString("cell", "").trim();
            String ipAddress = request.getRemoteAddr();
            String postdata = jsonData.toString();

            log.info("RECEIVED FINANCIER REQUEST: {}", postdata);

            if ("delete".equals(action)) {
                // DELETE logic
                String deleteQuery = "UPDATE financier SET intrash = 'YES' WHERE id = :id";
                Map<String, Object> deleteParams = new HashMap<>();
                deleteParams.put("id", cell);

                int deleted = crudService.executeNativeQuery(deleteQuery, deleteParams);
                if (deleted > 0) {
                    auditAction("DELETE", "Deleted financier id: " + cell, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Financier deleted successfully");
                    return apiResponse;
                }
                apiResponse.setResponseCode(ApiResponseCode.FAIL);
                apiResponse.setResponseMessage("Financier could not be deleted");
                return apiResponse;

            }if ("update".equals(action) && !cell.isEmpty())  {
                // UPDATE logic
                Map<String, Object> oldRecord = crudService.fetchRecordById("financier", "id = '" + cell + "'");

                String updateQuery = "UPDATE financier SET title = :title, code = :code WHERE id = :id";
                Map<String, Object> updateParams = new HashMap<>();
                updateParams.put("title", title);
                updateParams.put("code", code);
                updateParams.put("id", cell);

                int updated = crudService.executeNativeQuery(updateQuery, updateParams);
                if (updated > 0) {
                    Map<String, Object> newRecord = crudService.fetchRecordById("financier", "id = '" + cell + "'");
                    String logString = returnLogString(oldRecord, newRecord);
                    auditAction("UPDATED", "Updated financier " + title + " id: " + cell + " (" + logString + ")", ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Financier updated successfully");
                    return apiResponse;
                }
                apiResponse.setResponseCode(ApiResponseCode.FAIL);
                apiResponse.setResponseMessage("Financier could not be updated");
                return apiResponse;

            } else {
                // Duplicate check before insert
                String checkQuery = "SELECT COUNT(*) FROM financier WHERE (title = :title OR code = :code) AND intrash != 'YES'";
                Map<String, Object> checkParams = new HashMap<>();
                checkParams.put("title", title);
                checkParams.put("code", code);
                int count = (int) crudService.fetchCount(checkQuery, checkParams);

                if (count > 0) {
                    apiResponse.setResponseCode(ApiResponseCode.FAIL);
                    apiResponse.setResponseMessage("Financier with this title or code already exists.");
                    return apiResponse;
                }

                // INSERT logic
                String insertQuery = "INSERT INTO financier (title, code, creationtime) VALUES (:title, :code, :creationtime)";
                Map<String, Object> insertParams = new HashMap<>();
                insertParams.put("title", title);
                insertParams.put("code", code);
                insertParams.put("creationtime", System.currentTimeMillis() / 1000); // Unix timestamp

                int inserted = crudService.executeNativeQuery(insertQuery, insertParams);
                if (inserted > 0) {
                    auditAction("CREATED", "Created financier: " + title, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Financier created successfully");
                }
                apiResponse.setResponseCode(ApiResponseCode.FAIL);
                apiResponse.setResponseMessage("Financier could not be created");
                return apiResponse;
            }

        } catch (Exception e) {
            log.error("Error processing financier: {}", e.getMessage(), e);
        }

        return apiResponse;
    }

    public ApiResponse processOrderType(JSONObject jsonData,
                                        HttpServletRequest request,
                                        HttpServletResponse response) {

        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed to process order type")
                .build();

        try {
            String action = jsonData.optString("action", "").toLowerCase();
            String title = jsonData.optString("title", "");
            String orgId = jsonData.optString("organisation", "");
            String shareble = jsonData.optString("shareble", "");
            String cell = jsonData.optString("cell", "");
            String ipAddress = request.getRemoteAddr();
            String postdata = jsonData.toString();

            if ("delete".equals(action)) {
                String deleteQuery = "UPDATE ordertype SET intrash = 'YES' WHERE id = :id";
                Map<String, Object> deleteParams = new HashMap<>();
                deleteParams.put("id", cell);

                int deleted = crudService.executeNativeQuery(deleteQuery, deleteParams);
                if (deleted > 0) {
                    auditAction("DELETE", "Deleted payment type id: " + cell, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Order type deleted successfully");
                    return apiResponse;
                }
                apiResponse.setResponseCode(ApiResponseCode.FAIL);
                apiResponse.setResponseMessage("Could not delete order type");
                return apiResponse;

            } if ("update".equals(action) && !cell.isEmpty()) {
                // UPDATE
                Map<String, Object> oldRecord = crudService.fetchRecordById("ordertype", "id = '" + cell + "'");

                String updateQuery = "UPDATE ordertype SET title = :title, org_id = :orgId, shareble = :shareble WHERE id = :id";
                Map<String, Object> updateParams = new HashMap<>();
                updateParams.put("title", title);
                updateParams.put("orgId", orgId);
                updateParams.put("shareble", shareble);
                updateParams.put("id", cell);

                int updated = crudService.executeNativeQuery(updateQuery, updateParams);
                if (updated > 0) {
                    Map<String, Object> newRecord = crudService.fetchRecordById("ordertype", "id = '" + cell + "'");
                    String logString = returnLogString(oldRecord, newRecord);
                    auditAction("UPDATED", "Updated payment type " + title + " id: " + cell + " (" + logString + ")", ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Order type updated successfully");
                    return apiResponse;
                }
                apiResponse.setResponseCode(ApiResponseCode.FAIL);
                apiResponse.setResponseMessage("Order type could not be deleted");
                return apiResponse;

            } else {
                // INSERT
                String insertQuery = "INSERT INTO ordertype ( title, creationtime, org_id, shareble) " +
                        "VALUES ( :title, :creationtime, :orgId, :shareble)";
                Map<String, Object> insertParams = new HashMap<>();
                insertParams.put("title", title);
                insertParams.put("creationtime", System.currentTimeMillis() / 1000); // Unix timestamp
                insertParams.put("orgId", orgId);
                insertParams.put("shareble", shareble);

                int inserted = crudService.executeNativeQuery(insertQuery, insertParams);
                if (inserted > 0) {
                    auditAction("CREATED", "Created payment type " + title + " id: " + cell, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Order type created successfully");
                    return apiResponse;
                }
                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                apiResponse.setResponseMessage("Order type could not be created");
                return apiResponse;
            }

        } catch (Exception e) {
            log.error("Error processing order type: {}", e.getMessage(), e);
        }

        return apiResponse;
    }
    public ApiResponse processProvince(JSONObject jsonData,
                                       HttpServletRequest request,
                                       HttpServletResponse response) {

        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed to process province")
                .build();

        try {
            String action = jsonData.optString("action", "").toLowerCase();
            String title = jsonData.optString("title", "").trim();
            String cell = jsonData.optString("cell", "").trim();
            String ipAddress = request.getRemoteAddr();
            String postdata = jsonData.toString();

            log.info("RECEIVED PROVINCE REQUEST: {}", postdata);

            if ("delete".equals(action)) {
                // DELETE logic
                String deleteQuery = "UPDATE province SET intrash = 'YES' WHERE id = :id";
                Map<String, Object> deleteParams = new HashMap<>();
                deleteParams.put("id", cell);

                int deleted = crudService.executeNativeQuery(deleteQuery, deleteParams);
                if (deleted > 0) {
                    auditAction("DELETE", "Deleted province id: " + cell, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Region deleted successfully");
                    return apiResponse;
                }
                apiResponse.setResponseCode(ApiResponseCode.FAIL);
                apiResponse.setResponseMessage("Region could not be deleted");
                return apiResponse;

            } else if ("update".equals(action) && !cell.isEmpty()) {
                // UPDATE logic
                if (title.isBlank()) {
                    apiResponse.setResponseMessage("Title cannot be blank for update.");
                    return apiResponse;
                }

                Map<String, Object> oldRecord = crudService.fetchRecordById("province", "id = '" + cell + "'");

                String updateQuery = "UPDATE province SET title = :title WHERE id = :id";
                Map<String, Object> updateParams = new HashMap<>();
                updateParams.put("title", title);
                updateParams.put("id", cell);

                int updated = crudService.executeNativeQuery(updateQuery, updateParams);
                if (updated > 0) {
                    Map<String, Object> newRecord = crudService.fetchRecordById("province", "id = '" + cell + "'");
                    String logString = returnLogString(oldRecord, newRecord);
                    auditAction("UPDATED", "Updated region " + title + " id: " + cell + " (" + logString + ")", ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Region updated successfully");
                    return apiResponse;
                }
                apiResponse.setResponseCode(ApiResponseCode.FAIL);
                apiResponse.setResponseMessage("Region could not be updated");
                return apiResponse;

            } else {
                // INSERT logic

                if (title.isBlank()) {
                    apiResponse.setResponseMessage("Title cannot be blank.");
                    return apiResponse;
                }

                // Duplicate check
                String checkQuery = "SELECT COUNT(*) FROM province WHERE title = :title AND intrash != 'YES'";
                Map<String, Object> checkParams = new HashMap<>();
                checkParams.put("title", title);
                int count = (int) crudService.fetchCount(checkQuery, checkParams);

                if (count > 0) {
                    apiResponse.setResponseCode(ApiResponseCode.FAIL);
                    apiResponse.setResponseMessage("Region with this title already exists.");
                    return apiResponse;
                }

                String insertQuery = "INSERT INTO province (title, creationtime) VALUES (:title, :creationtime)";
                Map<String, Object> insertParams = new HashMap<>();
                insertParams.put("title", title);
                insertParams.put("creationtime", System.currentTimeMillis() / 1000); // Unix timestamp

                int inserted = crudService.executeNativeQuery(insertQuery, insertParams);
                if (inserted > 0) {
                    auditAction("CREATED", "Created Region: " + title, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Region created successfully");
                    return apiResponse;
                }
                apiResponse.setResponseCode(ApiResponseCode.FAIL);
                apiResponse.setResponseMessage("Region could not be created");
                return apiResponse;
            }

        } catch (Exception e) {
            log.error("Error processing province: {}", e.getMessage(), e);
        }

        return apiResponse;
    }

    public ApiResponse processBudget(JSONObject jsonData,
                                     HttpServletRequest request,
                                     HttpServletResponse response) {

        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed to process budget")
                .build();

        try {
            String action = jsonData.optString("action", "").toLowerCase();
            String cell = jsonData.optString("cell", "").trim();
            String ipAddress = request.getRemoteAddr();
            String postdata = jsonData.toString();
            String title = jsonData.optString("title", "");
            String notes = jsonData.optString("notes", "");
            String orgId = jsonData.optString("organisation", "");
//            String user = jsonData.optString("user", "");
            UserDetailsImpl userDetails = (UserDetailsImpl) SecurityContextHolder
                    .getContext()
                    .getAuthentication()
                    .getPrincipal();

            Long userId = userDetails.getId();

            // Only handle delete directly without validation
            if ("delete".equals(action)) {
                String deleteQuery = "UPDATE budget SET intrash = 'YES' WHERE id = :id";
                Map<String, Object> params = new HashMap<>();
                params.put("id", cell);

                int result = crudService.executeNativeQuery(deleteQuery, params);
                if (result > 0) {
                    auditAction("DELETE", "Deleted budget id: " + cell, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Budget deleted successfully");
                } else {
                    apiResponse.setResponseCode(ApiResponseCode.FAIL);
                    apiResponse.setResponseMessage("A problem occurred while deleting the budget record");
                }

                return apiResponse; // Exit early after delete
            } if ("update".equals(action) && !cell.isEmpty()) {
                // Update
                Map<String, Object> oldRecord = crudService.fetchRecordById("budget", "id = '" + cell + "'");

                String updateQuery = "UPDATE budget SET title = :title, notes = :notes, org_id = :orgId WHERE id = :id";
                Map<String, Object> updateParams = new HashMap<>();
                updateParams.put("title", title);
                updateParams.put("notes", notes);
                updateParams.put("orgId", orgId);
                updateParams.put("id", cell);

                int updated = crudService.executeNativeQuery(updateQuery, updateParams);
                if (updated > 0) {
                    Map<String, Object> newRecord = crudService.fetchRecordById("budget", "id = '" + cell + "'");
                    String logString = returnLogString(oldRecord, newRecord);
                    auditAction("UPDATE", "Updated budget id: " + cell + " - " + title + " (" + logString + ")", ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Budget updated successfully");
                }

            } else {
                // Insert
                String insertQuery = "INSERT INTO budget (user, title, notes, creationtime, org_id) " +
                        "VALUES (:user, :title, :notes, :creationtime, :orgId)";
                Map<String, Object> insertParams = new HashMap<>();
                insertParams.put("user", userId);
                insertParams.put("title", title);
                insertParams.put("notes", notes);
                insertParams.put("creationtime", System.currentTimeMillis() / 1000); // Unix time
                insertParams.put("orgId", orgId);

                int inserted = crudService.executeNativeQuery(insertQuery, insertParams);
                if (inserted > 0) {
                    auditAction("CREATE", "Created new budget: " + title, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Budget created successfully");
                }
                return apiResponse; // Exit early after delete
            }

        } catch (Exception e) {
            log.error("Error processing budget delete: {}", e.getMessage(), e);
        }

        return apiResponse;
    }

    public ApiResponse processPerdiem(JSONObject jsonData,
                                      HttpServletRequest request,
                                      HttpServletResponse response) {

        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed to process perdiem")
                .build();

        try {
            String action = jsonData.optString("action", "").toLowerCase();
            String cell = jsonData.optString("cell", "");
            String designation = jsonData.optString("designation", "");
            String amountStr = jsonData.optString("amount", ""); // Handle amount as String
            String lunchDinnerAmount = jsonData.optString("lunchdinneramount", "");
            String dinnerAmount = jsonData.optString("dinneramount", "");

            // Ensure that amount is a valid number or default to 0 if not
            Double amount = null;
            if (!amountStr.isEmpty()) {
                try {
                    amount = Double.parseDouble(amountStr); // Use Double to handle decimals
                } catch (NumberFormatException ex) {
                    // Log the error and return a message if parsing fails
                    apiResponse.setResponseMessage("Invalid amount format.");
                    return apiResponse;
                }
            }

            // If amount is empty or invalid, set a default value (e.g., 0)
            if (amount == null) {
                amount = 0.0;  // Or set it to null if the database allows NULL values
            }

            String createdByStr = jsonData.optString("user", "system");
            int createdBy = 0;  // Default user ID, or set this from session context

            if (!"system".equals(createdByStr)) {
                try {
                    createdBy = Integer.parseInt(createdByStr); // Convert the user string to an integer user ID
                } catch (NumberFormatException ex) {
                    // Handle the case where user ID is invalid
                    apiResponse.setResponseMessage("Invalid user ID format.");
                    return apiResponse;
                }
            }

            String ipAddress = request.getRemoteAddr();
            String postdata = jsonData.toString();

            if ("delete".equals(action)) {
                String deleteQuery = "UPDATE perdiem SET intrash = 'YES' WHERE id = :id";
                Map<String, Object> deleteParams = Map.of("id", cell);
                int deleted = crudService.executeNativeQuery(deleteQuery, deleteParams);

                if (deleted > 0) {
                    auditAction("DELETE", "Deleted perdiem id: " + cell, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Per diem successfully deleted");
                    return apiResponse;
                }
                apiResponse.setResponseCode(ApiResponseCode.FAIL);
                apiResponse.setResponseMessage("An error occurred deleting budget");
                return apiResponse;

            } else if ("update".equals(action) && !cell.isEmpty()) {
                // Update logic
                String updateQuery = "UPDATE perdiem SET Designation = :designation, Amount = :amount, " +
                        "lunchDinnerRate = :lunchDinner, dinnerRate = :dinnerRate WHERE id = :id";
                Map<String, Object> updateParams = new HashMap<>();
                updateParams.put("designation", designation);
                updateParams.put("amount", amount);
                updateParams.put("lunchDinner", lunchDinnerAmount);
                updateParams.put("dinnerRate", dinnerAmount);
                updateParams.put("id", cell);

                int updated = crudService.executeNativeQuery(updateQuery, updateParams);
                if (updated > 0) {
                    auditAction("UPDATED", "Updated perdiem " + designation + " id: " + cell, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Perdiem updated successfully");
                    return apiResponse;
                }
                apiResponse.setResponseCode(ApiResponseCode.FAIL);
                apiResponse.setResponseMessage("An error occurred updating budget");
                return apiResponse;

            } else {
                // Insert logic
                String existsQuery = "SELECT ID FROM perdiem WHERE Designation = :designation AND intrash = 'NO'";
                List<?> exists = crudService.runQuery(existsQuery, Map.of("designation", designation));

                if (exists.isEmpty()) {
                    String fetchOrgs = "SELECT ID FROM organisation WHERE intrash = 'NO'";
                    List<?> orgs = crudService.runQuery(fetchOrgs, Map.of());

                    for (Object orgObj : orgs) {
                        Object[] orgRow = (orgObj instanceof Object[]) ? (Object[]) orgObj : new Object[]{orgObj};
                        Object orgId = orgRow[0];

                        String insertQuery = "INSERT INTO perdiem (Designation, Amount, lunchDinnerRate, dinnerRate, createdBy, org_id) " +
                                "VALUES (:designation, :amount, :lunchDinner, :dinnerRate, :createdBy, :orgId)";
                        Map<String, Object> insertParams = new HashMap<>();
                        insertParams.put("designation", designation);
                        insertParams.put("amount", amount); // Ensure amount is set correctly
                        insertParams.put("lunchDinner", lunchDinnerAmount);
                        insertParams.put("dinnerRate", dinnerAmount);
                        insertParams.put("createdBy", createdBy); // Corrected to pass an integer ID here
                        insertParams.put("orgId", orgId);

                        crudService.executeNativeQuery(insertQuery, insertParams);
                    }

                    auditAction("CREATED", "Created perdiem " + designation, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Perdiem successfully created");

                } else {
                    apiResponse.setResponseMessage("Failed: Designation already exists");
                }
            }

        } catch (Exception e) {
            log.error("Error processing perdiem: {}", e.getMessage(), e);
            apiResponse.setResponseMessage("Unexpected error occurred");
        }

        return apiResponse;
    }

    public ApiResponse processJobGroup(JSONObject jsonData,
                                       HttpServletRequest request,
                                       HttpServletResponse response) {

        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed to process job group")
                .build();

        try {
            String action = jsonData.optString("action", "").toLowerCase();
            String cell = jsonData.optString("cell", "");
            String title = jsonData.optString("title", "");
            String createdByStr = jsonData.optString("user", "system");
            String ipAddress = request.getRemoteAddr();
            String postdata = jsonData.toString();

            // Convert createdBy to an integer, default to 0 for "system"
            int createdBy = 0; // Default value for "system"
            if (!"system".equals(createdByStr)) {
                try {
                    createdBy = Integer.parseInt(createdByStr); // Parse the user ID
                } catch (NumberFormatException e) {
                    log.error("Invalid user ID: {}", createdByStr);
                    apiResponse.setResponseMessage("Invalid user ID");
                    return apiResponse;
                }
            }

            // Handle the delete action
            if ("delete".equals(action)) {
                if (cell.isEmpty()) {
                    apiResponse.setResponseMessage("Missing job group ID for deletion");
                    return apiResponse;
                }

                String deleteQuery = "UPDATE jobgroup SET intrash = 'YES' WHERE id = :id";
                Map<String, Object> deleteParams = Map.of("id", cell);

                int deleted = crudService.executeNativeQuery(deleteQuery, deleteParams);
                if (deleted > 0) {
                    auditAction("DELETE", "Deleted jobgroup id: " + cell, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Job group deleted successfully");
                } else {
                    apiResponse.setResponseMessage("No job group found with the given ID to delete.");
                }
            }

            // Handle the update action
            else if ("update".equals(action) && !cell.isEmpty()) {
                // Update Query
                // Fetch the old record before update
                Map<String, Object> oldRecord = crudService.fetchRecordById("jobgroup", "id = '" + cell + "'");

                String updateQuery = "UPDATE jobgroup SET title = :title WHERE id = :id";
                Map<String, Object> updateParams = new HashMap<>();
                updateParams.put("title", title);
                updateParams.put("id", cell);

                int updated = crudService.executeNativeQuery(updateQuery, updateParams);
                if (updated > 0) {
                    // Fetch the updated record after update
                    Map<String, Object> newRecord = crudService.fetchRecordById("jobgroup", "id = '" + cell + "'");
                    String logString = returnLogString(oldRecord, newRecord);
                    auditAction("UPDATE", "Updated jobgroup id: " + cell + " - " + title + " (" + logString + ")", ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Job group updated successfully");
                }
            }

            // Handle the insert action
            else {
                // Check if job group with this title already exists
                String checkTitleQuery = "SELECT COUNT(*) FROM jobgroup WHERE title = :title";
                Map<String, Object> checkTitleParams = new HashMap<>();
                checkTitleParams.put("title", title);

                // Check if title already exists in the jobgroup table
                int titleCount = Math.toIntExact(crudService.executeCountQuery(checkTitleQuery, checkTitleParams));
                if (titleCount > 0) {
                    apiResponse.setResponseMessage("Job group with this title already exists.");
                    return apiResponse; // Prevent insert if the title is a duplicate
                }

                // Insert Query (create new jobgroup)
                String insertQuery = "INSERT INTO jobgroup (title, createdBy) VALUES (:title, :createdBy)";
                Map<String, Object> insertParams = new HashMap<>();
                insertParams.put("title", title);
                insertParams.put("createdBy", createdBy);

                int inserted = crudService.executeNativeQuery(insertQuery, insertParams);
                if (inserted > 0) {
                    auditAction("CREATE", "Created new jobgroup: " + title, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Job group created successfully");
                } else {
                    apiResponse.setResponseMessage("Failed to create job group.");
                }
            }

        } catch (Exception e) {
            log.error("Error processing job group: {}", e.getMessage(), e);
            apiResponse.setResponseMessage("Unexpected error occurred while processing job group");
        }

        return apiResponse;
    }
    public ApiResponse processRecipient(JSONObject jsonData, HttpServletRequest request, HttpServletResponse response) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed to process recipient")
                .build();

        try {
            String action = jsonData.optString("action", "").toLowerCase();
            String cell = jsonData.optString("cell", "");
            String scode = jsonData.optString("scode", "");
            String firstname = jsonData.optString("firstname", "");
            String middlename = jsonData.optString("middlename", "");
            String lastname = jsonData.optString("lastname", "");
            String msisdn = jsonData.optString("msisdn", "");
            String email = jsonData.optString("email", "");
            String IDNumber = jsonData.optString("IDNumber", "");
            String province = jsonData.optString("province", "");
            String countyid = jsonData.optString("countyid", "");
            String subcountyid = jsonData.optString("subcountyid", "");
            String designationid = jsonData.optString("designationid", "");
            String jobgroupid = jsonData.optString("jobgroupid", "");
            String gender = jsonData.optString("gender", "");
            String sector = jsonData.optString("sector", "");
            String category = jsonData.optString("category", "");
            String ipAddress = request.getRemoteAddr();
            String postdata = jsonData.toString();

            // DELETE
            if ("delete".equals(action)) {
                if (cell.isEmpty()) {
                    apiResponse.setResponseMessage("Missing recipient ID for deletion");
                    return apiResponse;
                }

                String deleteQuery = "UPDATE recipient SET intrash = 'YES' WHERE id = :id";
                Map<String, Object> deleteParams = Map.of("id", cell);

                int deleted = crudService.executeNativeQuery(deleteQuery, deleteParams);
                if (deleted > 0) {
                    auditAction("DELETE", "Deleted recipient id: " + cell, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Recipient deleted successfully");
                } else {
                    apiResponse.setResponseMessage("No recipient found with the given ID to delete.");
                }
            }

            // UPDATE
            else if ("update".equals(action) && !cell.isEmpty()) {
                Map<String, Object> oldRecord = crudService.fetchRecordById("recipient", "id = '" + cell + "'");

                String updateQuery = "UPDATE recipient SET firstname = :firstname, middlename = :middlename, lastname = :lastname, " +
                        "email_address = :email, IDNumber = :IDNumber, regionid = :regionid, countyid = :countyid, subcountyid = :subcountyid, " +
                        "designationid = :designationid, jobgroupid = :jobgroupid, gender = :gender, sector = :sector, category = :category " +
                        "WHERE id = :id";

                Map<String, Object> updateParams = new HashMap<>();
                updateParams.put("firstname", firstname);
                updateParams.put("middlename", middlename);
                updateParams.put("lastname", lastname);
                updateParams.put("email", email);
                updateParams.put("IDNumber", IDNumber);
                updateParams.put("regionid", province.isEmpty() ? null : Integer.valueOf(province));
                updateParams.put("countyid", countyid.isEmpty() ? null : Integer.valueOf(countyid));
                updateParams.put("subcountyid", subcountyid.isEmpty() ? null : Integer.valueOf(subcountyid));
                updateParams.put("designationid", designationid.isEmpty() ? null : Integer.valueOf(designationid));
                updateParams.put("jobgroupid", jobgroupid.isEmpty() ? null : Integer.valueOf(jobgroupid));
                updateParams.put("gender", gender);
                updateParams.put("sector", sector);
                updateParams.put("category", category);
                updateParams.put("id", cell);

                int updated = crudService.executeNativeQuery(updateQuery, updateParams);
                if (updated > 0) {
                    Map<String, Object> newRecord = crudService.fetchRecordById("recipient", "id = '" + cell + "'");
                    String logString = returnLogString(oldRecord, newRecord);
                    auditAction("UPDATE", "Updated recipient id: " + cell + " (" + logString + ")", ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Recipient updated successfully");
                } else {
                    apiResponse.setResponseMessage("No recipient found with the given ID to update.");
                }
            }

            // INSERT
            else {
                // Check if recipient with this MSISDN already exists (assumed unique key)
                String checkQuery = "SELECT COUNT(*) FROM recipient WHERE MSISDN = :msisdn";
                Map<String, Object> checkParams = Map.of("msisdn", msisdn);
                int count = Math.toIntExact(crudService.executeCountQuery(checkQuery, checkParams));

                if (count > 0) {
                    apiResponse.setResponseMessage("Recipient with this MSISDN already exists.");
                    return apiResponse;
                }

                String insertQuery = "INSERT INTO recipient (firstname, middlename, lastname, MSISDN, email_address, inTrash, IDNumber, regionid, countyid, subcountyid, designationid, jobgroupid, gender, sector, category) " +
                        "VALUES (:firstname, :middlename, :lastname, :msisdn, :email, 'No', :IDNumber, :regionid, :countyid, :subcountyid, :designationid, :jobgroupid, :gender, :sector, :category)";

                Map<String, Object> insertParams = new HashMap<>();
                insertParams.put("firstname", firstname);
                insertParams.put("middlename", middlename);
                insertParams.put("lastname", lastname);
                insertParams.put("msisdn", msisdn);
                insertParams.put("email", email);
                insertParams.put("IDNumber", IDNumber);
                insertParams.put("regionid", province.isEmpty() ? null : Integer.valueOf(province));
                insertParams.put("countyid", countyid.isEmpty() ? null : Integer.valueOf(countyid));
                insertParams.put("subcountyid", subcountyid.isEmpty() ? null : Integer.valueOf(subcountyid));
                insertParams.put("designationid", designationid.isEmpty() ? null : Integer.valueOf(designationid));
                insertParams.put("jobgroupid", jobgroupid.isEmpty() ? null : Integer.valueOf(jobgroupid));
                insertParams.put("gender", gender);
                insertParams.put("sector", sector);
                insertParams.put("category", category);

                int inserted = crudService.executeNativeQuery(insertQuery, insertParams);
                if (inserted > 0) {
                    auditAction("CREATE", "Created recipient with MSISDN: " + msisdn, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Recipient created successfully");
                } else {
                    apiResponse.setResponseMessage("Failed to create recipient.");
                }
            }

        } catch (Exception e) {
            log.error("Error processing recipient: {}", e.getMessage(), e);
            apiResponse.setResponseMessage("Unexpected error occurred while processing recipient");
        }

        return apiResponse;
    }
    public ApiResponse processOrganisation(JSONObject jsonData, HttpServletRequest request, HttpServletResponse response) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed to process organisation")
                .build();

        try {
            String action = jsonData.optString("action", "").toLowerCase();
            String cell = jsonData.optString("cell", "");
            String orgName = jsonData.optString("org_name", "");
            String shortCode = jsonData.optString("short_code", "");
            String email = jsonData.optString("email", "");
            String msisdn = jsonData.optString("msisdn", "");
            String status = jsonData.optString("status", "1");

            // Fix: Parse user ID as integer
            int userId = jsonData.has("user") ? jsonData.optInt("user", 0) : 0;

            JSONArray approvalLevels = jsonData.optJSONArray("approval_level");
            String ipAddress = request.getRemoteAddr();
            String postdata = jsonData.toString();

            if ("delete".equals(action)) {
                String deleteQuery = "UPDATE organisation SET intrash = 'YES' WHERE id = :id";
                Map<String, Object> deleteParams = Map.of("id", cell);
                int deleted = crudService.executeNativeQuery(deleteQuery, deleteParams);

                if (deleted > 0) {
                    String fetchName = "SELECT name FROM organisation WHERE id = :id";
                    List<?> nameResult = crudService.runQuery(fetchName, Map.of("id", cell));
                    String orgNameDeleted = nameResult.isEmpty() ? "Unknown" : String.valueOf(nameResult.get(0));

                    auditAction("organisation Deletion", "Deleted Organisation: " + orgNameDeleted, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Organisation deleted successfully");
                }
            }
            else if ("update".equals(action) && !cell.isEmpty()) {
                Map<String, Object> oldRecord = crudService.fetchRecordById("organisation", "id = '" + cell + "'");

                String updateQuery = "UPDATE organisation SET name = :name, phone_number = :msisdn, email = :email, status = :status, short_code = :shortCode WHERE id = :id";
                Map<String, Object> updateParams = Map.of(
                        "name", orgName,
                        "msisdn", msisdn,
                        "email", email,
                        "status", status,
                        "shortCode", shortCode,
                        "id", cell
                );

                int updated = crudService.executeNativeQuery(updateQuery, updateParams);

                if (updated > 0 && approvalLevels != null) {
                    for (int i = 0; i < approvalLevels.length(); i++) {
                        String level = approvalLevels.getString(i);
                        String insertLevel = "INSERT INTO org_approval_levels (approval_level, org_id, created_by, date_created, intrash) " +
                                "VALUES (:level, :orgId, :user, CURRENT_TIMESTAMP, 'NO')";
                        Map<String, Object> levelParams = Map.of(
                                "level", level,
                                "orgId", Integer.parseInt(cell),
                                "user", userId
                        );
                        crudService.executeNativeQuery(insertLevel, levelParams);
                    }
                }

                Map<String, Object> newRecord = crudService.fetchRecordById("organisation", "id = '" + cell + "'");
                String logString = returnLogString(oldRecord, newRecord);
                auditAction("organisation Update", "Updated organisation " + orgName + " (" + logString + ")", ipAddress, postdata);
                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                apiResponse.setResponseMessage("Organisation updated successfully");

            } else {
                String checkExists = "SELECT name FROM organisation WHERE name = :name OR short_code = :shortCode";
                List<?> exists = crudService.runQuery(checkExists, Map.of("name", orgName, "shortCode", shortCode));

                if (exists.isEmpty()) {
                    String fetchMaxId = "SELECT MAX(id) FROM organisation";
                    List<?> maxIdResult = crudService.runQuery(fetchMaxId, Map.of());

                    BigDecimal maxId = maxIdResult.isEmpty() || maxIdResult.get(0) == null
                            ? BigDecimal.ZERO
                            : new BigDecimal(String.valueOf(maxIdResult.get(0)));

                    BigDecimal nextId = maxId.add(BigDecimal.ONE);

                    String insertOrg = "INSERT INTO organisation (id, name, short_code, email, phone_number, created_by, intrash, status) " +
                            "VALUES (:id, :name, :shortCode, :email, :msisdn, :user, 'NO', '1')";
                    Map<String, Object> insertParams = Map.of(
                            "id", nextId,
                            "name", orgName,
                            "shortCode", shortCode,
                            "email", email,
                            "msisdn", msisdn,
                            "user", userId
                    );
                    crudService.executeNativeQuery(insertOrg, insertParams);

                    if (approvalLevels != null) {
                        for (int i = 0; i < approvalLevels.length(); i++) {
                            String level = approvalLevels.getString(i);
                            String insertLevel = "INSERT INTO org_approval_levels (approval_level, org_id, created_by, date_created, intrash) " +
                                    "VALUES (:level, :orgId, :user, CURRENT_TIMESTAMP, 'NO')";
                            Map<String, Object> levelParams = Map.of(
                                    "level", level,
                                    "orgId", nextId,
                                    "user", userId
                            );
                            crudService.executeNativeQuery(insertLevel, levelParams);
                        }
                    }

                    String clonePerdiem = "INSERT INTO perdiem (Designation, amount, creationTime, createdBy, inTrash, lunchDinnerRate, dinnerRate, org_id) " +
                            "SELECT Designation, amount, creationTime, createdBy, inTrash, lunchDinnerRate, dinnerRate, :orgId FROM perdiem WHERE org_id = 1";
                    crudService.executeNativeQuery(clonePerdiem, Map.of("orgId", nextId));

                    auditAction("Organisation Creation", "Created Organisation " + orgName, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Organisation created successfully");

                } else {
                    auditAction("Organisation Creation", "Failed to add organisation " + orgName + " (already exists)", ipAddress, postdata);
                    apiResponse.setResponseMessage("Organisation already exists");
                }
            }

        } catch (Exception e) {
            log.error("Error processing organisation: {}", e.getMessage(), e);
            apiResponse.setResponseMessage("Unexpected error occurred");
        }

        return apiResponse;
    }
    public ApiResponse processOrgOrderType(JSONObject jsonData, HttpServletRequest request, HttpServletResponse response) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed to process org_ordertype mapping")
                .build();

        try {
            String action = jsonData.optString("action", "").toLowerCase();
            String cell = jsonData.optString("cell", "");
            String title = jsonData.optString("title", ""); // now optional
            String ordertypeIdStr = jsonData.optString("ordertypeId", "").trim();
            String organisationStr = jsonData.optString("organisation", "").trim();
            String budgetStr = jsonData.optString("budget", "").trim();
            String user = jsonData.optString("user", "system");
            JSONArray approvalLevels = jsonData.optJSONArray("approval_level");
            JSONArray approvalLevelsResubmit = jsonData.optJSONArray("approval_level_resubmit");
            String ipAddress = request.getRemoteAddr();
            String postdata = jsonData.toString();

            if ("delete".equals(action)) {
                String deleteQuery = "UPDATE org_ordertype SET intrash = 'YES' WHERE id = :id";
                int deleted = crudService.executeNativeQuery(deleteQuery, Map.of("id", cell));

                if (deleted > 0) {
                    auditAction("org_ordertype Deletion", "Deleted org_ordertype ID: " + cell, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("org_ordertype deleted successfully");
                }

                // Prevent any further processing or validation
                return apiResponse;
            }


            Integer ordertypeId = null;
            Integer organisation, budget;

            try {
                if (!ordertypeIdStr.isEmpty()) {
                    ordertypeId = Integer.parseInt(ordertypeIdStr);
                }
                organisation = Integer.parseInt(organisationStr);
                budget = Integer.parseInt(budgetStr);
            } catch (NumberFormatException e) {
                apiResponse.setResponseMessage("Invalid numeric value for organisation, budget, or ordertypeId");
                return apiResponse;
            }

          if ("update".equals(action) && !cell.isEmpty()) {
                String checkExists = "SELECT id FROM org_ordertype WHERE intrash = 'NO' AND ordertypeId " +
                        (ordertypeId != null ? "= :orderType" : "IS NULL") +
                        " AND org_id = :orgId AND id != :id";

                Map<String, Object> checkParams = new HashMap<>();
                if (ordertypeId != null) checkParams.put("orderType", ordertypeId);
                checkParams.put("orgId", organisation);
                checkParams.put("id", cell);

                List<?> exists = crudService.runQuery(checkExists, checkParams);

                if (exists.isEmpty()) {
                    String approvalLevelStr = joinIntegerIds(approvalLevels);
                    String approvalOrder = getApprovalLevelsOrdered(approvalLevelStr);
                    String resubmitLevelStr = joinIntegerIds(approvalLevelsResubmit);
                    String resubmitOrder = getApprovalLevelsOrdered(resubmitLevelStr);

                    Map<String, Object> oldRecord = crudService.fetchRecordById("org_ordertype", "id = '" + cell + "'");

                    String updateQuery = "UPDATE org_ordertype SET ordertypeId = :orderType, approvalOrder = :approvalOrder, " +
                            "resubmitable_levels = :resubmitOrder, org_id = :orgId, budget_id = :budget, " +
                            "approval_levels = :approvalLevels, resubmitable_levels_ids = :resubmitLevels WHERE id = :id";

                    Map<String, Object> updateParams = new HashMap<>();
                    updateParams.put("orderType", ordertypeId);
                    updateParams.put("approvalOrder", approvalOrder);
                    updateParams.put("resubmitOrder", resubmitOrder);
                    updateParams.put("orgId", organisation);
                    updateParams.put("budget", budget);
                    updateParams.put("approvalLevels", approvalLevelStr);
                    updateParams.put("resubmitLevels", resubmitLevelStr);
                    updateParams.put("id", cell);

                    crudService.executeNativeQuery(updateQuery, updateParams);

                    Map<String, Object> newRecord = crudService.fetchRecordById("org_ordertype", "id = '" + cell + "'");
                    String logString = returnLogString(oldRecord, newRecord);
                    auditAction("org_ordertype Update", "Updated org_ordertype " + title + " (" + logString + ")", ipAddress, postdata);

                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("org_ordertype updated successfully");
                } else {
                    apiResponse.setResponseMessage("org_ordertype mapping already exists");
                }

            } else if ("create".equals(action) || action.isEmpty()) {
                // Skipped title validation here as requested

                String checkExists = "SELECT id FROM org_ordertype WHERE intrash = 'NO' AND ordertypeId " +
                        (ordertypeId != null ? "= :orderType" : "IS NULL") + " AND org_id = :orgId";

                Map<String, Object> checkParams = new HashMap<>();
                if (ordertypeId != null) checkParams.put("orderType", ordertypeId);
                checkParams.put("orgId", organisation);

                List<?> exists = crudService.runQuery(checkExists, checkParams);

                if (exists.isEmpty()) {
                    String approvalLevelStr = joinIntegerIds(approvalLevels);
                    String approvalOrder = getApprovalLevelsOrdered(approvalLevelStr);
                    String resubmitLevelStr = joinIntegerIds(approvalLevelsResubmit);
                    String resubmitOrder = getApprovalLevelsOrdered(resubmitLevelStr);

                    String insertQuery = "INSERT INTO org_ordertype (ordertypeId, approvalOrder, creationTime, inTrash, org_id, budget_id, " +
                            "approval_levels, resubmitable_levels, resubmitable_levels_ids) " +
                            "VALUES (:orderType, :approvalOrder, :creationTime, 'NO', :orgId, :budget, :approvalLevels, :resubmitOrder, :resubmitLevels)";

                    Map<String, Object> insertParams = new HashMap<>();
                    insertParams.put("orderType", ordertypeId);
                    insertParams.put("approvalOrder", approvalOrder);
                    insertParams.put("creationTime", System.currentTimeMillis() / 1000);
                    insertParams.put("orgId", organisation);
                    insertParams.put("budget", budget);
                    insertParams.put("approvalLevels", approvalLevelStr);
                    insertParams.put("resubmitOrder", resubmitOrder);
                    insertParams.put("resubmitLevels", resubmitLevelStr);

                    crudService.executeNativeQuery(insertQuery, insertParams);

                    auditAction("org_ordertype Creation", "Created org_ordertype mapping for " + title, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("org_ordertype mapping created successfully");

                } else {
                    auditAction("org_ordertype Creation", "Failed to create org_ordertype " + title + " (already exists)", ipAddress, postdata);
                    apiResponse.setResponseMessage("org_ordertype already exists");
                }

            } else {
                apiResponse.setResponseMessage("Invalid action provided: " + action);
            }

        } catch (Exception e) {
            log.error("Error processing org_ordertype: {}", e.getMessage(), e);
            apiResponse.setResponseMessage("Unexpected error occurred");
        }

        return apiResponse;
    }



    public ApiResponse processLineItem(JSONObject jsonData, HttpServletRequest request, HttpServletResponse response) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed to process line item")
                .build();

        try {
            String action = jsonData.optString("action", "").toLowerCase();
            String id = jsonData.optString("cell", "");
            String itemName = jsonData.optString("title", "");
            String requiredDoc = jsonData.optString("requiredDoc", "");
            String isDuplicateAllowed = jsonData.optString("isDuplicateAllowed", "");
            String ipAddress = request.getRemoteAddr();
            String postdata = jsonData.toString();

            if ("delete".equals(action)) {
                // DELETE = soft delete
                String deleteQuery = "UPDATE line_items SET intrash = 'Yes' WHERE id = :id";
                Map<String, Object> deleteParams = Map.of("id", id);
                int rows = crudService.executeNativeQuery(deleteQuery, deleteParams);
                if (rows > 0) {
                    auditAction("DELETE", "Deleted line item id: " + id, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Line item deleted successfully");
                }
            } else {
                if (id == null || id.isEmpty()) {
                    // Check for duplicate record before insert
                    String checkQuery = "SELECT COUNT(*) FROM line_items WHERE itemName = :itemName AND isDocumentsRequired = :requiredDoc";
                    Map<String, Object> checkParams = Map.of(
                            "itemName", itemName,
                            "requiredDoc", requiredDoc
                    );
                    int existingCount = Math.toIntExact(crudService.executeCountQuery(checkQuery, checkParams));

                    if (existingCount > 0) {
                        apiResponse.setResponseMessage("Line item already exists");
                        return apiResponse; // Prevent insert if item already exists
                    }

                    // Proceed with INSERT if no duplicates
                    String insertQuery = "INSERT INTO line_items (itemName, isDocumentsRequired, isDuplicateAllowed) " +
                            "VALUES (:itemName, :requiredDoc, :isDuplicateAllowed)";
                    Map<String, Object> insertParams = Map.of(
                            "itemName", itemName,
                            "requiredDoc", requiredDoc,
                            "isDuplicateAllowed", isDuplicateAllowed
                    );
                    crudService.executeNativeQuery(insertQuery, insertParams);
                    auditAction("INSERTED", "Inserted line item: " + itemName, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Line item created successfully");
                }
                else {
                    // UPDATE
                    String updateQuery = "UPDATE line_items SET itemName = :itemName, isDocumentsRequired = :requiredDoc, " +
                            "isDuplicateAllowed = :isDuplicateAllowed WHERE id = :id";
                    Map<String, Object> updateParams = Map.of(
                            "itemName", itemName,
                            "requiredDoc", requiredDoc,
                            "isDuplicateAllowed", isDuplicateAllowed,
                            "id", id
                    );
                    crudService.executeNativeQuery(updateQuery, updateParams);
                    auditAction("UPDATED", "Updated line item id: " + id, ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Line item updated successfully");
                }
            }
        } catch (Exception e) {
            log.error("Error processing line item: {}", e.getMessage(), e);
            apiResponse.setResponseMessage("Unexpected error occurred");
        }

        return apiResponse;
    }

    public ApiResponse processSyncedPayment(JSONObject jsonData,
                                            HttpServletRequest request,
                                            HttpServletResponse response) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed to process synced payment")
                .build();

        try {
            String pmsId = jsonData.optString("pmsId", "");
            String beneficiary = jsonData.optString("beneficiary", "");
            String initiator = jsonData.optString("initiator", "");
            String county = jsonData.optString("county", "");
            String treatmentModel = jsonData.optString("treatmentModel", "");
            String paymentId = jsonData.optString("paymentId", "");
            String msisdn = jsonData.optString("msisdn", "");
            String budgetLine = jsonData.optString("budgetLine", "");
            String amount = jsonData.optString("amount", "");
            String region = jsonData.optString("region", "");
            String comments = jsonData.optString("comments", "");
            String ipAddress = request.getRemoteAddr();
            String postdata = jsonData.toString();

            // Check for existing PMS_ID
            String checkQuery = "SELECT COUNT(*) FROM synced_payments WHERE pms_id = :pmsId";
            Map<String, Object> checkParams = Map.of("pmsId", pmsId);
            long count = crudService.fetchCount(checkQuery, checkParams); // This should return count of rows

            if (count > 0) {
                apiResponse.setResponseMessage("Payment already queued for resync");
            } else {
                String insertQuery = "INSERT INTO synced_payments " +
                        "(pms_id, beneficiary, initiator, county, treatment_model, payment_id, msisdn, budget_line, amount, region, status, comments) " +
                        "VALUES (:pmsId, :beneficiary, :initiator, :county, :treatmentModel, :paymentId, :msisdn, :budgetLine, :amount, :region, 'PENDING', :comments)";
                Map<String, Object> insertParams = new HashMap<>();
                insertParams.put("pmsId", pmsId);
                insertParams.put("beneficiary", beneficiary);
                insertParams.put("initiator", initiator);
                insertParams.put("county", county);
                insertParams.put("treatmentModel", treatmentModel);
                insertParams.put("paymentId", paymentId);
                insertParams.put("msisdn", msisdn);
                insertParams.put("budgetLine", budgetLine);
                insertParams.put("amount", amount);
                insertParams.put("region", region);
                insertParams.put("comments", comments);


                int rowsInserted = crudService.executeNativeQuery(insertQuery, insertParams);
                if (rowsInserted > 0) {
                    auditAction("CREATED", "Created synced payment records", ipAddress, postdata);
                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Payment successfully resynced to PMS");
                }
            }

        } catch (Exception e) {
            log.error("Error processing synced payment: {}", e.getMessage(), e);
        }

        return apiResponse;
    }

    public ApiResponse processSmsBroadcast(JSONObject jsonData, HttpServletRequest request, HttpServletResponse response) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed to process SMS broadcast")
                .build();

        try {
            String action = jsonData.optString("action", "");
            String message = jsonData.optString("message", "");
            String smsBatchNo = jsonData.optString("smsBatchNo", "");
            String sendTime = jsonData.optString("sendTime", "now");
            String schedulerMode = jsonData.optString("scheduler", "");
            String mode = jsonData.optString("mode", "");
            String shortcode = fetchGlobal("BROADCAST_SHORTCODE"); // Assume implemented
            String sessionUser = getSessionUser();                 // Assume implemented
            String orgId = getSessionOrgId();                      // Assume implemented
            String ipAddress = request.getRemoteAddr();
            String postdata = jsonData.toString();

            List<String> phoneNumbers = extractPhoneNumbers(jsonData); // Based on mode
            String smsBatchId = "TIBUSMS" + System.currentTimeMillis();

            if ("delete".equalsIgnoreCase(action)) {
                String cancelQuery = "UPDATE message_outbox SET status = 4, created_by = :createdBy WHERE status = 0 AND smsbatchno = :smsBatchNo";
                Map<String, Object> cancelParams = Map.of("createdBy", sessionUser, "smsBatchNo", smsBatchNo);
                crudService.executeNativeQuery(cancelQuery, cancelParams);
                auditAction("UPDATED", "Message for batch: " + smsBatchNo + " cancelled", ipAddress, postdata);
                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                apiResponse.setResponseMessage("Messages cancelled successfully");
                return apiResponse;
            }

            if ("now".equalsIgnoreCase(sendTime) && !"schedule".equalsIgnoreCase(schedulerMode)) {
                for (String phoneNumber : phoneNumbers) {
                    if (phoneNumber.length() >= 10) {
                        String insertQuery = "INSERT INTO message_outbox (smsbatchno, phone_number, shortcode, org_id, message_out, created_by, origin) " +
                                "VALUES (:smsBatchNo, :phoneNumber, :shortcode, :orgId, :messageOut, :createdBy, 'USER')";
                        Map<String, Object> insertParams = Map.of(
                                "smsBatchNo", smsBatchId,
                                "phoneNumber", phoneNumber,
                                "shortcode", shortcode,
                                "orgId", orgId,
                                "messageOut", message,
                                "createdBy", sessionUser
                        );
                        crudService.executeNativeQuery(insertQuery, insertParams);
                    }
                }
            } else {
                // Schedule for later
                String scheduleName = smsBatchId;
                String scheduleTime = jsonData.optString("scheduletime");
                String frequency = jsonData.optString("frequency");
                String endSchedule = jsonData.optString("endscheduletime");

                String insertScheduleQuery = "INSERT INTO message_schedule " +
                        "(schedule_name, schedule_time, next_schedule, frequency, end_schedule, created_by, org_id) " +
                        "VALUES (:name, :time, :next, :freq, :end, :createdBy, :orgId)";
                Map<String, Object> scheduleParams = Map.of(
                        "name", scheduleName,
                        "time", scheduleTime,
                        "next", scheduleTime,
                        "freq", frequency,
                        "end", endSchedule,
                        "createdBy", sessionUser,
                        "orgId", orgId
                );
                crudService.executeNativeQuery(insertScheduleQuery, scheduleParams);

                String fetchScheduleIdQuery = "SELECT id FROM message_schedule WHERE schedule_name = :name ORDER BY id DESC FETCH FIRST 1 ROWS ONLY";
                long scheduleId = crudService.fetchCount(fetchScheduleIdQuery, Map.of("name", scheduleName));

                for (String phoneNumber : phoneNumbers) {
                    if (phoneNumber.length() >= 10) {
                        String insertLaterQuery = "INSERT INTO message_out_later " +
                                "(schedule_id, smsbatchno, phone_number, shortcode, org_id, message_out, created_by, origin) " +
                                "VALUES (:scheduleId, :smsBatchNo, :phoneNumber, :shortcode, :orgId, :messageOut, :createdBy, 'USER')";
                        Map<String, Object> laterParams = Map.of(
                                "scheduleId", scheduleId,
                                "smsBatchNo", smsBatchId,
                                "phoneNumber", phoneNumber,
                                "shortcode", shortcode,
                                "orgId", orgId,
                                "messageOut", message,
                                "createdBy", sessionUser
                        );
                        crudService.executeNativeQuery(insertLaterQuery, laterParams);
                    }
                }
            }

            auditAction("CREATED", "Messages scheduled batch: " + smsBatchId, ipAddress, postdata);
            apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
            apiResponse.setResponseMessage(phoneNumbers.size() + " messages have been scheduled");

        } catch (Exception e) {
            log.error("Error processing SMS broadcast: {}", e.getMessage(), e);
        }

        return apiResponse;
    }
    public ApiResponse processMessageTemplate(JSONObject jsonData, HttpServletRequest request, HttpServletResponse response) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed to process message template")
                .build();

        try {
            String action = jsonData.optString("action", "").toLowerCase();
            String cell = jsonData.optString("cell", "");
            String message = jsonData.optString("message", "").trim();
            String payment = jsonData.optString("payment", "").trim();
            String userStr = getSessionUser();
            String orgIdStr = getSessionOrgId();
            String postdata = jsonData.toString();
            String ipAddress = request.getRemoteAddr();

            int createdBy, orgId;
            try {
                UserDetailsImpl userDetails = (UserDetailsImpl) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
                createdBy = Math.toIntExact(userDetails.getId()); // safely convert Long to int
                orgId = Integer.parseInt(orgIdStr);
            } catch (NumberFormatException e) {
                log.error("Invalid user/org ID. User: {}, OrgId: {}", userStr, orgIdStr);
                apiResponse.setResponseMessage("Invalid user or organization ID.");
                return apiResponse;
            } catch (Exception e) {
                log.error("Failed to retrieve authenticated user details.", e);
                apiResponse.setResponseMessage("Failed to identify user.");
                return apiResponse;
            }

            if ("delete".equals(action)) {
                // DELETE
                String deleteQuery = "UPDATE message_templates SET intrash = 'YES' WHERE id = :id";
                crudService.executeNativeQuery(deleteQuery, Map.of("id", cell));
                auditAction("DELETE", "Deleted Message Template id: " + cell, ipAddress, postdata);

                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                apiResponse.setResponseMessage("Template deleted successfully");
                return apiResponse;

            }  else if ("update".equals(action) && !cell.isEmpty()) {
                // UPDATE
                Map<String, Object> oldRecord = crudService.fetchRecordById("message_templates", "id = '" + cell + "'");

                String updateQuery = "UPDATE message_templates " +
                        "SET message = :message, payment_type = :payment, created_by = :createdBy " +
                        "WHERE id = :id";

                crudService.executeNativeQuery(updateQuery, Map.of(
                        "message", message,
                        "payment", payment,
                        "createdBy", createdBy,
                        "id", cell
                ));

                Map<String, Object> newRecord = crudService.fetchRecordById("message_templates", "id = '" + cell + "'");
                String logString = returnLogString(oldRecord, newRecord);
                auditAction("UPDATED", "Updated Message Template for type: " + payment + " id: " + cell + " (" + logString + ")", ipAddress, postdata);

                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                apiResponse.setResponseMessage("Template updated successfully");

            } else {
                // INSERT
                String existsQuery = "SELECT id FROM message_templates WHERE payment_type = :payment AND intrash = 'NO'";
                List<?> existingTemplates = crudService.runQuery(existsQuery, Map.of("payment", payment));

                if (existingTemplates.isEmpty()) {
                    String insertQuery = "INSERT INTO message_templates (payment_type, message, org_id, created_by, intrash) " +
                            "VALUES (:payment, :message, :orgId, :createdBy, 'NO')";

                    crudService.executeNativeQuery(insertQuery, Map.of(
                            "payment", payment,
                            "message", message,
                            "orgId", orgId,
                            "createdBy", createdBy
                    ));

                    auditAction("CREATED", "Created Message Template for type: " + payment, ipAddress, postdata);

                    apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                    apiResponse.setResponseMessage("Template created successfully");
                } else {
                    apiResponse.setResponseMessage("Template already exists for this payment type. Edit to change.");
                }
            }

        } catch (Exception e) {
            log.error("Error processing message template: {}", e.getMessage(), e);
            apiResponse.setResponseMessage("Unexpected error occurred.");
        }

        return apiResponse;
    }
    public ApiResponse processAddressBook(JSONObject jsonData, HttpServletRequest request, HttpServletResponse response) {
        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed to process address book")
                .build();

        try {
            String action = jsonData.optString("action", "").toLowerCase();
            String cell = jsonData.optString("cell", "").trim();
            String title = jsonData.optString("title", "").trim();
            String organisation = jsonData.optString("organisation", "").trim();
            String addressType = jsonData.optString("addressType", "").trim();
            String designation = jsonData.optString("designation", "0").trim();
            String postdata = jsonData.toString();
            String ipAddress = request.getRemoteAddr();

            String userStr = getSessionUser();
            String orgIdStr = getSessionOrgId();
            int createdBy, orgId;
            try {
                UserDetailsImpl userDetails = (UserDetailsImpl) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
                createdBy = Math.toIntExact(userDetails.getId()); // safely convert Long to int
                orgId = Integer.parseInt(orgIdStr);
            } catch (NumberFormatException e) {
                log.error("Invalid user/org ID. User: {}, OrgId: {}", userStr, orgIdStr);
                apiResponse.setResponseMessage("Invalid user or organization ID.");
                return apiResponse;
            } catch (Exception e) {
                log.error("Failed to retrieve authenticated user details.", e);
                apiResponse.setResponseMessage("Failed to identify user.");
                return apiResponse;
            }


            if ("delete".equals(action)) {
                // DELETE
                String deleteQuery = "UPDATE address_book SET intrash = 'YES' WHERE id = :id";
                crudService.executeNativeQuery(deleteQuery, Map.of("id", cell));
                auditAction("DELETE", "Deleted AddressBook id: " + cell, ipAddress, postdata);

                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                apiResponse.setResponseMessage("Address book entry deleted successfully");

            } else if (!cell.isEmpty()) {
                // UPDATE
                Map<String, Object> oldRecord = crudService.fetchRecordById("address_book", "id = '" + cell + "'");

                String updateQuery = "UPDATE address_book " +
                        "SET title = :title, org_id = :orgId " +
                        "WHERE id = :id";

                crudService.executeNativeQuery(updateQuery, Map.of(
                        "title", title,
                        "orgId", organisation,
                        "id", cell
                ));

                Map<String, Object> newRecord = crudService.fetchRecordById("address_book", "id = '" + cell + "'");
                String logString = returnLogString(oldRecord, newRecord);

                auditAction("UPDATED", "Updated AddressBook " + title + " id: " + cell + " (" + logString + ")", ipAddress, postdata);

                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                apiResponse.setResponseMessage("Address book updated successfully");

            } else {
                // INSERT
                String insertQuery = "INSERT INTO address_book " +
                        "(title, org_id, created_by, address_type, designationid, intrash) " +
                        "VALUES (:title, :orgId, :createdBy, :addressType, :designation, 'NO')";

                crudService.executeNativeQuery(insertQuery, Map.of(
                        "title", title,
                        "orgId", organisation,
                        "createdBy", createdBy,
                        "addressType", addressType,
                        "designation", designation
                ));

                auditAction("CREATED", "Created AddressBook " + title, ipAddress, postdata);

                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                apiResponse.setResponseMessage("Address book entry created successfully");
            }

        } catch (Exception e) {
            log.error("Error processing address book: {}", e.getMessage(), e);
            apiResponse.setResponseMessage("Unexpected error occurred.");
        }

        return apiResponse;
    }
    public ApiResponse processContactExcelUpload(
            byte[] decryptedFileBytes,
            String addressBookId,
            String fileName,
            HttpServletRequest request
    ) {
        Logger log = LoggerFactory.getLogger(getClass());
        ApiResponse apiResponse = new ApiResponse();
        int valid = 0, duplicate = 0;

        if (fileName == null || fileName.trim().isEmpty()) {
            apiResponse.setResponseCode(ApiResponseCode.FAIL);
            apiResponse.setResponseMessage("Filename is missing.");
            return apiResponse;
        }

        String safeFileName = fileName.trim();
        log.info("Processing file: '{}'", safeFileName);

        if (decryptedFileBytes == null || decryptedFileBytes.length < 10) {
            apiResponse.setResponseCode(ApiResponseCode.FAIL);
            apiResponse.setResponseMessage("Uploaded file is empty or invalid.");
            return apiResponse;
        }

        try {
            // Save file for debugging
            try {
                Path debugFile = Paths.get("debug_" + safeFileName);
                Files.write(debugFile, decryptedFileBytes);
                log.info("Saved decrypted file for debugging at: {}", debugFile.toAbsolutePath());
            } catch (IOException ioex) {
                log.warn("Failed to write debug file: {}", ioex.getMessage());
            }

            try (InputStream is = new ByteArrayInputStream(decryptedFileBytes)) {
                Workbook workbook = WorkbookFactory.create(is); // auto-detects XLS/XLSX

                Sheet sheet = workbook.getSheetAt(0);
                int totalRows = sheet.getPhysicalNumberOfRows() - 1;
                log.info("Total rows in sheet (excluding header): {}", totalRows);

                boolean foundCompleteRow = false;

                // FIRST PASS: Validate rows and detect if any fully filled rows exist
                for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                    Row row = sheet.getRow(rowIndex);

                    // Skip fully empty rows silently
                    if (row == null) continue;

                    Cell phoneCell = row.getCell(0);
                    Cell firstNameCell = row.getCell(1);
                    Cell lastNameCell = row.getCell(2);

                    boolean phoneHasData = !isCellEmpty(phoneCell);
                    boolean firstNameHasData = !isCellEmpty(firstNameCell);
                    boolean lastNameHasData = !isCellEmpty(lastNameCell);

                    // All cells empty - skip
                    if (!phoneHasData && !firstNameHasData && !lastNameHasData) continue;

                    // Partial data row - fail immediately
                    if (!(phoneHasData && firstNameHasData && lastNameHasData)) {
                        String msg = "Row " + rowIndex + " is partially filled (phone, firstname, lastname required). Upload aborted.";
                        log.warn(msg);
                        return failResponse(apiResponse, msg);
                    }

                    // Found at least one complete row
                    foundCompleteRow = true;
                }

                if (!foundCompleteRow) {
                    String msg = "No fully completed rows found in the Excel sheet. Upload aborted.";
                    log.warn(msg);
                    return failResponse(apiResponse, msg);
                }

                // SECOND PASS: Insert valid rows
                for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                    Row row = sheet.getRow(rowIndex);
                    if (row == null) continue;

                    Cell phoneCell = row.getCell(0);
                    Cell firstNameCell = row.getCell(1);
                    Cell lastNameCell = row.getCell(2);

                    boolean phoneHasData = !isCellEmpty(phoneCell);
                    boolean firstNameHasData = !isCellEmpty(firstNameCell);
                    boolean lastNameHasData = !isCellEmpty(lastNameCell);

                    // Skip empty rows silently
                    if (!phoneHasData && !firstNameHasData && !lastNameHasData) continue;

                    // Extract values (already validated that all 3 cells have data)
                    String rawPhone = getCellStringValue(phoneCell);
                    String firstname = getCellStringValue(firstNameCell);
                    String lastname = getCellStringValue(lastNameCell);

                    // Validate phone format
                    String phoneNumber = validatePhoneNumber(rawPhone);
                    if ("0".equals(phoneNumber)) {
                        // Should never reach here because of previous checks
                        continue;
                    }

                    // Check duplicates
                    String checkQuery = "SELECT id FROM address_contacts WHERE phone_number = :phone AND addr_id = :addrId";
                    List<?> exists = crudService.runQuery(checkQuery, Map.of(
                            "phone", phoneNumber,
                            "addrId", addressBookId
                    ));

                    if (exists.isEmpty()) {
                        String insertQuery = "INSERT INTO address_contacts (phone_number, firstname, lastname, addr_id) VALUES (:phone, :fname, :lname, :addrId)";
                        crudService.executeNativeQuery(insertQuery, Map.of(
                                "phone", phoneNumber,
                                "fname", firstname,
                                "lname", lastname,
                                "addrId", addressBookId
                        ));
                        valid++;
                    } else {
                        duplicate++;
                    }

                    auditAction("CREATE", "Created new contact for " + rawPhone, request.getRemoteAddr(), "addressbook=" + addressBookId);
                }

                String message = String.format("%d - Valid, %d - Duplicates out of %d rows",
                        valid, duplicate, totalRows);
                apiResponse.setResponseCode(ApiResponseCode.SUCCESS);
                apiResponse.setResponseMessage(message);

            } catch (Exception e) {
                log.error("Error processing Excel upload", e);
                apiResponse.setResponseCode(ApiResponseCode.FAIL);
                apiResponse.setResponseMessage("Error processing uploaded file.");
            }

        } catch (Exception ex) {
            log.error("Unexpected error", ex);
            apiResponse.setResponseCode(ApiResponseCode.FAIL);
            apiResponse.setResponseMessage("Unexpected error during file processing.");
        }

        return apiResponse;
    }

    private ApiResponse failResponse(ApiResponse apiResponse, String message) {
        apiResponse.setResponseCode(ApiResponseCode.FAIL);
        apiResponse.setResponseMessage(message);
        return apiResponse;
    }

    // Helper method: check if cell is empty or blank
    private boolean isCellEmpty(Cell cell) {
        if (cell == null) return true;
        if (cell.getCellType() == CellType.BLANK) return true;
        if (cell.getCellType() == CellType.STRING && cell.getStringCellValue().trim().isEmpty()) return true;
        return false;
    }

    // Helper method: get cell value as String (handles string and numeric)
    private String getCellStringValue(Cell cell) {
        if (cell == null) return "";
        if (cell.getCellType() == CellType.STRING) {
            return cell.getStringCellValue().trim();
        } else if (cell.getCellType() == CellType.NUMERIC) {
            return String.valueOf((long) cell.getNumericCellValue());
        }
        return "";
    }









    private String returnLogString(Map<String, Object> oldRecord, Map<String, Object> newRecord) {
        StringBuilder log = new StringBuilder();

        for (String key : oldRecord.keySet()) {
            Object oldValue = oldRecord.get(key);
            Object newValue = newRecord.get(key);

            if (!Objects.equals(oldValue, newValue)) {
                log.append(String.format("[%s: %s → %s] ", key, oldValue, newValue));
            }
        }

        return log.toString().trim();
    }

    private void auditAction(String actionType, String description, String ipAddress, String payload) {
        try {
            UserDetailsImpl userDetails = (UserDetailsImpl) SecurityContextHolder
                    .getContext()
                    .getAuthentication()
                    .getPrincipal();

            Long userId = userDetails.getId();
            Long orgId = Long.valueOf(userDetails.getOrganizationId());

            Map<String, Object> params = new HashMap<>();
            params.put("user", userId);
            params.put("activityType", actionType);
            params.put("description", description);
            params.put("resource", "beneficiarytype");
            params.put("getData", "");
            params.put("sourceip", ipAddress);
            params.put("postData", payload);
            params.put("creationTime", (int)(System.currentTimeMillis() / 1000));
            params.put("orgId", orgId);

            String insertAuditQuery = "INSERT INTO activity " +
                    "(user, activityType, description, resource, getData, sourceip, postData, creationTime, org_id) " +
                    "VALUES (:user, :activityType, :description, :resource, :getData, :sourceip, :postData, :creationTime, :orgId)";

            crudService.executeNativeQuery(insertAuditQuery, params);
        } catch (Exception e) {
            log.warn("Failed to insert audit log: {}", e.getMessage(), e);
        }
    }

    public String joinIntegerIds(JSONArray array) {
        if (array == null || array.isEmpty()) return "";
        List<String> ids = new ArrayList<>();
        for (int i = 0; i < array.length(); i++) {
            Object val = array.get(i);
            if (val instanceof Integer || val instanceof Long) {
                ids.add(String.valueOf(val));
            } else {
                throw new JSONException("Expected Integer but found: " + val.getClass());
            }
        }
        return String.join(",", ids);
    }


    private String getApprovalLevelsOrdered(String idsCommaSeparated) {
        if (idsCommaSeparated.isEmpty()) return "";

        // Protect against SQL injection (use parameterized if needed)
        String query = "SELECT approval_level FROM approval_levels WHERE id IN (" + idsCommaSeparated + ") ORDER BY approval_level ASC";

        List<?> results = crudService.fetchWithNativeQuery(query);
        return results.stream()
                .map(row -> {
                    if (row instanceof Object[]) return String.valueOf(((Object[]) row)[0]);
                    else return String.valueOf(row);
                })
                .collect(Collectors.joining(","));
    }
    public String fetchGlobal(String key) {
        String query = "SELECT value FROM global_parameters WHERE key = :key AND ROWNUM = 1";
        List<?> result = crudService.runQuery(query, Map.of("key", key));

        if (!result.isEmpty()) {
            Object row = result.get(0);
            if (row instanceof Object[]) {
                return String.valueOf(((Object[]) row)[0]);
            } else {
                return String.valueOf(row);
            }
        }

        log.warn("Global config not found for key: {}", key);
        return "";
    }


    private String getSessionUser() {
        // Example with Spring Security
        return SecurityContextHolder.getContext().getAuthentication().getName();
    }
    private String getSessionOrgId() {
        UserDetailsImpl userDetails = (UserDetailsImpl) SecurityContextHolder
                .getContext()
                .getAuthentication()
                .getPrincipal();
        return String.valueOf(userDetails.getOrganizationId());
    }
    private List<String> extractPhoneNumbers(JSONObject jsonData) {
        List<String> phoneNumbers = new ArrayList<>();

        String mode = jsonData.optString("mode", "").toLowerCase();

        switch (mode) {
            case "manual":
                // Expecting comma-separated numbers in a string field
                String manualNumbers = jsonData.optString("manualNumbers", "");
                if (!manualNumbers.isEmpty()) {
                    phoneNumbers = Arrays.stream(manualNumbers.split(","))
                            .map(String::trim)
                            .filter(number -> !number.isEmpty())
                            .collect(Collectors.toList());
                }
                break;

            case "upload":
                // Assuming an array of phone numbers from parsed file content
                JSONArray uploadArray = jsonData.optJSONArray("uploadedNumbers");
                if (uploadArray != null) {
                    for (int i = 0; i < uploadArray.length(); i++) {
                        phoneNumbers.add(uploadArray.getString(i).trim());
                    }
                }
                break;

            case "group":
                // Assume you received a JSONArray of numbers based on selected group
                JSONArray groupArray = jsonData.optJSONArray("groupNumbers");
                if (groupArray != null) {
                    for (int i = 0; i < groupArray.length(); i++) {
                        phoneNumbers.add(groupArray.getString(i).trim());
                    }
                }
                break;

            default:
                log.warn("Unknown SMS sending mode: {}", mode);
                break;
        }

        return phoneNumbers;
    }
    private ApiResponse processFundAllocation(FundsAllocationFormRequest fundsAllocationFormRequest, HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse response = ApiResponse.builder()
                .responseCode(ApiResponseCode.FAIL)
                .responseMessage("Failed")
                .build();
        try {
            UserDetailsImpl userDetails = (UserDetailsImpl) SecurityContextHolder.getContext()
                    .getAuthentication()
                    .getPrincipal();
            int userId = Math.toIntExact(userDetails.getId());
            String recordPrimaryKey = fundsAllocationFormRequest.getFormCell();
            String action = fundsAllocationFormRequest.getAction();
            if (action.equalsIgnoreCase("DELETE")){
                String deleteSql = "UPDATE fundAllocation SET intrash = 'Yes' WHERE id = :id";
                HashMap<String, Object> params = new HashMap<>();
                params.put("id", recordPrimaryKey);
                crudService.executeNativeQuery(deleteSql, params);
                response.setResponseMessage("Fund allocation has been deleted successfully");
                response.setResponseCode(ApiResponseCode.SUCCESS);
                sharedFunctions.auditAction("DELETE", "DELETED USER ID: "+ recordPrimaryKey, httpServletRequest.getRemoteAddr(), fundsAllocationFormRequest.toString(),null, userId, null);

                return response;
            }else {
                if (recordPrimaryKey != null){
                    String budget = fundsAllocationFormRequest.getBudget();
                    String financier = fundsAllocationFormRequest.getFinancier();
                    String notes = fundsAllocationFormRequest.getDescription();
                    String credit = fundsAllocationFormRequest.getAmountToCredit();
                    String update = "update fundAllocation set budget =:budget, financier=:financier,notes=:notes, credit=:credit where id:cell";
                    HashMap<String, Object> params1 = new HashMap<>();
                    params1.put("cell", recordPrimaryKey);
                    params1.put("budget", budget);
                    params1.put("financier", financier);
                    params1.put("notes", notes);
                    params1.put("credit", credit);
                    crudService.executeNativeQuery(update,params1);

                    Map<String, Object> paramsq = Map.of("id", new BigInteger(recordPrimaryKey));

                    String record1 = sharedFunctions.getRecordById("payment", "id = :id", paramsq);
                    String update2 = "update payment set credit_amount = :credit where order_type = :cell and trx_type = 'Fund Allocation'";
                    HashMap<String, Object> params2 = new HashMap<>();
                    params2.put("credit", credit);
                    params2.put("cell", recordPrimaryKey);
                    crudService.executeNativeQuery(update2, params2);


                    Map<String, Object> params3 = Map.of("id", new BigInteger(recordPrimaryKey));
                    String record2 = sharedFunctions.getRecordById("PARAM", "POSITION = :id", params3);
                    String logString = sharedFunctions.returnLogString(record1, record2);

                    // Audit the update
                    sharedFunctions.auditAction("UPDATE",
                            "Updated fund allocation to " + budget + " (" + logString + ")",
                            httpServletRequest.getRemoteAddr(),
                            fundsAllocationFormRequest.toString(), null,userId, null);


                }else {//insert new fund allocation
                    Integer orgid = Integer.valueOf(fundsAllocationFormRequest.getOrgId());
                    String trxType = fundsAllocationFormRequest.getTrxType();
                    String sourceIp = sharedFunctions.getSourceIp(servletRequest); // Assume you have this method
                    String notes = fundsAllocationFormRequest.getDescription();
                    String financier = fundsAllocationFormRequest.getFinancier();
                    String user = String.valueOf(userId);
                    String credit = fundsAllocationFormRequest.getAmountToCredit();

                    switch (trxType){
                        case "Transfer":
                            // Fetch MAX(id) from fundAllocation
                            String maxIdSql = "SELECT MAX(id) as max_id FROM fundAllocation";
                            List<Map<String, Object>> maxIdResult = crudService.fetchWithNativeQuery(maxIdSql, new HashMap<>(), 0, 1);
                            BigInteger idNew = (BigInteger) maxIdResult.get(0).get("max_id");

                            BigInteger idDebit = idNew.add(BigInteger.ONE);
                            BigInteger idCredit = idNew.add(BigInteger.TWO);

                            String budgetFrom = fundsAllocationFormRequest.getBudgetFrom();
                             notes = fundsAllocationFormRequest.getDescription();
                            String trxTypeTransfer = fundsAllocationFormRequest.getTrxType();
                            Integer orgId = Integer.valueOf(fundsAllocationFormRequest.getOrgId());
                            long now = Instant.now().getEpochSecond();

                            // Insert into fundAllocation (Debit)
                            String insertDebit = """
                                INSERT INTO fundAllocation (id, user, trx_type, debit, budget, notes, sourceip, financier, creationtime, org_id)
                                VALUES (:id, :user, :trx_type, :debit, :budget, :notes, :sourceip, :financier, :creationtime, :org_id)
                            """;

                            Map<String, Object> debitParams = Map.of(
                                    "id", idDebit,
                                    "user", user,
                                    "trx_type", trxTypeTransfer,
                                    "debit", credit,
                                    "budget", budgetFrom,
                                    "notes", notes,
                                    "sourceip", sourceIp,
                                    "financier", financier,
                                    "creationtime", now,
                                    "org_id", orgId
                            );

                            crudService.executeNativeQuery(insertDebit, debitParams);

                            // Insert into payment
                            String insertPayment = """
                                INSERT INTO payment(order_id, budget_id, debit_amount, trx_type, trx_status, order_type)
                                VALUES ('0', :budget_id, :debit_amount, 'Fund Allocation', 'Completed', :order_type)
                            """;

                            Map<String, Object> paymentParams = Map.of(
                                    "budget_id", budgetFrom,
                                    "debit_amount", credit,
                                    "order_type", idDebit
                            );

                            crudService.executeNativeQuery(insertPayment, paymentParams);
                            break;

                        case "Funds Allocation":
                            int i = 1;
                            StringBuilder test = new StringBuilder();
                            double totalAmount = 0.0;
                            Integer rowcount = Integer.valueOf(fundsAllocationFormRequest.getRowcount());

                            while (i <= rowcount) {
                                // Fetch MAX(id) from fundAllocation
                                String maxIdQuery = "SELECT MAX(id) as max_id FROM fundAllocation";
                                List<Map<String, Object>> maxIdResult1 = crudService.fetchWithNativeQuery(maxIdQuery, new HashMap<>(), 0, 1);
                                BigInteger idNew1 = (BigInteger) maxIdResult1.get(0).get("max_id");

                                BigInteger idDebit1 = idNew1.add(BigInteger.ONE);
                                BigInteger idCredit1 = idNew1.add(BigInteger.TWO);
                                test.append(" ").append(i);

                                // Read amount and budget ID from request (assuming it's passed from frontend)
                                String creditStr = fundsAllocationFormRequest.getFundalloAmount_() +i;
                                String budgetTo = fundsAllocationFormRequest.getBudgetLineId_() + i;
                                double credit1 = Double.parseDouble(creditStr);
                                totalAmount += credit1;

                                // Insert into fundAllocation (credit)
                                String insertCreditSQL = """
                                    INSERT INTO fundAllocation (id, user, trx_type, credit, source_budget, budget, notes, sourceip, financier, creationtime, org_id)
                                    VALUES (:id, :user, :trx_type, :credit, '0', :budget, :notes, :sourceip, :financier, :creationtime, :org_id)
                                """;

                                Map<String, Object> creditParams = Map.of(
                                        "id", idCredit1,
                                        "user", user,
                                        "trx_type", trxType,
                                        "credit", credit1,
                                        "budget", budgetTo,
                                        "notes", notes,
                                        "sourceip", sourceIp,
                                        "financier", financier,
                                        "creationtime", Instant.now().getEpochSecond(),
                                        "org_id", orgid
                                );

                                crudService.executeNativeQuery(insertCreditSQL, creditParams);

                                // Insert into payment
                                String insertPaymentSQL = """
                                    INSERT INTO payment(order_id, budget_id, credit_amount, trx_type, trx_status, order_type, org_id)
                                    VALUES ('0', :budget_id, :credit_amount, 'Fund Allocation', 'Completed', :order_type, :org_id)
                                """;

                                Map<String, Object> paymentParams1 = Map.of(
                                        "budget_id", budgetTo,
                                        "credit_amount", credit,
                                        "order_type", idCredit1,
                                        "org_id", orgid
                                );

                                crudService.executeNativeQuery(insertPaymentSQL, paymentParams1);

                                i++;
                            }

                            // --- Begin APIORDER Insert Logic (Intra Transfers) ---

                            // Fetch user info
                            String userSql = """
                                SELECT login, firstname, lastname, org_id, short_code
                                FROM user
                                INNER JOIN organisation ON user.org_id = organisation.id
                                WHERE user.id = :userId
                            """;
                            Map<String, Object> userParams = Map.of("userId", userId);
                            List<Map<String, Object>> userResultList = crudService.fetchWithNativeQuery(userSql, userParams, 0, 1);
                            Map<String, Object> userRow = userResultList.get(0);

                            String firstname = (String) userRow.get("firstname");
                            String middlename = (String) userRow.get("lastname"); // used twice in PHP
                            String lastname = (String) userRow.get("lastname");
                            String msisdn = (String) userRow.get("short_code");

                            // Fetch ordertype id
                            String ordertypeSql = "SELECT id FROM ordertype WHERE title = 'BusinessTransferFromMMFToUtility'";
                            List<Map<String, Object>> orderTypeList = crudService.fetchWithNativeQuery(ordertypeSql, new HashMap<>(), 0, 1);
                            String ordertypeId = orderTypeList.get(0).get("id").toString();

                            // Fetch approval order
                            // 1. Fetch approvalOrder
                            String approvalQuery = "SELECT approvalOrder FROM org_ordertype WHERE ordertypeId = :orderTypeId AND org_id = :orgId";
                            Map<String, Object> approvalParams = Map.of("orderTypeId", ordertypeId, "orgId", orgid);
                            List<Map<String, Object>> approvalList = crudService.fetchWithNativeQuery(approvalQuery, approvalParams, 0, 1);

                            String approvalOrderStr = approvalList.get(0).get("approvalOrder").toString();
                            String[] levels = approvalOrderStr.split(",");
                            String currentLevel = levels.length > 0 ? levels[0] : "1";

                            // 2. Get current timestamp as string
                            String requestTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

                            // 3. Determine approval logic
                            String approvalLevel;
                            String approvalStatus;

                            if ("APPROVE".equalsIgnoreCase(approvalOrderStr.trim())) {
                                approvalLevel = "8";
                                approvalStatus = "Approved";
                            } else {
                                approvalLevel = currentLevel;
                                approvalStatus = "Pending";
                            }

                            // 4. Prepare and execute insert into apiorder
                            String insertApiOrder = """
                                INSERT INTO apiorder(treatment_model, initiator_id, initiator_username, firstname, middlename, lastname,
                                ordertype, budget, credit, msisdn, notes, district, county, province, request_src, requesttime, approval_level,
                                approval_status, sourceip, batchno, org_id, general_comments)
                                VALUES ('0', :initiator_id, :initiator_username, :firstname, :middlename, :lastname, :ordertype, '0',
                                :credit, :msisdn, 'BusinessTransferFromMMFToUtility request', 'HeadQuartes', 'HeadQuartes', 'HeadQuartes',
                                'web', :requesttime, :approval_level, :approval_status, :sourceip, 'BATCHINTRA', :org_id, :general_comments)
                            """;

                            Map<String, Object> apiOrderParams = new HashMap<>();
                            apiOrderParams.put("initiator_id", userId);
                            apiOrderParams.put("initiator_username", user);
                            apiOrderParams.put("firstname", firstname);
                            apiOrderParams.put("middlename", middlename);
                            apiOrderParams.put("lastname", lastname);
                            apiOrderParams.put("ordertype", ordertypeId);
                            apiOrderParams.put("credit", totalAmount);
                            apiOrderParams.put("msisdn", msisdn);
                            apiOrderParams.put("requesttime", requestTime);
                            apiOrderParams.put("approval_level", approvalLevel);
                            apiOrderParams.put("approval_status", approvalStatus);
                            apiOrderParams.put("sourceip", sourceIp);
                            apiOrderParams.put("org_id", orgid);
                            apiOrderParams.put("general_comments", notes);

                            // Step 5: Execute the insert
                            int result = crudService.executeNativeQuery(insertApiOrder, apiOrderParams);

                            // Step 6: Audit allocation action
                            sharedFunctions.auditAction("CREATE",
                                    "Allocated funds to budget line " + fundsAllocationFormRequest.getBudget(),
                                    httpServletRequest.getRemoteAddr(),
                                    fundsAllocationFormRequest.toString(), null, userId, null);

                            // Step 7: If insert succeeded
                            if (result > 0) {
                                // Step 8: Fetch back the inserted order (just like in PHP)
                                String selectOrderQuery = """
                                    SELECT id, msisdn, credit, driver_amount, driver_phone, recipient2credit, recipient2msisdn
                                    FROM apiorder
                                    WHERE requesttime = :requesttime AND msisdn = :msisdn
                                    ORDER BY id DESC LIMIT 1
                                """;

                                Map<String, Object> orderParams = new HashMap<>();
                                orderParams.put("requesttime", requestTime);
                                orderParams.put("msisdn", msisdn);

                                List<Map<String, Object>> orderResult = crudService.fetchWithNativeQuery(selectOrderQuery, orderParams, 0, 1);

                                if (!orderResult.isEmpty()) {
                                    Map<String, Object> row = orderResult.get(0);
                                    Long orderId = ((Number) row.get("id")).longValue();

                                    // Step 9: Build transaction map
                                    HashMap<String, String> trxMap = new HashMap<>();
                                    trxMap.put("id", (String) row.get("id"));
                                    trxMap.put("msisdn", (String) row.get("msisdn"));
                                    trxMap.put("credit", (String) row.get("credit"));
                                    trxMap.put("driver_amount", (String) row.get("driver_amount"));
                                    trxMap.put("driver_phone", (String) row.get("driver_phone"));
                                    trxMap.put("recipient2credit", (String) row.get("recipient2credit"));
                                    trxMap.put("recipient2msisdn", (String) row.get("recipient2msisdn"));

                                    // Step 10: Generate hash
                                    String trxChain = sharedFunctions.updateChainTrx(null, trxMap, String.valueOf(orderId),null);

                                    // Step 11: Save to TRX_VALUES
                                    String trxInsertQuery = "INSERT INTO TRX_VALUES(ORDER_ID, T_VALUE) VALUES(:orderId, :t_value)";
                                    Map<String, Object> trxParams = Map.of(
                                            "orderId", orderId,
                                            "t_value", trxChain
                                    );
                                    crudService.executeNativeQuery(trxInsertQuery, trxParams);

                                    // Step 12: Final audit
                                    sharedFunctions.auditAction("CREATED",
                                            "Created new order transaction id",
                                            orderId.toString(),
                                            httpServletRequest.getRemoteAddr(),
                                            null, userId, null);
                                    response.setResponseMessage(""+trxType+" Successfully Executed");
                                    response.setResponseCode(ApiResponseCode.SUCCESS);
                                }
                            } else {
                                log.info("Insert query failed");
                            }


                            break;
                        case "Funds Reversal" :
                            // Step 1: Get the max ID from fundAllocation
                            String maxIdQuery4 = "SELECT MAX(id) FROM fundAllocation";
                            List<Map<String, Object>> maxIdResult4 = crudService.fetchWithNativeQuery(maxIdQuery4, new HashMap<>(), 0, 1);
                            Long idNew4 = ((Number) maxIdResult4.get(0).get("MAX(id)")).longValue();
                            Long idDebit4 = idNew4 + 1;
                            Long idCredit4 = idNew4 + 2;

                             requestTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                            Long unixTime = Instant.now().getEpochSecond();

                             sourceIp = httpServletRequest.getRemoteAddr();
                             budgetFrom = fundsAllocationFormRequest.getBudgetFrom();;

                            // Step 2: Insert into fundAllocation (debit as reversal)
                            String insertReversal = """
                                INSERT INTO fundAllocation (id, user, trx_type, debit, source_budget, budget, notes, sourceip, financier, creationtime, org_id)
                                VALUES (:id, :user, :trx_type, :debit, :source_budget, :budget, :notes, :sourceip, :financier, :creationtime, :org_id)
                            """;

                            Map<String, Object> reversalParams = new HashMap<>();
                            reversalParams.put("id", idCredit4);
                            reversalParams.put("user", userId);
                            reversalParams.put("trx_type", trxType);
                            reversalParams.put("debit", credit);
                            reversalParams.put("source_budget", budgetFrom);
                            reversalParams.put("budget", budgetFrom);
                            reversalParams.put("notes", notes);
                            reversalParams.put("sourceip", sourceIp);
                            reversalParams.put("financier", financier);
                            reversalParams.put("creationtime", unixTime);
                            reversalParams.put("org_id", orgid);
                            crudService.executeNativeQuery(insertReversal, reversalParams);

                            // Step 3: Insert into payment
                            String insertPayment3 = """
                                INSERT INTO payment (order_id, budget_id, debit_amount, trx_type, trx_status, order_type, org_id)
                                VALUES ('0', :budget_id, :debit_amount, 'Fund Reversal', 'Completed', :order_type, :org_id)
                            """;
                            Map<String, Object> paymentParams3 = new HashMap<>();
                            paymentParams3.put("budget_id", budgetFrom);
                            paymentParams3.put("debit_amount", credit);
                            paymentParams3.put("order_type", idCredit4);
                            paymentParams3.put("org_id", orgid);
                            crudService.executeNativeQuery(insertPayment3, paymentParams3);

                            // Step 4: Get user info
                            String userSql3 = """
                                SELECT login, firstname, lastname, org_id, short_code 
                                FROM user INNER JOIN organisation ON user.org_id = organisation.id 
                                WHERE user.id = :uid
                            """;
                            Map<String, Object> userParams3 = Map.of("uid", userId);
                            Map<String, Object> userResult = (Map<String, Object>) crudService.fetchWithNativeQuery(userSql3, userParams3, 0, 1).get(0);

                            // Step 5: Get ordertype for BusinessTransferFromUtilityToMMF
                            String ordertypeSql3 = "SELECT id FROM ordertype WHERE title = 'BusinessTransferFromUtilityToMMF' LIMIT 1";

                            // Execute query
                            List<?> orderTypeResultList = crudService.fetchWithNativeQuery(ordertypeSql3, new HashMap<>(), 0, 1);

                            // Extract the ID safely
                            Long ordertypeId3 = null;
                            if (!orderTypeResultList.isEmpty()) {
                                Object[] row = (orderTypeResultList.get(0) instanceof Object[])
                                        ? (Object[]) orderTypeResultList.get(0)
                                        : new Object[]{orderTypeResultList.get(0)};

                                ordertypeId3 = row[0] != null ? Long.parseLong(row[0].toString()) : null;
                            }
                            // Step 6: Get approvalOrder and level
                            String approvalQuery3 = "SELECT approvalOrder FROM org_ordertype WHERE ordertypeId = :ordertypeId AND org_id = :orgId";

                            Map<String, Object> approvalParams3 = new HashMap<>();
                            approvalParams3.put("ordertypeId", ordertypeId3);
                            approvalParams3.put("orgId", orgid);

                            List<Map<String, Object>> approvalResults3 = crudService.fetchWithNativeQuery(approvalQuery3, approvalParams3, 0, 1);

                            // Extract approvalOrder and current level safely
                            String approvalOrderStr3 = "";
                            String currentLevel3 = "";

                            if (!approvalResults3.isEmpty() && approvalResults3.get(0).get("approvalOrder") != null) {
                                approvalOrderStr3 = approvalResults3.get(0).get("approvalOrder").toString();
                                currentLevel3 = approvalOrderStr3.split(",")[0];
                            }

                            // Step 7: Insert apiorder
                            String apiOrderInsert = """
                                INSERT INTO apiorder (treatment_model, initiator_id, initiator_username, firstname, middlename, lastname, ordertype, budget, 
                                credit, msisdn, notes, district, county, province, request_src, requesttime, approval_level, approval_status, sourceip, batchno, org_id, general_comments)
                                VALUES ('0', :initiator_id, :initiator_username, :firstname, :middlename, :lastname, :ordertype, '0',
                                :credit, :msisdn, 'BusinessTransferFromUtilityToMMF request', 'HeadQuartes', 'HeadQuartes', 'HeadQuartes', 
                                'web', :requesttime, :approval_level, :approval_status, :sourceip, 'BATCHINTRA', :org_id, :comments)
                            """;

                            Map<String, Object> apiParams = new HashMap<>();
                            apiParams.put("initiator_id", userId);
                            apiParams.put("initiator_username", user);
                            apiParams.put("firstname", userResult.get("firstname"));
                            apiParams.put("middlename", userResult.get("lastname"));
                            apiParams.put("lastname", userResult.get("lastname"));
                            apiParams.put("ordertype", ordertypeId3);
                            apiParams.put("credit", credit);
                            apiParams.put("msisdn", userResult.get("short_code"));
                            apiParams.put("requesttime", requestTime);
                            apiParams.put("approval_level", currentLevel3);
                            apiParams.put("approval_status", "Pending");
                            apiParams.put("sourceip", sourceIp);
                            apiParams.put("org_id", orgid);
                            apiParams.put("comments", notes);
                            int result3 = crudService.executeNativeQuery(apiOrderInsert, apiParams);

                            // Step 8: Audit allocation
                            sharedFunctions.auditAction("CREATE",
                                    "Reversed funds from budget line " + budgetFrom,
                                    sourceIp,
                                    fundsAllocationFormRequest.toString(), null, userId, null);

                            // Step 9: Post-insert logic
                            if (result3 > 0) {
                                String fetchInsertedOrder = """
                                    SELECT id, msisdn, credit, driver_amount, driver_phone, recipient2credit, recipient2msisdn
                                    FROM apiorder 
                                    WHERE requesttime = :requesttime AND msisdn = :msisdn 
                                    ORDER BY id DESC LIMIT 1
                                """;

                                // Use HashMap instead of Map.of()
                                Map<String, Object> fetchParams = new HashMap<>();
                                fetchParams.put("requesttime", requestTime);
                                fetchParams.put("msisdn", userResult.get("short_code"));

                                // Execute query
                                List<Map<String, Object>> resultList = crudService.fetchWithNativeQuery(fetchInsertedOrder, fetchParams, 0, 1);

                                if (!resultList.isEmpty()) {
                                    Map<String, Object> row = resultList.get(0);

                                    Long insertedOrderId = row.get("id") != null ? ((Number) row.get("id")).longValue() : null;
                                    String msisdn1 = row.get("msisdn") != null ? row.get("msisdn").toString() : null;
                                    String credit1 = row.get("credit") != null ? row.get("credit").toString() : null;
                                    String driverAmount = row.get("driver_amount") != null ? row.get("driver_amount").toString() : null;
                                    String driverPhone = row.get("driver_phone") != null ? row.get("driver_phone").toString() : null;
                                    String recipient2Credit = row.get("recipient2credit") != null ? row.get("recipient2credit").toString() : null;
                                    String recipient2Msisdn = row.get("recipient2msisdn") != null ? row.get("recipient2msisdn").toString() : null;


                                    // Step 10: Build transaction map
                                    HashMap<String, String> trxMap = new HashMap<>();
                                    trxMap.put("id", String.valueOf(insertedOrderId));
                                    trxMap.put("msisdn", msisdn1);
                                    trxMap.put("credit", credit1);
                                    trxMap.put("driver_amount", driverAmount);
                                    trxMap.put("driver_phone", driverPhone);
                                    trxMap.put("recipient2credit", recipient2Credit);
                                    trxMap.put("recipient2msisdn", recipient2Msisdn);

                                    // Step 11: Generate chain and insert
                                    String trxChain = sharedFunctions.updateChainTrx(null, trxMap, String.valueOf(insertedOrderId), null);
                                    String insertChain = "INSERT INTO TRX_VALUES(ORDER_ID, T_VALUE) VALUES(:orderId, :t_value)";
                                    Map<String, Object> chainParams = Map.of("orderId", insertedOrderId, "t_value", trxChain);
                                    crudService.executeNativeQuery(insertChain, chainParams);

                                    // Step 12: Final audit
                                    sharedFunctions.auditAction("CREATED",
                                            "Created new order transaction id",
                                            insertedOrderId.toString(),
                                            sourceIp, null, userId, null);
                                }
                                response.setResponseMessage(""+trxType+" Successfully Executed");
                                response.setResponseCode(ApiResponseCode.valueOf("00"));
                            } else {
                                log.info("Insert query failed");
                            }

                            // Optional: Set confirmation message in session-equivalent
                            // session.setAttribute("notes", trxType + " Successfully Executed");
                            break;
                    }









                }
            }



        } catch (Exception e) {
            throw new RuntimeException(e);
        }
       return response;
    }
    private ApiResponse processReverseApproval(ReverseApprovalRequest reverseApprovalRequest, HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse response = ApiResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL)
                .build();

        try {
            // Extract and map request/session variables
            String approvalLevel = reverseApprovalRequest.getApprovalLevelName();
            String reversalNotes = reverseApprovalRequest.getReversalNotes();
            String approvedAmount = reverseApprovalRequest.getApprovedAmount();
            Long apiOrderId = Long.valueOf(reverseApprovalRequest.getFormCell()); // cell
            int approverId = Math.toIntExact(((UserDetailsImpl) SecurityContextHolder.getContext().getAuthentication().getPrincipal()).getId());
            String approvalStatus = "Reversed Approval";
            String approvalTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            // 1. Update apiorder approval_level where approval_status is Pending
            String updateApiOrder = """
                UPDATE apiorder 
                SET approval_level = :approval_level 
                WHERE approval_status = 'Pending' AND id = :cell
            """;
            Map<String, Object> updateParams = new HashMap<>();
            updateParams.put("approval_level", approvalLevel);
            updateParams.put("cell", apiOrderId);
            crudService.executeNativeQuery(updateApiOrder, updateParams);

            // 2. Insert new record into payment_approval by selecting from an earlier one
            String insertCopyApproval = """
                INSERT INTO payment_approval(apiorder_id, approver_id, approval_level, approval_level_name, approval_notes, 
                approved_amount, original_amount, original_recipient2amount, approved_recipient2amount, approval_time, approval_status)
                SELECT apiorder_id, :approver_id, approval_level, approval_level_name, :reversal_notes, approved_amount, 
                original_amount, original_recipient2amount, approved_recipient2amount, CURRENT_TIMESTAMP, 'Reversed Approval'
                FROM payment_approval WHERE approval_status = 'Approved' AND id = :pa_id
            """;
            Map<String, Object> insertCopyParams = new HashMap<>();
            insertCopyParams.put("approver_id", approverId);
            insertCopyParams.put("reversal_notes", reversalNotes);
            insertCopyParams.put("pa_id", reverseApprovalRequest.getPaId());
            crudService.executeNativeQuery(insertCopyApproval, insertCopyParams);

            // 3. Direct insert into payment_approval (like bind_param)
            String directInsert = """
                INSERT INTO payment_approval (apiorder_id, approver_id, approval_level, approval_status, approval_notes, 
                approved_amount, approval_time)
                VALUES (:apiorder_id, :approver_id, :approval_level, :approval_status, :approval_notes, :approved_amount, :approval_time)
            """;
            Map<String, Object> insertParams = new HashMap<>();
            insertParams.put("apiorder_id", apiOrderId);
            insertParams.put("approver_id", approverId);
            insertParams.put("approval_level", approvalLevel);
            insertParams.put("approval_status", approvalStatus);
            insertParams.put("approval_notes", reversalNotes);
            insertParams.put("approved_amount", approvedAmount);
            insertParams.put("approval_time", approvalTime);
            crudService.executeNativeQuery(directInsert, insertParams);

            // 4. Audit before/after states
            Map<String, Object> auditFetchParams = new HashMap<>();
            auditFetchParams.put("id", apiOrderId);

            String fetchRecord = "SELECT * FROM apiorder WHERE id = :id";
            List<Map<String, Object>> beforeRecordList = crudService.fetchWithNativeQuery(fetchRecord, auditFetchParams, 0, 1);
            Map<String, Object> record1 = beforeRecordList.isEmpty() ? new HashMap<>() : beforeRecordList.get(0);

            // assuming after update fetch
            List<Map<String, Object>> afterRecordList = crudService.fetchWithNativeQuery(fetchRecord, auditFetchParams, 0, 1);
            Map<String, Object> record2 = afterRecordList.isEmpty() ? new HashMap<>() : afterRecordList.get(0);

            String logString = sharedFunctions.returnLogString(record1.toString(), record2.toString());
            sharedFunctions.auditAction("Reversed Approval",
                    "Reversed Approval of payment: " + apiOrderId + " (" + logString + ")",
                    servletRequest.getRemoteAddr(),
                    reverseApprovalRequest.toString(),
                    null,
                    approverId,
                    null
            );

            response.setResponseMessage("Reversal of approval successful");
            response.setResponseCode(ApiResponseCode.SUCCESS);

        } catch (Exception e) {
            log.error("An error occurred during reversal approval: ", e);
            response.setResponseCode(ApiResponseCode.FAIL);
            response.setResponseMessage("An error occurred: " + e.getMessage());
            throw new RuntimeException(e);
        }

        return response;
    }
    private ApiResponse processReverseRejection(ReversalRejectionRequest reversalRejectionRequest, HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        ApiResponse response = ApiResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL)
                .build();
        try {
            UserDetailsImpl userDetails = (UserDetailsImpl) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            int userId = Math.toIntExact(userDetails.getId());

            Long cell = Long.valueOf(reversalRejectionRequest.getFormCell());
            String reversalNotes = reversalRejectionRequest.getReversalNotes();
            Long paId = Long.valueOf(reversalRejectionRequest.getPaId());

            // 1. Get approved_amount
            String amountQuery = "SELECT approved_amount FROM payment_approval WHERE apiorder_id = :cell AND approved_amount > 0 ORDER BY id DESC ";
            Map<String, Object> amountParams = new HashMap<>();
            amountParams.put("cell", cell);

            List<Object> amountResult = crudService.fetchWithNativeQuery(amountQuery, amountParams, 0, 1);
            BigDecimal amount = (amountResult != null && !amountResult.isEmpty())
                    ? (BigDecimal) amountResult.get(0)
                    : BigDecimal.ZERO;


            // 2. Save before record (for auditing)
            Map<String, Object> paramsq = Map.of("id", new BigInteger(String.valueOf(cell)));

            String record1 = sharedFunctions.getRecordById("apiorder", "id = :id", paramsq);

            // 3. Update apiorder to set approval_status back to Pending and restore credit
            String updateQuery = """
                UPDATE apiorder
                SET approval_status = 'Pending',
                    credit = CASE WHEN credit = 0 THEN :amount ELSE credit END
                WHERE approval_status = 'Rejected' AND id = :cell
                """;

            Map<String, Object> updateParams = new HashMap<>();
            updateParams.put("amount", amount);
            updateParams.put("cell", cell);
            crudService.executeNativeQuery(updateQuery, updateParams);

            // 4. Insert into payment_approval by selecting from rejected record
            String insertQuery = """
                INSERT INTO payment_approval (
                    apiorder_id, approver_id, approval_level, approval_level_name,
                    approval_notes, approved_amount, original_amount,
                    original_recipient2amount, approved_recipient2amount,
                    approval_time, approval_status
                )
                SELECT apiorder_id, :userId, approval_level, approval_level_name,
                       :reversalNotes, approved_amount, original_amount,
                       original_recipient2amount, approved_recipient2amount,
                       CURRENT_TIMESTAMP, 'Reversed Rejection'
                FROM payment_approval
                WHERE approval_status = 'Rejected' AND id = :paId
                """;

            Map<String, Object> insertParams = new HashMap<>();
            insertParams.put("userId", userId);
            insertParams.put("reversalNotes", reversalNotes);
            insertParams.put("paId", paId);
            crudService.executeNativeQuery(insertQuery, insertParams);

            // 5. Save after record
            Map<String, Object> paramsq1 = Map.of("id", new BigInteger(String.valueOf(cell)));

            String record2 = sharedFunctions.getRecordById("apiorder", "id = :id", paramsq1);

            // 6. Generate audit log
            String logString = sharedFunctions.returnLogString(record1, record2);
            sharedFunctions.auditAction("Rejection Reversal",
                    "Reversed rejection of payment: " + cell + " (" + logString + ")",
                    servletRequest.getRemoteAddr(),
                    reversalRejectionRequest.toString(),
                    null, userId, null);

            // 7. Final response
            response.setResponseCode(ApiResponseCode.SUCCESS);
            response.setResponseMessage("Rejected reversal successful");

        } catch (Exception e) {
            log.error("An error occurred while reversing rejection", e);
            response.setResponseCode(ApiResponseCode.FAIL);
            response.setResponseMessage("An error occurred");
        }
        return response;
    }


    /**
     * Validates a phone number.
     * This method returns the cleaned phone number if valid, or "0" if invalid.
     */

        private String validatePhoneNumber(String phoneNumber) {
        if (phoneNumber == null) return "0";

        // Remove spaces, dashes, and other non-digit characters
        String cleaned = phoneNumber.replaceAll("[^\\d]", "");

        // Basic length and prefix checks for Kenyan numbers, adjust as needed
        if (cleaned.matches("^(2547\\d{8}|07\\d{8})$")) {
            // Normalize to international format
            if (cleaned.startsWith("07")) {
                return "254" + cleaned.substring(1);
            } else {
                return cleaned;
            }
        }

        return "0"; // Invalid number
    }
    private MultipartFile extractFileFromRequest(HttpServletRequest request) {
        if (request instanceof MultipartHttpServletRequest multipartRequest) {
            return multipartRequest.getFile("contactsfile"); // "contactsfile" should match your frontend input name
        }
        return null;
    }
    public String sanitizeDescription(String description) {
        return description.replaceAll("[^\\x00-\\x7F]", ""); // Remove non-ASCII characters
    }
    private boolean isValidInteger(String value) {
        return value != null && value.matches("\\d+");
    }









}

