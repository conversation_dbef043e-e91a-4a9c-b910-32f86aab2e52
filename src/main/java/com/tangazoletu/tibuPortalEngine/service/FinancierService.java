package com.tangazoletu.tibuPortalEngine.service;

import com.tangazoletu.tibuPortalEngine.dto.FinancierResponse;
import com.tangazoletu.tibuPortalEngine.dto.FundAllocation;
import com.tangazoletu.tibuPortalEngine.dto.TransactionResponse;
import com.tangazoletu.tibuPortalEngine.entities.Financier;
import com.tangazoletu.tibuPortalEngine.dto.FinancierRequest;
import com.tangazoletu.tibuPortalEngine.enums.ApiResponse;
import com.tangazoletu.tibuPortalEngine.enums.ApiResponseCode;
import com.tangazoletu.tibuPortalEngine.repositories.FinancierRepo;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class FinancierService {
    private final FinancierRepo financierRepo;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    public FinancierResponse findAll(FinancierRequest  request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {

        FinancierResponse financierResponse = FinancierResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();
        try {
            int page = request.getPage();
            int pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);
            String keyword = request.getKeyword();
            Integer id = request.getId();

            Page<Financier> financiers = null;

            if (id != null) {
                financiers = financierRepo.findById(id, pageable);
            }else {
                financiers = financierRepo.findByKeyword(keyword, pageable);
            }
            if (!financiers.isEmpty()) {
                financierResponse.setData(financiers);
                financierResponse.setResponseMessage("Success");
                financierResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
            }else {
                financierResponse.setResponseMessage("No data found");
                financierResponse.setResponseCode(ApiResponse.FAIL.getCode());

            }


        }catch (Exception e){
            financierResponse.setResponseCode(ApiResponse.FAIL.getCode());
            financierResponse.setResponseMessage(e.getMessage());
            log.error("FinancierService findAll exception", e);
            e.printStackTrace();
        }
        return financierResponse;
        
    }


    public TransactionResponse<FundAllocation> findAllTransactions() {
        String sql = "SELECT * FROM fundAllocation";

        List<FundAllocation> records = jdbcTemplate.query(sql, (rs, rowNum) -> {
            FundAllocation fa = new FundAllocation();
            fa.setId(rs.getInt("ID"));
            fa.setUser(rs.getInt("user"));
            fa.setDebit(rs.getBigDecimal("debit"));
            fa.setCredit(rs.getBigDecimal("credit"));
            fa.setTrxType(rs.getString("trx_type"));
            fa.setSourceBudget(rs.getObject("source_budget", Integer.class));
            fa.setBudget(rs.getInt("budget"));
            fa.setFinancier(rs.getInt("financier"));
            fa.setSourceIP(rs.getString("sourceIP"));
            fa.setNotes(rs.getString("notes"));
            fa.setCreationTime(rs.getInt("creationTime"));
            fa.setInTrash(rs.getString("inTrash"));
            fa.setOrgId(rs.getInt("org_id"));
            return fa;
        });

        // Create Page manually
        Page<FundAllocation> page = new PageImpl<>(records, PageRequest.of(0, records.size()), records.size());

        TransactionResponse<FundAllocation> response = new TransactionResponse<>();
        response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
        response.setResponseMessage("Fetched successfully");
        response.setData(page);

        return response;
    }
}
