package com.tangazoletu.tibuPortalEngine.service;

import com.tangazoletu.tibuPortalEngine.dto.UtilityRequest;
import com.tangazoletu.tibuPortalEngine.dto.UtilityResponse;
import com.tangazoletu.tibuPortalEngine.entities.Budget;
import com.tangazoletu.tibuPortalEngine.entities.Financier;
import com.tangazoletu.tibuPortalEngine.entities.MessageOutbox;
import com.tangazoletu.tibuPortalEngine.enums.ApiResponseCode;
import com.tangazoletu.tibuPortalEngine.repositories.BudgetRepo;
import com.tangazoletu.tibuPortalEngine.repositories.FinancierRepo;
import com.tangazoletu.tibuPortalEngine.repositories.MessageOutboxRepository;
import com.tangazoletu.tibuPortalEngine.util.SharedFunctions;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.net.URLEncoder;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
@RequiredArgsConstructor
public class UtilityService {
    private final BudgetRepo budgetRepo;
    private final FinancierRepo  financierRepo;
    private final MessageOutboxRepository messageRepo;
    private final SharedFunctions sharedFunctions;

    @Value("${sms.apiUrl}")
    private String smsApiUrl;
    @Value("${sms.userId}")
    private String smsUserId;
    @Value("${sms.service}")
    private String smsService;
    @Value("${sms.passkey}")
    private String smsPasskey;

    public UtilityResponse getBudget(UtilityRequest request, HttpServletResponse response, HttpServletRequest httpServletResponse) {
        UtilityResponse utilityResponse = UtilityResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL.getCode())
                .build();
        try {
            List<Budget> budget = budgetRepo.findAllByInTrashOrderByIdAsc("No");
            if (!budget.isEmpty()) {
                utilityResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                utilityResponse.setResponseMessage("Success");
                utilityResponse.setData(budget);
            }else {
                utilityResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                utilityResponse.setResponseMessage("No budget found");
            }

        }catch (Exception e){
            utilityResponse.setResponseCode(ApiResponseCode.FAIL.getCode());
            utilityResponse.setResponseMessage("Failed Error occured");
            log.error("An error occured {}",e.getMessage());
            e.printStackTrace();
        }
        return utilityResponse;
    }
    public UtilityResponse getFinancier(UtilityRequest request, HttpServletResponse response, HttpServletRequest httpServletResponse) {
        UtilityResponse utilityResponse = UtilityResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL.getCode())
                .build();
        try {
            List<Financier> financier = financierRepo.findByInTrashOrderByIdAsc(Financier.TrashStatus.No);
            if (!financier.isEmpty()) {
                utilityResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                utilityResponse.setResponseMessage("Success");
                utilityResponse.setData(financier);
            }else {
                utilityResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                utilityResponse.setResponseMessage("No record found");
            }


        }catch (Exception e){
            utilityResponse.setResponseCode(ApiResponseCode.FAIL.getCode());
            utilityResponse.setResponseMessage("Failed Error occured");
            log.error("An error occured {}",e.getMessage());
            e.printStackTrace();
        }

        return utilityResponse;

    }
    public UtilityResponse sendSms(HttpServletRequest request, HttpServletResponse response) {
        List<MessageOutbox> messages = messageRepo.findTop100PendingMessages(PageRequest.of(0, 100));

        if (messages.isEmpty()) {
            log.info("No pending messages found for sending.");
            return UtilityResponse.builder()
                    .responseCode("00")
                    .responseMessage("No pending messages")
                    .build();
        }

        for (MessageOutbox msg : messages) {
            try {
                String encodedMsg = URLEncoder.encode(msg.getMessageOut(), StandardCharsets.UTF_8.name());

                String smsUrl = smsApiUrl + "?User_ID=" + smsUserId
                        + "&passkey=" + smsPasskey
                        + "&service=" + smsService
                        + "&sender=" + msg.getShortcode()
                        + "&dest=" + msg.getPhoneNumber()
                        + "&msg=" + encodedMsg;

                log.info("Sending SMS to [{}] with message ID [{}]: {}", msg.getPhoneNumber(), msg.getId(), msg.getMessageOut());
                log.info("send sms url {}",smsUrl);

                String responseBody = sharedFunctions.makeGetRequest(smsUrl);

                log.info("Received response for message ID [{}]: {}", msg.getId(), responseBody);

                int newStatus = (responseBody != null && responseBody.contains("Successful")) ? 2 : 3;
                msg.setStatus(newStatus);
                msg.setProcessedAt(Timestamp.from(Instant.now()).toLocalDateTime());
                messageRepo.save(msg);

            } catch (Exception e) {
                log.error("Error sending SMS to [{}] with message ID [{}]: {}", msg.getPhoneNumber(), msg.getId(), e.getMessage(), e);
                msg.setStatus(3); // failed
                msg.setProcessedAt(Timestamp.from(Instant.now()).toLocalDateTime());
                messageRepo.save(msg);
            }
        }

        return UtilityResponse.builder()
                .responseCode("00")
                .responseMessage("SMS processing complete")
                .build();
    }



}
