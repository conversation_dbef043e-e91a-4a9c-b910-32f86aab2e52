package com.tangazoletu.tibuPortalEngine.service;

import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Service
public class CaptchaService {

    @Value("${recaptcha.secret}")
    private String secret;

    @PostConstruct
    public void testConfig() {
        if (secret == null || secret.isBlank()) {
            throw new IllegalStateException("reCAPTCHA secret is missing! Check application.properties");
        }
        System.out.println(" Captcha secret loaded: " + secret);
    }

    public boolean verifyToken(String captchaToken) {
        String VERIFY_URL = "https://www.google.com/recaptcha/api/siteverify";

        RestTemplate restTemplate = new RestTemplate();

        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("secret", secret);
        params.add("response", captchaToken);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

        try {
            ResponseEntity<Map> response = restTemplate.postForEntity(VERIFY_URL, request, Map.class);
            return (Boolean) response.getBody().get("success");
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
