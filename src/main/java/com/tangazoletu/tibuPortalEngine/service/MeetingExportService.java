package com.tangazoletu.tibuPortalEngine.service;

import com.tangazoletu.tibuPortalEngine.annotations.ExcelColumn;
import com.tangazoletu.tibuPortalEngine.dto.ColumRisizeDataResponse;
import com.tangazoletu.tibuPortalEngine.dto.ColumnResizeRow;
import com.tangazoletu.tibuPortalEngine.dto.MeetingCodesRequest;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class MeetingExportService {

    @Autowired
    private MeetingService meetingService;

    /**
     * DTO for meeting export rows (simplified without annotations)
     */
    @Data
    public static class MeetingExportRow {
        private String name;
        private String idNumber;
        private String county;
        private String station;
        private String designation;
        private String jobGroup;
        private String payableTravellingDays;

        // Dynamic day attendance fields will be added programmatically
        private Map<String, String> dayAttendanceFields = new LinkedHashMap<>();

        private String noOfDays;
        private String telephoneNumber;
        private String rate;
        private String amount;
        private String lessLunch;
        private String transport;
        private String extraPerDiem;
        private String others;
        private String netPay;
        private String status;
        private String processingComments;
        private String approvalComments;
        private String process;
        private String comments;
        private String attendanceId;
    }

    /**
     * Export meeting data to Excel format using existing createExcelFile pattern
     */
    public byte[] exportToExcel(MeetingCodesRequest request, HttpServletRequest req, HttpServletResponse res) throws IOException {
        // Get the meeting data
        ColumRisizeDataResponse meetingData = meetingService.getColumRisizeData(request, req, res);

        if (meetingData == null || meetingData.getData() == null || meetingData.getData().isEmpty()) {
            throw new RuntimeException("No data available for export");
        }

        // Convert to format compatible with existing createExcelFile method
        List<MeetingExportRow> exportRows = convertToExportRows(meetingData);
        String sheetName = "Meeting_" + request.getMeetingcode() + "_Mode_" + request.getMode();

        return createExcelFileForMeeting(exportRows, sheetName, meetingData.getTotal(),request.getMeetingcode());
    }

    /**
     * Export meeting data to CSV format
     */
    public byte[] exportToCsv(MeetingCodesRequest request, HttpServletRequest req, HttpServletResponse res) throws IOException {
        // Get the meeting data
        ColumRisizeDataResponse meetingData = meetingService.getColumRisizeData(request, req, res);
        
        if (meetingData == null || meetingData.getData() == null || meetingData.getData().isEmpty()) {
            throw new RuntimeException("No data available for export");
        }

        return createCsvFile(meetingData, request);
    }

    /**
     * Convert ColumnResizeRow data to MeetingExportRow format
     */
    private List<MeetingExportRow> convertToExportRows(ColumRisizeDataResponse meetingData) {
        List<MeetingExportRow> exportRows = new ArrayList<>();

        for (ColumnResizeRow row : meetingData.getData()) {
            MeetingExportRow exportRow = new MeetingExportRow();

            // Basic fields
            exportRow.setName(row.getName());
            exportRow.setIdNumber(row.getIdNumber());
            exportRow.setCounty(row.getCounty());
            exportRow.setStation(row.getStation());
            exportRow.setDesignation(row.getDesignation());
            exportRow.setJobGroup(row.getJobGroup());
            exportRow.setPayableTravellingDays(row.getPayableTravellingDays() != null ? row.getPayableTravellingDays().toString() : "");

            // Day attendance fields
            if (row.getDayAttendance() != null) {
                for (Map.Entry<String, Object> entry : row.getDayAttendance().entrySet()) {
                    exportRow.getDayAttendanceFields().put(entry.getKey(), entry.getValue().toString());
                }
            }

            // Remaining fields
            exportRow.setNoOfDays(row.getNoOfDays());
            exportRow.setTelephoneNumber(row.getTelephoneNumber());
            exportRow.setRate(row.getRate() != null ? row.getRate().toString() : "");
            exportRow.setAmount(row.getAmount() != null ? row.getAmount().toString() : "");
            exportRow.setLessLunch(row.getLessLunch() != null ? row.getLessLunch().toString() : "");
            exportRow.setTransport(row.getTransport() != null ? row.getTransport().toString() : "");
            exportRow.setExtraPerDiem(row.getExtraPerDiem() != null ? row.getExtraPerDiem().toString() : "");
            exportRow.setOthers(row.getOthers() != null ? row.getOthers().toString() : "");
            exportRow.setNetPay(row.getNetPay() != null ? row.getNetPay().toString() : "");
            exportRow.setStatus(row.getStatus());
            exportRow.setProcessingComments(row.getProcessingComments());
            exportRow.setApprovalComments(row.getApprovalComments());
            exportRow.setProcess(row.getProcess());
            exportRow.setComments(row.getComments());
            exportRow.setAttendanceId(row.getAttendanceId() != null ? row.getAttendanceId().toString() : "");

            exportRows.add(exportRow);
        }

        return exportRows;
    }

    /**
     * Create Excel file following existing project pattern with logos
     */
    private byte[] createExcelFileForMeeting(List<MeetingExportRow> data, String sheetName, BigDecimal total, String batchNo) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook();

        try {
            Sheet sheet = workbook.createSheet(sheetName);

            // Build dynamic headers including day attendance
            List<String> allHeaders = buildDynamicHeaders(data);

            // Create headers (row 7, 0-indexed = row 8 in Excel) - following existing pattern
            Row headerRow = sheet.createRow(7);
            int headerIndex = 0;

            for (String header : allHeaders) {
                headerRow.createCell(headerIndex++).setCellValue(header);
            }

            // Style headers (following existing pattern)
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);

            for (Cell cell : headerRow) {
                cell.setCellStyle(headerStyle);
            }

            // Add data rows (starting from row 8, 0-indexed)
            int rowIndex = 8;
            for (MeetingExportRow exportRow : data) {
                Row dataRow = sheet.createRow(rowIndex++);
                int cellIndex = 0;

                // Add all field values in the same order as headers
                addRowData(dataRow, exportRow, allHeaders, cellIndex);
            }

            // Add signature block after data (following existing pattern)
            Row batchRow = sheet.createRow(rowIndex++);
            batchRow.createCell(0).setCellValue("BATCH NO: " + batchNo + " Totaling to: " +
                (total != null ? total.toString() : "0.00"));

            Row dateRow = sheet.createRow(rowIndex++);
            dateRow.createCell(0).setCellValue("Date: " +
                    LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd-MMM-yyyy h:mm a")));

            Row signedByRow = sheet.createRow(rowIndex++);
            signedByRow.createCell(0).setCellValue("Signed by Name:");

            Row signatureRow = sheet.createRow(rowIndex++);
            signatureRow.createCell(0).setCellValue("Signature:");

            // Add image using existing method pattern
            addImageToSheet(workbook, sheet, 0, 0);

            // Auto-size columns
            for (int i = 0; i < allHeaders.size(); i++) {
                sheet.autoSizeColumn(i);
            }

            // Set workbook properties (following existing pattern)
            workbook.getProperties().getCoreProperties().setCreator("Tangazo Letu");
            workbook.getProperties().getCoreProperties().setTitle(sheetName);
            workbook.getProperties().getCoreProperties().setDescription("Tangazoletu Meeting Reports.");

            // Convert to byte array
            byte[] excelBytes;
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                workbook.write(outputStream);
                outputStream.flush();
                excelBytes = outputStream.toByteArray();
            }

            return excelBytes;

        } finally {
            workbook.close();
        }
    }

    /**
     * Build dynamic headers including day attendance fields
     */
    private List<String> buildDynamicHeaders(List<MeetingExportRow> data) {
        List<String> headers = new ArrayList<>();

        // Add static headers in order
        headers.add("Name");
        headers.add("IDNumber");
        headers.add("County");
        headers.add("Station");
        headers.add("Designation");
        headers.add("Job Group");
        headers.add("Payable Travelling Days");

        // Add dynamic day attendance headers
        if (!data.isEmpty() && data.get(0).getDayAttendanceFields() != null) {
            headers.addAll(data.get(0).getDayAttendanceFields().keySet());
        }

        // Add remaining static headers
        headers.add("No of Days");
        headers.add("Telephone Number");
        headers.add("Rate");
        headers.add("Amount");
        headers.add("Less lunch day @500");
        headers.add("Transport");
        headers.add("Extra per diem");
        headers.add("Others");
        headers.add("Net Pay");
        headers.add("Status");
        headers.add("Processing Comments");
        headers.add("Approval Comments");
        headers.add("Process");
        headers.add("Comments");
        headers.add("ID");

        return headers;
    }

    /**
     * Add row data in the same order as headers
     */
    private void addRowData(Row dataRow, MeetingExportRow exportRow, List<String> headers, int startCellIndex) {
        int cellIndex = startCellIndex;

        for (String header : headers) {
            Cell cell = dataRow.createCell(cellIndex++);
            String value = getFieldValueByHeader(exportRow, header);
            cell.setCellValue(value != null ? value : "");
        }
    }

    /**
     * Get field value by header name
     */
    private String getFieldValueByHeader(MeetingExportRow exportRow, String header) {
        switch (header) {
            case "Name": return exportRow.getName();
            case "IDNumber": return exportRow.getIdNumber();
            case "County": return exportRow.getCounty();
            case "Station": return exportRow.getStation();
            case "Designation": return exportRow.getDesignation();
            case "Job Group": return exportRow.getJobGroup();
            case "Payable Travelling Days": return exportRow.getPayableTravellingDays();
            case "No of Days": return exportRow.getNoOfDays();
            case "Telephone Number": return exportRow.getTelephoneNumber();
            case "Rate": return exportRow.getRate();
            case "Amount": return exportRow.getAmount();
            case "Less lunch day @500": return exportRow.getLessLunch();
            case "Transport": return exportRow.getTransport();
            case "Extra per diem": return exportRow.getExtraPerDiem();
            case "Others": return exportRow.getOthers();
            case "Net Pay": return exportRow.getNetPay();
            case "Status": return exportRow.getStatus();
            case "Processing Comments": return exportRow.getProcessingComments();
            case "Approval Comments": return exportRow.getApprovalComments();
            case "Process": return exportRow.getProcess();
            case "Comments": return exportRow.getComments();
            case "ID": return exportRow.getAttendanceId();
            default:
                // Check if it's a day attendance field
                if (exportRow.getDayAttendanceFields() != null) {
                    return exportRow.getDayAttendanceFields().get(header);
                }
                return "";
        }
    }

    /**
     * Add image to sheet following existing pattern
     */
    private void addImageToSheet(XSSFWorkbook workbook, Sheet sheet, int row, int col) {
        try (InputStream imageStream = getClass().getResourceAsStream("/TIBU.png")) {
            if (imageStream == null) {
                log.warn("Image not found: /TIBU.png");
                return;
            }

            byte[] imageBytes = IOUtils.toByteArray(imageStream);
            int pictureIndex = workbook.addPicture(imageBytes, Workbook.PICTURE_TYPE_JPEG);

            BufferedImage bufferedImage = ImageIO.read(new ByteArrayInputStream(imageBytes));
            int imageWidth = bufferedImage.getWidth();
            int imageHeight = bufferedImage.getHeight();

            double excelColWidthPx = 64.0;
            double excelRowHeightPx = 20.0;

            int colSpan = (int) Math.round(imageWidth / excelColWidthPx);
            int rowSpan = (int) Math.round(imageHeight / excelRowHeightPx);

            XSSFDrawing drawing = (XSSFDrawing) sheet.createDrawingPatriarch();
            XSSFClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, col, row, col + colSpan, row + rowSpan);

            drawing.createPicture(anchor, pictureIndex);

            for (int i = col; i < col + colSpan; i++) {
                sheet.autoSizeColumn(i);
            }

        } catch (IOException e) {
            log.warn("Could not add image to Excel: " + e.getMessage());
        }
    }



    /**
     * Create CSV file from meeting data
     */
    private byte[] createCsvFile(ColumRisizeDataResponse meetingData, MeetingCodesRequest request) throws IOException {
        StringBuilder csv = new StringBuilder();
        
        // Add title and metadata
        csv.append("Meeting Report - ").append(request.getMeetingcode()).append(" (Mode ").append(request.getMode()).append(")\n");
        csv.append("Generated on: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        csv.append("Total Amount: ").append(meetingData.getTotal() != null ? meetingData.getTotal().toString() : "0.00").append("\n");
        csv.append("\n");
        
        // Add headers
        List<String> headers = meetingData.getHeaders();
        for (int i = 0; i < headers.size(); i++) {
            if (i > 0) csv.append(",");
            csv.append("\"").append(headers.get(i).replace("\"", "\"\"")).append("\"");
        }
        csv.append("\n");
        
        // Add data rows
        List<ColumnResizeRow> data = meetingData.getData();
        for (ColumnResizeRow rowData : data) {
            csv.append("\"").append(escapeCsv(rowData.getName())).append("\",");
            csv.append("\"").append(escapeCsv(rowData.getIdNumber())).append("\",");
            csv.append("\"").append(escapeCsv(rowData.getCounty())).append("\",");
            csv.append("\"").append(escapeCsv(rowData.getStation())).append("\",");
            csv.append("\"").append(escapeCsv(rowData.getDesignation())).append("\",");
            csv.append("\"").append(escapeCsv(rowData.getJobGroup())).append("\",");
            
            // Add payable travelling days if available
            if (rowData.getPayableTravellingDays() != null) {
                csv.append("\"").append(rowData.getPayableTravellingDays().toString()).append("\",");
            }
            
            // Add daily attendance data
            if (rowData.getDayAttendance() != null) {
                for (Map.Entry<String, Object> entry : rowData.getDayAttendance().entrySet()) {
                    csv.append("\"").append(entry.getValue().toString()).append("\",");
                }
            }
            
            // Add no of days if available
            if (rowData.getNoOfDays() != null) {
                csv.append("\"").append(escapeCsv(rowData.getNoOfDays())).append("\",");
            }
            
            // Add remaining fields
            csv.append("\"").append(escapeCsv(rowData.getTelephoneNumber())).append("\",");
            csv.append("\"").append(rowData.getRate() != null ? rowData.getRate().toString() : "").append("\",");
            csv.append("\"").append(rowData.getAmount() != null ? rowData.getAmount().toString() : "").append("\",");
            csv.append("\"").append(rowData.getLessLunch() != null ? rowData.getLessLunch().toString() : "").append("\",");
            csv.append("\"").append(rowData.getTransport() != null ? rowData.getTransport().toString() : "").append("\",");
            csv.append("\"").append(rowData.getExtraPerDiem() != null ? rowData.getExtraPerDiem().toString() : "").append("\",");
            csv.append("\"").append(rowData.getOthers() != null ? rowData.getOthers().toString() : "").append("\",");
            csv.append("\"").append(rowData.getNetPay() != null ? rowData.getNetPay().toString() : "").append("\",");
            csv.append("\"").append(escapeCsv(rowData.getStatus())).append("\",");
            csv.append("\"").append(escapeCsv(rowData.getProcessingComments())).append("\",");
            csv.append("\"").append(escapeCsv(rowData.getApprovalComments())).append("\",");
            csv.append("\"").append(rowData.getAttendanceId() != null ? rowData.getAttendanceId().toString() : "").append("\"");
            
            // Add mode-specific fields
            if (rowData.getProcess() != null) {
                csv.append(",\"").append(escapeCsv(rowData.getProcess())).append("\"");
            }
            if (rowData.getComments() != null) {
                csv.append(",\"").append(escapeCsv(rowData.getComments())).append("\"");
            }
            
            csv.append("\n");
        }
        
        return csv.toString().getBytes("UTF-8");
    }

    /**
     * Helper method to escape CSV values
     */
    private String escapeCsv(String value) {
        if (value == null) return "";
        return value.replace("\"", "\"\"");
    }
}
