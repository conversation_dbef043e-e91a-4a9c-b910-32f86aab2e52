package com.tangazoletu.tibuPortalEngine.service;

import com.tangazoletu.tibuPortalEngine.entities.MailDeliveryLog;
import com.tangazoletu.tibuPortalEngine.repositories.MailDeliveryLogRepository;
import com.tangazoletu.tibuPortalEngine.repositories.ParamRepository;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.util.Properties;

@Service
@Slf4j
public class EmailService {
    @Autowired
    private ParamRepository paramRepository;

    @Autowired
    private MailDeliveryLogRepository mailDeliveryLogRepository;

    /**
     * Sends an email to the specified recipient with the given message body.
     *
     * @param toEmail the recipient's email address
     * @param msg     the message body
     * @return true if the email was sent successfully, false otherwise
     */
    public boolean sendEmail(String toEmail, String msg) {
        try {
            // Get mail server credentials from the database
            String mailUsername = paramRepository.findValueByParameter("MAIL_SERVER_USERNAME");
            String mailPassword = paramRepository.findValueByParameter("MAIL_SERVER_PASSWORD");

            // Create a mail sender instance
            JavaMailSenderImpl mailSender = getConfiguredMailSender(mailUsername, mailPassword);

            // Create a mime message
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            // Set email properties
            helper.setFrom("TIBU <" + mailUsername + ">");
            helper.setTo(toEmail);
            helper.setSubject("Notification");
            helper.setText(msg, true); // true indicates HTML content

            // Send the email
            mailSender.send(message);

            // Log successful email delivery
            saveEmailLog(toEmail, true, "Login");
            return true;
        } catch (MessagingException e) {
            // Log the error
            //log.error("Error sending email to {}: {}", toEmail, e.getMessage());

            // Log failed email delivery
            saveEmailLog(toEmail, false, "Login");
            return false;
        }catch (Exception e) {
            log.error("Unexpected error sending email to {}: ", toEmail, e);
            saveEmailLog(toEmail, false, "Login");
            return false;
        }
    }

    public void sendOtpEmail(String to, String otp) {
        // Create HTML message with OTP
        String htmlMsg = "<div style='font-family: Arial, sans-serif; padding: 20px; max-width: 600px;'>"
                + "<h2 style='color: #333;'>Your One-Time Password</h2>"
                + "<p>Your one-time password is:</p>"
                + "<div style='background-color: #f0f0f0; padding: 15px; font-size: 24px; font-weight: bold; "
                + "text-align: center; margin: 20px 0; letter-spacing: 5px;'>" + otp + "</div>"
                + "<p>This OTP will expire in 5 minutes.</p>"
                + "<p>If you did not request this OTP, please ignore this email.</p>"
                + "<p>Best regards,<br>TIBU Team</p>"
                + "</div>";

        // Send the email
        sendEmail(to, htmlMsg);
    }

    /**
     * Create a configured {@link JavaMailSenderImpl} instance for sending emails using Gmail SMTP server.
     *
     * @param username the Gmail address to use for sending emails
     * @param password the App Password (16-character code) for the given Gmail address
     * @return a configured {@link JavaMailSenderImpl} instance
     */
private JavaMailSenderImpl getConfiguredMailSender(String username, String password) {
    JavaMailSenderImpl mailSender = new JavaMailSenderImpl();

    // Gmail SMTP configuration
    mailSender.setHost("smtp.gmail.com");
    mailSender.setPort(587); // or 465 for SSL
    mailSender.setUsername(username); // your Gmail address
    mailSender.setPassword(password); // your App Password (16-character code)

    // Mail properties for Gmail
    Properties props = mailSender.getJavaMailProperties();
    props.put("mail.transport.protocol", "smtp");
    props.put("mail.smtp.auth", "true");
    props.put("mail.smtp.starttls.enable", "true"); // for port 587
    // props.put("mail.smtp.ssl.enable", "true"); // use this for port 465 instead
    props.put("mail.debug", "false"); // set to true for debugging

    return mailSender;
}

    /**
     * Logs an email delivery in the database.
     *
     * @param recipient the recipient's email address
     * @param status    true if the email was sent successfully, false otherwise
     * @param feature   the feature that triggered the email (e.g. "Login", "Reset Password")
     */
    private void saveEmailLog(String recipient, boolean status, String feature) {
        MailDeliveryLog log = new MailDeliveryLog();
        log.setStatus(status ? 1 : 0);
        log.setFeature(feature);
        log.setRecipient(recipient);

        mailDeliveryLogRepository.save(log);
    }
}

