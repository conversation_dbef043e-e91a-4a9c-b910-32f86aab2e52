package com.tangazoletu.tibuPortalEngine.service;

import com.tangazoletu.tibuPortalEngine.dto.*;
import com.tangazoletu.tibuPortalEngine.entities.*;
import com.tangazoletu.tibuPortalEngine.repositories.*;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.Tuple;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class PaymentsService {
    private final PaymentsRepo paymentsRepo;
    private final ActualPaymentsRepo actualPaymentsRepo;
    private final PaymentRequestDetailsRepo paymentRequestDetailsRepo;
    private final BudgetRepo budgetRepo;
    private final ApiOrderRepo apiOrderRepo;
    private final ViewMdrSctlcSummaryRepository viewMdrSctlcSummaryRepository;
    private final ViewDotPaymentReportRepo viewDotPaymentReportRepo;
    private final MDRpatientsandcostpercountyRepo mdrpatientsandcostpercountyRepo;
    private final ViewMonthlyPatientSummaryRepository viewMonthlyPatientSummaryRepository;
    private final ViewPatientCompletedPatientReportRepo viewPatientCompletedPatientReportRepo;
    private final FundsTransferViewRepository fundsTransferViewRepository;
    private final BudgetaryViewRepository budgetaryViewRepository;
    private final DetailedSummaryViewRepository detailedSummaryViewRepository;
    private final ExpectedVsActualViewRepository expectedVsActualViewRepository;
    private final ApprovalLevelRepo approvalLevelRepo;
    @PersistenceContext
    private EntityManager entityManager;

    public Page<PaymentDTO> getFilteredPayments(TransactionChargesRequest request) {
        Page<Payment> page = paymentsRepo.searchPayments(
                request.getKeyword(),
                request.getOrgFilterList(),
                PageRequest.of(request.getPage(), request.getPageSize())
        );

        return page.map(payment -> {
            PaymentDTO dto = new PaymentDTO();
            dto.setBatchNumber(payment.getBatchId());
            dto.setRequestTime(payment.getRequestDate());
            dto.setBeneficiary(payment.getRecipientName()); // Adjust as per real mapping
            dto.setPhoneNo(payment.getMsisdn());
            dto.setRequestType(payment.getTrxType() != null ? payment.getTrxType().toString() : null);
            dto.setAmount(payment.getDebitAmount());
            dto.setSendingCharge(payment.getSendingCharge());
            dto.setWithdrawalCharge(payment.getWithdrawalCharge());
            dto.setStatus(payment.getStatus());

            if (payment.getBudget() != null) {
                dto.setBudgetLine(payment.getBudget().getTitle());
            }

            return dto;
        });
    }


    public Page<?> getFilteredFundsTransfer(FundsTransferRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        log.info("getFilteredFundsTransfer {}",  request.toString());
        List<Integer> orgIds = request.getOrgFilterList();
        String keyword = request.getKeyword();
        Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize());

        return fundsTransferViewRepository.findFundsTransferData(orgIds, keyword, pageable);

    }
    public Page<PaymentRequestDetails> getPaymentRequestDetailById(ApiRequestDto requestDto, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        Page<PaymentRequestDetails> paymentRequestDetails = null;
        try {
            Pageable pageable = PageRequest.of(requestDto.getPage(), requestDto.getPageSize());
            Long id = Long.parseLong(requestDto.getId());

            paymentRequestDetails = paymentRequestDetailsRepo.findAllById(id, pageable);

        }catch (Exception e){
            e.printStackTrace();
            log.error("getPaymentRequestDetailById error");
        }
        return paymentRequestDetails;
    }
    public Page<Map<String, Object>> getPaymentSummary(PaymentRequest request, HttpServletResponse response, HttpServletRequest request1) {
        try {
            String startDateStr = request.getStartTimestamp();
            Timestamp date1 = parseDateOrTimestamp(startDateStr);
            String formattedDate1 = formatDateForQuery(date1);

            String endDateStr = request.getEndTimestamp();
            Timestamp date2 = parseDateOrTimestamp(endDateStr);
            String formattedDate2 = formatDateForQuery(date2);

            List<Integer> orgIds = request.getOrgFilterList();
            List<Integer> budgetIds = request.getBudgetIds();
            String keyword = request.getKeyword();
            if (budgetIds != null && budgetIds.isEmpty()) budgetIds = null;
            if (orgIds != null && orgIds.isEmpty()) orgIds = null;


            Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize());
            log.info("dates {} {}",  formattedDate1, formattedDate2);

            return paymentsRepo.findPaymentSummary(
                    keyword,
                    formattedDate1,
                    formattedDate2,
                    budgetIds,
                    orgIds,
                    pageable
            );
        } catch (Exception e) {
            log.error("Failed to fetch Payment Summary: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    public Timestamp parseDateOrTimestamp(String dateStr) throws ParseException {
        if (dateStr == null) {
            return null;
        }

        try {
            // Check if the input is a numeric timestamp
            long timestamp = Long.parseLong(dateStr);
            return new Timestamp(timestamp);
        } catch (NumberFormatException e) {
            // If parsing as a timestamp fails, try parsing as a formatted date
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return (Timestamp) formatter.parse(dateStr);
        }
    }
    public String formatDateForQuery(Timestamp timestamp) {
        if (timestamp == null) return null;
        return new SimpleDateFormat("yyyy-MM-dd").format(timestamp);
    }

    public ProgramIntelligenceResponse getDetailedSummary(DetailedSummaryRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        ProgramIntelligenceResponse  programIntelligenceResponse = ProgramIntelligenceResponse.builder()
                .responseCode("01")
                .build();
        try {
            log.info("getDetailedSummary {}", request.toString());

            List<Integer> orgIds = request.getOrgIds();
            Integer page = request.getPage();;
            Integer pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);
            Page<DetailedSummaryView> rawResult = detailedSummaryViewRepository.findSummaryByOrgIds(orgIds,pageable);



            if (rawResult.isEmpty()) {
                programIntelligenceResponse = ProgramIntelligenceResponse.builder()
                        .responseMessage("No records")
                        .responseCode("00")
                        .build();
            } else {
                programIntelligenceResponse.setResponseCode("00");
                programIntelligenceResponse.setResponseMessage("Success");
                programIntelligenceResponse.setData(rawResult);
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.error("getDetailedSummary error", e);
        }

        return programIntelligenceResponse;


    }

    public ProgramIntelligenceResponse getBudgetary(BudgetaryRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        ProgramIntelligenceResponse programIntelligenceResponse =  ProgramIntelligenceResponse.builder()
                .responseCode("01")
                .build();
        try {
            String startDateStr = request.getStartTimestamp();
            Timestamp date1 = parseDateOrTimestamp(startDateStr);
            String formattedDate1 = formatDateForQuery(date1);

            String endDateStr = request.getEndTimestamp();
            Timestamp date2 = parseDateOrTimestamp(endDateStr);
            String formattedDate2 = formatDateForQuery(date2);

            List<Integer> countyIds = request.getCountyIds();
            List<Integer> budgetIds = request.getBudgetIds();
            List<String> budgets= new ArrayList<>();
            List<String> counties = new ArrayList<>();
            Integer page = request.getPage();
            Integer pageSize = request.getPageSize();
            Integer provinceId = request.getProvinceId();
            String month = request.getMonth();
            String year = request.getYear();
            String keyword = request.getKeyword() != null ? request.getKeyword() : null;
            if (budgetIds != null && budgetIds.isEmpty()){
                budgets = Collections.singletonList(String.valueOf(budgetIds));
            }else {
                budgets = null;
            }
            if (countyIds != null && countyIds.isEmpty()){
                counties = Collections.singletonList(String.valueOf(countyIds));
            }else {
                counties = null;
            }
            Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize());
            Page<Map<String, Object>> budgetary = budgetRepo.getBudgetPaymentSummary(
                    keyword,
                    provinceId,
                    budgetIds,
                    countyIds,
                    month,
                    year,
                    date1,
                    date2,
                    pageable
            );
            Page<BudgetaryView> budgetaryViews = budgetaryViewRepository.findBudgetaryData(keyword,provinceId.toString(), String.valueOf(counties),budgets,month,year,pageable);
            if (budgetaryViews.isEmpty()) {
                programIntelligenceResponse.setResponseMessage("No records");
                programIntelligenceResponse.setResponseCode("00");
            }else {
                programIntelligenceResponse.setResponseCode("00");
                programIntelligenceResponse.setResponseMessage("Success");
                programIntelligenceResponse.setData(budgetaryViews);
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error("getBudgetary error");
            response.setStatus(500);
        }

        return programIntelligenceResponse;
    }

    public ProgramIntelligenceResponse getExpectedVsActual(PaymentRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        ProgramIntelligenceResponse programIntelligenceResponse = ProgramIntelligenceResponse.builder()
                .responseMessage("Error Occured")
                .responseCode("01")
                .build();
        try {
            String keyword =  request.getKeyword();
            List<Integer> orgIds = request.getOrgFilterList();
            Integer page = request.getPage();
            Integer pageCount = request.getPageSize();
            Pageable pageable = PageRequest.of(page,pageCount);

            Page<ExpectedVsActualView> exctedvsactual = expectedVsActualViewRepository.searchCompletedTransactions(keyword,orgIds,pageable);
            if (exctedvsactual.isEmpty()) {
                programIntelligenceResponse.setResponseMessage("No records");
                programIntelligenceResponse.setResponseCode("00");
            }else {
                programIntelligenceResponse.setResponseCode("00");
                programIntelligenceResponse.setResponseMessage("Success");
                programIntelligenceResponse.setData(exctedvsactual);
            }

        }catch (Exception e){
            log.error("getExpectedVsActual error",e.getMessage());
            e.printStackTrace();
            response.setStatus(500);
            programIntelligenceResponse.setResponseCode("01");
            programIntelligenceResponse.setResponseMessage("error ");
        }
        return programIntelligenceResponse;
    }
    public ProgramIntelligenceResponse getSupervisionPayments(PaymentRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        ProgramIntelligenceResponse programIntelligenceResponse = ProgramIntelligenceResponse.builder()
                .responseCode("01")
                .responseMessage("Failed")
                .build();
        try {
            String keyword =  request.getKeyword();
            List<Integer> orgIds = request.getOrgFilterList();
            Integer page = request.getPage();
            Integer pageCount = request.getPageSize();
            Pageable pageable = PageRequest.of(page,pageCount);

            Page<Map<String,Object>> supervisonPayment = apiOrderRepo.findSupervisionSummaryByKeywordAndOrg(keyword,orgIds,pageable);
            if (supervisonPayment.isEmpty()) {
                programIntelligenceResponse.setResponseMessage("No records");
                programIntelligenceResponse.setResponseCode("00");
            }else {
                programIntelligenceResponse.setResponseCode("00");
                programIntelligenceResponse.setResponseMessage("Success");
                programIntelligenceResponse.setData(supervisonPayment);
            }




        }catch (Exception e){
            log.error("getSupervisionPayments error",e.getMessage());
            e.printStackTrace();
            programIntelligenceResponse.setResponseCode("01");
            programIntelligenceResponse.setResponseMessage("Failed");

        }
        return programIntelligenceResponse;
    }
    public ProgramIntelligenceResponse getMdrSurveillancePayments(PaymentRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        ProgramIntelligenceResponse programIntelligenceResponse = ProgramIntelligenceResponse.builder()
                .responseCode("01")
                .responseMessage("Failed")
                .build();
        try {
            String keyword =  request.getKeyword();
            List<Integer> orgIds = request.getOrgFilterList();
            Integer page = request.getPage();
            Integer pageCount = request.getPageSize();
            Pageable pageable = PageRequest.of(page,pageCount);

            Page<ViewMdrSctlcSummary> mdrOtherPaymentsWithSummary = viewMdrSctlcSummaryRepository.findByKeywordAndOrgIds(keyword,orgIds,pageable);
            if (mdrOtherPaymentsWithSummary.isEmpty()) {
                programIntelligenceResponse.setResponseMessage("No records");
                programIntelligenceResponse.setResponseCode("00");
            }else {
                programIntelligenceResponse.setResponseCode("00");
                programIntelligenceResponse.setResponseMessage("Success");
                programIntelligenceResponse.setData(mdrOtherPaymentsWithSummary);
            }


        }catch (Exception e){
            log.error("getMdrSurveillancePayments error",e.getMessage());
            e.printStackTrace();
            programIntelligenceResponse.setResponseCode("01");
            programIntelligenceResponse.setResponseMessage("Failed");
        }
        return programIntelligenceResponse;
    }
    public ProgramIntelligenceResponse getMdrSupportPerCounty(PaymentRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        ProgramIntelligenceResponse programIntelligenceResponse = ProgramIntelligenceResponse.builder()
                .responseCode("01")
                .responseMessage("Failed")
                .build();
        try {
            String keyword =  request.getKeyword();
            List<Integer> orgIds = request.getOrgFilterList();
            Integer page = request.getPage();
            Integer pageCount = request.getPageSize();
            Pageable pageable = PageRequest.of(page,pageCount);

            Page<Map<String,Object>> mdrSupportPerCounty = apiOrderRepo.getPatientSummaryByClaimMonth(keyword,orgIds,pageable);
            if (mdrSupportPerCounty.isEmpty()) {
                programIntelligenceResponse.setResponseMessage("No records");
                programIntelligenceResponse.setResponseCode("00");
            }else {
                programIntelligenceResponse.setResponseCode("00");
                programIntelligenceResponse.setResponseMessage("Success");
                programIntelligenceResponse.setData(mdrSupportPerCounty);
            }


        }catch (Exception e){
            log.error("getMdrSurveillancePayments error",e.getMessage());
            e.printStackTrace();
            programIntelligenceResponse.setResponseCode("01");
            programIntelligenceResponse.setResponseMessage("Failed");
        }
        return programIntelligenceResponse;
    }
    public ProgramIntelligenceResponse getMdrDotPayments(MdrDotPaymetsRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        ProgramIntelligenceResponse programIntelligenceResponse = ProgramIntelligenceResponse.builder()
                .responseCode("01")
                .responseMessage("Failed")
                .build();
        try {
            String startDateStr = request.getStartTimestamp();
            Timestamp date1 = parseDateOrTimestamp(startDateStr);
            String formattedDate1 = formatDateForQuery(date1);

            String endDateStr = request.getEndTimestamp();
            Timestamp date2 = parseDateOrTimestamp(endDateStr);
            String formattedDate2 = formatDateForQuery(date2);

            Integer countyIds = request.getCountyId();
            Integer budgetIds = request.getBudgetId();
            String budget = request.getBudget();
            String county = request.getCounty();
            Integer page = request.getPage();
            Integer pageSize = request.getPageSize();
            Integer provinceId = request.getProvince();
            String month = request.getMonth();
            String year = request.getYear();
            String keyword = request.getKeyword() != null ? request.getKeyword() : null;
            List<Integer> orgIds = request.getOrgIds();

            Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize());
            LocalDateTime from = null;
            LocalDateTime to = null;

            if (startDateStr != null) {
                from = LocalDate.parse(startDateStr)
                        .atStartOfDay(); // e.g. 2023-11-22T00:00
            }

            if (endDateStr != null) {
                to = LocalDate.parse(endDateStr)
                        .plusDays(1)
                        .atStartOfDay(); // e.g. 2025-06-29T00:00 (exclusive)
            }

            Page<ViewDotPaymentReport> mdrSupportPerCounty = viewDotPaymentReportRepo.findFilteredDotReports(keyword,provinceId,budget,county,month,year,from,to,orgIds,pageable);
            if (mdrSupportPerCounty.isEmpty()) {
                programIntelligenceResponse.setResponseMessage("No records");
                programIntelligenceResponse.setResponseCode("00");
            }else {
                programIntelligenceResponse.setResponseCode("00");
                programIntelligenceResponse.setResponseMessage("Success");
                programIntelligenceResponse.setData(mdrSupportPerCounty);
            }


        }catch (Exception e){
            log.error("getMdrSurveillancePayments error",e.getMessage());
            e.printStackTrace();
            programIntelligenceResponse.setResponseCode("01");
            programIntelligenceResponse.setResponseMessage("Failed");
        }
        return programIntelligenceResponse;
    }
    public ProgramIntelligenceResponse getMdrPatientsAndCostPerCounty(MDRpatientsandcostpercountyRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        ProgramIntelligenceResponse programIntelligenceResponse = ProgramIntelligenceResponse.builder()
                .responseCode("01")
                .responseMessage("Failed")
                .build();
        try {


            Integer page = request.getPage();
            Integer pageSize = request.getPageSize();

            String keyword = request.getSearch() != null ? request.getSearch().getValue() : null;
            List<Integer> orgIds = request.getOrgIds();


            Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize());
            String treatmentModel = request.getTreatmentModel();
            String county = request.getCounty();

            Page<MDRpatientsandcostpercounty> mdrPatientsAndCostPerCounty = mdrpatientsandcostpercountyRepo.findFilteredSummaryReport(treatmentModel,county,keyword,orgIds,pageable);
            if (mdrPatientsAndCostPerCounty.isEmpty()) {
                programIntelligenceResponse.setResponseMessage("No records");
                programIntelligenceResponse.setResponseCode("00");
            }else {
                programIntelligenceResponse.setResponseCode("00");
                programIntelligenceResponse.setResponseMessage("Success");
                programIntelligenceResponse.setData(mdrPatientsAndCostPerCounty);
            }


        }catch (Exception e){
            log.error("getMdrSurveillancePayments error",e.getMessage());
            e.printStackTrace();
            programIntelligenceResponse.setResponseCode("01");
            programIntelligenceResponse.setResponseMessage("Failed");
        }
        return programIntelligenceResponse;
    }
    public ProgramIntelligenceResponse getPatientPaymentOverTime(PatientPaymentOverTimeRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        ProgramIntelligenceResponse programIntelligenceResponse = ProgramIntelligenceResponse.builder()
                .responseCode("01")
                .responseMessage("Failed")
                .build();
        try {
            String startDateStr = request.getStartTimestamp();
            Timestamp date1 = parseDateOrTimestamp(startDateStr);
            String formattedDate1 = formatDateForQuery(date1);

            String endDateStr = request.getEndTimestamp();
            Timestamp date2 = parseDateOrTimestamp(endDateStr);
            String formattedDate2 = formatDateForQuery(date2);

            String countyIds = String.valueOf(request.getCountyId());
            Integer page = request.getPage();
            Integer pageSize = request.getPageSize();
            String provinceId = String.valueOf(request.getProvince());
            String month = request.getMonth();
            String year = request.getYear();
            String keyword = request.getKeyword() != null ? request.getKeyword() : null;
            List<Integer> orgIds = request.getOrgIds();
            String region = request.getRegion();

            Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize());
            LocalDateTime from = null;
            LocalDateTime to = null;

            if (startDateStr != null) {
                from = LocalDate.parse(startDateStr)
                        .atStartOfDay(); // e.g. 2023-11-22T00:00
            }

            if (endDateStr != null) {
                to = LocalDate.parse(endDateStr)
                        .plusDays(1)
                        .atStartOfDay(); // e.g. 2025-06-29T00:00 (exclusive)
            }

            Page<ViewMonthlyPatientSummary> patientMonthlySummaryReport = viewMonthlyPatientSummaryRepository.searchMonthlySummary(keyword,provinceId,countyIds,month,year,from,to,orgIds,region,pageable);
            if (patientMonthlySummaryReport.isEmpty()) {
                programIntelligenceResponse.setResponseMessage("No records");
                programIntelligenceResponse.setResponseCode("00");
            }else {
                programIntelligenceResponse.setResponseCode("00");
                programIntelligenceResponse.setResponseMessage("Success");
                programIntelligenceResponse.setData(patientMonthlySummaryReport);
            }


        }catch (Exception e){
            log.error("getPatientPaymentOver error",e.getMessage());
            e.printStackTrace();
            programIntelligenceResponse.setResponseCode("01");
            programIntelligenceResponse.setResponseMessage("Failed");
        }
        return programIntelligenceResponse;
    }

    public ProgramIntelligenceResponse getPatientPaymentOverTimeById(PatientPaymentOverTimeRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        ProgramIntelligenceResponse programIntelligenceResponse = ProgramIntelligenceResponse.builder()
                .responseCode("01")
                .responseMessage("Failed")
                .build();
        try {

            String orderId = String.valueOf(request.getOrderId());

            Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize());

            Page<Map<String,Object>> patientMonthlySummaryReportById = paymentRequestDetailsRepo.getPaymentRequestDetailsByOrderId(orderId,pageable);
            if (patientMonthlySummaryReportById.isEmpty()) {
                programIntelligenceResponse.setResponseMessage("No records");
                programIntelligenceResponse.setResponseCode("00");
                programIntelligenceResponse.setData(patientMonthlySummaryReportById);
            }else {
                programIntelligenceResponse.setResponseCode("00");
                programIntelligenceResponse.setResponseMessage("Success");
                programIntelligenceResponse.setData(patientMonthlySummaryReportById);
            }


        }catch (Exception e){
            log.error("getPatientPaymentOverById error",e.getMessage());
            e.printStackTrace();
            programIntelligenceResponse.setResponseCode("01");
            programIntelligenceResponse.setResponseMessage("Failed");
        }
        return programIntelligenceResponse;
    }
    public ProgramIntelligenceResponse getCompletedTreatment(PaymentRequest request,HttpServletRequest httpServletRequest, HttpServletResponse response){
        ProgramIntelligenceResponse programIntelligenceResponse = ProgramIntelligenceResponse.builder()
                .responseCode("01")
                .responseMessage("Failed")
                .build();
        try {
            int page = request.getPage();
            int pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);

            Page<Map<String, Object>> getCompletedTreatment = paymentRequestDetailsRepo.getPatientCompleteTreatment(pageable);
            if (getCompletedTreatment.isEmpty()) {
                programIntelligenceResponse.setResponseMessage("No records");
                programIntelligenceResponse.setResponseCode("00");
                programIntelligenceResponse.setData(getCompletedTreatment);
            }else {
                programIntelligenceResponse.setResponseCode("00");
                programIntelligenceResponse.setResponseMessage("Success");
                programIntelligenceResponse.setData(getCompletedTreatment);
            }

        }catch (Exception e){
            log.error("getCompletedTreatment error",e.getMessage());
            e.printStackTrace();
        }
        return programIntelligenceResponse;
    }

    public ProgramIntelligenceResponse getCompletedTreatmentById(PaymentRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        ProgramIntelligenceResponse programIntelligenceResponse = ProgramIntelligenceResponse.builder()
                .responseMessage("Failed")
                .responseCode("01")
                .build();
        try {

            String id = request.getId();
            int page = request.getPage();
            int pageSize = request.getPageSize();

            Pageable pageable = PageRequest.of(page, pageSize);

            Page<Map<String, Object>> completedTreatmentById = paymentRequestDetailsRepo.getCompletedTreatmentById(Long.valueOf(id), pageable);

            if (completedTreatmentById.isEmpty()) {
                programIntelligenceResponse.setResponseMessage("No records");
                programIntelligenceResponse.setResponseCode("00");
                programIntelligenceResponse.setData(completedTreatmentById);
            }else {
                programIntelligenceResponse.setResponseCode("00");
                programIntelligenceResponse.setResponseMessage("Success");
                programIntelligenceResponse.setData(completedTreatmentById);
            }


        }catch (Exception e){
            e.printStackTrace();
            log.error("getCompletedTreatmentById error",e.getMessage());

        }
        return programIntelligenceResponse;
    }
    public ProgramIntelligenceResponse getPatientCompletedTreatmentPayments(PatientCompletedTreatmentPayments request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        ProgramIntelligenceResponse programIntelligenceResponse = ProgramIntelligenceResponse.builder()
                .responseCode("01")
                .responseMessage("Failed")
                .build();

        try {
            Timestamp date1 = parseDateOrTimestamp(request.getDateFrom());
            Timestamp date2 = parseDateOrTimestamp(request.getDateTo());

            Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize());
            StringBuilder sql = new StringBuilder("""
                SELECT 
                    ao.ID AS primarykey,
                    ao.batchno AS batchno,
                    al.approval_level_name AS approval_level,
                    IFNULL(ao.month, ao.month_of_claim) AS month,
                    DATE_FORMAT(ao.requesttime, '%Y') AS year,
                    b.title AS budget_line,
                    COALESCE((SELECT p.title FROM province p WHERE p.id = ao.province), 'N/A') AS region,
                    COALESCE((SELECT c.title FROM county c WHERE c.id = ao.county), 'N/A') AS county,
                    ao.facility AS facility,
                    tm.title AS request_type,
                    ao.msisdn AS phone_no,
                    ao.approval_status AS status,
                    CONCAT(ao.firstname, ' ', ao.middlename, ' ', ao.lastname) AS beneficiary,
                    ao.initiator_username AS initiator,
                    CONVERT_TZ(ao.requesttime, '+00:00', '+03:00') AS request_time,
                    ao.patient_registration_number AS patient_number,
                    ao.date_treatment_started AS date_treatment_started,
                    ao.dot_nurse_name AS dot_name,
                    ao.dot_nurse_phoneno AS dot_phone,
                    ao.recipient2credit AS dot_amount,
                    ao.driver_amount AS driver_amt,
                    ao.credit AS amount
                FROM apiorder ao
                LEFT JOIN ordertype ot ON ao.ordertype = ot.ID
                LEFT JOIN budget b ON ao.budget = b.ID
                LEFT JOIN treatment_model tm ON ao.treatment_model = tm.ID
                LEFT JOIN approval_levels al ON ao.approval_level = al.approval_level
                WHERE ao.inTrash = 'No'
                AND ao.approval_status = 'Pending'
            """);

            Map<String, Object> parameters = new HashMap<>();

            if (request.getKeywords() != null && request.getKeywords() != null) {
                sql.append("""
                AND (
                    b.title LIKE :keyword OR 
                    tm.title LIKE :keyword OR 
                    ao.msisdn LIKE :keyword OR 
                    ao.credit LIKE :keyword OR 
                    ao.firstname LIKE :keyword OR 
                    ao.middlename LIKE :keyword OR 
                    ao.lastname LIKE :keyword OR 
                    ao.facility LIKE :keyword OR 
                    ao.batchno LIKE :keyword
                )
            """);
                parameters.put("keyword", "%" + request.getKeywords() + "%");
            }

            if (request.getProvince() != null) {
                sql.append(" AND ao.province = :province ");
                parameters.put("province", request.getProvince());
            }

            if (request.getCounties() != null && !request.getCounties().isEmpty()) {
                sql.append(" AND ao.county IN :counties ");
                parameters.put("counties", request.getCounties());
            }

            if (request.getBudgets() != null && !request.getBudgets().isEmpty()) {
                sql.append(" AND ao.budget IN :budgets ");
                parameters.put("budgets", request.getBudgets());
            }

            if (date1 != null) {
                sql.append(" AND ao.requesttime >= :dateFrom ");
                parameters.put("dateFrom", date1);
            }

            if (date2 != null) {
                sql.append(" AND ao.requesttime <= :dateTo ");
                parameters.put("dateTo", date2);
            }

            if (request.getBatch() != null) {
                sql.append(" AND ao.batchno = :batch ");
                parameters.put("batch", request.getBatch());
            }

            if (request.getMonth() != null) {
                sql.append(" AND UPPER(ao.month_of_claim) = UPPER(:month) ");
                parameters.put("month", request.getMonth());
            }

            if (request.getYear() != null) {
                sql.append(" AND DATE_FORMAT(ao.requesttime, '%Y') = :year ");
                parameters.put("year", request.getYear());
            }

            if (request.getApprovalStatus() != null) {
                sql.append(" AND ao.approval_status = :approvalStatus ");
                parameters.put("approvalStatus", request.getApprovalStatus());
            }

            if (request.getRegion() != null && request.getRegion() != 0) {
                sql.append(" AND ao.county = :region ");
                parameters.put("region", request.getRegion());
            }

            if (request.getPhoneNumbers() != null && !request.getPhoneNumbers().isEmpty()) {
                sql.append(" AND ao.msisdn IN :phoneNumbers ");
                parameters.put("phoneNumbers", request.getPhoneNumbers());
            }

            sql.append(" ORDER BY ao.ID DESC ");

            Query query = entityManager.createNativeQuery(sql.toString(), Tuple.class);

            parameters.forEach(query::setParameter);

            // Pagination
            query.setFirstResult((int) pageable.getOffset());
            query.setMaxResults(pageable.getPageSize());

            List<Tuple> results = query.getResultList();

            List<Map<String, Object>> finalResults = results.stream().map(tuple -> {
                Map<String, Object> map = new HashMap<>();
                tuple.getElements().forEach(column -> map.put(column.getAlias(), tuple.get(column.getAlias())));
                return map;
            }).toList();
            LocalDateTime from = null;
            LocalDateTime to = null;
            String startDateStr = request.getDateFrom();
            String endDateStr = request.getDateTo();

            if (startDateStr != null) {
                from = LocalDate.parse(startDateStr)
                        .atStartOfDay(); // e.g. 2023-11-22T00:00
            }

            if (endDateStr != null) {
                to = LocalDate.parse(endDateStr)
                        .plusDays(1)
                        .atStartOfDay(); // e.g. 2025-06-29T00:00 (exclusive)
            }
            Page<ViewPatientCompletedPatientReport> viewPatientCompletedPatientReports = viewPatientCompletedPatientReportRepo.searchPendingOrders(
                    request.getKeywords(),
                    request.getProvinceStr(),
                    request.getCounty(),
                    request.getBatch(),
                    request.getMonth(),
                    request.getYear(),
                    request.getApprovalStatus(),
                    request.getRegion() != null ? String.valueOf(request.getRegion()) : null,
                    request.getPhoneNumbers(),
                    from,
                    to,
                    PageRequest.of(request.getPage(), request.getPageSize())
            );

            programIntelligenceResponse.setResponseCode("00");
            programIntelligenceResponse.setResponseMessage(finalResults.isEmpty() ? "No records" : "Success");
            programIntelligenceResponse.setData(viewPatientCompletedPatientReports);

        } catch (Exception e) {
            log.error("getPatientPaymentOver error", e);
            programIntelligenceResponse.setResponseCode("01");
            programIntelligenceResponse.setResponseMessage("Failed");
        }

        return programIntelligenceResponse;
    }
    public ProgramIntelligenceResponse getDotNurseMonthlySummary(DotNurseSummaryRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        ProgramIntelligenceResponse response = ProgramIntelligenceResponse.builder()
                .responseCode("01")
                .responseMessage("Failed")
                .build();

        try {
            Integer selectedYear = request.getYear() != null ? request.getYear() : 2023;

            StringBuilder sql = new StringBuilder("""
            SELECT 
                p.ID AS primarykey,
                p.dot_nurse_name AS DotName,
                p.dot_nurse_phoneno AS DotNursePhoneNumber,
                p.year AS Year,
                SUM(Jan) AS Jan, SUM(Feb) AS Feb, SUM(Mar) AS Mar,
                SUM(Apr) AS Apr, SUM(May) AS May, SUM(Jun) AS Jun,
                SUM(Jul) AS Jul, SUM(Aug) AS Aug, SUM(Sep) AS Sep,
                SUM(Oct) AS Oct, SUM(Nov) AS Nov, SUM(Dece) AS Dece,
                (SUM(Jan)+SUM(Feb)+SUM(Mar)+SUM(Apr)+SUM(May)+SUM(Jun)+
                 SUM(Jul)+SUM(Aug)+SUM(Sep)+SUM(Oct)+SUM(Nov)+SUM(Dece)) AS Total
            FROM (
                SELECT r.*, 
                    CASE WHEN month = 'January' THEN recipient2credit ELSE 0 END AS Jan,
                    CASE WHEN month = 'February' THEN recipient2credit ELSE 0 END AS Feb,
                    CASE WHEN month = 'March' THEN recipient2credit ELSE 0 END AS Mar,
                    CASE WHEN month = 'April' THEN recipient2credit ELSE 0 END AS Apr,
                    CASE WHEN month = 'May' THEN recipient2credit ELSE 0 END AS May,
                    CASE WHEN month = 'June' THEN recipient2credit ELSE 0 END AS Jun,
                    CASE WHEN month = 'July' THEN recipient2credit ELSE 0 END AS Jul,
                    CASE WHEN month = 'August' THEN recipient2credit ELSE 0 END AS Aug,
                    CASE WHEN month = 'September' THEN recipient2credit ELSE 0 END AS Sep,
                    CASE WHEN month = 'October' THEN recipient2credit ELSE 0 END AS Oct,
                    CASE WHEN month = 'November' THEN recipient2credit ELSE 0 END AS Nov,
                    CASE WHEN month = 'December' THEN recipient2credit ELSE 0 END AS Dece
                FROM (
                    SELECT ao.*
                    FROM apiorder ao
                    LEFT JOIN budget b ON ao.budget = b.ID
                    LEFT JOIN treatment_model tm ON ao.treatment_model = tm.ID
                    WHERE ao.year = :selectedYear
                    AND ao.inTrash = 'No'
                    AND (ao.dot_nurse_phoneno IS NOT NULL AND ao.dot_nurse_phoneno != '')
                    AND (ao.treatment_model IN (1, 2, 5))
        """);

            Map<String, Object> params = new HashMap<>();
            params.put("selectedYear", selectedYear);

            // Apply filters
            if (request.getSearch() != null && !request.getSearch().isRegex()) {
                sql.append("""
                AND (
                    b.title LIKE :keyword OR 
                    tm.title LIKE :keyword OR 
                    ao.dot_nurse_name LIKE :keyword OR 
                    ao.recipient2credit LIKE :keyword OR 
                    ao.dot_nurse_phoneno LIKE :keyword
                )
            """);
                params.put("keyword", "%" + request.getSearch() + "%");
            }

            if (request.getProvince() != null) {
                sql.append(" AND ao.province = :province ");
                params.put("province", request.getProvince());
            }

            if (request.getBudget() != null) {
                sql.append(" AND ao.budget = :budget ");
                params.put("budget", request.getBudget());
            }

            if (request.getCounty() != null) {
                sql.append(" AND ao.county = :county ");
                params.put("county", request.getCounty());
            }

            if (request.getMonth() != null) {
                sql.append(" AND (ao.month_of_claim = :month OR ao.month = :month) ");
                params.put("month", request.getMonth());
            }

            if (request.getDateFrom() != null) {
                sql.append(" AND ao.requesttime >= :dateFrom ");
                params.put("dateFrom", Timestamp.valueOf(request.getDateFrom() + " 00:00:00"));
            }

            if (request.getDateTo() != null) {
                sql.append(" AND ao.requesttime <= :dateTo ");
                params.put("dateTo", Timestamp.valueOf(request.getDateTo() + " 23:59:59"));
            }

            // Optional region filter (similar to $_SESSION['region'])
            if (request.getRegion() != null && request.getRegion() != 0) {
                sql.append(" AND ao.county = :region ");
                params.put("region", request.getRegion());
            }

            sql.append("""
                ORDER BY ao.dot_nurse_phoneno, ao.ID ASC
                ) r
            ) p
            GROUP BY p.dot_nurse_phoneno
        """);

            Query query = entityManager.createNativeQuery(sql.toString(), Tuple.class);
            params.forEach(query::setParameter);

            List<Tuple> result = query.getResultList();

            List<Map<String, Object>> finalResults = result.stream().map(row -> {
                Map<String, Object> map = new HashMap<>();
                row.getElements().forEach(el -> map.put(el.getAlias(), row.get(el.getAlias())));
                return map;
            }).toList();

            response.setResponseCode("00");
            response.setResponseMessage(finalResults.isEmpty() ? "No records found" : "Success");
            response.setFinalResults(finalResults);

        } catch (Exception e) {
            log.error("getDotNurseMonthlySummary error", e);
            response.setResponseCode("01");
            response.setResponseMessage("Failed to retrieve summary");
        }

        return response;
    }
    public ProgramIntelligenceResponse getApprovalLevels(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        ProgramIntelligenceResponse response = ProgramIntelligenceResponse.builder()
                .responseCode("01")
                .responseMessage("Failed to retrieve summary")
                .build();
        try {
            Integer page = 0;
            Integer limit = 100;
            Pageable pageable = PageRequest.of(page, limit);
            Page<ApprovalLevelProjection> approvalLevels = approvalLevelRepo.findByApprovalLevels(pageable);
            if (approvalLevels.getTotalElements() == 0) {
                response.setResponseMessage("No records found");
                response.setResponseCode("01");
            }else {
                response.setResponseMessage("Success");
                response.setResponseCode("00");
                response.setData(approvalLevels);
            }

        }catch (Exception e) {
            log.error("getApprovalLevels error", e);
            e.printStackTrace();
        }
        return response;
    }







}
