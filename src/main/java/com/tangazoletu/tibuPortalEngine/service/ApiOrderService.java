package com.tangazoletu.tibuPortalEngine.service;

import com.tangazoletu.tibuPortalEngine.dto.*;
import com.tangazoletu.tibuPortalEngine.dto.batchdetailsdto.BatchDetailDTO;
import com.tangazoletu.tibuPortalEngine.dto.batchdetailsdto.BatchDetailsResponseDTO;
import com.tangazoletu.tibuPortalEngine.dto.batchdetailsdto.BatchDetailsWrapper;
import com.tangazoletu.tibuPortalEngine.dto.paymentapidto.FinanceApprovalPageWrapper;
import com.tangazoletu.tibuPortalEngine.dto.paymentapidto.PaymentTimelineResponse;
import com.tangazoletu.tibuPortalEngine.dto.paymentapidto.PendingWebInitiatedPaymentsFilterDTO;
import com.tangazoletu.tibuPortalEngine.entities.*;
import com.tangazoletu.tibuPortalEngine.enums.ApiResponseCode;
import com.tangazoletu.tibuPortalEngine.repositories.*;
import com.tangazoletu.tibuPortalEngine.util.SharedFunctions;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ApiOrderService {
    private final CrudService crudService;
    private final ApiOrderRepo apiOderRepo;
    private final ApprovalProcessingRepo approvalProcessingRepo;
    private final ApiOrderApprovalSummaryViewRepository apiOrderApprovalSummaryViewRepository;
    private final ViewUnconfirmedDriversNumbersRepo viewUnconfirmedDriversNumbersRepo;
    private final ViewPendingRecipientsRepo viewPendingRecipientsRepo;
    private final SharedFunctions sharedFunctions;
    private final BatchMetadataViewRepository batchMetadataViewRepository;
    private final PendingLevel2OrderViewRepository pendingLevel2OrderViewRepository;
    private final FundAllocationViewRepository fundAllocationViewRepository;
    private final PendingWebInitiatedPaymentsRepo pendingWebInitiatedPaymentsRepo;
    private final ViewCtclPendingApiOrdersRepository viewCtclPendingApiOrdersRepository;
    private final WebInitiatedBatchSummaryPendingRepository webInitiatedBatchSummaryPendingRepository;
    private final PaymentTimelineRepository paymentTimelineRepository;



    public FinanceApprovalResponse getApiOrderApproval(ApiOrderApprovalRequest request, HttpServletResponse response, HttpServletRequest request1) {
        FinanceApprovalResponse financeApprovalResponse = FinanceApprovalResponse.builder()
                .responseCode(ApiResponseCode.FAIL.getCode())
                .responseMessage("Failed")
                .build();
        try {
            List<Integer> orgIds = request.getOrgFilterList();
            int pageSize = request.getPageSize();
            int page = request.getPage();
            String apiOrderId = request.getApiOrderId();
            String confirmAbleColumn = request.getConfirmAbleColumn();
            String currentLevel = "1";
            String searchQuery = request.getKeyword() != null
                    ? request.getKeyword().toUpperCase()
                    : "";
            if (apiOrderId != null && !apiOrderId.isEmpty()) {
                ApiOrderFullDetailsResponseDTO responseDTO = ApiOrderFullDetailsResponseDTO.builder()
                        .build();
                Map<String, Object> params = new HashMap<>();

                // STEP 1: Get ids_string list from approval_processing
                String retrySql = "SELECT ids_string FROM approval_processing WHERE retry_count >= 3 AND status = 1";
                List<String> idStringRows = crudService.fetchWithNativeQuery(retrySql, params, 0, 0);

                Set<String> idsSet = new HashSet<>();
                for (String row : idStringRows) {
                    if (row != null && !row.isEmpty()) {
                        String[] ids = row.split(",");
                        idsSet.addAll(Arrays.asList(ids));
                    }
                }

                boolean stageApproval = idsSet.contains(apiOrderId);

                // STEP 2: Determine confirmable column logic based on currentLevel
                String confirmableQueryPart = "";
                if ("1".equals(currentLevel)) {
                    String ctlcDeadlineSql = "SELECT VALUE FROM PARAM WHERE PARAMETER = 'CTLC Approval Deadline'";
                    List<String> deadlineResult = crudService.fetchWithNativeQuery(ctlcDeadlineSql, params, 0, 1);
                    String deadline = deadlineResult.isEmpty() ? "01" : deadlineResult.get(0);
                    String confirmableCase = "CASE WHEN ( CONVERT_TZ(NOW(),'+00:00','+03:00') " +
                            "BETWEEN DATE_FORMAT(CONVERT_TZ(NOW(),'+00:00','+03:00'),'%Y-%m-01 00:00') AND " +
                            "DATE_FORMAT(CONVERT_TZ(NOW(),'+00:00','+03:00'),'%Y-%m-" + deadline + " 23:59') " +
                            ") THEN 'CONFIRMABLE' ELSE 'INCONFIRMABLE' END AS CONFIRMABLE";

                    confirmableQueryPart = ", " + confirmableCase;


                } else if (Arrays.asList("2", "3", "4").contains(currentLevel)) {
                    String startDaySql = "SELECT VALUE FROM PARAM WHERE PARAMETER = 'Program Approval Start Day'";
                    String endDaySql = "SELECT VALUE FROM PARAM WHERE PARAMETER = 'Program Approval End Day'";

                    List<String> startRes = crudService.fetchWithNativeQuery(startDaySql, params, 0, 1);
                    List<String> endRes = crudService.fetchWithNativeQuery(endDaySql, params, 0, 1);

                    String startDay = startRes.isEmpty() ? "01" : startRes.get(0);
                    String endDay = endRes.isEmpty() ? "28" : endRes.get(0);

                    String confirmableCase = "CASE WHEN ( CONVERT_TZ(NOW(),'+00:00','+03:00') " +
                            "BETWEEN DATE_FORMAT(CONVERT_TZ(NOW(),'+00:00','+03:00'),'%Y-%m-" + startDay + " 00:00') AND " +
                            "DATE_FORMAT(CONVERT_TZ(NOW(),'+00:00','+03:00'),'%Y-%m-" + endDay + " 23:59') " +
                            ") THEN 'CONFIRMABLE' ELSE 'INCONFIRMABLE' END AS CONFIRMABLE";

                    confirmableQueryPart = ", (" + confirmableCase + ")";
                }
                log.info("confirm query part {}",  confirmableQueryPart);

                // Step 3: Execute query with fetchWithNativeQuery
                String sql = "SELECT recipient, credit - (SELECT driver_amount FROM apiorder WHERE id = :key) AS balance, " +
                        "msisdn, onbehalfrecipient, recipient2credit, recipient2msisdn, recipient2, notes, ordertype, treatment_model, " +
                        "(SELECT beneficiaryType FROM apiorder WHERE id = :key), " +
                        "(SELECT beneficiary2Type FROM apiorder WHERE id = :key)" + confirmableQueryPart + " " +
                        "FROM paymentrequestdetails WHERE id = :key";

                params.put("key", apiOrderId);
                List<Object[]> result = crudService.fetchWithNativeQuery(sql, params, 0, 1);
                String orderType = "";
                String msisdn = "";
                String patientRegNo = "";

                if (!result.isEmpty()) {
                    Object[] row = result.get(0);  // row is an Object[]

                    orderType = row[8] != null ? row[8].toString().trim() : "";
                    msisdn = row[2] != null ? row[2].toString().replace("+", "").trim() : "";
                    String confirmableStatus = row.length > 12 && row[12] != null ? row[12].toString().trim() : "INCONFIRMABLE";
                    Integer beneficiarytye1;
                    try {
                        beneficiarytye1 = row[10] != null ? Integer.valueOf(row[10].toString().trim()) : -1;
                    } catch (NumberFormatException e) {
                        beneficiarytye1 = -1;
                    }

                    Integer beneficiarytye2;
                    try {
                        beneficiarytye2 = row[11] != null ? Integer.valueOf(row[11].toString().trim()) : -1;
                    } catch (NumberFormatException e) {
                        beneficiarytye2 = -1;
                    }

                    patientRegNo = (row.length > 12 && row[12] != null) ? row[12].toString().trim() : "";
                    String treatmentModel = row[9] != null ? row[9].toString().trim() : "";
                    Integer treamModelInt = row[9] != null ? (Integer) row[9] : -1;
                    String patientname = row[3] != null ? row[3].toString().trim() : "";
                    String recipient1Title = row[0] != null ? row[0].toString().trim() : "";
                    String AmountToSentToRecipient1 = row[1] != null ? row[1].toString().trim() : "";
                    String recipientPhoneNumber = row[2] != null ? row[2].toString().trim() : "";
                    String recipient2Title = row[6] != null ? row[6].toString().trim() : "";
                    String AmountToSentToRecipient2 = row[4] != null ? row[4].toString().trim() : "";
                    String secondRecipientPhoneNumber = row[5] != null ? row[5].toString().trim() : "";
                    String description = row[7] != null ? row[7].toString().trim() : "";
                    String paymentType = row[8] != null ? row[8].toString().trim() : "";

                    String beneficiaryTitle = "Patient support".equalsIgnoreCase(orderType) ? "Patient Name" : "Beneficiary Name:";
                    String nomineeTitle = "Patient support".equalsIgnoreCase(orderType) ? "Nominee Name" : "Onbehalf Recipient Name:";
                     recipient1Title = "Recipient Name:";
                     recipient2Title = "Second Recipient's Name:";

                    if ("Patient support".equalsIgnoreCase(orderType)) {
                        switch (treamModelInt) {
                            case 1: // Facility based
                                switch (beneficiarytye1) {
                                    case 1:
                                        recipient1Title = "Recipient Name(Patient):";
                                        break;
                                    case 2:
                                        recipient1Title = "Recipient Name(HCW):";
                                        break;
                                    case 3:
                                        recipient1Title = "Recipient Name(SCTLC):";
                                        break;
                                    case 4:
                                        recipient1Title = "Recipient Name(Supporter):";
                                        break;
                                    default:
                                        break;
                                }
                                break;

                            case 2: // Community based
                                switch (beneficiarytye1) {
                                    case 1:
                                        recipient1Title = "Recipient Name(Patient):";
                                        break;
                                    case 4:
                                        recipient1Title = "Recipient Name(Supporter):";
                                        break;
                                    default:
                                        break;
                                }

                                if (Integer.parseInt(apiOrderId.toString()) > 51335) {
                                    switch (beneficiarytye2) {
                                        case 2:
                                            recipient2Title = "Second Recipient's Name(HCW):";
                                            break;
                                        case 3:
                                            recipient2Title = "Second Recipient's Name(SCTLC):";
                                            break;
                                        default:
                                            break;
                                    }
                                }
                                break;

                            default:
                                break;
                        }
                    }

                    ApiOrderApprovalTwoDTO dto = ApiOrderApprovalTwoDTO.builder()
                            .stageApproval(stageApproval)
                            .orderType(orderType)
                            .beneficiaryTitle(beneficiaryTitle)
                            .nomineeTitle(nomineeTitle)
                            .confirmableStatus(confirmableStatus)
                            .msisdn(msisdn)
                            .beneficiarytye1(String.valueOf(beneficiarytye1))
                            .beneficiarytye2(String.valueOf(beneficiarytye2))
                            .patientRegNo(patientRegNo)
                            .treatmentModel(treatmentModel)
                            .recipient2Title(recipient2Title)
                            .amountToSentToRecipient1(AmountToSentToRecipient1)
                            .recipient1Title(recipient1Title)
                            .description(description)
                            .paymentType(paymentType)
                            .secondRecipientPhoneNumber(secondRecipientPhoneNumber)
                            .amountToSentToRecipient2(AmountToSentToRecipient2)
                            .recipientPhoneNumber(recipientPhoneNumber)
                            .patientName(patientname)
                            .onBehalfRecipient(patientname)
                            .build();
                    responseDTO.setOrderApproval(dto);

                }
                String sqlOrderType = "SELECT ot.title, ao.attached_to, ao.driver_name, ao.driver_phone, ao.driver_amount, ao.id " +
                        "FROM ordertype ot " +
                        "LEFT JOIN apiorder ao ON ao.ordertype = ot.id " +
                        "WHERE ao.id = :key";

                Map<String, Object> paramsSupervisor = new HashMap<>();
                paramsSupervisor.put("key", apiOrderId);

                List<Object[]> resList = crudService.fetchWithNativeQuery(sqlOrderType, paramsSupervisor, 0, 1);

                if (!resList.isEmpty()) {
                    Object[] res = resList.get(0);
                    String title = res[0] != null ? res[0].toString().toUpperCase() : "";

                    boolean isSupervision = List.of("SUPERVISION", "MDR OTHER PAYMENTS", "PATIENT SUPPORT").contains(title);
                    if (isSupervision) {
                        String query = "select id,title,credit,url,local_url,expense_id from supervision_details where apiorder_id=:id";
                        Map<String, Object> parms = new HashMap<>();
                        parms.put("id", apiOrderId);
                        List<Object[]> detailsList = crudService.fetchWithNativeQuery(query, parms, 0, 100);
                        if (!detailsList.isEmpty()) {
                            Object[] result1 = detailsList.get(0);

                            String supervisionId = result1[0] != null ? result1[0].toString().trim() : null;
                            String supervisionTitle = result1[1] != null ? result1[1].toString().trim() : null;
                            List<Integer> expenseIds = new ArrayList<>();
                            List<Integer> expenseIds1 = new ArrayList<>();
                            List<SupervisionItemDTO> items = new ArrayList<>();

                            String sqlComments = """
                                SELECT spa.notes, spa.status, al.approval_level_name
                                FROM supervision_details_approval spa
                                INNER JOIN approval_levels al ON spa.approval_level = al.approval_level
                                WHERE spa.supdetails_id = :supdetailsId
                                ORDER BY spa.approval_time ASC
                            """;

                            for (Object[] row : detailsList) {
                                 supervisionId = String.valueOf(row[0] != null ? Long.parseLong(row[0].toString()) : null);

                                List<ApprovalCommentDTO> comments = new ArrayList<>();
                                if (supervisionId != null) {
                                    Map<String, Object> commentParams = Map.of("supdetailsId", supervisionId);
                                    List<Object[]> commentsList = crudService.fetchWithNativeQuery(sqlComments, commentParams, 0, 10);
                                    comments = commentsList.stream().map(commentRow ->
                                            ApprovalCommentDTO.builder()
                                                    .notes(commentRow[0] != null ? commentRow[0].toString() : "")
                                                    .approvalStatus(commentRow[1] != null ? commentRow[1].toString() : "")
                                                    .approvalLevelName(commentRow[2] != null ? commentRow[2].toString() : "")
                                                    .build()
                                    ).collect(Collectors.toList());
                                }

                                SupervisionItemDTO item = SupervisionItemDTO.builder()
                                        .id(Long.valueOf(supervisionId))
                                        .title(row[1] != null ? row[1].toString().trim() : "")
                                        .credit(row[2] != null ? Double.parseDouble(row[2].toString()) : 0.0)
                                        .url(row[3] != null ? row[3].toString().trim() : "")
                                        .localUrl(row[4] != null ? row[4].toString().trim() : "")
                                        .expenseId(row[5] != null ? Long.parseLong(row[5].toString()) : null)
                                        .comments(comments)  // ✅ Nested comments here
                                        .build();

                                items.add(item);
                            }

                            responseDTO.setSupervisionItems(items);

                            if (res[1] != null) {
                                // Safely parse expenseIds from result1[5]
                                if (result1[5] instanceof String) {
                                    String[] ids = result1[5].toString().split(",");
                                    for (String id : ids) {
                                        try {
                                            expenseIds.add(Integer.parseInt(id.trim()));
                                            expenseIds1= expenseIds;
                                        } catch (NumberFormatException ignored) {
                                        }
                                    }
                                } else if (result1[5] instanceof Collection) {
                                    expenseIds.addAll((Collection<Integer>) result1[5]);
                                }

                                // Only continue if there are valid expense IDs
                                if (!expenseIds.isEmpty()) {

                                    String relatedQuery = "SELECT sd.credit, sd.url, sd.approval_status, sd.title, sd.apiorder_id, " +
                                            "FROM supervision_details sd " +
                                            "WHERE sd.apiorder_id IN (:expenseIds) AND sd.title = :title";

                                    Map<String, Object> itemParams = new HashMap<>();
                                    itemParams.put("expenseIds", expenseIds1);
                                    itemParams.put("title", supervisionTitle);

                                    List<Object[]> relatedList = crudService.fetchWithNativeQuery(relatedQuery, itemParams, 0, 100);
                                    String fetchRequestTimeSql = "SELECT CONVERT_TZ(requesttime, '+00:00', '+03:00') AS requesttime " +
                                            "FROM apiorder WHERE id = :orderId";

                                    Map<String, Object> timeParams = new HashMap<>();
                                    timeParams.put("orderId", apiOrderId); // replace with the dynamic id

                                    List<Object> timeResult = crudService.fetchWithNativeQuery(fetchRequestTimeSql, timeParams, 0, 1);

                                    String requestTime = !timeResult.isEmpty() && timeResult.get(0) != null
                                            ? timeResult.get(0).toString()
                                            : "";


                                    List<RelatedSupervisionItemDTO> relatedItems = relatedList.stream().map(row -> {
                                        Double amount = "Expense Sheet".equalsIgnoreCase(supervisionTitle) ? null : (row[0] != null ? Double.parseDouble(row[0].toString()) : null);
                                        String approvalStatus = "Expense Sheet".equalsIgnoreCase(supervisionTitle) ? "N/A" : (row[2] != null ? row[2].toString() : "N/A");

                                        return RelatedSupervisionItemDTO.builder()
                                                .requestTime(requestTime)
                                                .amount(amount)
                                                .approvalStatus(approvalStatus)
                                                .receiptUrl(row[1] != null ? row[1].toString() : "")
                                                .build();
                                    }).collect(Collectors.toList());
                                    responseDTO.setRelatedSupervisionItemDTOS(relatedItems);

                                    // ✅ Now you can return this list as part of your API response
                                }


                            }


                            // Proceed with next queries
                        }

                        SupervisionDetailsDTO dto = SupervisionDetailsDTO.builder()
                                .title(res[0] != null ? res[0].toString() : "")
                                .attachedTo(res[1] != null ? res[1].toString() : "")
                                .driverName(res[2] != null ? res[2].toString() : "")
                                .driverPhone(res[3] != null ? res[3].toString() : "")
                                .driverAmount(res[4] != null ? Double.parseDouble(res[4].toString()) : 0)
                                .apiOrderId(res[5] != null ? Long.parseLong(res[5].toString()) : null)
                                .build();
                        responseDTO.setSupervisionDetailsDTOS(dto);
                    }
                }
                if (orderType.equalsIgnoreCase("Patient support")){

                    String orgId = request.getOrgId();

                    String sql2 = "SELECT payment.trx_id, payment.trx_date, payment.recipient_name " +
                                "FROM payment " +
                                "LEFT JOIN apiorder ON apiorder.ID = payment.order_id " +
                                "WHERE apiorder.patient_registration_number IS NOT NULL " +
                                "AND apiorder.patient_registration_number != '' " +
                                "AND apiorder.patient_registration_number = :patientRegNo " +
                                "AND payment.trx_status = 'Completed' " +
                                "AND apiorder.inTrash = 'No' " +
                                "ORDER BY apiorder.ID DESC";

                        Map<String, Object> params2 = new HashMap<>();
                        params2.put("patientRegNo", patientRegNo);

                        List<Object[]> result2 = crudService.fetchWithNativeQuery(sql2, params2, 0, 100); // adjust limit as needed

                        List<PatientPaymentRecordDTO> records = new ArrayList<>();
                        for (Object[] row : result2) {
                            PatientPaymentRecordDTO dto = PatientPaymentRecordDTO.builder()
                                    .transactionId(row[0] != null ? row[0].toString() : "")
                                    .transactionDate(row[1] != null ? row[1].toString() : "")
                                    .recipientName(row[2] != null ? row[2].toString() : "")
                                    .msisdn(msisdn) // already defined elsewhere
                                    .build();
                            records.add(dto);

                        }
                        responseDTO.setPatientPaymentRecordDTOS(records);

                        // Now you can return or use `records` as needed

                }
                String sql4 = "SELECT approval_level_name, approval_status, approved_amount, approval_time, " +
                        "approval_notes, CONCAT(firstname, ' ', lastname) AS approver, phone_number " +
                        "FROM payment_approval " +
                        "LEFT JOIN user ON user.id = approver_id " +
                        "WHERE apiorder_id = :apiOrderId " +
                        "ORDER BY payment_approval.ID ASC";

                Map<String, Object> params4 = new HashMap<>();
                params4.put("apiOrderId", apiOrderId); // assume apiOrderId is already defined

                List<Object[]> result4 = crudService.fetchWithNativeQuery(sql4, params4, 0, 100); // or as needed

                List<PaymentApprovalDTO> approvals = new ArrayList<>();
                for (Object[] row : result4) {
                    PaymentApprovalDTO dto = PaymentApprovalDTO.builder()
                            .approvalLevel(row[0] != null ? row[0].toString() : "")
                            .status(row[1] != null ? row[1].toString() : "")
                            .approvedAmount(row[2] != null ? row[2].toString() : "")
                            .approvalTime(row[3] != null ? row[3].toString() : "")
                            .notes(row[4] != null ? row[4].toString() : "")
                            .approver(row[5] != null ? row[5].toString() : "")
                            .phoneNumber(row[6] != null ? row[6].toString() : "")
                            .build();
                    approvals.add(dto);
                }
                responseDTO.setPaymentApprovalDTOS(approvals);

                // return or process `approvals` list as needed
                if (currentLevel.equalsIgnoreCase("1")) {
                    String sql5 = "SELECT id, name, phone_number, amount FROM drivers_details WHERE apiorder_id = :orderId";

                    Map<String, Object> params5 = new HashMap<>();
                    params5.put("orderId", apiOrderId); // assume `apiOrderId` is passed or resolved before this

                    List<Object[]> results = crudService.fetchWithNativeQuery(sql5, params5, 0, 100);

                    List<DriverDetailsDTO> driverDetails = new ArrayList<>();
                    for (Object[] row : results) {
                        DriverDetailsDTO dto = DriverDetailsDTO.builder()
                                .id(row[0] != null ? Integer.parseInt(row[0].toString()) : null)
                                .name(row[1] != null ? row[1].toString() : "")
                                .phoneNumber(row[2] != null ? row[2].toString() : "")
                                .amount(row[3] != null ? row[3].toString() : "")
                                .build();
                        driverDetails.add(dto);
                    }
                    responseDTO.setDriverDetailsDTOS(driverDetails);

                    // return or process `driverDetails` list as needed
                }
                if (orderType.equalsIgnoreCase("Patient support")
                        || request.getApiOrderId().equalsIgnoreCase("55")) {

                    String sql6 = "SELECT apiorder.batchno, approval_level_name, facility, " +
                            "treatment_model.title AS RequestType, " +
                            "CONVERT_TZ(apiorder.requesttime,'+00:00','+03:00'), " +
                            "credit, approval_status, apiorder.notes " +
                            "FROM apiorder " +
                            "LEFT JOIN ordertype ON apiorder.ordertype = ordertype.ID " +
                            "LEFT JOIN budget ON apiorder.budget = budget.ID " +
                            "LEFT JOIN treatment_model ON apiorder.treatment_model = treatment_model.ID " +
                            "LEFT JOIN approval_levels ON apiorder.approval_level = approval_levels.approval_level " +
                            "WHERE msisdn LIKE :msisdn AND apiorder.inTrash = 'No' " +
                            "ORDER BY apiorder.ID DESC";

                    Map<String, Object> params6= new HashMap<>();
                    params6.put("msisdn", "%" + msisdn + "%");
                    List<Object[]> results = crudService.fetchWithNativeQuery(sql6, params6, 0, 1);

                    List<PatientPaymentRecordHistoryDTO> records = new ArrayList<>();

                    for (Object[] row : results) {
                        PatientPaymentRecordHistoryDTO dto = PatientPaymentRecordHistoryDTO.builder()
                                .batchNo(row[0] != null ? row[0].toString() : null)
                                .approvalLevel(row[1] != null ? row[1].toString() : null)
                                .facility(row[2] != null ? row[2].toString() : null)
                                .requestType(row[3] != null ? row[3].toString() : null)
                                .requestTime(row[4] != null ? row[4].toString() : null)
                                .amount(row[5] != null ? row[5].toString() : null)
                                .approvalStatus(row[6] != null ? row[6].toString() : null)
                                .notes(row[7] != null ? row[7].toString() : null)
                                .build();

                        records.add(dto);
                    }
                    responseDTO.setPatientPaymentRecordHistoryDTOS(records);


                    // Now 'records' contains all the data you need
                }


                // Return as Page
                List<ApiOrderFullDetailsResponseDTO> list = Collections.singletonList(responseDTO);
                Page<ApiOrderFullDetailsResponseDTO> page1 = new PageImpl<>(list, PageRequest.of(0, 1), 1);
                if (page1.isEmpty()){

                }else {
                    Map<String, Object> pageMeta = new HashMap<>();
                    pageMeta.put("size", page1.getSize());
                    pageMeta.put("number", page1.getNumber());
                    pageMeta.put("totalElements", page1.getTotalElements());
                    pageMeta.put("totalPages", page1.getTotalPages());



                    FinanceApprovalPageWrapper wrapper = FinanceApprovalPageWrapper.builder()
                            .content(page1.getContent())
                            .page(pageMeta)
                            .build();
                    financeApprovalResponse.setResponseMessage("Success");
                    financeApprovalResponse.setResponseCode("00");
                    financeApprovalResponse.setData(wrapper);
                }

            } else {


                // Get deadline format from param table
                String deadlineFormatQuery = "SELECT VALUE FROM PARAM WHERE PARAMETER = 'CTLC Approval Deadline'";
                List<String> result = crudService.fetchWithNativeQuery(deadlineFormatQuery, new HashMap<>(), 0, 1);
                String format = result.isEmpty() ? "25" : result.getFirst(); // fallback default

                Pageable pageable = PageRequest.of(page, pageSize);
                String region = null;
                String dateFrom = request.getDateFrom();
                String dateTo = request.getDateTo();
                String county = null;
                String keyword = null;
                String orgId = request.getOrgId();
                if (request.getRegion() != null) {
                     region = request.getRegion();
                }
                if (county != null) {
                    county = request.getCounty();
                }
                if (keyword != null) {
                    keyword = request.getKeyword();
                }
                LocalDateTime from = null;
                LocalDateTime to = null;

                if (dateFrom != null) {
                    from = LocalDate.parse(dateFrom)
                            .atStartOfDay(); // e.g. 2023-11-22T00:00
                }

                if (dateTo != null) {
                    to = LocalDate.parse(dateTo)
                            .plusDays(1)
                            .atStartOfDay(); // e.g. 2025-06-29T00:00 (exclusive)
                }
                region = (region == null || region.trim().isEmpty() || region.trim().equalsIgnoreCase("null")) ? null : region.trim();
                county = (county == null || county.trim().isEmpty() || county.trim().equalsIgnoreCase("null")) ? null : county.trim();
                searchQuery = (searchQuery == null || searchQuery.trim().isEmpty() || searchQuery.trim().equalsIgnoreCase("null")) ? null : searchQuery.trim();
                Long orgIdLong = (orgId == null || orgId.trim().isEmpty()) ? null : Long.valueOf(orgId.trim());
                log.info("Calling findFilteredPendingOrders with search={}, region={}, county={}, from={}, to={}, orgId={}",
                        keyword, region, county, from, to, orgIdLong);


                Page<ViewCtclPendingApiOrders> page1 = viewCtclPendingApiOrdersRepository.findFilteredPendingOrders(searchQuery, String.valueOf(region), county,from,to, orgIdLong, pageable);
                //Page<ViewCtclPendingApiOrders> page1 = viewCtclPendingApiOrdersRepository.findAll(pageable)
                if (page1.getTotalElements() > 0) {
                    BigDecimal totalDotAmount = BigDecimal.ZERO;
                    BigDecimal totalDriverAmount = BigDecimal.ZERO;
                    BigDecimal totalAmount = BigDecimal.ZERO;

                    for (ViewCtclPendingApiOrders order : page1.getContent()) {
                        totalDotAmount = totalDotAmount.add(order.getDotAmount() != null ? order.getDotAmount() : BigDecimal.ZERO);
                        totalDriverAmount = totalDriverAmount.add(order.getDriverAmount() != null ? order.getDriverAmount() : BigDecimal.ZERO);
                        totalAmount = totalAmount.add(order.getAmount() != null ? order.getAmount() : BigDecimal.ZERO);
                    }

                    Map<String, Object> pageMeta = new HashMap<>();
                    pageMeta.put("size", page1.getSize());
                    pageMeta.put("number", page1.getNumber());
                    pageMeta.put("totalElements", page1.getTotalElements());
                    pageMeta.put("totalPages", page1.getTotalPages());

                    Map<String, BigDecimal> totals = new HashMap<>();
                    totals.put("totalDotAmount", totalDotAmount);
                    totals.put("totalDriverAmount", totalDriverAmount);
                    totals.put("totalAmount", totalAmount);

                    FinanceApprovalPageWrapper wrapper = FinanceApprovalPageWrapper.builder()
                            .content(page1.getContent())
                            .page(pageMeta)
                            .totals(totals)
                            .build();

                    financeApprovalResponse = FinanceApprovalResponse.builder()
                            .responseCode(ApiResponseCode.SUCCESS.getCode())
                            .responseMessage("Success")
                            .data(wrapper)
                            .build();

                }else {
                    financeApprovalResponse = FinanceApprovalResponse.builder()
                            .responseCode("00")
                            .responseMessage("No pending orders")
                            .build();
                }

            }
        } catch (Exception e) {
            log.error("Failed to fetch API Order Approvals: {}", e.getMessage(), e);
            financeApprovalResponse.setResponseMessage("Failed");
        }
        return financeApprovalResponse;

    }


    public Page<Map<String, Object>> getPendingApiOrders(ApiOrderApprovalRequest request,  HttpServletResponse response, HttpServletRequest request1) {
        log.info("Fetching Pending API Order Approvals: {}", request.toString());
        try {
            List<Integer> orgIds = request.getOrgFilterList();
            String keyword = request.getKeyword() != null ? request.getKeyword() : null;
            Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize());
            String approval = request.getCurrentApprovalLevel();
            String id = request.getBatchId();
            if (id != null && !id.isEmpty() && approval != null && !approval.isEmpty()) {
                Integer count = apiOderRepo.countAllByBatchnoAndApprovalLevel(id, approval);
                int inlineheight = (count == 7) ? 560 : 520;

                Map<String, Object> result = new HashMap<>();
                result.put("inlineheight", inlineheight);

                return new PageImpl<>(List.of(result));
            } else {

                return apiOderRepo.searchApiOrders(orgIds, keyword, pageable);
            }
        } catch (Exception e) {
            log.error("Failed to fetch Pending API Order Approvals: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    public com.tangazoletu.tibuPortalEngine.dto.batchdetailsdto.BatchDetailsResponse getBatchDetails(ViewBatchDetailsRequest request, HttpServletResponse response, HttpServletRequest servletRequest) {
        com.tangazoletu.tibuPortalEngine.dto.batchdetailsdto.BatchDetailsResponse batchDetailsResponse = com.tangazoletu.tibuPortalEngine.dto.batchdetailsdto.BatchDetailsResponse.builder()
                .responseMessage("Failed")
                .responseCode("01")
                .build();

        try {
            String batchNumber = request.getBatchNumber();
            String county = request.getCounty();
            String region = request.getRegion();
            String keywords = request.getKeyword(); // Assuming this maps to `keywords`
            Integer budget = request.getBudget() != null ? request.getBudget() : 0;
            Integer orgId = request.getOrgId();
            String type = request.getType();
            String approvalLevelStr = request.getApprovalLevel();
            int approvalLevel = approvalLevelStr != null ? Integer.parseInt(approvalLevelStr) : 0;

            int page = request.getPage() != null ? request.getPage() : 0;
            int pageSize = request.getPageSize() != null ? request.getPageSize() : 10;
            Pageable pageable = PageRequest.of(page, pageSize);

            Map<String, Object> params = new HashMap<>();
            StringBuilder query = new StringBuilder();
            StringBuilder searchFilter = new StringBuilder(" ");
            StringBuilder typeFilter = new StringBuilder(" ");

            // Handle keyword filters
            if (keywords != null && !keywords.isBlank()) {
                searchFilter.append("""
                            AND (
                                apiorder.requestTime LIKE :kw OR apiorder.dot_nurse_phoneno LIKE :kw OR apiorder.dot_nurse_name LIKE :kw OR
                                apiorder.batchno LIKE :kw OR budget.title LIKE :kw OR treatment_model.title LIKE :kw OR
                                apiorder.msisdn LIKE :kw OR apiorder.credit LIKE :kw OR
                                apiorder.firstname LIKE :kw OR apiorder.lastname LIKE :kw OR apiorder.middlename LIKE :kw
                            )
                        """);
                params.put("kw", "%" + keywords + "%");
            }

            if (region != null && !region.isBlank()) {
                searchFilter.append(" AND apiorder.province = :region");
                params.put("region", region);
            }

            if (county != null && !county.isBlank()) {
                searchFilter.append(" AND apiorder.county = :county");
                params.put("county", county);
            }

            if (budget > 0) {
                searchFilter.append(" AND apiorder.budget = :budget");
                params.put("budget", budget);
            }
            if (orgId != null) {
                searchFilter.append(" AND apiorder.ORG_ID = :orgId");
                params.put("orgId", orgId);
            }

            LocalDateTime dateFrom = null;
            LocalDateTime dateTo = null;
            if (request.getStartDate() != null && request.getEndDate() != null) {
                dateFrom = convertMillisStringToLocalDateTime(request.getStartDate()).withHour(0).withMinute(0);
                dateTo = convertMillisStringToLocalDateTime(request.getEndDate()).withHour(23).withMinute(59);
                searchFilter.append(" AND apiorder.requestTime BETWEEN :dateFrom AND :dateTo");
                params.put("dateFrom", dateFrom);
                params.put("dateTo", dateTo);
            }
            if (request.getAll() != null && request.getAll().equalsIgnoreCase("0")) {
                if ("1".equals(type)) {
                    typeFilter.append(" AND apiorder.treatment_model IN (4,5)");
                } else if ("2".equals(type)) {
                    typeFilter.append(" AND apiorder.treatment_model IN (1,2)");
                } else if ("3".equals(type)) {
                    typeFilter.append(" AND apiorder.treatment_model = 3");
                }

            }

            if (approvalLevel > 0) {
                typeFilter.append(" AND apiorder.approval_level = :approvalLevel");
                params.put("approvalLevel", approvalLevel);
            }

            // ✅ Handle validation and confirmable column logic (approval = 6)
            String validationColumns = "";
            String validationColumns2 = "";
            String confirmableColumn = "";

            if (approvalLevel == 6) {
                validationColumns = """
                            CONCAT(
                                IFNULL((
                                    CASE 
                                        WHEN (SELECT COUNT(*) FROM confirmed_recipient WHERE msisdn = apiorder.msisdn OR msisdn = CONCAT('+', apiorder.msisdn)) > 0 
                                        THEN 'Beneficiary Confirmed' 
                                        ELSE 'Beneficiary Not Confirmed' 
                                    END
                                ), ''),
                                '\n',
                                IFNULL((
                                    CASE 
                                        WHEN (SELECT COUNT(*) FROM confirmed_recipient WHERE msisdn = apiorder.driver_phone OR msisdn = CONCAT('+', apiorder.driver_phone)) > 0 
                                        THEN (SELECT CONCAT('Confirmed Driver:(', Full_name, '(', msisdn, '))') FROM confirmed_recipient WHERE msisdn = apiorder.driver_phone OR msisdn = CONCAT('+', apiorder.driver_phone) LIMIT 1) 
                                        ELSE (CASE WHEN driver_phone IS NULL OR driver_phone = '' THEN '' ELSE 'Driver Not Confirmed' END)
                                    END
                                ), ''),
                                '\n',
                                IFNULL((
                                    CASE 
                                        WHEN (SELECT COUNT(*) FROM confirmed_recipient WHERE msisdn = apiorder.recipient2msisdn OR msisdn = CONCAT('+', apiorder.recipient2msisdn)) > 0 
                                        THEN (SELECT CONCAT('Confirmed DOT:(', Full_name, '(', msisdn, '))') FROM confirmed_recipient WHERE msisdn = apiorder.recipient2msisdn OR msisdn = CONCAT('+', apiorder.recipient2msisdn) LIMIT 1) 
                                        ELSE (CASE WHEN recipient2msisdn IS NULL OR recipient2msisdn = '' THEN '' ELSE 'DOT Not Confirmed' END)
                                    END
                                ), '')
                            ) AS `Mpesa Confirmation`,
                        """;

                validationColumns2 = """
                            CASE 
                                WHEN (SELECT COUNT(*) FROM confirmed_recipient WHERE msisdn = apiorder.msisdn OR msisdn = CONCAT('+', apiorder.msisdn)) > 0 
                                THEN 'Confirmed' 
                                ELSE 'Not Confirmed' 
                            END AS `Mpesa Confirmation`,
                            (SELECT CONCAT(Full_name, '(', msisdn, ')') FROM confirmed_recipient WHERE msisdn = apiorder.msisdn OR msisdn = CONCAT('+', apiorder.msisdn) LIMIT 1) AS `Confirmed Name`,
                        """;
            }


            if (approvalLevel == 1) {
                String deadlineFormatQuery = "SELECT VALUE FROM PARAM WHERE PARAMETER = 'CTLC Approval Deadline'";
                List<String> result = crudService.fetchWithNativeQuery(deadlineFormatQuery, new HashMap<>(), 0, 1);
                String format1 = result.isEmpty() ? "25" : result.getFirst(); // fallback default


                confirmableColumn = String.format("""
                            , CASE
                                WHEN apiorder.approval_status = 'Pending' AND
                                    (CONVERT_TZ(NOW(),'+00:00','+03:00') BETWEEN
                                    DATE_FORMAT(CONVERT_TZ(NOW(),'+00:00','+03:00'), '%%Y-%%m-01 00:00') AND
                                    DATE_FORMAT(CONVERT_TZ(NOW(),'+00:00','+03:00'), '%%Y-%%m-%s'))
                                THEN 'CONFIRMABLE'
                                ELSE 'INCONFIRMABLE'
                            END AS `CONFIRMABLE`
                        """, format1);
            } else if (List.of(2, 3, 4).contains(approvalLevel)) {
                String startDaySql = "SELECT VALUE FROM PARAM WHERE PARAMETER = 'Program Approval Start Day'";
                String endDaySql = "SELECT VALUE FROM PARAM WHERE PARAMETER = 'Program Approval End Day'";

                List<String> startRes = crudService.fetchWithNativeQuery(startDaySql, new HashMap<>(), 0, 1);
                List<String> endRes = crudService.fetchWithNativeQuery(endDaySql, new HashMap<>(), 0, 1);

                String startDay = startRes.isEmpty() ? "01" : startRes.get(0);
                String endDay = endRes.isEmpty() ? "28" : endRes.get(0);

                confirmableColumn = String.format("""
                            , CASE
                                WHEN apiorder.approval_status = 'Pending' AND
                                    (CONVERT_TZ(NOW(),'+00:00','+03:00') BETWEEN
                                    DATE_FORMAT(CONVERT_TZ(NOW(),'+00:00','+03:00'), '%%Y-%%m-%s') AND
                                    DATE_FORMAT(CONVERT_TZ(NOW(),'+00:00','+03:00'), '%%Y-%%m-%s'))
                                THEN 'CONFIRMABLE'
                                ELSE 'INCONFIRMABLE'
                            END AS `CONFIRMABLE`
                        """, startDay, endDay);
            }
            String baseSelect = "";

            // ✅ SELECT logic depending on budget and batchNumber content


            if (batchNumber != null && batchNumber.contains("TIBU")) {
                baseSelect = "SELECT apiorder.ID AS `primarykey`, apiorder.batchno AS `Batch number`, " +
                        "IFNULL(apiorder.month, apiorder.month_of_claim) AS `Month`, " +
                        "YEAR(apiorder.requesttime) AS `Year`, " +
                        "CONVERT_TZ(apiorder.requesttime,'+00:00','+03:00') AS `Request Time`, " +
                        "apiorder.initiator_username AS `Initiated By`, " +
                        "apiorder.msisdn AS `Phone No.`, " + validationColumns +
                        "treatment_model.title AS `Request Type`, " +
                        "CONCAT(apiorder.firstname, ' ', apiorder.middlename, ' ', apiorder.lastname) AS `Beneficiary`, " +
                        "budget.title AS `Budget Line`, " +
                        "(CASE WHEN (SELECT title FROM province WHERE province.id = apiorder.province) IS NULL " +
                        "THEN apiorder.province ELSE (SELECT title FROM province WHERE province.id = apiorder.province) END) AS `Region`, " +
                        "(CASE WHEN (SELECT title FROM county WHERE county.id = apiorder.county) IS NULL " +
                        "THEN apiorder.district ELSE (SELECT title FROM county WHERE county.id = apiorder.county) END) AS `County`, " +
                        "recipient2credit AS `DOT Amount`, driver_name AS `Driver`, driver_amount AS `Driver Amount`, " +
                        "apiorder.approval_status AS `approval_status`, " +
                        "(credit - driver_amount) AS `Amount` " + confirmableColumn;
            } else if (budget == 1 || "2".equalsIgnoreCase(type)) {
                baseSelect = "SELECT apiorder.ID AS `primarykey`, apiorder.batchno AS `Batch number`, " +
                        "IFNULL(apiorder.month, apiorder.month_of_claim) AS `Month`, " +
                        "IFNULL(apiorder.year, YEAR(apiorder.requesttime)) AS `Year`, " +
                        "CONVERT_TZ(apiorder.requesttime,'+00:00','+03:00') AS `Request Time`, " +
                        "apiorder.initiator_username AS `Initiated By`, " +
                        "CONCAT(apiorder.firstname, ' ', apiorder.middlename, ' ', apiorder.lastname) AS `Beneficiary`, " +
                        "apiorder.msisdn AS `Phone No.`, " + validationColumns +
                        "treatment_model.title AS `Request Type`, budget.title AS `Budget Line`, " +
                        "facility, patient_registration_number AS `Patient Number`, date_treatment_started AS `Treatment Start Date`, " +
                        "CONCAT(recipient2firstname, ' ', recipient2middlename, ' ', recipient2lastname) AS `DOT name`, " +
                        "recipient2msisdn AS `DOT Phone`, recipient2credit AS `DOT Amount`, " +
                        "apiorder.approval_status AS `approval_status`, (credit - driver_amount) AS `Patient Amount`, " +
                        "(recipient2credit + (credit - driver_amount)) AS Total " + confirmableColumn;
            } else if (budget == 3 || budget == 0) {
                baseSelect = "SELECT apiorder.ID AS `primarykey`, apiorder.batchno AS `Batch number`, " +
                        "IFNULL(apiorder.month, apiorder.month_of_claim) AS `Month`, " +
                        "YEAR(apiorder.requesttime) AS `Year`, " +
                        "CONVERT_TZ(apiorder.requesttime,'+00:00','+03:00') AS `Request Time`, " +
                        "apiorder.initiator_username AS `Supervisor Name`, " +
                        "apiorder.msisdn AS `Phone No.`, " + validationColumns +
                        "treatment_model.title AS `Request Type`, budget.title AS `Budget Line`, " +
                        "(CASE WHEN (SELECT title FROM province WHERE province.id = apiorder.province) IS NULL " +
                        "THEN apiorder.province ELSE (SELECT title FROM province WHERE province.id = apiorder.province) END) AS `Region`, " +
                        "(CASE WHEN (SELECT title FROM county WHERE county.id = apiorder.county) IS NULL " +
                        "THEN apiorder.district ELSE (SELECT title FROM county WHERE county.id = apiorder.county) END) AS `County`, " +
                        "recipient2credit AS `DOT Amount`, driver_name AS `Driver`, driver_amount AS `Driver Amount`, " +
                        "apiorder.approval_status AS `approval_status`, (credit - driver_amount) AS `Amount` " + confirmableColumn;
            } else {
                baseSelect = "SELECT apiorder.ID AS `primarykey`, apiorder.batchno AS `Batch number`, " +
                        "IFNULL(apiorder.month, apiorder.month_of_claim) AS `Month`, apiorder.year AS `Year`, " +
                        "CONVERT_TZ(apiorder.requesttime,'+00:00','+03:00') AS `Request Time`, " +
                        "apiorder.initiator_username AS `Initiated By`, " +
                        "CONCAT(apiorder.firstname, ' ', apiorder.middlename, ' ', apiorder.lastname) AS `Beneficiary`, " +
                        "apiorder.msisdn AS `Phone No.`, " + validationColumns +
                        "treatment_model.title AS `Request Type`, budget.title AS `Budget Line`, " +
                        "(CASE WHEN (SELECT title FROM province WHERE province.id = apiorder.province) IS NULL " +
                        "THEN apiorder.province ELSE (SELECT title FROM province WHERE province.id = apiorder.province) END) AS `Region`, " +
                        "(CASE WHEN (SELECT title FROM county WHERE county.id = apiorder.county) IS NULL " +
                        "THEN apiorder.district ELSE (SELECT title FROM county WHERE county.id = apiorder.county) END) AS `County`, " +
                        "recipient2msisdn AS `DOT Phone`, recipient2credit AS `DOT Amount`, " +
                        "apiorder.approval_status AS `approval_status`, (credit - driver_amount) AS `Amount` " + confirmableColumn;
            }


            // ✅ Final query assembly
            query.append(baseSelect);
            query.append("""
                        FROM apiorder
                        LEFT JOIN ordertype ON apiorder.ordertype = ordertype.ID
                        LEFT JOIN budget ON apiorder.budget = budget.ID
                        LEFT JOIN treatment_model ON apiorder.treatment_model = treatment_model.ID
                        WHERE apiorder.approval_status = 'Pending'
                    """);

            query.append(searchFilter).append(typeFilter);
            query.append(" AND apiorder.inTrash = 'No'");
            query.append(" AND apiorder.batchno = :batchno");
            params.put("batchno", batchNumber);
            log.info("sql query", query.toString());
            String rawQuery = query.toString();
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                String placeholder = ":" + entry.getKey();
                String value = entry.getValue() instanceof String ? "'" + entry.getValue() + "'" : entry.getValue().toString();
                rawQuery = rawQuery.replace(placeholder, value);
            }
            log.info("Executable SQL:\n{}", rawQuery);

            // 🔍 Execute final native query using crudService
            List<Map<String, Object>> resultMaps = crudService.fetchWithNativeQueryAsMap(query.toString(), params, 0, pageSize);
            List<BatchDetailDTO> dtos = new ArrayList<>();

            for (Map<String, Object> row : resultMaps) {
                BatchDetailDTO dto = BatchDetailDTO.builder()
                        .id((row.containsKey("primarykey") && row.get("primarykey") != null) ? Long.valueOf(row.get("primarykey").toString()) : null)
                        .batchNo((String) row.getOrDefault("Batch number", null))
                        .month((String) row.getOrDefault("Month", null))
                        .year((Integer) row.getOrDefault("Year", 0))
                        .requestTime((Timestamp) row.getOrDefault("Request Time", null))
                        .initiatedBy((String) row.getOrDefault("Initiated By", row.getOrDefault("Supervisor Name", null)))
                        .phoneNo((String) row.getOrDefault("Phone No.", null))
                        .mpesaConfirmation((String) row.getOrDefault("Mpesa Confirmation", null))
                        .requestType((String) row.getOrDefault("Request Type", null))
                        .beneficiary((String) row.getOrDefault("Beneficiary", null))
                        .budgetLine((String) row.getOrDefault("Budget Line", null))
                        .region((String) row.getOrDefault("Region", null))
                        .county((String) row.getOrDefault("County", null))
                        .dotAmount((row.containsKey("DOT Amount") && row.get("DOT Amount") != null)
                                ? new BigDecimal(row.get("DOT Amount").toString())
                                : BigDecimal.ZERO)
                        .driver((String) row.getOrDefault("Driver", null))
                        .driverAmount((row.containsKey("Driver Amount") && row.get("Driver Amount") != null)
                                ? new BigDecimal(row.get("Driver Amount").toString())
                                : BigDecimal.ZERO)
                        .approvalStatus((String) row.getOrDefault("approval_status", null))
                        .amount((row.containsKey("Amount") && row.get("Amount") != null)
                                ? new BigDecimal(row.get("Amount").toString())
                                : BigDecimal.ZERO)
                        .confirmable((String) row.getOrDefault("CONFIRMABLE", null))
                        .total((BigDecimal) row.getOrDefault("Total", null))
                        .patientAmount((BigDecimal) row.getOrDefault("Patient Amount", null))
                        .dotNurseName((String) row.getOrDefault("DOT Name", null))
                        .facility((String) row.getOrDefault("facility", null))
                        .patientNumber((String) row.getOrDefault("Patient Number", null))
                        .build();

                dtos.add(dto);
            }

            // Then set to response DTO
            BatchDetailsResponseDTO responseDTO = BatchDetailsResponseDTO.builder().build();
            responseDTO.setBatchDetails(dtos);

            boolean permission = false;
            boolean permissionDownloadexcel = false;
            //User user = sharedFunctions.getLoggedInUser();
            Integer userId = 222268;

            Page<ViewPendingRecipients> pendingRecipientsPage = Page.empty();
            if (approvalLevel == 6) {
                Long count = viewUnconfirmedDriversNumbersRepo.countAllByBatchNo(batchNumber);
                if (count > 0) {
                    pendingRecipientsPage = viewPendingRecipientsRepo.findAllByBatchNo(batchNumber, pageable);
                    permissionDownloadexcel = sharedFunctions.hasPermission("approvalsixy", 3, userId);

                }


            }
            switch (approvalLevel) {
                case 1: {
                    permission = sharedFunctions.hasPermission("approvalone", 3, userId);
                    break;
                }
                case 2: {
                    permission = sharedFunctions.hasPermission("approvaltwo", 3, userId);
                    break;
                }
                case 3: {
                    permission = sharedFunctions.hasPermission("approvalthree", 3, userId);
                    break;
                }
                case 4: {
                    permission = sharedFunctions.hasPermission("approvalfour", 3, userId);
                    break;
                }
                case 5: {
                    permission = sharedFunctions.hasPermission("approvalfive", 3, userId);
                    break;
                }
                case 6: {
                    permission = sharedFunctions.hasPermission("approvalsix", 3, userId);
                    break;
                }
                case 7: {
                    permission = sharedFunctions.hasPermission("approvalseven", 3, userId);
                    break;
                }
                case 8: {
                    permission = sharedFunctions.hasPermission("approvaleight", 3, userId);
                    break;
                }
                case 9: {
                    permission = sharedFunctions.hasPermission("approvalnine", 3, userId);
                    break;
                }
                case 10: {
                    permission = sharedFunctions.hasPermission("approvalten", 3, userId);
                    break;
                }
                default: {
                    permission = false;
                    break;
                }

            }
            Page<BatchMetadataView> batchMetadataViews = batchMetadataViewRepository.findAllByBatchNo(batchNumber, pageable);
            if (batchMetadataViews.isEmpty()) {
                log.info("no data found for beneficiaries");
            } else {
                responseDTO.setBatchMetadataViews(batchMetadataViews);
            }
            List<BatchDetailsResponseDTO> list = Collections.singletonList(responseDTO);
            Page<BatchDetailsResponseDTO> page1 = new PageImpl<>(list, PageRequest.of(0, 1), 1);
            if (page1.isEmpty()) {

            } else {
                Map<String, Object> pageMeta = new HashMap<>();
                pageMeta.put("size", page1.getSize());
                pageMeta.put("number", page1.getNumber());
                pageMeta.put("totalElements", page1.getTotalElements());
                pageMeta.put("totalPages", page1.getTotalPages());


                BatchDetailsWrapper wrapper = BatchDetailsWrapper.builder()
                        .content(page1.getContent())
                        .page(pageMeta)
                        .build();
                batchDetailsResponse.setResponseMessage("Success");
                batchDetailsResponse.setResponseCode("00");
                batchDetailsResponse.setData(wrapper);
                batchDetailsResponse.setPermission(permission);
                batchDetailsResponse.setPermissionDownloadExcel(permissionDownloadexcel);
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }

        return batchDetailsResponse;
    }

    public BatchResponse getBatchApprovalPerLevelAndType(CountyBatchRequest req, HttpServletResponse response, HttpServletRequest request1) {
        String sqlStart = "SELECT VALUE FROM PARAM WHERE PARAMETER = 'Program Approval Start Day'";
        String sqlEnd = "SELECT VALUE FROM PARAM WHERE PARAMETER = 'Program Approval End Day'";

        String programStart = crudService.fetchWithNativeQuery(sqlStart, new HashMap<>(), 0, 1).get(0) + " 00:00";
        String programEnd = crudService.fetchWithNativeQuery(sqlEnd, new HashMap<>(), 0, 1).get(0) + " 23:59";


//        String programStart = (String) startResult.get(0).get("VALUE") + " 00:00";
//        String programEnd = (String) endResult.get(0).get("VALUE") + " 23:59";

        StringBuilder sql = new StringBuilder(
                "SELECT ao.batchno AS batchNo, " +
                        "SUM(CASE WHEN ao.approval_status='Rejected' THEN 1 ELSE 0 END) AS rejected, " +
                        "SUM(CASE WHEN ao.approval_level < :approval AND ao.approval_status IN ('Pending','Processing') THEN 1 ELSE 0 END) AS precedingApproval, " +
                        "SUM(CASE WHEN ao.approval_level = :approval AND ao.approval_status = 'Pending' THEN 1 ELSE 0 END) AS pendingMyApproval, " +
                        "SUM(CASE WHEN ao.approval_level > :approval AND ao.approval_status <> 'Rejected' THEN 1 ELSE 0 END) AS myApproved, " +
                        "COUNT(*) AS total"
        );

        if (req.getType() != null && req.getType() != 3 && Set.of(2, 3, 4).contains(req.getApprovalLevel())) {
            sql.append(", CASE WHEN CONVERT_TZ(NOW(),'+00:00','+03:00') BETWEEN '")
                    .append(programStart).append("' AND '").append(programEnd).append("' ")
                    .append("THEN 'CONFIRMABLE' ELSE 'INCONFIRMABLE' END AS confirmable");
        } else {
            sql.append(", '' AS confirmable");
        }

        sql.append(" FROM apiorder ao WHERE ao.batchno IS NOT NULL AND ao.inTrash='No'");

        Map<String, Object> params = new HashMap<>();
        params.put("approval", req.getApprovalLevel());
        sql.append(" AND ao.approval_level = :approval");

        if (req.getType() != null) {
            switch (req.getType()) {
                case 1 -> sql.append(" AND (ao.batchno LIKE '%SUP%' OR (ao.ordertype<>1 AND ao.batchno='0'))");
                case 2 -> sql.append(" AND (ao.batchno LIKE '%MDR%' OR (ao.ordertype=1 AND ao.batchno='0'))");
                case 3 -> sql.append(" AND ao.treatment_model=3");
            }
        }

        if (StringUtils.hasText(req.getKeywords())) {
            sql.append(" AND (ao.requestTime LIKE :kw OR ao.batchno LIKE :kw OR ao.dot_nurse_phoneno LIKE :kw")
                    .append(" OR ao.recipient2credit LIKE :kw OR ao.dot_nurse_name LIKE :kw")
                    .append(" OR ao.county LIKE :kw OR ao.credit LIKE :kw")
                    .append(" OR ao.month LIKE :kw OR ao.year LIKE :kw")
                    .append(" OR ao.firstname LIKE :kw OR ao.middlename LIKE :kw")
                    .append(" OR ao.lastname LIKE :kw OR ao.msisdn LIKE :kw)");
            params.put("kw", "%" + req.getKeywords() + "%");
        }

        if (req.getProvinceIds() != null && !req.getProvinceIds().isEmpty()) {
            sql.append(" AND ao.province IN (:province)");
            params.put("province", req.getProvinceIds());
        }
        if (req.getBudgetIds() != null && !req.getBudgetIds().isEmpty()) {
            sql.append(" AND ao.budget IN (:budgetIds)");
            params.put("budgetIds", req.getBudgetIds());
        }
        if (req.getCountyIds() != null && !req.getCountyIds().isEmpty()) {
            sql.append(" AND ao.county IN (:countyIds)");
            params.put("countyIds", req.getCountyIds());
        }
        if (StringUtils.hasText(req.getMonth())) {
            sql.append(" AND ao.month = :month");
            params.put("month", req.getMonth());
        }
        if (StringUtils.hasText(req.getYear())) {
            sql.append(" AND ao.year = :year");
            params.put("year", req.getYear());
        }
        if (req.getDateFrom() != null) {
            sql.append(" AND ao.requestTime >= :dateFrom");
            params.put("dateFrom", req.getDateFrom());
        }
        if (req.getDateTo() != null) {
            sql.append(" AND ao.requestTime <= :dateTo");
            params.put("dateTo", req.getDateTo());
        }
        if (req.getOrgId() != null) {
            sql.append(" AND ao.org_id = :orgId");
            params.put("orgId", req.getOrgId());
        }

        sql.append(" GROUP BY ao.batchno HAVING pendingMyApproval > 0 ORDER BY MAX(ao.id) DESC");

        log.info("sql ",  sql.toString());
        log.info("sql query", sql.toString());
        String rawQuery = sql.toString();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String placeholder = ":" + entry.getKey();
            String value = entry.getValue() instanceof String ? "'" + entry.getValue() + "'" : entry.getValue().toString();
            rawQuery = rawQuery.replace(placeholder, value);
        }
        log.info("Executable SQL:\n{}", rawQuery);
        List<Object[]> rows = crudService.fetchWithNativeQuery(sql.toString(), params, 0, 100);
        int page = req.getPage() != null ? req.getPage() : 0;
        int size = req.getPageSize() != null ? req.getPageSize() : 10;


        List<BatchApprovalSummaryDTO> batchList = rows.stream().map(row -> BatchApprovalSummaryDTO.builder()
                .batchNo((String) row[0])
                .rejected(getLong(row[1]))
                .precedingApproval(getLong(row[2]))
                .pendingMyApproval(getLong(row[3]))
                .myApproved(getLong(row[4]))
                .total(getLong(row[5]))
                .confirmable((String) row[6])
                .build()
        ).toList();

        int start = Math.min(page * size, batchList.size());
        int end = Math.min(start + size, batchList.size());
        List<BatchApprovalSummaryDTO> pagedResults = batchList.subList(start, end);
        Page<BatchApprovalSummaryDTO> resultPage = new PageImpl<>(
                pagedResults,
                PageRequest.of(page, size),
                batchList.size()
        );


        // PERMISSION CHECK
        boolean permission = false;
        //User user = sharedFunctions.getLoggedInUser();
        Integer userId = 0;
        userId = 222268;
        Integer approval = req.getApprovalLevel() != null ? req.getApprovalLevel() : 0;
        switch (approval) {
            case 1 -> permission = sharedFunctions.hasPermission("approvalone", 3, userId);
            case 2 -> permission = sharedFunctions.hasPermission("approvaltwo", 3, userId);
            case 3 -> permission = sharedFunctions.hasPermission("approvalthree", 3, userId);
            case 4 -> permission = sharedFunctions.hasPermission("approvalfour", 3, userId);
            case 5 -> permission = sharedFunctions.hasPermission("approvalfive", 3, userId);
            case 6 -> permission = sharedFunctions.hasPermission("approvalsix", 3, userId);
            case 7 -> permission = sharedFunctions.hasPermission("approvalseven", 3, userId);
            case 8 -> permission = sharedFunctions.hasPermission("approvaleight", 3, userId);
            case 9 -> permission = sharedFunctions.hasPermission("approvalnine", 3, userId);
            case 10 -> permission = sharedFunctions.hasPermission("approvalten", 3, userId);
        }

        return BatchResponse.builder()
                .responseCode("00")
                .responseMessage("Batch report fetched succesfully.")
                .data(resultPage)
//                .permission(permission)
                .build();
    }

    // Helper

    public LineItemsResponse getLineItems(ReportRequest reportRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        LineItemsResponse response = LineItemsResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL.getCode())
                .build();

        try {
            int page = reportRequest.getPage();
            int pageSize = reportRequest.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);

            // Normalize filters: empty string → null
            String keyword = hasText(reportRequest.getKeyword()) ? reportRequest.getKeyword().trim() : null;
            String province = hasText(reportRequest.getProvince()) ? reportRequest.getProvince().trim() : null;
            String budget = hasText(reportRequest.getBudgetLine()) ? reportRequest.getBudgetLine().trim() : null;
            String paymentType = hasText(reportRequest.getPaymentType()) ? reportRequest.getPaymentType().trim() : null;
            List<String> rawCounties = reportRequest.getCounties();
            String county1 = null;
            List<String> rawCounties1 = reportRequest.getCounties();

            List<String> county = (rawCounties1 != null)
                    ? rawCounties1.stream().filter(c -> c != null && !c.trim().isEmpty()).toList()
                    : null;

            if (county != null && !county.isEmpty()) {
                county1 = county.get(0);
            } else {
                county = null;
                county1 = null;
            }
            Long orgId = null;
            if (reportRequest.getOrgId() != null) {
                orgId = Long.valueOf(reportRequest.getOrgId());
            }
            province = (province != null && province.trim().isEmpty()) ? null : province;
            paymentType = (paymentType != null && paymentType.trim().isEmpty()) ? null : paymentType;
            keyword = (keyword != null && keyword.trim().isEmpty()) ? null : keyword;

            log.info("Filter Parameters => county: {}, keyword: {}",
                    county,  keyword);
            Long budget1 = null;
            if (budget != null) {
                budget1 = Long.valueOf(budget);

            }
            String month = reportRequest.getMonth();
            String year = reportRequest.getYear();
            String status = reportRequest.getStatus();
            String apiOrderId = reportRequest.getId();;
            String currentLevel = reportRequest.getCurrentLevel();

            if (reportRequest.getId() != null && !reportRequest.getId().isEmpty()) {
                ApiOrderFullDetailsResponseDTO responseDTO = ApiOrderFullDetailsResponseDTO.builder()
                        .build();
                Map<String, Object> params = new HashMap<>();

                // STEP 1: Get ids_string list from approval_processing
                String retrySql = "SELECT ids_string FROM approval_processing WHERE retry_count >= 3 AND status = 1";
                List<String> idStringRows = crudService.fetchWithNativeQuery(retrySql, params, 0, 0);

                Set<String> idsSet = new HashSet<>();
                for (String row : idStringRows) {
                    if (row != null && !row.isEmpty()) {
                        String[] ids = row.split(",");
                        idsSet.addAll(Arrays.asList(ids));
                    }
                }

                boolean stageApproval = idsSet.contains(apiOrderId);

                // STEP 2: Determine confirmable column logic based on currentLevel
                String confirmableQueryPart = "";
                if ("1".equals(currentLevel)) {
                    String ctlcDeadlineSql = "SELECT VALUE FROM PARAM WHERE PARAMETER = 'CTLC Approval Deadline'";
                    List<String> deadlineResult = crudService.fetchWithNativeQuery(ctlcDeadlineSql, params, 0, 1);
                    String deadline = deadlineResult.isEmpty() ? "01" : deadlineResult.get(0);
                    String confirmableCase = "CASE WHEN ( CONVERT_TZ(NOW(),'+00:00','+03:00') " +
                            "BETWEEN DATE_FORMAT(CONVERT_TZ(NOW(),'+00:00','+03:00'),'%Y-%m-01 00:00') AND " +
                            "DATE_FORMAT(CONVERT_TZ(NOW(),'+00:00','+03:00'),'%Y-%m-" + deadline + " 23:59') " +
                            ") THEN 'CONFIRMABLE' ELSE 'INCONFIRMABLE' END AS CONFIRMABLE";

                    confirmableQueryPart = ", " + confirmableCase;


                } else if (Arrays.asList("2", "3", "4").contains(currentLevel)) {
                    String startDaySql = "SELECT VALUE FROM PARAM WHERE PARAMETER = 'Program Approval Start Day'";
                    String endDaySql = "SELECT VALUE FROM PARAM WHERE PARAMETER = 'Program Approval End Day'";

                    List<String> startRes = crudService.fetchWithNativeQuery(startDaySql, params, 0, 1);
                    List<String> endRes = crudService.fetchWithNativeQuery(endDaySql, params, 0, 1);

                    String startDay = startRes.isEmpty() ? "01" : startRes.get(0);
                    String endDay = endRes.isEmpty() ? "28" : endRes.get(0);

                    String confirmableCase = "CASE WHEN ( CONVERT_TZ(NOW(),'+00:00','+03:00') " +
                            "BETWEEN DATE_FORMAT(CONVERT_TZ(NOW(),'+00:00','+03:00'),'%Y-%m-" + startDay + " 00:00') AND " +
                            "DATE_FORMAT(CONVERT_TZ(NOW(),'+00:00','+03:00'),'%Y-%m-" + endDay + " 23:59') " +
                            ") THEN 'CONFIRMABLE' ELSE 'INCONFIRMABLE' END AS CONFIRMABLE";

                    confirmableQueryPart = ", (" + confirmableCase + ")";
                }
                log.info("confirm query part {}",  confirmableQueryPart);

                // Step 3: Execute query with fetchWithNativeQuery
                String sql = "SELECT recipient, credit - (SELECT driver_amount FROM apiorder WHERE id = :key) AS balance, " +
                        "msisdn, onbehalfrecipient, recipient2credit, recipient2msisdn, recipient2, notes, ordertype, treatment_model, " +
                        "(SELECT beneficiaryType FROM apiorder WHERE id = :key), " +
                        "(SELECT beneficiary2Type FROM apiorder WHERE id = :key)" + confirmableQueryPart + " " +
                        "FROM paymentrequestdetails WHERE id = :key";

                params.put("key", apiOrderId);
                List<Object[]> result = crudService.fetchWithNativeQuery(sql, params, 0, 1);
                String orderType = "";
                String msisdn = "";
                String patientRegNo = "";

                if (!result.isEmpty()) {
                    Object[] row = result.get(0);  // row is an Object[]

                    orderType = row[8] != null ? row[8].toString().trim() : "";
                    msisdn = row[2] != null ? row[2].toString().replace("+", "").trim() : "";
                    String confirmableStatus = row.length > 12 && row[12] != null ? row[12].toString().trim() : "INCONFIRMABLE";
                    String beneficiarytye1 = row[10] != null ? row[10].toString().trim() : "";
                    String beneficiarytye2 = row[11] != null ? row[11].toString().trim() : "";
                     patientRegNo = row.length > 12 && row[12] != null ? row[12].toString().trim() : "";
                    String treatmentModel = row[9] != null ? row[9].toString().trim() : "";
                    String patientname = row[3] != null ? row[3].toString().trim() : "";
                    String recipient1Title = row[0] != null ? row[0].toString().trim() : "";
                    String recipientName = row[0] != null ? row[0].toString().trim() : "";
                    String AmountToSentToRecipient1 = row[1] != null ? row[1].toString().trim() : "";
                    String recipientPhoneNumber = row[2] != null ? row[2].toString().trim() : "";
                    String recipient2Title = row[6] != null ? row[6].toString().trim() : "";
                    String AmountToSentToRecipient2 = row[4] != null ? row[4].toString().trim() : "";
                    String secondRecipientPhoneNumber = row[5] != null ? row[5].toString().trim() : "";
                    String description = row[7] != null ? row[7].toString().trim() : "";
                    String paymentType1 = row[8] != null ? row[8].toString().trim() : "";

                    String beneficiaryTitle = "Patient support".equalsIgnoreCase(orderType) ? "Patient Name" : "Beneficiary Name:";
                    String nomineeTitle = "Patient support".equalsIgnoreCase(orderType) ? "Nominee Name" : "Onbehalf Recipient Name:";

                    ApiOrderApprovalTwoDTO dto = ApiOrderApprovalTwoDTO.builder()
                            .stageApproval(stageApproval)
                            .orderType(orderType)
                            .beneficiaryTitle(beneficiaryTitle)
                            .nomineeTitle(nomineeTitle)
                            .confirmableStatus(confirmableStatus)
                            .msisdn(msisdn)
                            .beneficiarytye1(beneficiarytye1)
                            .beneficiarytye2(beneficiarytye2)
                            .patientRegNo(patientRegNo)
                            .treatmentModel(treatmentModel)
                            .recipient2Title(recipient2Title)
                            .amountToSentToRecipient1(AmountToSentToRecipient1)
                            .recipient1Title(recipient1Title)
                            .description(description)
                            .paymentType(paymentType1)
                            .secondRecipientPhoneNumber(secondRecipientPhoneNumber)
                            .amountToSentToRecipient2(AmountToSentToRecipient2)
                            .recipientPhoneNumber(recipientPhoneNumber)
                            .patientName(patientname)
                            .recipientName(recipientName)
                            .onBehalfRecipient(patientname)
                            .build();
                    responseDTO.setOrderApproval(dto);

                }
                String sqlOrderType = "SELECT ot.title, ao.attached_to, ao.driver_name, ao.driver_phone, ao.driver_amount, ao.id " +
                        "FROM ordertype ot " +
                        "LEFT JOIN apiorder ao ON ao.ordertype = ot.id " +
                        "WHERE ao.id = :key";

                Map<String, Object> paramsSupervisor = new HashMap<>();
                paramsSupervisor.put("key", apiOrderId);

                List<Object[]> resList = crudService.fetchWithNativeQuery(sqlOrderType, paramsSupervisor, 0, 1);

                if (!resList.isEmpty()) {
                    Object[] res = resList.get(0);
                    String title = res[0] != null ? res[0].toString().toUpperCase() : "";

                    boolean isSupervision = List.of("SUPERVISION", "MDR OTHER PAYMENTS", "PATIENT SUPPORT").contains(title);
                    if (isSupervision) {
                        String query = "select id,title,credit,url,local_url,expense_id from supervision_details where apiorder_id=:id";
                        Map<String, Object> parms = new HashMap<>();
                        parms.put("id", apiOrderId);
                        List<Object[]> detailsList = crudService.fetchWithNativeQuery(query, parms, 0, 1);
                        if (!detailsList.isEmpty()) {
                            Object[] result1 = detailsList.get(0);

                            String supervisionId = result1[0] != null ? result1[0].toString().trim() : null;
                            String supervisionTitle = result1[1] != null ? result1[1].toString().trim() : null;
                            List<Integer> expenseIds = new ArrayList<>();
                            List<Integer> expenseIds1 = new ArrayList<>();


                            List<SupervisionItemDTO> items = detailsList.stream().map(row ->
                                    SupervisionItemDTO.builder()
                                            .id(result1[0] != null ? Long.parseLong(result1[0].toString()) : null)
                                            .title(result1[1] != null ? result1[1].toString() : "")
                                            .credit(result1[2] != null ? Double.parseDouble(result1[2].toString()) : 0)
                                            .url(result1[3] != null ? result1[3].toString() : "")
                                            .localUrl(result1[4] != null ? result1[4].toString() : "")
                                            .expenseId(result1[5] != null ? Long.parseLong(result1[5].toString()) : null)
                                            .build()
                            ).collect(Collectors.toList());

                            responseDTO.setSupervisionItems(items);
                            String sqlComments = "SELECT spa.notes, spa.status, al.approval_level_name " +
                                    "FROM supervision_details_approval spa " +
                                    "INNER JOIN approval_levels al ON spa.approval_level = al.approval_level " +
                                    "WHERE spa.supdetails_id = :supdetailsId " +
                                    "ORDER BY spa.approval_time ASC";

                            Map<String, Object> commentParams = Map.of("supdetailsId", supervisionId);  // Loop each ID
                            List<Object[]> commentsList = crudService.fetchWithNativeQuery(sqlComments, commentParams, 0, 10);
                            List<ApprovalCommentDTO> comments = commentsList.stream().map(row ->
                                    ApprovalCommentDTO.builder()
                                            .notes(row[0] != null ? row[0].toString() : "")
                                            .approvalStatus(row[1] != null ? row[1].toString() : "")
                                            .approvalLevelName(row[2] != null ? row[2].toString() : "")
                                            .build()
                            ).collect(Collectors.toList());
                            responseDTO.setApprovalComments(comments);

                            if (res[1] != null) {
                                // Safely parse expenseIds from result1[5]
                                if (result1[5] instanceof String) {
                                    String[] ids = result1[5].toString().split(",");
                                    for (String id : ids) {
                                        try {
                                            expenseIds.add(Integer.parseInt(id.trim()));
                                            expenseIds1= expenseIds;
                                        } catch (NumberFormatException ignored) {
                                        }
                                    }
                                } else if (result1[5] instanceof Collection) {
                                    expenseIds.addAll((Collection<Integer>) result1[5]);
                                }

                                // Only continue if there are valid expense IDs
                                if (!expenseIds.isEmpty()) {

                                    String relatedQuery = "SELECT sd.credit, sd.url, sd.approval_status, sd.title, sd.apiorder_id, " +
                                            "FROM supervision_details sd " +
                                            "WHERE sd.apiorder_id IN (:expenseIds) AND sd.title = :title";

                                    Map<String, Object> itemParams = new HashMap<>();
                                    itemParams.put("expenseIds", expenseIds1);
                                    itemParams.put("title", supervisionTitle);

                                    List<Object[]> relatedList = crudService.fetchWithNativeQuery(relatedQuery, itemParams, 0, 100);
                                    String fetchRequestTimeSql = "SELECT CONVERT_TZ(requesttime, '+00:00', '+03:00') AS requesttime " +
                                            "FROM apiorder WHERE id = :orderId";

                                    Map<String, Object> timeParams = new HashMap<>();
                                    timeParams.put("orderId", apiOrderId); // replace with the dynamic id

                                    List<Object> timeResult = crudService.fetchWithNativeQuery(fetchRequestTimeSql, timeParams, 0, 1);

                                    String requestTime = !timeResult.isEmpty() && timeResult.get(0) != null
                                            ? timeResult.get(0).toString()
                                            : "";


                                    List<RelatedSupervisionItemDTO> relatedItems = relatedList.stream().map(row -> {
                                        Double amount = "Expense Sheet".equalsIgnoreCase(supervisionTitle) ? null : (row[0] != null ? Double.parseDouble(row[0].toString()) : null);
                                        String approvalStatus = "Expense Sheet".equalsIgnoreCase(supervisionTitle) ? "N/A" : (row[2] != null ? row[2].toString() : "N/A");

                                        return RelatedSupervisionItemDTO.builder()
                                                .requestTime(requestTime)
                                                .amount(amount)
                                                .approvalStatus(approvalStatus)
                                                .receiptUrl(row[1] != null ? row[1].toString() : "")
                                                .build();
                                    }).collect(Collectors.toList());
                                    responseDTO.setRelatedSupervisionItemDTOS(relatedItems);

                                    // ✅ Now you can return this list as part of your API response
                                }


                            }


                            // Proceed with next queries
                        }

                        SupervisionDetailsDTO dto = SupervisionDetailsDTO.builder()
                                .title(res[0] != null ? res[0].toString() : "")
                                .attachedTo(res[1] != null ? res[1].toString() : "")
                                .driverName(res[2] != null ? res[2].toString() : "")
                                .driverPhone(res[3] != null ? res[3].toString() : "")
                                .driverAmount(res[4] != null ? Double.parseDouble(res[4].toString()) : 0)
                                .apiOrderId(res[5] != null ? Long.parseLong(res[5].toString()) : null)
                                .build();
                        responseDTO.setSupervisionDetailsDTOS(dto);
                    }
                }
                if (orderType.equalsIgnoreCase("Patient support")){


                    String sql2 = "SELECT payment.trx_id, payment.trx_date, payment.recipient_name " +
                            "FROM payment " +
                            "LEFT JOIN apiorder ON apiorder.ID = payment.order_id " +
                            "WHERE apiorder.patient_registration_number IS NOT NULL " +
                            "AND apiorder.patient_registration_number != '' " +
                            "AND apiorder.patient_registration_number = :patientRegNo " +
                            "AND payment.trx_status = 'Completed' " +
                            "AND apiorder.inTrash = 'No' " +
                            "ORDER BY apiorder.ID DESC";

                    Map<String, Object> params2 = new HashMap<>();
                    params2.put("patientRegNo", patientRegNo);

                    List<Object[]> result2 = crudService.fetchWithNativeQuery(sql2, params2, 0, 100); // adjust limit as needed

                    List<PatientPaymentRecordDTO> records = new ArrayList<>();
                    for (Object[] row : result2) {
                        PatientPaymentRecordDTO dto = PatientPaymentRecordDTO.builder()
                                .transactionId(row[0] != null ? row[0].toString() : "")
                                .transactionDate(row[1] != null ? row[1].toString() : "")
                                .recipientName(row[2] != null ? row[2].toString() : "")
                                .msisdn(msisdn) // already defined elsewhere
                                .build();
                        records.add(dto);

                    }
                    responseDTO.setPatientPaymentRecordDTOS(records);

                    // Now you can return or use `records` as needed

                }
                String sql4 = "SELECT approval_level_name, approval_status, approved_amount, approval_time, " +
                        "approval_notes, CONCAT(firstname, ' ', lastname) AS approver, phone_number " +
                        "FROM payment_approval " +
                        "LEFT JOIN user ON user.id = approver_id " +
                        "WHERE apiorder_id = :apiOrderId " +
                        "ORDER BY payment_approval.ID ASC";

                Map<String, Object> params4 = new HashMap<>();
                params4.put("apiOrderId", apiOrderId); // assume apiOrderId is already defined

                List<Object[]> result4 = crudService.fetchWithNativeQuery(sql4, params4, 0, 100); // or as needed

                List<PaymentApprovalDTO> approvals = new ArrayList<>();
                for (Object[] row : result4) {
                    PaymentApprovalDTO dto = PaymentApprovalDTO.builder()
                            .approvalLevel(row[0] != null ? row[0].toString() : "")
                            .status(row[1] != null ? row[1].toString() : "")
                            .approvedAmount(row[2] != null ? row[2].toString() : "")
                            .approvalTime(row[3] != null ? row[3].toString() : "")
                            .notes(row[4] != null ? row[4].toString() : "")
                            .approver(row[5] != null ? row[5].toString() : "")
                            .phoneNumber(row[6] != null ? row[6].toString() : "")
                            .build();
                    approvals.add(dto);
                }
                responseDTO.setPaymentApprovalDTOS(approvals);

                // return or process `approvals` list as needed
                if (currentLevel != null && currentLevel.equalsIgnoreCase("1")) {
                    String sql5 = "SELECT id, name, phone_number, amount FROM drivers_details WHERE apiorder_id = :orderId";

                    Map<String, Object> params5 = new HashMap<>();
                    params5.put("orderId", apiOrderId); // assume `apiOrderId` is passed or resolved before this

                    List<Object[]> results = crudService.fetchWithNativeQuery(sql5, params5, 0, 100);

                    List<DriverDetailsDTO> driverDetails = new ArrayList<>();
                    for (Object[] row : results) {
                        DriverDetailsDTO dto = DriverDetailsDTO.builder()
                                .id(row[0] != null ? Integer.parseInt(row[0].toString()) : null)
                                .name(row[1] != null ? row[1].toString() : "")
                                .phoneNumber(row[2] != null ? row[2].toString() : "")
                                .amount(row[3] != null ? row[3].toString() : "")
                                .build();
                        driverDetails.add(dto);
                    }

                    // return or process `driverDetails` list as needed
                    responseDTO.setDriverDetailsDTOS(driverDetails);
                }
                if (orderType.equalsIgnoreCase("Patient support") && reportRequest.getApproval() > 1
                        || reportRequest.getId().equalsIgnoreCase("55")) {

                    String sql6 = "SELECT apiorder.batchno, approval_level_name, facility, " +
                            "treatment_model.title AS RequestType, " +
                            "CONVERT_TZ(apiorder.requesttime,'+00:00','+03:00'), " +
                            "credit, approval_status, apiorder.notes " +
                            "FROM apiorder " +
                            "LEFT JOIN ordertype ON apiorder.ordertype = ordertype.ID " +
                            "LEFT JOIN budget ON apiorder.budget = budget.ID " +
                            "LEFT JOIN treatment_model ON apiorder.treatment_model = treatment_model.ID " +
                            "LEFT JOIN approval_levels ON apiorder.approval_level = approval_levels.approval_level " +
                            "WHERE msisdn LIKE :msisdn AND apiorder.inTrash = 'No' " +
                            "ORDER BY apiorder.ID DESC";

                    Map<String, Object> params6= new HashMap<>();
                    params6.put("msisdn", "%" + msisdn + "%");

                    List<Map<String, Object>> results = crudService.fetchWithNativeQuery(sql6, params6, 0, 1);

                    List<PatientPaymentRecordHistoryDTO> records = results.stream()
                            .map(row -> PatientPaymentRecordHistoryDTO.builder()
                                    .batchNo((String) row.get("batchno"))
                                    .approvalLevel((String) row.get("approval_level_name"))
                                    .facility((String) row.get("facility"))
                                    .requestType((String) row.get("RequestType"))
                                    .requestTime(String.valueOf(row.get("CONVERT_TZ(apiorder.requesttime,'+00:00','+03:00')")))
                                    .amount(row.get("credit") != null ? row.get("credit").toString() : null)
                                    .approvalStatus((String) row.get("approval_status"))
                                    .notes((String) row.get("notes"))
                                    .build())
                            .toList();
                    responseDTO.setPatientPaymentRecordHistoryDTOS(records);

                    // Now 'records' contains all the data you need
                }


                // Return as Page
                List<ApiOrderFullDetailsResponseDTO> list = Collections.singletonList(responseDTO);
                Page<ApiOrderFullDetailsResponseDTO> page1 = new PageImpl<>(list, PageRequest.of(0, 1), 1);
                response.setApiOrderFullDetailsResponseDTOS(page1);
                response.setResponseMessage("success");
            }else {
                Page<PendingLevel2OrderView> pendingLevel2OrderViews = pendingLevel2OrderViewRepository.searchByKeywordAndCounty(
                        keyword, county1, pageable);
                if (pendingLevel2OrderViews.isEmpty()) {
                    response.setResponseMessage("No records found for orgId: " + orgId);
                    response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                } else {
                    response.setResponseMessage("Success");
                    response.setData(pendingLevel2OrderViews);
                    response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                }
            }

        } catch (Exception e) {
            log.error("An error occured");
            response.setResponseMessage("failed");
            response.setResponseCode(ApiResponseCode.FAIL.getCode());
            e.printStackTrace();


        }
        return response;
    }

    public FundAllocationResponse fundAllocationResponse(FundAllocationRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        FundAllocationResponse fundAllocationResponse = FundAllocationResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL.getCode())
                .build();
        try{
            int page = request.getPage();
            int pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);
            String keyword = request.getKeyword();
            String id = request.getId();
            if(id != null){
                Optional<FundAllocationView> fundAllocationView = fundAllocationViewRepository.findById(Long.parseLong(id));
                if(fundAllocationView.isPresent()){
                    fundAllocationResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                    fundAllocationResponse.setResponseMessage("Success");
                    fundAllocationResponse.setFundAllocationView(fundAllocationView);

                }else {
                    fundAllocationResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                    fundAllocationResponse.setResponseMessage("No funds allocation found by that id" + id);
                }
            }else {

                Page<FundAllocationView> fundAllocationViews = fundAllocationViewRepository.searchByKeyword(keyword, pageable);
                if (fundAllocationViews.isEmpty()) {
                    fundAllocationResponse.setResponseMessage("No records found for " + keyword);
                    fundAllocationResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());

                } else {
                    fundAllocationResponse.setResponseMessage("Success");
                    fundAllocationResponse.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                    fundAllocationResponse.setData(fundAllocationViews);
                }
            }

        }catch (Exception e){
            fundAllocationResponse.setResponseMessage("Failed");
            fundAllocationResponse.setResponseCode(ApiResponseCode.FAIL.getCode());
            log.error("An error occured");
            e.printStackTrace();
        }
        return fundAllocationResponse;
    }
    public FinanceApprovalResponse getWebInitiatePendingPayments(PendingWebInitiatedPaymentsFilterDTO request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        FinanceApprovalResponse response = FinanceApprovalResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL.getCode())
                .build();
        try {
            String keyword = request.getKeywords();
            String province = request.getProvince();
            String budget = request.getBudget();
            Long userId = request.getUserId();
            Long orgId = request.getOrgId();
            List<String> county = request.getCounty();
            String dateFrom = request.getDateFrom();
            String dateTo = request.getDateTo();
            Integer page = request.getPage();
            Integer pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);
            LocalDateTime from = null;
            LocalDateTime to = null;

            if (dateFrom != null) {
                 from = LocalDate.parse(dateFrom)
                        .atStartOfDay(); // e.g. 2023-11-22T00:00
            }

            if (dateTo != null) {
                 to = LocalDate.parse(dateTo)
                        .plusDays(1)
                        .atStartOfDay(); // e.g. 2025-06-29T00:00 (exclusive)
            }
            String batchApprove = request.getBatchApprove();
            if (batchApprove != null && batchApprove.equalsIgnoreCase("yes")) {

                Page<WebInitiatedBatchSummaryPending> webInitiatedBatchSummaryPendings = webInitiatedBatchSummaryPendingRepository.findByFilters(keyword, from,to,pageable);
                if (webInitiatedBatchSummaryPendings.isEmpty()) {
                    response.setResponseMessage("No records found for " + keyword);
                    response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                }else {
                    Map<String, Object> pageMeta = new HashMap<>();
                    pageMeta.put("size", webInitiatedBatchSummaryPendings.getSize());
                    pageMeta.put("number", webInitiatedBatchSummaryPendings.getNumber());
                    pageMeta.put("totalElements", webInitiatedBatchSummaryPendings.getTotalElements());
                    pageMeta.put("totalPages", webInitiatedBatchSummaryPendings.getTotalPages());



                    FinanceApprovalPageWrapper wrapper = FinanceApprovalPageWrapper.builder()
                            .content(webInitiatedBatchSummaryPendings.getContent())
                            .page(pageMeta)
                            .build();
                    response.setResponseMessage("Success");
                    response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                    response.setData(wrapper);
                }
            }else {
                Page<PendingWebInitiatedPayments> pendingWebInitiatedPayments = pendingWebInitiatedPaymentsRepo.searchWithFilters(userId, orgId, keyword, province, budget, county, from, to, pageable);
                if (pendingWebInitiatedPayments.isEmpty()) {
                    response.setResponseMessage("No records found for " + orgId);
                    response.setResponseCode(ApiResponseCode.SUCCESS.getCode());

                } else {

                    Map<String, Object> pageMeta = new HashMap<>();
                    pageMeta.put("size", pendingWebInitiatedPayments.getSize());
                    pageMeta.put("number", pendingWebInitiatedPayments.getNumber());
                    pageMeta.put("totalElements", pendingWebInitiatedPayments.getTotalElements());
                    pageMeta.put("totalPages", pendingWebInitiatedPayments.getTotalPages());



                    FinanceApprovalPageWrapper wrapper = FinanceApprovalPageWrapper.builder()
                            .content(pendingWebInitiatedPayments.getContent())
                            .page(pageMeta)
                            .build();
                    response.setResponseMessage("Success");
                    response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                    response.setData(wrapper);
                }

            }
        }catch (Exception e){
            log.error("An error occured with request {}", e.getMessage());
            response.setResponseMessage("Failed");
            response.setResponseCode(ApiResponseCode.FAIL.getCode());
            e.printStackTrace();
        }
        return response;

    }
    public PaymentTimelineResponse getPaymentTimelines(PaymentTimelineRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        PaymentTimelineResponse response = PaymentTimelineResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponseCode.FAIL.getCode())
                .build();
        try {
            String keyword = request.getKeyword();
            String id = request.getApiOrderId();
            if (id != null) {
                Pageable pageable = PageRequest.of(0, 1000);
                Page<PaymentTimelineView> paymentTimelineView = paymentTimelineRepository.findAllByApiorderId(Integer.valueOf(id), pageable);
                if (!paymentTimelineView.isEmpty()) {
                    response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                    response.setResponseMessage("Success");
                    response.setData(paymentTimelineView);
                }else {
                    response.setResponseCode(ApiResponseCode.SUCCESS.getCode());
                    response.setResponseMessage("No payment timeline found by that id" + id);
                }
            }

        }catch (Exception e){
            log.error("An error occured with request {}", e.getMessage());
            response.setResponseMessage("Failed");
            response.setResponseCode(ApiResponseCode.FAIL.getCode());
            e.printStackTrace();
        }
        return response;

    }




    public LocalDateTime convertMillisStringToLocalDateTime(String millisStr) {
        long millis = Long.parseLong(millisStr);
        return Instant.ofEpochMilli(millis)
                .atZone(ZoneId.of("Africa/Nairobi"))
                .toLocalDateTime();
    }
    // Optional helper method
    private Long getLong(Object val) {
        return val == null ? 0L : ((Number) val).longValue();
    }
    private boolean hasText(String str) {
        return str != null && !str.trim().isEmpty();
    }







}
