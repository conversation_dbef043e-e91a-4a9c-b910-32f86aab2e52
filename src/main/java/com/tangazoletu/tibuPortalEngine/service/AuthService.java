package com.tangazoletu.tibuPortalEngine.service;

import com.tangazoletu.tibuPortalEngine.configurations.JwtUtils;
import com.tangazoletu.tibuPortalEngine.dto.*;
import com.tangazoletu.tibuPortalEngine.entities.*;
import com.tangazoletu.tibuPortalEngine.exceptions.ResourceNotFoundException;
import com.tangazoletu.tibuPortalEngine.exceptions.TokenRefreshException;
import com.tangazoletu.tibuPortalEngine.repositories.*;
import jakarta.persistence.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.sql.Timestamp;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {
    @Autowired
    private final CrudService crudService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private OrganizationRepository organizationRepository;

    @Autowired
    private OtpService otpService;
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private JavaMailSender mailSender;

    @Autowired
    private ParamRepository paramRepository;
    @Autowired
    private CaptchaService captchaService;

    @Autowired
    private EmailService emailService;
    @Autowired
    private PasswordHistoryRepository passwordHistoryRepository;

    @Autowired
    private RefreshTokenService refreshTokenService;

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private JwtUtils jwtUtils;

    @PersistenceContext
    private EntityManager entityManager;

    public LoginResponse  processLogin(LoginRequest loginRequest) {
        if (!captchaService.verifyToken(loginRequest.getCaptchaToken())) {
            throw new RuntimeException("CAPTCHA validation failed. Please try again.");
        }
        Authentication authentication = null;

        int max_login_retries = 0; //initialize max_login_retries
        int retry_lock_period = 15; // Get the login retry lock period
        int account_status = 0; // Get the account status. 0 = inactive, 1 = active

        // fetching the maximum login attempts allowed
        try {
            Object max_login_attempts = entityManager
                    .createNativeQuery("SELECT `value` FROM PARAM WHERE parameter = :param")
                    .setParameter("param", "MAX_LOGIN_ATTEMPTS")
                    .getSingleResult();

            max_login_retries = Integer.parseInt((String) max_login_attempts);

        } catch (NoResultException e) {
            System.out.println("Allowed login attempts fetch: No result found.");
        } catch (NonUniqueResultException e) {
            System.out.println("Allowed login attempts fetch: Multiple results found in the database.");
        }catch (NumberFormatException e) {
            System.out.println("Allowed login attempts fetch: Invalid number format fetched.");
        }

        // Validate organization
        Organisation organization = organizationRepository.findByName(loginRequest.getOrganizationName())
                .orElseThrow(() -> new ResourceNotFoundException("Organization not found"));

        User userOpt = userRepository.findValidUserWithPassword(
                loginRequest.getUsername(),
                organization.getId()
        );
        // Get user
        User user = userOpt;

        // Check if user belongs to the specified organization
        if (!user.getOrganization().getId().equals(organization.getId())) {
            throw new ResourceNotFoundException("User does not belong to the specified organization");
        }

        // Check password age
        Timestamp lastReset = user.getLast_reset();
        if (lastReset != null) {
            long millisIn30Days = 30L * 24 * 60 * 60 * 1000; // 30 days in milliseconds
            long now = System.currentTimeMillis();
            long ageInMillis = now - lastReset.getTime();

            if (ageInMillis > millisIn30Days) {
                // Password expired — force user to reset
                throw new PasswordExpiredException("Your password has expired. Please reset it.");
            }
        } else {
            // Never changed password (null reset date) — treat as expired
            throw new RuntimeException("Your password must be changed. Please reset it before logging in.");
        }

        int login_reties = user.getLoginAttempts();

        // Authenticate user based on username and password provided
        try {
            authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(loginRequest.getUsername(), loginRequest.getPassword())
            );

            if (login_reties>0){
                user.setLoginAttempts(0);
                userRepository.save(user);
            }

            SecurityContextHolder.getContext().setAuthentication(authentication);
        } catch (BadCredentialsException ex) {
            // Increment the retry count and save
            login_reties++;

            if (login_reties >= max_login_retries){
                user.setLoginAttempts(login_reties);
                user.setStatus(0);
                userRepository.save(user);
                return new LoginResponse("02", "Max retries reached. Account locked, please contact your administrator.");
            } else {
                user.setLoginAttempts(login_reties);
                userRepository.save(user);
                return new LoginResponse("01", "Password did not match! " + (max_login_retries-login_reties) + " attempt(s) remaining.");
            }
        }
        catch(DisabledException ex){
            //User account is deactivated/disabled
            log.error("DisabledException :: {}", ex.getMessage());
            return new LoginResponse("03", "This account is locked. Kindly contact the system admin for assistance.");

        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        if (authentication != null) {
            // Set authentication in security context
            SecurityContextHolder.getContext().setAuthentication(authentication);

            // Generate OTP
            String otp = otpService.generateOtp();
            System.out.println("Generated otp is > " + otp);
            user.setOtp(otp);
            user.setOtpExpiration(Timestamp.valueOf(otpService.getOtpExpiryTime()));
            userRepository.save(user);

            // Send OTP to user's email
            emailService.sendOtpEmail(user.getEmail(), otp);
            jwtUtils.generateJwtToken(authentication);

            return new LoginResponse("00", "Success. OTP has been sent to your email.");
        }

        return new LoginResponse("99", "Unexpected error occurred during login.");
    }

    public class PasswordExpiredException extends RuntimeException {
        public PasswordExpiredException(String message) {
            super(message);
        }
    }


    public JwtResponse verifyOtp(OtpVerificationRequest otpRequest) {
        // Get user with username and organization
        Organisation organization = organizationRepository.findByName(otpRequest.getOrganizationName())
                .orElseThrow(() -> new ResourceNotFoundException("Organization not found"));

        User user = userRepository.findByLogin(otpRequest.getUsername())
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        if (!captchaService.verifyToken(otpRequest.getCaptchaToken())) {
            throw new RuntimeException("CAPTCHA validation failed. Please try again.");
        }
        // Validate organization
        if (!user.getOrganization().getId().equals(organization.getId())) {
            throw new ResourceNotFoundException("User does not belong to the specified organization");
        }

        // Validate OTP
        if (user.getOtp() == null || !user.getOtp().equals(otpRequest.getOtp())) {
            throw new ResourceNotFoundException("Invalid OTP");
        }

        // Check if OTP is expired
        if (otpService.isOtpExpired(user.getOtpExpiration().toLocalDateTime())) {
            throw new TokenRefreshException("OTP has expired. Please request a new one");
        }

        // Generate secret key for encryption
        String secretKey = generateSecretKey();
        user.setSecretKey(secretKey);

        // Clear OTP after successful verification
        user.setOtp(null);
        user.setOtpExpiration(null);
        userRepository.save(user);

        // Generate JWT token with secret key included
        String jwt = jwtUtils.generateJwtToken(user.getLogin(), organization.getName(), secretKey);

        // Generate refresh token
        RefreshToken refreshToken = refreshTokenService.createRefreshToken(user.getId());

        System.out.println("Generated jwt is > " + jwt);

        return new JwtResponse("00",jwt, refreshToken.getToken(), secretKey,
                user.getLogin(), organization.getName(), user.getEmail(), user.getProvince(), user.getId(), organization.getId());
    }

    private String generateSecretKey() {
        byte[] bytes = new byte[32]; // 256 bits
        new SecureRandom().nextBytes(bytes);
        return Base64.getEncoder().encodeToString(bytes);
    }

    public String processForgotPassword(String username, String organization, String captcha) {

        Optional<Organisation> orgObject = organizationRepository.findByName(organization);
        Organisation org = orgObject.get();
        Optional<User> userOpt = userRepository.findByLoginAndOrganizationName(username, org.getName());
        if (userOpt.isEmpty()) {
            throw new RuntimeException("User not found or inactive.");
        }
        User result = userOpt.get();

        if (!captchaService.verifyToken(captcha)) {
            throw new RuntimeException("CAPTCHA validation failed. Please try again.");
        }
        if (result == null) {
            return "The username and/or organization provided do not match any TIBU user account";
        }

        String email =  result.getEmail();
        String firstname = result.getFirstname() ;
        String userName = result.getLogin();
        Long userId = result.getId();
        String orgIdName = org.getName();

        String tempPassword = generateRandomPassword();
        String resetId = null;
        try {
            resetId = hashSha512(tempPassword);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        result.setShaPassword(resetId);
        userRepository.save(result);

        String homeLink = jdbcTemplate.queryForObject("SELECT VALUE FROM PARAM WHERE PARAMETER = 'SYSTEM_HOME_LINK'", String.class);
        String resetLink = homeLink + "reset-password?reset=" + resetId;
        String resetMsg = "Hi " + userName + ",<br/>Your temporary password is <b>" + tempPassword + "</b>. " +
                ",<br/>Your Organisation is <b>" + orgIdName + "</b>. "+
                "<a href='" + resetLink + "'>Reset Password</a>";

        log.info("this is the reset link" + resetLink);
        // Update password in DB
        jdbcTemplate.update("UPDATE user SET shaPassword = ? WHERE login = ? AND org_id IN " +
                        "(SELECT id FROM organisation WHERE name = ? AND intrash = 'NO')",
                resetId, username, organization);

        // Send email
        emailService.sendEmail(email, resetMsg);

        // Log password reset
        jdbcTemplate.update("INSERT INTO PASSWORD_RESET (USER_ID, RESET_ID, RESET_LINK, STATUS, REQUEST_TIME) VALUES (?, ?, ?, 1, NOW())",
                userId, resetId, resetLink);

        jdbcTemplate.update(
                "INSERT INTO MAIL_DELIVERY_LOG (status, feature, date_created, recipient) VALUES (1, ?, NOW(), ?)",
                resetLink, email);

        return "An email with a password reset link has been sent to your inbox";
    }

    private String generateRandomPassword() {
        return UUID.randomUUID().toString().substring(0, 8); // Simplified
    }

    public ResetPasswordResponse resetPassword(ResetPasswordRequest request) {
        try {
            // Validation
            if (request.getNewPassword() == null || request.getConfirmPassword() == null ||
                    request.getOrganization() == null || request.getTempPassword() == null) {
                return new ResetPasswordResponse<>("01", "Missing required fields", "0");
            }

            if (!request.getNewPassword().equals(request.getConfirmPassword())) {
                return new ResetPasswordResponse<>("01", "Passwords do not match", "1");
            }

            if (!isPasswordStrong(request.getNewPassword())) {
                return new ResetPasswordResponse<>("01", "Weak password format", "2");
            }


            String orgName = request.getOrganization();
            Optional<Organisation> orgOpt = organizationRepository.findByName(orgName);
            if (!orgOpt.isPresent()) {
                return new ResetPasswordResponse<>("01", "Organization not found", "10");
            }
            Organisation org = orgOpt.get();

            // Check user with temp password
            String hashedTempPass = hashSha512(request.getTempPassword());
            User userOpt = userRepository.findValidUserWithTempPassword(
                    request.getUsername(),
                    hashedTempPass,
                    org.getId()
            );
            if (userOpt== null) {
                incrementResetAttempts(request.getUsername(), org.getId());
                return new ResetPasswordResponse<>("01", "Invalid user credentials", "5");
            }

            User user = userOpt;

            String parameter= "MAX_RESET_ATTEMPTS";
            int maxAttempts = Integer.parseInt(paramRepository.findValueByParameter(parameter));
            if (user.getResetAttempts() >= maxAttempts) {
                user.setStatus(0); // lock
                userRepository.save(user);
                return new ResetPasswordResponse<>("01", "Account locked", "15");
            }

            String newHashedPassword = hashSha512(request.getNewPassword());

            List<String> oldPasswords = passwordHistoryRepository
                    .findRecentHashedPasswords(user.getId(), PageRequest.of(0, 5));

            if (oldPasswords == null || oldPasswords.isEmpty()) {
                // No old passwords found — proceed safely
            } else {
                boolean isReused = oldPasswords.stream()
                        .anyMatch(p -> p.equals(newHashedPassword));

                if (isReused) {
                    return new ResetPasswordResponse<>("99", "New password must be different from the last 5 used.", "REUSED");
                }
            }

            // Check for match
            boolean isReused = oldPasswords.stream().anyMatch(p -> p.equals(newHashedPassword));

            if (isReused) {
                return new ResetPasswordResponse<>("99", "New password must be different from the last 5 used.", "REUSED");
            }
            // Update password
            user.setShaPassword(hashSha512(request.getNewPassword()));
            user.setResetAttempts(0);
            user.setLast_reset(new Timestamp( new Date().getTime()));
            userRepository.save(user);

            PasswordHistory history = new PasswordHistory();
            history.setUser(user);
            history.setPassword(newHashedPassword);
            history.setDateCreated(new Timestamp(System.currentTimeMillis()));

            passwordHistoryRepository.save(history);
            // Notify user
            String message = "Dear " + user.getFirstname() + ", your TIBU password was successfully reset.";
            emailService.sendEmail(user.getEmail(), message);

            return new ResetPasswordResponse<>("00", "Password reset successful", "4");

        } catch (Exception e) {
            log.error("Error in resetPassword: ", e);
            return new ResetPasswordResponse<>("99", "Internal server error", null);
        }
    }
    public String hashSha512(String input) throws Exception {
        MessageDigest md = MessageDigest.getInstance("SHA-512");
        byte[] inputBytes = input.getBytes();
        BigInteger hash = new BigInteger(1, md.digest(inputBytes));
        StringBuilder hashString = new StringBuilder(hash.toString(16));
        while (hashString.length() < 32) {
            hashString.insert(0, '0');

        }
        return hashString.toString();
    }

    public ChangePasswordResponse changePassword(ChangePasswordRequest request) {
        try {
            // Validation
            if (request.getNewPassword() == null || request.getConfirmPassword() == null ||
                    request.getOrganization() == null || request.getOldPassword() == null) {
                return new ChangePasswordResponse<>("01", "Missing required fields", 0);
            }

            if (!request.getNewPassword().equals(request.getConfirmPassword())) {
                return new ChangePasswordResponse<>("01", "Passwords do not match", "1");
            }

            if (!isPasswordStrong(request.getNewPassword())) {
                return new ChangePasswordResponse<>("01", "Weak password format", "2");
            }


            String orgName = request.getOrganization();
            Optional<Organisation> orgOpt = organizationRepository.findByName(orgName);
            if (!orgOpt.isPresent()) {
                return new ChangePasswordResponse<>("01", "Organization not found", "10");
            }
            Organisation org = orgOpt.get();

            // Check user with temp password
            String hashedTempPass = hashSha512(request.getOldPassword());
            Optional<User> portalUser = userRepository.findByLoginAndOrganizationName(
                    request.getUsername(),
                    request.getOrganization()
            );
            User userOpt = portalUser.get();
            if (userOpt== null) {
                incrementResetAttempts(request.getUsername(), org.getId());
                return new ChangePasswordResponse<>("01", "Invalid user credentials", "5");
            }


            String parameter= "MAX_RESET_ATTEMPTS";
            int maxAttempts = Integer.parseInt(paramRepository.findValueByParameter(parameter));
            if (userOpt.getResetAttempts() >= maxAttempts) {
                userOpt.setStatus(0); // lock
                userRepository.save(userOpt);
                return new ChangePasswordResponse<>("01", "Account locked", "15");
            }

            String newHashedPassword = hashPassword(request.getNewPassword());

            List<String> oldPasswords = passwordHistoryRepository
                    .findRecentHashedPasswords(userOpt.getId(), PageRequest.of(0, 5));

            if (oldPasswords == null || oldPasswords.isEmpty()) {
                // No old passwords found — proceed safely
            } else {

                // Check for match
                boolean isReused = oldPasswords.stream()
                        .anyMatch(p -> p.equals(newHashedPassword));

                if (isReused) {
                    return new ChangePasswordResponse<>("99", "New password must be different from the last 5 used.", "REUSED");
                }
            }


            // Update password
            userOpt.setPassword(hashSha512(request.getNewPassword()));
            userOpt.setResetAttempts(0);
            userOpt.setLast_reset(new Timestamp(new Date().getTime()));
            userRepository.save(userOpt);

            PasswordHistory history = new PasswordHistory();
            history.setUser(userOpt);
            history.setPassword(newHashedPassword);
            history.setDateCreated(new Timestamp(System.currentTimeMillis()));

            passwordHistoryRepository.save(history);
            // Notify user
            String message = "Dear " + userOpt.getFirstname() + ", your TIBU password was successfully Changed.";
            emailService.sendEmail(userOpt.getEmail(), message);

            return new ChangePasswordResponse<>("00", "Password change successful", "4");

        } catch (Exception e) {
            log.error("Error in changePassword: ", e);
            return new ChangePasswordResponse<>("99", "Internal server error", null);
        }
    }

    private String hashPassword(String password) {
        return DigestUtils.sha512Hex(password);
    }

    private boolean isPasswordStrong(String password) {
        return password.length() >= 6 &&
                password.length() <= 15 &&
                password.matches(".*[A-Z].*") &&
                password.matches(".*[a-z].*") &&
                password.matches(".*[-a-zA-Z0-9_ @./\\\\!#$%&*?].*");
    }

    private void incrementResetAttempts(String username, Long orgId) {
        Optional<Organisation> Organisation = organizationRepository.findById(orgId);
        Optional<User> userOpt = userRepository.findByLogin(username);
        userOpt.ifPresent(user -> {
            user.setResetAttempts(user.getResetAttempts() + 1);
            userRepository.save(user);
        });
    }
}
