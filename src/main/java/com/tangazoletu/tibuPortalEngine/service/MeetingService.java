package com.tangazoletu.tibuPortalEngine.service;

import com.tangazoletu.tibuPortalEngine.dto.*;
import com.tangazoletu.tibuPortalEngine.entities.*;
import com.tangazoletu.tibuPortalEngine.enums.ApiResponse;
import com.tangazoletu.tibuPortalEngine.repositories.*;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.Tuple;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j

public class MeetingService {

    /**
     * Helper class to store day information for filtering
     */
    private static class DayInfo {
        final int dayOfMonth;
        final String dayLabel;
        final String dateStr;

        DayInfo(int dayOfMonth, String dayLabel, String dateStr) {
            this.dayOfMonth = dayOfMonth;
            this.dayLabel = dayLabel;
            this.dateStr = dateStr;
        }
    }

    private final EventRepo eventRepo;
    @PersistenceContext
    private EntityManager entityManager;
    private final EventCodesRepo eventCodesRepo;
    private final EventCodesViewRepo eventCodesViewRepo;
    private final EventCodesLatestRepo eventCodesLatestRepo;
    private final AttendanceRepo attendanceRepo;
    private final AttendanceRecipientViewRepository attendanceRecipientViewRepository;
    private final Mode6RecipientViewRepository mode6RecipientViewRepository;
    private final AttendanceSummaryViewRepository attendanceSummaryViewRepository;
    private final PerdiemRepo perdiemRepo;
    private final EventSummaryViewRepo eventSummaryViewRepo;
    private final CrudService crudService;

    public MeetingResponse getMeeting(EventRequest request, HttpServletResponse response, HttpServletRequest httpServletRequest) {
        MeetingResponse meetingResponse = MeetingResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();

        try {
            Pageable pageable = PageRequest.of(
                    request.getPage() != null ? request.getPage() : 0,
                    request.getPageSize() != null ? request.getPageSize() : 10
            );

            // Fetch by ID (single record mode)
            if (request.getId() != null) {
                Page<Event> events = eventRepo.findByIdAndInTrash(request.getId(), "No", pageable);
                meetingResponse.setData(events);
                meetingResponse.setResponseMessage("Success");
                meetingResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                return meetingResponse;
            }

            // Keyword filter
            String keyword = (request.getKeywords() != null && !request.getKeywords().isBlank())
                    ? request.getKeywords().trim()
                    : null;

            // Parse budgetIds
            List<Long> budgetIds = null;
            if (request.getBudgetIds() != null && !request.getBudgetIds().isBlank()) {
                budgetIds = Arrays.stream(request.getBudgetIds().split(","))
                        .map(String::trim)
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
            }

            // Parse countyIds
            List<Long> countyIds = null;
            if (request.getCountyIds() != null && !request.getCountyIds().isBlank()) {
                countyIds = Arrays.stream(request.getCountyIds().split(","))
                        .map(String::trim)
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
            }

            // Parse dates
            // Parse 'from' date
            LocalDateTime from = null;
            if (request.getDateFrom() != null && !request.getDateFrom().isBlank()) {
                from = LocalDate.parse(request.getDateFrom().trim()).atStartOfDay(); // 00:00
            }

            // Parse 'to' date — add 1 day to include full last day
            LocalDateTime to = null;
            if (request.getDateTo() != null && !request.getDateTo().isBlank()) {
                to = LocalDate.parse(request.getDateTo().trim()).plusDays(1).atStartOfDay(); // exclusive upper bound
            }


            // Call JPA query method using view
            Page<EventSummaryView> pageResult = eventSummaryViewRepo.filterEvents(
                    keyword,
                    budgetIds,
                    countyIds,
                    request.getMeetingStatus(),
                    from,
                    to,
                    pageable
            );

            meetingResponse.setResponseMessage("Success");
            meetingResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
            meetingResponse.setData(pageResult);

        } catch (Exception e) {
            log.error("An error occurred while fetching meetings", e);
            throw new RuntimeException("Failed to fetch meetings", e);
        }

        return meetingResponse;
    }


//    public MeetingResponse getMeeetingCode(MeetingCodesRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
//        try {
//            String mcode = request.getMcode();
//            String keyword = request.getKeyword();
//            String meetingCode = request.getMeetingcode();
//            String codesOption = request.getCodeOption(); // e.g. "LATEST"
//            int page = request.getPage();
//            int pageSize = request.getPageSize();
//            Pageable pageable = PageRequest.of(page, pageSize);
//            if (meetingCode.isEmpty()|| meetingCode == null) {
//                Page<Map<String, Object>> codesPage;
//                if (mcode != null && !mcode.isEmpty()) {
//                    codesPage = eventCodesViewRepo.findCodesByMeetingCode(meetingCode, pageable);
//                } else {
//                    codesPage = eventCodesViewRepo.findAllCodes(pageable);
//                }
//                if (codesPage.getTotalElements() == 0) {
//
//                }else {
//                    return MeetingResponse.builder()
//                            .responseCode(ApiResponse.SUCCESS.getCode())
//                            .responseMessage("Success")
//                            .data(codesPage)
//                            .build();
//                }
//            }
//
//
//
//            // 1. Fetch Event Info
//            Optional<Event> optionalEvent = eventRepo.findByMeetingCode(meetingCode.trim(), mcode.trim());
//            if (optionalEvent.isEmpty()) {
//                return MeetingResponse.builder()
//                        .responseCode(ApiResponse.FAIL.getCode())
//                        .responseMessage("Event not found")
//                        .build();
//            }
//
//
//            Event event = optionalEvent.get();
//
//            Date startDateRaw = event.getStartDate();
//            Date endDateRaw = event.getEndDate();
//
//            LocalDate startDate = startDateRaw.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
//            LocalDate endDate = endDateRaw.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
//            LocalDate currentDate = LocalDate.now();
//
//            int day = (int) ChronoUnit.DAYS.between(startDate, currentDate) + 1;
//            int noOfDays = (int) ChronoUnit.DAYS.between(startDate, endDate) + 1;
//
//
//            long noOfCodes = eventCodesRepo.countAllByMeetingCode(meetingCode);
//
//            // 2. Get Codes
//            Page<Map<String, Object>> codesPage;
//            if ("LATEST".equalsIgnoreCase(codesOption)) {
//                codesPage = eventCodesLatestRepo.findLatestCodes(meetingCode, pageable);
//            } else {
//                if (mcode != null && !mcode.isEmpty()) {
//                    codesPage = eventCodesViewRepo.findCodesByMeetingCode(meetingCode, pageable);
//                } else {
//                    codesPage = eventCodesViewRepo.findAllCodes(pageable);
//                }
//            }
//
//            Page<Map<String, Object>> enrichedPage = codesPage.map(row -> {
//                Map<String, Object> newRow = new HashMap<>(row); // copy original
//                newRow.put("day", day);
//                newRow.put("noOfDays", noOfDays);
//                newRow.put("noOfCodes", noOfCodes);
//                return newRow;
//            });
//
//
//            return MeetingResponse.builder()
//                    .responseCode(ApiResponse.SUCCESS.getCode())
//                    .responseMessage("Successfully fetched meeting codes")
//                    .data(enrichedPage)
//                    .build();
//
//        } catch (Exception e) {
//            log.error("An error occurred while fetching meetings", e);
//            return MeetingResponse.builder()
//                    .responseCode(ApiResponse.FAIL.getCode())
//                    .responseMessage("Failed to fetch meeting codes")
//                    .build();
//        }
//    }

    public EventCodesResponse getMeeetingCode(MeetingCodesRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        try {
            String meetingCode = "";
            if (request.getMcode() != null && !request.getMcode().isEmpty()) {
                meetingCode = request.getMcode();
            }

            if (request.getMeetingcode() != null && !request.getMeetingcode().isEmpty()) {
                meetingCode = request.getMeetingcode();
            }
            int page = request.getPage();
            int pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);

            Page<Map<String, Object>> codesPage = new PageImpl<>(new ArrayList<>());

            if (!meetingCode.isEmpty()) {
                if (request.getCodeOption() != null && request.getCodeOption().equalsIgnoreCase("LATEST")) {
                    codesPage = eventCodesLatestRepo.findLatestCodes(meetingCode, pageable);
                } else {
                    codesPage = eventCodesViewRepo.findCodesByMeetingCode(meetingCode, pageable);
                }
            } else {
                if (request.getCodeOption() != null && request.getCodeOption().equalsIgnoreCase("LATEST")) {
                    codesPage = eventCodesLatestRepo.findAllLatestCodes(pageable);
                } else {
                    codesPage = eventCodesViewRepo.findAllCodes(pageable);
                }
            }


            return EventCodesResponse.builder()
                    .responseCode(ApiResponse.SUCCESS.getCode())
                    .responseMessage("Successfully fetched meeting codes")
                    .data(codesPage)
                    .build();
        } catch (Exception e) {
            log.error("An error occurred while fetching meetings", e);
            return EventCodesResponse.builder()
                    .responseCode(ApiResponse.FAIL.getCode())
                    .responseMessage("Failed to fetch meeting codes")
                    .build();
        }
    }
    public MeetingResponse getCodeVerification(CodeVerificationRequest request,
                                               HttpServletRequest httpServletRequest,
                                               HttpServletResponse httpServletResponse) {
        MeetingResponse meetingResponse = MeetingResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();
        try {
            String budget = request.getBudget();
            String keyword = request.getKeyword();
            Timestamp from = null, to = null;
            String code = request.getCode();
            if (code != null && !code.isEmpty()) {

                Optional<Event> event = eventRepo.findTopByMeetingCode(code);
                Object[] row = attendanceRepo.getAttendanceStatusCounts(code);

                int processingStage = 0;
                int approvalStage = 0;

                if (row != null && row.length == 2) {
                    processingStage = row[0] != null ? ((BigDecimal) row[0]).intValue() : 0;
                    approvalStage = row[1] != null ? ((BigDecimal) row[1]).intValue() : 0;
                }

                AttendanceStatusDTO dto = new AttendanceStatusDTO(processingStage, approvalStage);

                meetingResponse.setAttendanceStatusDTO(dto);
                meetingResponse.setEvent(event);
                meetingResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                meetingResponse.setResponseMessage("Successfully fetched attendance statuses");
                return meetingResponse;

            }


            if (request.getDateFrom() != null && !request.getDateFrom().isEmpty()) {
                from = Timestamp.valueOf(request.getDateFrom().trim() + " 00:00:00");
            }
            if (request.getDateTo() != null && !request.getDateTo().isEmpty()) {
                to = Timestamp.valueOf(request.getDateTo().trim() + " 23:59:59");
            }

            Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize());
            Page<Object[]> resultPage = attendanceRepo.findCodeVerificationReport(budget, from, to, keyword, pageable);

            Page<Map<String, Object>> mappedPage = resultPage.map(row -> {
                Map<String, Object> map = new HashMap<>();
                map.put("meetingCode", row[0]);
                map.put("unprocessed", row[1]);
                map.put("processed", row[2]);
                map.put("approved", row[3]);
                map.put("rejected", row[4]);
                map.put("totalApprovedAmt", row[5]);
                map.put("total",row[6]);


                return map;
            });

            meetingResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
            meetingResponse.setResponseMessage("Success");
            meetingResponse.setData(mappedPage);

        } catch (Exception e) {
            log.error("An error occurred while fetching meetings", e);
            meetingResponse.setResponseMessage("Exception: " + e.getMessage());
        }

        return meetingResponse;
    }
    public ColumRisizeDataResponse getColumRisizeData(MeetingCodesRequest request, HttpServletRequest req, HttpServletResponse res) {
        ColumRisizeDataResponse.ColumRisizeDataResponseBuilder responseBuilder = ColumRisizeDataResponse.builder()
                .responseCode(ApiResponse.FAIL.getCode())
                .responseMessage("Failed");

        try {
            int page = request.getPage();
            int pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);
            BigDecimal total = new BigDecimal("0.00");
            List<String> dayLabels = new ArrayList<>();
            List<ColumnResizeRow> data = new ArrayList<>();
            String ordIdQuery = "SELECT ORG_ID FROM event WHERE meeting_code =:meetingCode";
            Map<String, Object> ordIdParams = Map.of("meetingCode", request.getMeetingcode());
            String orgId = crudService.getSingleResult(ordIdQuery, ordIdParams).get("ORG_ID").toString();
            if (orgId == null) {
                orgId = "1";
            }else {
                orgId = orgId.trim();
                log.info("Org ID: " + orgId);
            }
            if ("5".equalsIgnoreCase(request.getMode())) { // section for attendance processing
                String meetingCode = request.getMeetingcode();

                // 1️⃣ Get meeting details using native query (matching PHP logic)
                String meetingDetailsQuery = "SELECT (TIMESTAMPDIFF(DAY, start_date, end_date)+1) AS NOofDays, " +
                        "start_date, travelling_days, meeting_type " +
                        "FROM event WHERE meeting_code = :meetingCode LIMIT 1";

                Map<String, Object> meetingParams = Map.of("meetingCode", meetingCode);
                Map<String, Object> meetingDetails = crudService.getSingleResult(meetingDetailsQuery, meetingParams);

                if (meetingDetails == null) {
                    throw new RuntimeException("Meeting not found");
                }

                int numberOfDays = ((Number) meetingDetails.get("NOofDays")).intValue();
                Date startDate = (Date) meetingDetails.get("start_date");
                int travellingDays = ((Number) meetingDetails.get("travelling_days")).intValue();
                String meetingType = (String) meetingDetails.get("meeting_type");

                // 2️⃣ Build dynamic column headers and attendance query parts (matching PHP logic)
                List<String> meetingDayLabels = new ArrayList<>();
                meetingDayLabels.add("'Payable Travelling Days'");

                StringBuilder didHeAttendQuery = new StringBuilder();
                // Add arrival day query (matching PHP logic - note the != 0 condition)
                didHeAttendQuery.append("(SELECT (CASE WHEN COUNT(*)>0 THEN 0 ELSE ")
                        .append(travellingDays)
                        .append(" END) FROM attendance WHERE processing_status!=0 AND meeting_code='")
                        .append(meetingCode)
                        .append("' AND recipient_id=R.recipient_id) AS 'arrivalday', ");

                // Store day information for filtering later
                List<DayInfo> dayInfoList = new ArrayList<>();

                Calendar cal = Calendar.getInstance();
                cal.setTime(startDate);

                for (int i = 0; i < numberOfDays; i++) {
                    int dayOfMonth = cal.get(Calendar.DAY_OF_MONTH);
                    int month = cal.get(Calendar.MONTH) + 1; // Calendar months are 0-based
                    int year = cal.get(Calendar.YEAR);

                    String monthName = cal.getDisplayName(Calendar.MONTH, Calendar.SHORT, Locale.ENGLISH);
                    String dayLabel = monthName + " " + dayOfMonth;
                    meetingDayLabels.add("'" + dayLabel + "'");

                    String dateStr = String.format("%04d-%02d-%02d", year, month, dayOfMonth);

                    // Store day info for later filtering
                    dayInfoList.add(new DayInfo(dayOfMonth, dayLabel, dateStr));

                    didHeAttendQuery.append("(SELECT COUNT(*) FROM attendance A WHERE DATE(requesttime)='")
                            .append(dateStr)
                            .append("' AND A.MSISDN=R.msisdn AND verification_status='Approved' AND processing_status='0' AND meeting_code='")
                            .append(meetingCode)
                            .append("') AS '").append(dayOfMonth).append("', ");

                    cal.add(Calendar.DAY_OF_MONTH, 1);
                }

                meetingDayLabels.add("'No of Days'");
                didHeAttendQuery.append("(SELECT (CASE WHEN COUNT(*)>0 THEN COUNT(*)+arrivalday ELSE COUNT(*) END) " +
                        "FROM attendance A WHERE A.MSISDN=R.msisdn AND verification_status='Approved' AND processing_status='0' AND meeting_code='")
                        .append(meetingCode)
                        .append("') AS 'NoofDays', ");

                // 3️⃣ Build arrival day query for total calculation (matching PHP logic)
                String didHeAttendQuery2 = "(SELECT (CASE WHEN COUNT(*)>0 THEN 0 ELSE " + travellingDays +
                        " END) FROM attendance WHERE processing_status!=0 AND meeting_code='" + meetingCode +
                        "' AND recipient_id=R.recipient_id)";

                // 4️⃣ Get total amount (matching PHP logic exactly)
                String totalQuery = "SELECT SUM(" +
                        "(SELECT (CASE WHEN '" + meetingType + "'='DayOnly' THEN lunchDinnerRate " +
                        "WHEN '" + meetingType + "'='HalfBoard' THEN dinnerRate ELSE amount END) " +
                        "FROM perdiem WHERE id=C.designationid AND org_id='" + orgId + "') * " +
                        "(SELECT (CASE WHEN COUNT(*)>0 THEN COUNT(*)+" + didHeAttendQuery2 + " ELSE COUNT(*) END) " +
                        "FROM attendance A WHERE A.MSISDN=R.msisdn AND verification_status='Approved' AND processing_status='0' AND meeting_code='" + meetingCode + "') - 0" +
                        ") AS 'Total' " +
                        "FROM attendance R INNER JOIN recipient C ON R.recipient_id=C.ID " +
                        "WHERE R.meeting_code='" + meetingCode + "' AND R.processing_status='0'";

                Map<String, Object> totalResult = crudService.getSingleResult(totalQuery, Map.of());
                total = totalResult != null ? BigDecimal.valueOf(((Number) totalResult.get("Total")).doubleValue()) : BigDecimal.ZERO;

                // 5️⃣ Build main query (matching PHP logic exactly)
                String rateLabel = "DayOnly".equals(meetingType) ? "Lunch Rate" :
                                 ("HalfBoard".equals(meetingType) ? "Dinner Rate" : "Per Diem Rate");

                String mainQuery = "SELECT " +
                        "(SELECT CONCAT(firstname,' ',middlename,' ',lastname) FROM recipient WHERE id=R.recipient_id) AS Name, " +
                        "(SELECT IDNumber FROM recipient WHERE id=R.recipient_id) AS IDNumber, " +
                        "(SELECT title FROM county WHERE county.id=C.countyid) AS NameofCounty, " +
                        "(SELECT title FROM district WHERE id=C.subcountyid) AS Station, " +
                        "(SELECT designation FROM perdiem WHERE id=C.designationid AND org_id='" + orgId + "') AS Designation, " +
                        "(SELECT title FROM jobgroup WHERE id=C.jobgroupid) AS JobGroup, " +
                        didHeAttendQuery.toString() +
                        "R.MSISDN AS 'Telephone Number', " +
                        "(SELECT (CASE WHEN '" + meetingType + "'='DayOnly' THEN lunchDinnerRate " +
                        "WHEN '" + meetingType + "'='HalfBoard' THEN dinnerRate ELSE amount END) " +
                        "FROM perdiem WHERE id=C.designationid AND org_id='" + orgId + "') AS Rate, " +
                        "(SELECT (CASE WHEN '" + meetingType + "'='DayOnly' THEN lunchDinnerRate " +
                        "WHEN '" + meetingType + "'='HalfBoard' THEN dinnerRate ELSE amount END) " +
                        "FROM perdiem WHERE id=C.designationid AND org_id='" + orgId + "') * " +
                        "(SELECT (CASE WHEN COUNT(*)>0 THEN COUNT(*)+arrivalday ELSE COUNT(*) END) " +
                        "FROM attendance A WHERE A.MSISDN=R.msisdn AND verification_status='Approved' AND processing_status='0' AND meeting_code='" + meetingCode + "') AS Amount, " +
                        "0 AS 'Less lunch', " +
                        "0 AS 'Transport', " +
                        "0 AS 'Extra per diem', " +
                        "0 AS 'Others', " +
                        "(" +
                        "(SELECT (CASE WHEN '" + meetingType + "'='DayOnly' THEN lunchDinnerRate " +
                        "WHEN '" + meetingType + "'='HalfBoard' THEN dinnerRate ELSE amount END) " +
                        "FROM perdiem WHERE id=C.designationid AND org_id='" + orgId + "') * " +
                        "(SELECT (CASE WHEN COUNT(*)>0 THEN COUNT(*)+arrivalday ELSE COUNT(*) END) " +
                        "FROM attendance A WHERE A.MSISDN=R.msisdn AND verification_status='Approved' AND processing_status='0' AND meeting_code='" + meetingCode + "') - 0" +
                        ") AS 'Net Pay', 'YES' AS 'Process', '' AS 'Comments', R.ID " +
                        "FROM attendance R INNER JOIN recipient C ON R.recipient_id=C.ID " +
                        "WHERE meeting_code='" + meetingCode + "' AND processing_status='0' GROUP BY R.MSISDN";

                // 6️⃣ Execute main query with pagination
                int offset = page * pageSize;
                List<Map<String, Object>> results = crudService.fetchWithNativeQueryAsMap(mainQuery, Map.of(), offset, pageSize);

                // 7️⃣ Process results into data rows and collect active days
                Set<String> activeDays = new LinkedHashSet<>(); // Track days with attendance > 0

                for (Map<String, Object> row : results) {
                    // Create simplified dayAttendance object with ONLY daily attendance counts > 0
                    Map<String, Object> dayAttendanceMap = new LinkedHashMap<>();

                    // Add ONLY daily attendance counts with proper date labels (filter out 0s)
                    for (DayInfo dayInfo : dayInfoList) {
                        Object dayCount = row.get(String.valueOf(dayInfo.dayOfMonth));
                        int attendanceCount = dayCount != null ? ((Number) dayCount).intValue() : 0;

                        // Only include days with attendance count > 0
                        if (attendanceCount > 0) {
                            dayAttendanceMap.put(dayInfo.dayLabel, attendanceCount);
                            activeDays.add("'" + dayInfo.dayLabel + "'"); // Track active days for headers
                        }
                    }

                    data.add(ColumnResizeRow.builder()
                            .name(safeString(row.get("Name")))
                            .idNumber(safeString(row.get("IDNumber")))
                            .county(safeString(row.get("NameofCounty")))
                            .station(safeString(row.get("Station")))
                            .designation(safeString(row.get("Designation")))
                            .jobGroup(safeString(row.get("JobGroup")))
                            .payableTravellingDays(travellingDays)
                            .dayAttendance(dayAttendanceMap)
                            .noOfDays(safeString(row.get("NoofDays")))
                            .telephoneNumber(safeString(row.get("Telephone Number")))
                            .rate(safeDecimal(row.get("Rate")))
                            .amount(safeDecimal(row.get("Amount")))
                            .lessLunch(safeDecimal(row.get("Less lunch")))
                            .transport(safeDecimal(row.get("Transport")))
                            .extraPerDiem(safeDecimal(row.get("Extra per diem")))
                            .others(safeDecimal(row.get("Others")))
                            .netPay(safeDecimal(row.get("Net Pay")))
                            .process(safeString(row.get("Process")))
                            .comments(safeString(row.get("Comments")))
                            .status("Pending Processing") // Default status for mode 5
                            .processingComments("")
                            .approvalComments("")
                            .attendanceId(safeLong(row.get("ID")))
                            .build());
                }

                // 8️⃣ Build dynamic column headers with only active days (days with attendance > 0)
                dayLabels = new ArrayList<>();
                dayLabels.addAll(List.of("Name", "IDNumber", "County", "Station", "Designation", "Job Group"));
                dayLabels.add("'Payable Travelling Days'");
                dayLabels.addAll(activeDays); // Only include days with attendance > 0
                dayLabels.addAll(List.of("'No of Days'", "Telephone Number", rateLabel, "Amount", "Less lunch",
                                       "Transport", "Extra per diem", "Others", "Net Pay", "Process", "Comments", "ID"));
            } else if ("6".equalsIgnoreCase(request.getMode())) { // section for attendance approval
                String meetingCode = request.getMeetingcode();
                 // Default org ID since MeetingCodesRequest doesn't have orgId field

                // 1️⃣ Get meeting details using native query (matching PHP logic)
                String meetingDetailsQuery = "SELECT (TIMESTAMPDIFF(DAY, start_date, end_date)+1) AS NOofDays, " +
                        "start_date, travelling_days, meeting_type " +
                        "FROM event WHERE meeting_code = :meetingCode LIMIT 1";

                Map<String, Object> meetingParams = Map.of("meetingCode", meetingCode);
                Map<String, Object> meetingDetails = crudService.getSingleResult(meetingDetailsQuery, meetingParams);

                if (meetingDetails == null) {
                    throw new RuntimeException("Meeting not found");
                }

                int numberOfDays = ((Number) meetingDetails.get("NOofDays")).intValue();
                Date startDate = (Date) meetingDetails.get("start_date");
                int travellingDays = ((Number) meetingDetails.get("travelling_days")).intValue();
                String meetingType = (String) meetingDetails.get("meeting_type");

                // 2️⃣ Build dynamic column headers and attendance query parts (matching PHP logic)
                List<String> meetingDayLabels = new ArrayList<>();
                meetingDayLabels.add("'Payable Travelling Days'");

                StringBuilder didHeAttendQuery = new StringBuilder();
                // Add arrival day query (matching PHP logic)
                didHeAttendQuery.append("(SELECT (CASE WHEN COUNT(*)>0 THEN ")
                        .append(travellingDays)
                        .append(" ELSE ")
                        .append(travellingDays)
                        .append(" END) FROM attendance WHERE processing_status=1 AND meeting_code='")
                        .append(meetingCode)
                        .append("' AND recipient_id=R.recipient_id) AS 'arrivalday', ");

                // Store day information for filtering later
                List<DayInfo> dayInfoList = new ArrayList<>();

                Calendar cal = Calendar.getInstance();
                cal.setTime(startDate);

                for (int i = 0; i < numberOfDays; i++) {
                    int dayOfMonth = cal.get(Calendar.DAY_OF_MONTH);
                    int month = cal.get(Calendar.MONTH) + 1; // Calendar months are 0-based
                    int year = cal.get(Calendar.YEAR);

                    String monthName = cal.getDisplayName(Calendar.MONTH, Calendar.SHORT, Locale.ENGLISH);
                    String dayLabel = monthName + " " + dayOfMonth;
                    meetingDayLabels.add("'" + dayLabel + "'");

                    String dateStr = String.format("%04d-%02d-%02d", year, month, dayOfMonth);

                    // Store day info for later filtering
                    dayInfoList.add(new DayInfo(dayOfMonth, dayLabel, dateStr));

                    didHeAttendQuery.append("(SELECT COUNT(*) FROM attendance A WHERE DATE(requesttime)='")
                            .append(dateStr)
                            .append("' AND A.MSISDN=R.msisdn AND verification_status='Approved' AND processing_status='1' AND meeting_code='")
                            .append(meetingCode)
                            .append("') AS '").append(dayOfMonth).append("', ");

                    cal.add(Calendar.DAY_OF_MONTH, 1);
                }

                meetingDayLabels.add("'No of Days'");
                didHeAttendQuery.append("(SELECT (CASE WHEN COUNT(*)>0 THEN COUNT(*)+arrivalday ELSE COUNT(*) END) " +
                        "FROM attendance A WHERE A.MSISDN=R.msisdn AND verification_status='Approved' AND processing_status='1' AND meeting_code='")
                        .append(meetingCode)
                        .append("') AS 'NoofDays', ");

                // 3️⃣ Build attendance IDs subquery
                String attendanceIds = "(SELECT id FROM attendance WHERE recipient_id=R.recipient_id AND meeting_code='" + meetingCode + "' AND processing_status='1')";

                // 4️⃣ Get total amount (matching PHP logic)
                String totalQuery = "SELECT SUM(payable_amount_1) AS 'Total' " +
                        "FROM attendance R INNER JOIN recipient C ON R.recipient_id=C.ID " +
                        "WHERE R.meeting_code='" + meetingCode + "' AND R.processing_status='1'";

                Map<String, Object> totalResult = crudService.getSingleResult(totalQuery, Map.of());
                total = totalResult != null ? BigDecimal.valueOf(((Number) totalResult.get("Total")).doubleValue()) : BigDecimal.ZERO;

                // 5️⃣ Build main query (matching PHP logic exactly)
                String rateLabel = "DayOnly".equals(meetingType) ? "Lunch Rate" :
                                 ("HalfBoard".equals(meetingType) ? "Dinner Rate" : "Per Diem Rate");

                String mainQuery = "SELECT " +
                        "(SELECT CONCAT(firstname,' ',middlename,' ',lastname) FROM recipient WHERE id=R.recipient_id) AS Name, " +
                        "(SELECT IDNumber FROM recipient WHERE id=R.recipient_id) AS IDNumber, " +
                        "(SELECT title FROM county WHERE county.id=C.countyid) AS NameofCounty, " +
                        "(SELECT title FROM district WHERE id=C.subcountyid) AS Station, " +
                        "(SELECT title FROM jobgroup WHERE id=C.jobgroupid) AS JobGroup, " +
                        didHeAttendQuery.toString() +
                        "R.MSISDN AS 'Telephone Number', " +
                        "(SELECT (CASE WHEN '" + meetingType + "'='DayOnly' THEN lunchDinnerRate " +
                        "WHEN '" + meetingType + "'='HalfBoard' THEN dinnerRate ELSE amount END) " +
                        "FROM perdiem WHERE id=C.designationid AND org_id='" + orgId + "') AS Rate, " +
                        "(SELECT SUM(credit) FROM temp_supervision_details WHERE title='Per Diem' AND apiorder_id IN " + attendanceIds + ") AS Amount, " +
                        "(SELECT SUM(credit) FROM temp_supervision_details WHERE title='Lunch' AND apiorder_id IN " + attendanceIds + ") AS 'Less_lunch_day_500', " +
                        "(SELECT SUM(credit) FROM temp_supervision_details WHERE title='Transport/Fuel' AND apiorder_id IN " + attendanceIds + ") AS 'Transport', " +
                        "(SELECT SUM(credit) FROM temp_supervision_details WHERE title='Extra Per Diem' AND apiorder_id IN " + attendanceIds + ") AS 'Extra_per_diem', " +
                        "(SELECT SUM(credit) FROM temp_supervision_details WHERE title='Others' AND apiorder_id IN " + attendanceIds + ") AS 'Others', " +
                        "payable_amount_1 AS 'Net Pay', 'YES' AS 'Approve', '' AS 'Comments', " +
                        "(SELECT CONVERT(GROUP_CONCAT(id) USING 'utf8') FROM attendance WHERE recipient_id=R.recipient_id " +
                        "AND meeting_code='" + meetingCode + "' AND processing_status='1') AS ID " +
                        "FROM attendance R INNER JOIN recipient C ON R.recipient_id=C.ID " +
                        "WHERE meeting_code='" + meetingCode + "' AND processing_status='1' GROUP BY R.MSISDN";

                // 6️⃣ Execute main query with pagination
                int offset = page * pageSize;
                List<Map<String, Object>> results = crudService.fetchWithNativeQueryAsMap(mainQuery, Map.of(), offset, pageSize);

                // 7️⃣ Process results into data rows and collect active days
                Set<String> activeDays = new LinkedHashSet<>(); // Track days with attendance > 0

                for (Map<String, Object> row : results) {
                    // Create simplified dayAttendance object with ONLY daily attendance counts > 0
                    Map<String, Object> dayAttendanceMap = new LinkedHashMap<>();

                    // Add ONLY daily attendance counts with proper date labels (filter out 0s)
                    for (DayInfo dayInfo : dayInfoList) {
                        Object dayCount = row.get(String.valueOf(dayInfo.dayOfMonth));
                        int attendanceCount = dayCount != null ? ((Number) dayCount).intValue() : 0;

                        // Only include days with attendance count > 0
                        if (attendanceCount > 0) {
                            dayAttendanceMap.put(dayInfo.dayLabel, attendanceCount);
                            activeDays.add("'" + dayInfo.dayLabel + "'"); // Track active days for headers
                        }
                    }

                    data.add(ColumnResizeRow.builder()
                            .name(safeString(row.get("Name")))
                            .idNumber(safeString(row.get("IDNumber")))
                            .county(safeString(row.get("NameofCounty")))
                            .station(safeString(row.get("Station")))
                            .designation("")
                            .jobGroup(safeString(row.get("JobGroup")))
                            .payableTravellingDays(travellingDays)
                            .dayAttendance(dayAttendanceMap)
                            .noOfDays(safeString(row.get("NoofDays")))
                            .telephoneNumber(safeString(row.get("Telephone Number")))
                            .rate(BigDecimal.valueOf((Integer)row.get("Rate")))
                            .amount(safeDecimal(row.get("Amount")))
                            .lessLunch(safeDecimal(row.get("Less_lunch_day_500")))
                            .transport(safeDecimal(row.get("Transport")))
                            .extraPerDiem(safeDecimal(row.get("Extra_per_diem")))
                            .others(safeDecimal(row.get("Others")))
                            .netPay(BigDecimal.valueOf((Integer)(row.get("Net Pay"))))
                            .approve(safeString(row.get("Approve")))
                            .comments(safeString(row.get("Comments")))
                            .status("Processed Pending Approval") // Default status for mode 6
                            .processingComments("")
                            .approvalComments("")
                            .attendanceId(safeLong(row.get("ID")))
                            .build());
                }

                // 8️⃣ Build dynamic column headers with only active days (days with attendance > 0)
                dayLabels = new ArrayList<>();
                dayLabels.addAll(List.of("Name", "IDNumber", "County", "Station", "Job Group"));
                dayLabels.add("'Payable Travelling Days'");
                dayLabels.addAll(activeDays); // Only include days with attendance > 0
                dayLabels.addAll(List.of("'No of Days'", "Telephone Number", rateLabel, "Amount", "Less lunch day @500",
                                       "Transport", "Extra per diem", "Others", "Net Pay", "Approve", "Comments", "ID"));
            } else  { // view all attendance
                String meetingCode = request.getMeetingcode();
                orgId = "1"; // Default org ID since MeetingCodesRequest doesn't have orgId field

                // 1️⃣ Get meeting details using native query (matching PHP logic)
                String meetingDetailsQuery = "SELECT (TIMESTAMPDIFF(DAY, start_date, end_date)+1) AS NOofDays, " +
                        "start_date, travelling_days, meeting_type " +
                        "FROM event WHERE meeting_code = :meetingCode LIMIT 1";

                Map<String, Object> meetingParams = Map.of("meetingCode", meetingCode);
                Map<String, Object> meetingDetails = crudService.getSingleResult(meetingDetailsQuery, meetingParams);

                if (meetingDetails == null) {
                    responseBuilder.responseMessage("Meeting not found");
                    responseBuilder.responseCode(ApiResponse.SUCCESS.getCode());
                    return responseBuilder.build();
                }

                int numberOfDays = ((Number) meetingDetails.get("NOofDays")).intValue();
                Date startDate = (Date) meetingDetails.get("start_date");
                int travellingDays = ((Number) meetingDetails.get("travelling_days")).intValue();
                String meetingType = (String) meetingDetails.get("meeting_type");

                // 2️⃣ Build dynamic column headers and attendance query parts (matching PHP logic)
                List<String> meetingDayLabels = new ArrayList<>();
                meetingDayLabels.add("'Payable Travelling Days'");

                StringBuilder didHeAttendQuery = new StringBuilder();
                didHeAttendQuery.append(travellingDays).append(", ");

                // Store day information for filtering later
                List<DayInfo> dayInfoList = new ArrayList<>();

                Calendar cal = Calendar.getInstance();
                cal.setTime(startDate);

                for (int i = 0; i < numberOfDays; i++) {
                    int dayOfMonth = cal.get(Calendar.DAY_OF_MONTH);
                    int month = cal.get(Calendar.MONTH) + 1; // Calendar months are 0-based
                    int year = cal.get(Calendar.YEAR);

                    String monthName = cal.getDisplayName(Calendar.MONTH, Calendar.SHORT, Locale.ENGLISH);
                    String dayLabel = monthName + " " + dayOfMonth;
                    String dateStr = String.format("%04d-%02d-%02d", year, month, dayOfMonth);

                    // Store day info for later filtering
                    dayInfoList.add(new DayInfo(dayOfMonth, dayLabel, dateStr));

                    didHeAttendQuery.append("(SELECT COUNT(*) FROM attendance A WHERE DATE(requesttime)='")
                            .append(dateStr)
                            .append("' AND A.MSISDN=R.msisdn AND verification_status='Approved' AND meeting_code='")
                            .append(meetingCode)
                            .append("') AS '").append(dayOfMonth).append("', ");

                    cal.add(Calendar.DAY_OF_MONTH, 1);
                }

                meetingDayLabels.add("'No of Days'");
                didHeAttendQuery.append("(SELECT (CASE WHEN COUNT(*)>0 THEN COUNT(*)+")
                        .append(travellingDays)
                        .append(" ELSE COUNT(*) END) FROM attendance A WHERE A.MSISDN=R.msisdn AND verification_status='Approved' AND meeting_code='")
                        .append(meetingCode)
                        .append("') AS 'NoofDays', ");

                // 3️⃣ Build attendance IDs subquery
                String attendanceIds = "(SELECT id FROM attendance WHERE recipient_id=R.recipient_id AND meeting_code='" + meetingCode + "')";

                // 4️⃣ Get total amount (matching PHP logic)
                String totalQuery = "SELECT SUM(" +
                        "(SELECT amount FROM perdiem WHERE id=C.designationid AND org_id='" + orgId + "') * " +
                        "(SELECT (CASE WHEN COUNT(*)>0 THEN COUNT(*)+" + travellingDays + " ELSE COUNT(*) END) " +
                        "FROM attendance A WHERE A.MSISDN=R.msisdn AND meeting_code='" + meetingCode + "') - " +
                        "((SELECT (CASE WHEN COUNT(*)>0 THEN COUNT(*) ELSE COUNT(*) END) " +
                        "FROM attendance A WHERE A.MSISDN=R.msisdn AND meeting_code='" + meetingCode + "')*500)" +
                        ") AS 'Total' " +
                        "FROM attendance R INNER JOIN recipient C ON R.recipient_id=C.ID " +
                        "WHERE R.meeting_code='" + meetingCode + "'";
                log.info("Total query: " + totalQuery);

                Map<String, Object> totalResult = crudService.getSingleResult(totalQuery, Map.of());
                total = totalResult != null ? BigDecimal.valueOf(((Number) totalResult.get("Total")).doubleValue()) : BigDecimal.ZERO;

                // 5️⃣ Build main query (matching PHP logic exactly)
                String rateLabel = "DayOnly".equals(meetingType) ? "Lunch Rate" :
                                 ("HalfBoard".equals(meetingType) ? "Dinner Rate" : "Per Diem Rate");
                String keyword = request.getKeyword();
                String statusFilter = "";

                if (keyword != null && !keyword.isEmpty()) {
                    switch (keyword.toLowerCase()) {
                        case "approved":
                            statusFilter = " AND processing_status = 4";
                            break;
                        case "rejected":
                            statusFilter = " AND processing_status = 3";
                            break;
                        case "processed":
                            statusFilter = " AND processing_status IN (1, 4)";
                            break;
                        case "unprocessed":
                            statusFilter = " AND processing_status = 0";
                            break;
                        default:
                            break;
                    }
                }

                String mainQuery = "SELECT " +
                        "(SELECT CONCAT(firstname,' ',middlename,' ',lastname) FROM recipient WHERE id=R.recipient_id) AS Name, " +
                        "(SELECT IDNumber) AS IDNumber, " +
                        "(SELECT title FROM county WHERE county.id=C.countyid) AS NameofCounty, " +
                        "(SELECT title FROM district WHERE id=C.subcountyid) AS Station, " +
                        "(SELECT title FROM jobgroup WHERE id=C.jobgroupid) AS JobGroup, " +
                        didHeAttendQuery.toString() +
                        "R.MSISDN AS 'Telephone Number', " +
                        "(SELECT amount FROM perdiem WHERE id=C.designationid AND org_id='" + orgId + "') AS Rate, " +
                        "(SELECT SUM(credit) FROM temp_supervision_details WHERE title='Per Diem' AND apiorder_id IN " + attendanceIds + ") AS Amount, " +
                        "(SELECT SUM(credit) FROM temp_supervision_details WHERE title='Lunch' AND apiorder_id IN " + attendanceIds + ") AS 'Less_lunch_day_500', " +
                        "(SELECT SUM(credit) FROM temp_supervision_details WHERE title='Transport/Fuel' AND apiorder_id IN " + attendanceIds + ") AS 'Transport', " +
                        "(SELECT SUM(credit) FROM temp_supervision_details WHERE title='Extra Per Diem' AND apiorder_id IN " + attendanceIds + ") AS 'Extra_per_diem', " +
                        "(SELECT SUM(credit) FROM temp_supervision_details WHERE title='Others' AND apiorder_id IN " + attendanceIds + ") AS 'Others', " +
                        "payable_amount_1 AS 'Net Pay', " +
                        "(CASE WHEN processing_status='0' THEN 'Pending Processing' " +
                        "WHEN processing_status='1' THEN 'Processed Pending Approval' " +
                        "WHEN processing_status='3' THEN 'Rejected' " +
                        "WHEN processing_status='4' THEN 'Processed and Approved' END) AS 'Status', " +
                        "processing_comments, approval_comments, R.ID " +
                        "FROM attendance R INNER JOIN recipient C ON R.recipient_id=C.ID " +
                        "WHERE meeting_code='" + meetingCode + "'" + statusFilter + " " +
                        "GROUP BY R.MSISDN";

                // 6️⃣ Execute main query with pagination
                int offset = page * pageSize;
                List<Map<String, Object>> results = crudService.fetchWithNativeQueryAsMap(mainQuery, Map.of(), offset, pageSize);

                // 7️⃣ Process results into data rows and collect active days
                Set<String> activeDays = new LinkedHashSet<>(); // Track days with attendance > 0

                for (Map<String, Object> row : results) {
                    // Create simplified dayAttendance object with ONLY daily attendance counts > 0
                    Map<String, Object> dayAttendanceMap = new LinkedHashMap<>();

                    // Add ONLY daily attendance counts with proper date labels (filter out 0s)
                    for (DayInfo dayInfo : dayInfoList) {
                        Object dayCount = row.get(String.valueOf(dayInfo.dayOfMonth));
                        int attendanceCount = dayCount != null ? ((Number) dayCount).intValue() : 0;

                        // Only include days with attendance count > 0
                        if (attendanceCount > 0) {
                            dayAttendanceMap.put(dayInfo.dayLabel, attendanceCount);
                            activeDays.add("'" + dayInfo.dayLabel + "'"); // Track active days for headers
                        }
                    }

                    data.add(ColumnResizeRow.builder()
                            .name(safeString(row.get("Name")))
                            .idNumber(safeString(row.get("IDNumber")))
                            .county(safeString(row.get("NameofCounty")))
                            .station(safeString(row.get("Station")))
                            .designation("")
                            .jobGroup(safeString(row.get("JobGroup")))
                            .payableTravellingDays(travellingDays)
                            .dayAttendance(dayAttendanceMap)
                            .noOfDays(safeString(row.get("NoofDays")))
                            .telephoneNumber(safeString(row.get("Telephone Number")))
                            .rate(BigDecimal.valueOf((Integer)row.get("Rate")))
                            .amount(safeDecimal(row.get("Amount")))
                            .lessLunch(safeDecimal(row.get("Less_lunch_day_500")))
                            .transport(safeDecimal(row.get("Transport")))
                            .extraPerDiem(safeDecimal(row.get("Extra_per_diem")))
                            .others(safeDecimal(row.get("Others")))
                            .netPay(BigDecimal.valueOf((Integer)row.get("Net Pay")))
                            .status(safeString(row.get("Status")))
                            .processingComments(safeString(row.get("processing_comments")))
                            .approvalComments(safeString(row.get("approval_comments")))
                            .attendanceId(safeLong(row.get("ID")))
                            .build());
                }

                // 8️⃣ Build dynamic column headers with only active days (days with attendance > 0)
                dayLabels = new ArrayList<>();
                dayLabels.addAll(List.of("Name", "IDNumber", "County", "Station", "Job Group"));
                dayLabels.add("'Payable Travelling Days'");
                dayLabels.addAll(activeDays); // Only include days with attendance > 0
                dayLabels.addAll(List.of("'No of Days'", "Telephone Number", rateLabel, "Amount", "Less lunch day @500",
                                       "Transport", "Extra per diem", "Others", "Net Pay", "Status",
                                       "Processing Comments", "Approval Comments", "ID"));
            }

                return responseBuilder
                    .responseCode(ApiResponse.SUCCESS.getCode())
                    .responseMessage("Success")
                    .headers(dayLabels)
                    .total(total)
                    .data(data)
                    .build();

        } catch (Exception e) {
            log.error("Error in getColumRisizeData: ", e);
            return responseBuilder.build();
        }
    }

    private String getRateHeader(String type) {
        return switch (type) {
            case "DayOnly" -> "Lunch Rate";
            case "HalfBoard" -> "Dinner Rate";
            default -> "Per Diem Rate";
        };
    }
        private BigDecimal safeDecimal(Object obj) {
            return obj != null ? (BigDecimal) obj : BigDecimal.ZERO;
        }

        private String safeString(Object obj) {
            return obj != null ? obj.toString() : "";
        }

        private Long safeLong(Object obj) {
            if (obj == null) return null;
            if (obj instanceof Number) {
                return ((Number) obj).longValue();
            }
            try {
                return Long.parseLong(obj.toString());
            } catch (NumberFormatException e) {
                return null;
            }
        }


    public Page<AttendanceSummaryView> getDynamicAttendanceSummary(String meetingCode, int page, int size) {
        Event event = eventRepo.findFirstByMeetingCode(meetingCode)
                .orElseThrow(() -> new RuntimeException("Meeting not found for meetingCode: " + meetingCode));

        Pageable pageable = PageRequest.of(page, size);

        return attendanceSummaryViewRepository.findByMeetingCode(meetingCode, pageable);
    }






}



