package com.tangazoletu.tibuPortalEngine.service;


import com.tangazoletu.tibuPortalEngine.dto.ApiDto;
import com.tangazoletu.tibuPortalEngine.exceptions.EncryptionException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Base64;

/*
    Encryption service - AES
 */
@Service
@Slf4j
public class AesEncryptionService {
    @Value("${encryption.key}")
    private String secret;

    private static final String ALGO = "AES/CBC/PKCS5Padding"; // De<PERSON>ult uses ECB PKCS5Padding

    /*
        Encryption logic wrapped in a private helper method
        Logging and exception handling are centralized
     */
    private String encryptData(String data, String keyString) throws EncryptionException {
        try {
            Key secretKey = generateKey(keyString);

            // Generate random 16-byte IV
            byte[] ivBytes = new byte[16];
            new SecureRandom().nextBytes(ivBytes);
            IvParameterSpec iv = new IvParameterSpec(ivBytes);

            Cipher cipher = Cipher.getInstance(ALGO);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, iv);
            byte[] encVal = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));

            // Concatenate IV + ciphertext
            byte[] combined = new byte[ivBytes.length + encVal.length];
            System.arraycopy(ivBytes, 0, combined, 0, ivBytes.length);
            System.arraycopy(encVal, 0, combined, ivBytes.length, encVal.length);

            // Base64 encode and URL encode
            String base64Encoded = Base64.getEncoder().encodeToString(combined);
            return URLEncoder.encode(base64Encoded, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new EncryptionException("Failed to encrypt data", e);
        }
    }

    /*
       Method to encrypt a string.
       Only accepts a string as a parameter.
       The encryption key is fetched from the application variables.
     */
    public String encrypt(String data) throws EncryptionException {
        return encryptData(data, secret);
    }
    /*
        Overload the encrypt method to accept encryption key as a param.
    */
    public String encrypt(String data, String encryptionKey) throws EncryptionException {
        return encryptData(data, encryptionKey);
    }

    /*
        Decryption logic wrapped in a private helper method
        Logging and exception handling are centralized
     */
    private String decryptData(String encryptedData, String keyString) throws EncryptionException {
        try {
            // Convert Base64 URL-safe back to standard Base64
            String base64String = encryptedData
                    .replace('-', '+')
                    .replace('_', '/');

            int padding = (4 - base64String.length() % 4) % 4;
            base64String += "=".repeat(padding);

            byte[] combined = Base64.getDecoder().decode(base64String);

            // Extract IV (first 16 bytes)
            byte[] ivBytes = Arrays.copyOfRange(combined, 0, 16);
            IvParameterSpec iv = new IvParameterSpec(ivBytes);

            // Extract ciphertext
            byte[] encryptedBytes = Arrays.copyOfRange(combined, 16, combined.length);

            // Generate secret key
            Key secretKey = generateKey(keyString);

            Cipher cipher = Cipher.getInstance(ALGO); // AES/CBC/PKCS5Padding
            cipher.init(Cipher.DECRYPT_MODE, secretKey, iv);

            byte[] originalBytes = cipher.doFinal(encryptedBytes);

            System.out.println("Decrypted: " + new String(originalBytes, StandardCharsets.UTF_8));

            return new String(originalBytes, StandardCharsets.UTF_8);

        } catch (Exception e) {
            throw new EncryptionException("Failed to decrypt data", e);
        }
    }


    public byte[] decryptFileFromMemory(String encryptedFile, String keyString)
            throws EncryptionException {
        try{
            // Convert Base64 URL-safe back to standard Base64
            String base64String = encryptedFile
                    .replace('-', '+')
                    .replace('_', '/');

            int padding = (4 - base64String.length() % 4) % 4;
            base64String += "=".repeat(padding);

            byte[] combined = Base64.getDecoder().decode(base64String);

            // Extract IV (first 16 bytes)
            byte[] ivBytes = Arrays.copyOfRange(combined, 0, 16);
            IvParameterSpec iv = new IvParameterSpec(ivBytes);

            // Extract ciphertext
            byte[] encryptedBytes = Arrays.copyOfRange(combined, 16, combined.length);

            // Generate secret key
            Key secretKey = generateKey(keyString);

            Cipher cipher = Cipher.getInstance(ALGO); // AES/CBC/PKCS5Padding
            cipher.init(Cipher.DECRYPT_MODE, secretKey, iv);
            byte [] file = cipher.doFinal(encryptedBytes);

            // Convert decrypted bytes to base64 string
            String base64Decrypted = new String(file, StandardCharsets.UTF_8);

            // Convert base64 string back to binary
            byte[] fileBytes = Base64.getDecoder().decode(base64Decrypted);


            System.out.println("Decrypted length: " + fileBytes.length);
            return fileBytes;
        } catch (Exception e) {
            throw new EncryptionException("Failed to decrypt data", e);
        }
    }

    /*
        Method to decrypt a string.
        Only accepts a string as a parameter.
        The encryption key is fetched from the application variables.
     */
    public String decrypt(String strToDecrypt) throws EncryptionException {
        return decryptData(strToDecrypt, secret);
    }
    /*
        Overload the decrypt method to accept encryption key as a param.
     */
    public String decrypt(String strToDecrypt, String encryptionKey) throws EncryptionException {
        return decryptData(strToDecrypt, encryptionKey);
    }
    /*
        Decrypt method to accept a file as a param.
     */
    public byte[] decryptFile(String fileToDecrypt) throws EncryptionException {
        return decryptFileFromMemory(fileToDecrypt, secret);
    }

    public boolean isRequestValid(ApiDto encryptedRequest){
        String encryptedData = encryptedRequest.getPayload();
        return encryptedData != null && !encryptedData.isEmpty();
    }

    private Key generateKey(String secret) {
        byte[] keyBytes = hexStringToByteArray(secret);
        return new SecretKeySpec(keyBytes, "AES");
    }

    // Helper to convert hex string to byte array
    private static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte)
                    ((Character.digit(s.charAt(i), 16) << 4)
                            + Character.digit(s.charAt(i+1), 16));
        }
        return data;
    }
}
