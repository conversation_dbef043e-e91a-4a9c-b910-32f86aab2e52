package com.tangazoletu.tibuPortalEngine.service;

import com.tangazoletu.tibuPortalEngine.dto.PaymentAPiResponse;
import com.tangazoletu.tibuPortalEngine.dto.paymentapidto.NonSupervisionPaymentRequestDTO;
import com.tangazoletu.tibuPortalEngine.dto.paymentapidto.PaymentRequestDTO;
import com.tangazoletu.tibuPortalEngine.dto.paymentapidto.PaymentRequestResponse;
import com.tangazoletu.tibuPortalEngine.dto.paymentapidto.SupervisionRequestDto;
import com.tangazoletu.tibuPortalEngine.entities.AppLog;
import com.tangazoletu.tibuPortalEngine.entities.ReceiptUploadLog;
import com.tangazoletu.tibuPortalEngine.repositories.*;
import com.tangazoletu.tibuPortalEngine.util.SharedFunctions;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class PaymentApiService {

    private final AppLogRepository appLogRepository;
    private final ApiLogRepository apiLogRepository;
    private final SharedFunctions sharedFunctions;
    private final CrudService crudService;
    private final ReceiptUploadLogRepository receiptUploadLogRepository;
    private final OrderTypeRepo orderTypeRepo;
    private final TrxValueRepository trxValueRepository;

    @Value("${receipts.uploadPath}")
    private String receiptsUploadPath;

    public PaymentAPiResponse processSupervision(SupervisionRequestDto supervisionRequestDto, HttpServletRequest request, HttpServletResponse response) {
        PaymentAPiResponse paymentAPiResponse = PaymentAPiResponse.builder()
                .isPayment_Sent("0")
                .build();
        String requestTime = LocalDateTime.now().toString();
        Long orderId = null;

        try {
            String queryString = request.getQueryString();

            // Read raw body (like file_get_contents('php://input'))
            StringBuilder postDataBuilder = new StringBuilder();
            try (BufferedReader reader = request.getReader()) {
                String line;
                while ((line = reader.readLine()) != null) {
                    postDataBuilder.append(line);
                }
            }
            String postdata = postDataBuilder.toString().replace("'", "");

            // Determine request type
            String logComment = request.getMethod().equalsIgnoreCase("POST") ? "Inserted via POST" : "Inserted via GET";

            // Insert into app_logs (similar to mysqli_query)
            AppLog appLog = AppLog.builder()
                    .rawdata(queryString)
                    .sourceip(sharedFunctions.getSourceIp(request))
                    .paymentType("SUPERVISION")
                    .requesttime(LocalDateTime.now())
                    .comment(logComment)
                    .status("INITIALIZED")
                    .build();
            appLogRepository.save(appLog);

            // Whitelist check
            String ipStatus = sharedFunctions.checkIfIpIsWhitelisted(request, postdata);
            if (!"OK".equals(ipStatus)) {
                log.warn("IP is not whitelisted. Aborting.");
                return paymentAPiResponse; // exit early like PHP `exit`
            }

            // Check for invalid line items
            boolean invalid = false;
            String comment = "";

            List<Map<String, Object>> itemsPre = supervisionRequestDto.getSupItems();
            if (itemsPre != null) {
                for (Map<String, Object> item : itemsPre) {
                    Object expenseId = item.get("Expense_Id");
                    Object url = item.get("URL");

                    if ("0".equals(String.valueOf(expenseId)) || url == null || "null".equalsIgnoreCase(String.valueOf(url)) || String.valueOf(url).trim().isEmpty()) {
                        invalid = true;
                    }
                }
            }

            int originalLineItemsCount = (itemsPre != null) ? itemsPre.size() : 0;
            // 1. Fetch base PMS links
            String baseLinkQuery = "SELECT value FROM PARAM WHERE parameter='NEW_PMS_API_LINKS'";
            List<String> baseLinkResult = crudService.fetchWithNativeQuery(baseLinkQuery);
            String baseLinkValue = baseLinkResult != null && !baseLinkResult.isEmpty() ? baseLinkResult.get(0) : "";
            String[] baseUrls = baseLinkValue.split(",");

            // 2. Validate download URLs
            List<String> fileLinks = new ArrayList<>();
            for (Map<String, Object> item : itemsPre) {
                String downloadUrl = String.valueOf(item.get("Download_URL"));
                String expenseId = String.valueOf(item.get("Expense_Id"));

                if (!("-1".equals(expenseId) && (downloadUrl == null || downloadUrl.trim().isEmpty())) &&
                        !(sharedFunctions.startsWithAny(downloadUrl, baseUrls))) {

                    fileLinks.add("Invalid " + downloadUrl);

                    // Insert to receipt_upload_logs

                    ReceiptUploadLog receiptUploadLog = ReceiptUploadLog.builder()
                            .rawdata(postdata)
                            .sourceip(sharedFunctions.getSourceIp(request))
                            .comment("Invalid file type for " + downloadUrl)
                            .build();
                    receiptUploadLogRepository.save(receiptUploadLog);



                    // Compose early return response
                    String commentMsg = "Supervision: " + supervisionRequestDto.getPayment_id() +
                            "  Missing download url on  Item: " + item.get("Name");

                    paymentAPiResponse.setIsPayment_Sent("0");
                    paymentAPiResponse.setResponse_Code("500");
                    paymentAPiResponse.setResponse_Value(commentMsg);
                    return paymentAPiResponse;
                }
            }

            // 3. Get org_id from ordertype
            String orgQuery = "SELECT org_id FROM ordertype WHERE title = 'Supervision'";
            List<Integer> orgResult = crudService.fetchWithNativeQuery(orgQuery);
            String orgId = orgResult != null && !orgResult.isEmpty() ? String.valueOf(orgResult.get(0)) : null;

            // 4. Validate phone number
            String msisdnCheck1 = sharedFunctions.validatePaymentsPhoneNumber(supervisionRequestDto.getMsisdn());

            // 5. Check mandatory SUP items
            Object[] res = sharedFunctions.validateMandatorySUPItems(itemsPre);
            int supItemCount = Integer.parseInt(String.valueOf(res[0]));
            String missingItems = String.valueOf(res[1]);

            if (supItemCount < 5) {
                String commentMsg = "Supervision: " + supervisionRequestDto.getPayment_id() + "  Missing Mandatory Item(s): " + missingItems;
                paymentAPiResponse.setIsPayment_Sent("0");
                paymentAPiResponse.setResponse_Code("800");
                paymentAPiResponse.setResponse_Value(commentMsg);
                return paymentAPiResponse;
            } else if ("Invalid".equals(msisdnCheck1)) {
                String commentMsg = "Supervision: " + supervisionRequestDto.getPayment_id() + "  Invalid phone Number: " + supervisionRequestDto.getMsisdn();
                paymentAPiResponse.setIsPayment_Sent("0");
                paymentAPiResponse.setResponse_Code("400");
                paymentAPiResponse.setResponse_Value(commentMsg);
                return paymentAPiResponse;
            } else if (!invalid) {
                String msisdn = supervisionRequestDto.getMsisdn();

                // Normalize MSISDN
                if ("Replace".equals(msisdnCheck1)) {
                    msisdn = msisdn.replace("25407", "2547");
                } else if ("Replace1".equals(msisdnCheck1)) {
                    msisdn = msisdn.replace("25401", "2541");
                }

                // Get payment_id, fallback if needed
                String paymentId = supervisionRequestDto.getPayment_id();
                if (paymentId == null || paymentId.isEmpty()) {
                    paymentId = supervisionRequestDto.getPayment_id(); // or fetch from request param if needed
                }

                String notes = supervisionRequestDto.getNotes().replace("'", "''");

                // Check for duplicate
                String duplicateQuery = """
                            SELECT id FROM apiorder 
                            WHERE payment_id = :payment_id 
                            AND intrash = 'No' 
                            AND treatment_model IN (SELECT id FROM treatment_model WHERE title = 'Supervision Request') 
                            AND (resubmission_status IS NULL OR resubmission_status != 'In Progress')
                        """;
                Map<String, Object> params = new HashMap<>();
                params.put("payment_id", paymentId);
                List<Object[]> duplicateResult = crudService.fetchWithNativeQuery(duplicateQuery, params, 0, 1);

                if (duplicateResult.isEmpty() && paymentId != null && !paymentId.isEmpty()) {
                    // 1. Ordertype
                    String ordertypeQuery = "SELECT id FROM ordertype WHERE title = 'Supervision'";
                    List<Integer> ordertypeResult = crudService.fetchWithNativeQuery(ordertypeQuery);
                    String ordertype = ordertypeResult.get(0).toString();

                    // 2. Treatment Model
                    String treatmentModelQuery = "SELECT id FROM treatment_model WHERE title = 'Supervision Request'";
                    List<Integer> treatmentModelResult = crudService.fetchWithNativeQuery(treatmentModelQuery);
                    String treatmentModel = treatmentModelResult.get(0).toString();

                    // 3. Budget
                    String budgetTitle = "HCW".equalsIgnoreCase(supervisionRequestDto.getPayment_due_to())
                            ? "Non Supervision Budget"
                            : "6.2.1 TB Program Supervision at all level";
                    String budgetQuery = "SELECT id FROM budget WHERE title = :title";
                    Map<String, Object> params3 = new HashMap<>();
                    params3.put("title", budgetTitle);
                    List<Integer> budgetResult = crudService.fetchWithNativeQuery(budgetQuery, params3, 0, 1);
                    String budget = budgetResult.get(0).toString();

                    // 4. Approval Order / Level
                    String approvalQuery = """
                            SELECT approvalOrder FROM org_ordertype 
                            WHERE ordertypeId = :ordertypeId AND org_id =:orgId
                            """;
                    Map<String, Object> params4 = new HashMap<>();
                    params4.put("ordertypeId", ordertype);
                    params4.put("orgId", orgId);
                    List<String> approvalResult = crudService.fetchWithNativeQuery(approvalQuery, params4, 0, 1);
                    String approvalOrder = approvalResult.get(0).toString();
                    String[] levels = approvalOrder.split(",");
                    String currentLevel = levels[0];

                    // 5. Resubmission logic
                    int approval_level = 0;
                    String r_comment = "";
                    String childOf = "";
                    if (supervisionRequestDto.getResubmission_status() != null && !supervisionRequestDto.getResubmission_status().isEmpty()) {
                        String resubQuery = """
                                    SELECT id, approval_level FROM apiorder 
                                    WHERE payment_id = :payment_id AND intrash = 'No' 
                                    AND treatment_model IN (SELECT id FROM treatment_model WHERE title = 'Supervision Request') 
                                    AND resubmission_status = 'In Progress'
                                """;
                        Map<String, Object> params5 = new HashMap<>();
                        params5.put("payment_id", paymentId);
                        List<Object[]> resubResult = crudService.fetchWithNativeQuery(resubQuery, params5, 0, 1);
                        if (!resubResult.isEmpty()) {
                             childOf = resubResult.get(0)[0].toString();
                            approval_level = Integer.parseInt(resubResult.get(0)[1].toString());

                            // Update old record
                            String updateQuery = """
                                        UPDATE apiorder 
                                        SET resubmission_status = ?, approval_status = 'Stopped', 
                                            resubmission_comments = 'Request Marked as Complete new Request created after Successful resubmission' 
                                        WHERE id = :payment_id
                                    """;
                            Map<String, Object> params6 = new HashMap<>();
                            params6.put("payment_id", childOf);
                            crudService.executeNativeQuery(updateQuery, params6);

                            r_comment = "(Resubmission of " + childOf + ")";
                        }
                    }
                    // Insert into beneficiary if not exists
                    String insertBeneficiaryQuery = """
                        INSERT INTO beneficiary (
                            pmsid, firstname, lastname, middlename, beneficiarytype, county, province, district, 
                            resubmission_status, child_of, approval_level, msisdn, org_id, sourceip, creationtime
                        )
                        SELECT :pmsid, :firstname, '', '', id, :county, :province, :district, 
                               :resubmission_status, :child_of, :approval_level, :msisdn, :org_id, :sourceip, :creationtime
                        FROM beneficiarytype 
                        WHERE title = 'dtlc'
                          AND NOT EXISTS (
                              SELECT 1 FROM beneficiary WHERE msisdn = :msisdn_check
                          )
                    """;

                    Map<String, Object> beneficiaryParams = new HashMap<>();
                    beneficiaryParams.put("pmsid", supervisionRequestDto.getSupervisor_pms_id());
                    beneficiaryParams.put("firstname", supervisionRequestDto.getSupervisor_name());
                    beneficiaryParams.put("county", supervisionRequestDto.getCounty_id());
                    beneficiaryParams.put("province", supervisionRequestDto.getProvince_id());
                    beneficiaryParams.put("district", supervisionRequestDto.getDistrict_id());
                    beneficiaryParams.put("resubmission_status",
                            supervisionRequestDto.getResubmission_status() == null ? "" : supervisionRequestDto.getResubmission_status());
                    beneficiaryParams.put("child_of", childOf);
                    beneficiaryParams.put("approval_level", approval_level);
                    beneficiaryParams.put("msisdn", msisdn);
                    beneficiaryParams.put("org_id", orgId);
                    beneficiaryParams.put("sourceip", sharedFunctions.getSourceIp(request));
                    beneficiaryParams.put("creationtime", Instant.now().getEpochSecond());
                    beneficiaryParams.put("msisdn_check", msisdn); // For the NOT EXISTS subquery

                    crudService.executeNativeQuery(insertBeneficiaryQuery, beneficiaryParams);


                    // Fetch existing payment_ids for same year and month
                    String paymentQuery = """
                    SELECT id FROM apiorder 
                    WHERE month = :month AND year = :year AND msisdn = :msisdn
                    """;

                    Map<String, Object> paymentParams = new HashMap<>();
                    paymentParams.put("month", supervisionRequestDto.getMonth());
                    paymentParams.put("year", supervisionRequestDto.getYear());
                    paymentParams.put("msisdn", msisdn);

                    List<Integer> existingPayments = crudService.fetchWithNativeQuery(paymentQuery, paymentParams,0,1);

                    String paymentIDs = existingPayments.stream()
                            .map(row -> row.toString())
                            .collect(Collectors.joining(","));

                    if (paymentIDs.isEmpty()) {
                        paymentIDs = "0";
                    }


                    String zoneId = (supervisionRequestDto.getZone_id() == null || supervisionRequestDto.getZone_id().isEmpty())
                            ? "0" : supervisionRequestDto.getZone_id().toString();


                    // Fetch beneficiary details
                    String benDetailsQuery = """
                        SELECT b.id, firstname, middlename, lastname, c.title, p.title, d.title 
                        FROM beneficiary b
                        LEFT JOIN county c ON b.county = c.id
                        LEFT JOIN province p ON b.province = p.id
                        LEFT JOIN district d ON b.district = d.id
                        WHERE b.msisdn = :msisdn
                    """;

                    Map<String, Object> benDetailsParams = new HashMap<>();
                    benDetailsParams.put("msisdn", msisdn);

                    List<Object[]> beneficiaryDetails = crudService.fetchWithNativeQuery(benDetailsQuery, benDetailsParams,0,1);

                    String firstname = supervisionRequestDto.getSupervisor_name();
                    String middlename = "";
                    String lastname = "";
                    if (!beneficiaryDetails.isEmpty()) {
                        middlename = String.valueOf(beneficiaryDetails.get(0)[2]);
                        lastname = String.valueOf(beneficiaryDetails.get(0)[3]);
                    }

                    String insertApiOrder = """
                        INSERT INTO apiorder (
                            treatment_model, initiator_id, initiator_username, firstname, middlename, lastname, 
                            ordertype, budget, credit, msisdn, notes, county, province, district, zone_id,
                            request_src, requesttime, approval_level, approval_status, sourceip, latitude, longitude,
                            year, month, payment_id, org_id, attached_to, payment_due_to
                        ) VALUES (
                            :treatment_model, :initiator_id, :initiator_username, :firstname, :middlename, :lastname,
                            :ordertype, :budget, :credit, :msisdn, :notes, :county, :province, :district, :zone_id,
                            :request_src, :requesttime, :approval_level, 'Pending', :sourceip, :latitude, :longitude,
                            :year, :month, :payment_id, :org_id, :attached_to, :payment_due_to
                        )
                    """;

                    Map<String, Object> apiOrderParams = new HashMap<>();
                    apiOrderParams.put("treatment_model", treatmentModel);
                    apiOrderParams.put("initiator_id", supervisionRequestDto.getSupervisor_pms_id());
                    apiOrderParams.put("initiator_username", supervisionRequestDto.getSupervisor_name());
                    apiOrderParams.put("firstname", firstname);
                    apiOrderParams.put("middlename", middlename);
                    apiOrderParams.put("lastname", lastname);
                    apiOrderParams.put("ordertype", ordertype);
                    apiOrderParams.put("budget", budget);
                    apiOrderParams.put("credit", supervisionRequestDto.getTotal_amount());
                    apiOrderParams.put("msisdn", msisdn);
                    apiOrderParams.put("notes", notes);
                    apiOrderParams.put("county", supervisionRequestDto.getCounty_id());
                    apiOrderParams.put("province", supervisionRequestDto.getProvince_id());
                    apiOrderParams.put("district", supervisionRequestDto.getDistrict_id());
                    apiOrderParams.put("zone_id", zoneId);
                    apiOrderParams.put("request_src", "api");
                    apiOrderParams.put("requesttime", requestTime);
                    apiOrderParams.put("approval_level", currentLevel);
                    apiOrderParams.put("sourceip", sharedFunctions.getSourceIp(request));
                    apiOrderParams.put("latitude", supervisionRequestDto.getLatitude());
                    apiOrderParams.put("longitude", supervisionRequestDto.getLongitude());
                    apiOrderParams.put("year", supervisionRequestDto.getYear());
                    apiOrderParams.put("month", supervisionRequestDto.getMonth());
                    apiOrderParams.put("payment_id", supervisionRequestDto.getPayment_id());
                    apiOrderParams.put("org_id", orgId);
                    apiOrderParams.put("attached_to", paymentIDs);
                    apiOrderParams.put("payment_due_to", supervisionRequestDto.getPayment_due_to());

                    crudService.executeNativeQuery(insertApiOrder, apiOrderParams);

                    // ✅ Retrieve the inserted apiorder (by timestamp + msisdn)
                    String fetchInsertedOrder = """
                        SELECT id, msisdn, credit, driver_amount, driver_phone, recipient2credit, recipient2msisdn 
                        FROM apiorder 
                        WHERE requesttime = :requestTime AND msisdn = :msisdn 
                        ORDER BY id DESC 
                    """;


                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    String formattedRequestTime = LocalDateTime.parse(requestTime).format(formatter);

                    Map<String, Object> latestParams = Map.of("requestTime", formattedRequestTime, "msisdn", msisdn);
                    List<Object[]> latestResult = crudService.fetchWithNativeQuery(fetchInsertedOrder, latestParams, 0, 1);
                    orderId = latestResult.isEmpty() ? null : Long.parseLong(latestResult.get(0)[0].toString());

                    // Continue from here with insert into `apiorder`, logging, and response
                    if (orderId != null) {
                        // Build map for hash generation
                        // 🚀 1. Build transaction map and generate transaction chain
                        HashMap<String, String> trxMap = new HashMap<>();
                        trxMap.put("id", String.valueOf(orderId));
                        trxMap.put("msisdn", (String) latestResult.get(0)[1]);
                        BigDecimal creditValue = (BigDecimal) latestResult.get(0)[2];
                        trxMap.put("credit", creditValue.toPlainString()); // More accurate for financial data
                        BigDecimal driverAmount = (BigDecimal) latestResult.get(0)[3];
                        trxMap.put("driver_amount", driverAmount.toPlainString());
                        trxMap.put("driver_phone", (String) latestResult.get(0)[4]);
                        BigDecimal recipient2CreditValue = (BigDecimal) latestResult.get(0)[5];
                        trxMap.put("recipient2credit", recipient2CreditValue.toPlainString());
                        trxMap.put("recipient2msisdn", (String) latestResult.get(0)[6]);

                        String trxChain = sharedFunctions.updateChainTrx(null, trxMap, orderId.toString(), null);

                        // 🚀 2. Insert into TRX_VALUES
                        String trxInsertQuery = "INSERT INTO TRX_VALUES(ORDER_ID, T_VALUE,DATE_CREATED) VALUES(:orderId, :trxChain, :dateCreated)";
                        Map<String, Object> trxParams = Map.of(
                                "orderId", orderId,
                                "trxChain", trxChain,
                                "dateCreated", requestTime
                        );
                        crudService.executeNativeQuery(trxInsertQuery, trxParams);

                        // 🚀 3. Audit the creation
                        sharedFunctions.auditAction("CREATED", "Created new order transaction id"+ orderId, sharedFunctions.getSourceIp(request), null, null, null,null);

                        // 🚀 4. Download receipts to /var/www/html/recipients
                        List<Map<String, Object>> items = supervisionRequestDto.getSupItems();

                        String homeLinkQuery = "SELECT value FROM PARAM WHERE parameter='SYSTEM_HOME_LINK'";
                        List<String> systemUrlRow = crudService.fetchWithNativeQuery(homeLinkQuery);
                        String systemUrl = systemUrlRow.isEmpty() ? "" : String.valueOf(systemUrlRow.get(0));
                        // Get the target directory from yml

                        String targetDir = receiptsUploadPath;

                        for (Map<String, Object> item : items) {
                            String itemName = Optional.ofNullable((String) item.get("Name")).orElse((String) item.get("name"));
                            if ("Expensive Sheet".equalsIgnoreCase(itemName)) {
                                itemName = "Expense Sheet";
                            }

                            String downloadUrl = String.valueOf(item.get("Download_URL"));
                            String fileNameRaw = String.valueOf(item.get("File_Receipt_Name"));
                            String expenseId = String.valueOf(item.get("Expense_Id"));
                            if (fileNameRaw == null || fileNameRaw.equals("null") || fileNameRaw.isBlank()) {
                                // Extract filename from the URL if File_Receipt_Name is not provided
                                fileNameRaw = downloadUrl.substring(downloadUrl.lastIndexOf('/') + 1); // e.g. 40ab8772...jpg
                            }
                            String fileName = orderId + "_" + expenseId + "_" + fileNameRaw;

                            List<String> allowedTypes = List.of("jpg", "png", "xls", "pdf", "xlsx", "jpeg");
                            String localFilePath = targetDir + fileName;
                            String fileExtension = FilenameUtils.getExtension(fileName).toLowerCase();
                            boolean isExtensionAllowed = allowedTypes.contains(fileExtension);

                            String localUrl = "";

                            // Attempt file download (but allow record insert regardless)
                            if (!downloadUrl.isBlank()) {
                                try (InputStream in = new URL(downloadUrl).openStream()) {
                                    Files.copy(in, Paths.get(localFilePath), StandardCopyOption.REPLACE_EXISTING);

                                    if (!isExtensionAllowed) {
                                        Files.deleteIfExists(Paths.get(localFilePath));
                                        log.warn("Invalid file type for URL: " + downloadUrl);

                                        // Log invalid extension
                                        String logQuery = "INSERT INTO receipt_upload_logs (apiorder_id, rawdata, sourceip, comment) VALUES(:id, :data, :ip, :comment)";
                                        Map<String, Object> logParams = Map.of(
                                                "id", orderId,
                                                "data", postdata,
                                                "ip", sharedFunctions.getSourceIp(request),
                                                "comment", "Invalid file extension for URL: " + downloadUrl
                                        );
                                        crudService.executeNativeQuery(logQuery, logParams);
                                    } else {
                                        localUrl = systemUrl + "receipts/" + fileName;
                                    }
                                } catch (Exception e) {
                                    log.error("File download failed for URL: " + downloadUrl, e);

                                    // Log download error
                                    String logQuery = "INSERT INTO receipt_upload_logs (apiorder_id, rawdata, sourceip, comment) VALUES(:id, :data, :ip, :comment)";
                                    Map<String, Object> logParams = Map.of(
                                            "id", orderId,
                                            "data", postdata,
                                            "ip", sharedFunctions.getSourceIp(request),
                                            "comment", "File download failed: " + e.getMessage()
                                    );
                                    crudService.executeNativeQuery(logQuery, logParams);
                                }
                            }

                            // Insert into supervision_details regardless of download outcome
                            Map<String, Object> insertParams = new HashMap<>();
                            insertParams.put("orderId", orderId);
                            insertParams.put("expenseId", expenseId);
                            insertParams.put("title", itemName);
                            insertParams.put("url", item.get("URL"));
                            insertParams.put("localUrl", localUrl);

                            String insertQuery;
                            if (item.containsKey("Amount")) {
                                insertParams.put("credit", item.get("Amount"));
                                insertParams.put("originalAmount", item.get("Amount"));

                                insertQuery = """
                                    INSERT INTO supervision_details(apiorder_id, expense_id, title, credit, original_amount, url, local_url)
                                    VALUES(:orderId, :expenseId, :title, :credit, :originalAmount, :url, :localUrl)
                                """;
                            } else {
                                insertQuery = """
                                    INSERT INTO supervision_details(apiorder_id, expense_id, title, url, local_url)
                                    VALUES(:orderId, :expenseId, :title, :url, :localUrl)
                                """;
                            }

                            crudService.executeNativeQuery(insertQuery, insertParams);
                        }


                        // Continue here: Handle driver_details if provided
                        // List<Map<String, Object>> driverDetails = supervisionRequestDto.getDriver_details(); // if added in DTO
                        List<Map<String, Object>> driverDetails = supervisionRequestDto.getSup_driver_details(); // Already parsed
                        int dr = 0;

                        if (driverDetails != null && !driverDetails.isEmpty()) {
                            for (Map<String, Object> driverItem : driverDetails) {
                                String driverName = String.valueOf(driverItem.get("Driver_Name"));
                                String driverPhone = String.valueOf(driverItem.get("Driver_Mobile_No"));
                                String driverAmount1 = String.valueOf(driverItem.get("Driver_Amount"));

                                // Insert driver details
                                String insertDriverQuery = """
                                    INSERT INTO drivers_details (apiorder_id, name, phone_number, amount)
                                    VALUES (:orderId, :name, :phone, :amount)
                                """;
                                Map<String, Object> driverParams = Map.of(
                                        "orderId", orderId,
                                        "name", driverName,
                                        "phone", driverPhone,
                                        "amount", driverAmount1
                                );
                                crudService.executeNativeQuery(insertDriverQuery, driverParams);

                                // First driver update to apiorder
                                if (dr == 0) {
                                    String driverPhoneFormatted = driverPhone.length() < 12 ? "+254" + driverPhone : driverPhone;

                                    String updateOrderDriverQuery = """
                                        UPDATE apiorder 
                                        SET driver_name = :name, 
                                            driver_amount = :amount, 
                                            original_credit = credit, 
                                            driver_phone = :phone 
                                        WHERE id = :orderId
                                    """;

                                    Map<String, Object> updateParams = Map.of(
                                            "name", driverName,
                                            "amount", supervisionRequestDto.getTotal_driver_amount(),
                                            "phone", driverPhoneFormatted,
                                            "orderId", orderId
                                    );
                                    crudService.executeNativeQuery(updateOrderDriverQuery, updateParams);
                                }
                                dr++;
                            }
                        }
                        String updateConfirmationQuery = """
                            UPDATE apiorder
                            SET 
                                Confirmed_Beneficairy = (
                                    CASE 
                                        WHEN (SELECT COUNT(*) FROM confirmed_recipient WHERE msisdn = apiorder.msisdn OR msisdn = CONCAT('+', apiorder.msisdn)) > 0 
                                        THEN 'Beneficiary Confirmed' 
                                        ELSE 'Beneficiary Not Confirmed' 
                                    END
                                ),
                                Confirmed_Driver = (
                                    CASE 
                                        WHEN (SELECT COUNT(*) FROM confirmed_recipient WHERE msisdn = apiorder.driver_phone OR msisdn = CONCAT('+', apiorder.driver_phone)) > 0 
                                        THEN CONCAT(
                                            'Confirmed Driver:(',
                                            (SELECT Full_name FROM confirmed_recipient WHERE msisdn = apiorder.driver_phone OR msisdn = CONCAT('+', apiorder.driver_phone) LIMIT 1),
                                            '(',
                                            (SELECT msisdn FROM confirmed_recipient WHERE msisdn = apiorder.driver_phone OR msisdn = CONCAT('+', apiorder.driver_phone) LIMIT 1),
                                            '))'
                                        ) 
                                        ELSE 
                                            CASE 
                                                WHEN driver_phone IS NULL OR driver_phone = '' 
                                                THEN '' 
                                                ELSE 'Driver Not Confirmed' 
                                            END 
                                    END
                                ),
                                Confirmed_DOT = (
                                    CASE 
                                        WHEN (SELECT COUNT(*) FROM confirmed_recipient WHERE msisdn = apiorder.recipient2msisdn OR msisdn = CONCAT('+', apiorder.recipient2msisdn)) > 0 
                                        THEN CONCAT(
                                            'Confirmed DOT:(',
                                            (SELECT Full_name FROM confirmed_recipient WHERE msisdn = apiorder.recipient2msisdn OR msisdn = CONCAT('+', apiorder.recipient2msisdn) LIMIT 1),
                                            '(',
                                            (SELECT msisdn FROM confirmed_recipient WHERE msisdn = apiorder.recipient2msisdn OR msisdn = CONCAT('+', apiorder.recipient2msisdn) LIMIT 1),
                                            '))'
                                        ) 
                                        ELSE 
                                            CASE 
                                                WHEN recipient2msisdn IS NULL OR recipient2msisdn = '' 
                                                THEN '' 
                                                ELSE 'DOT Not Confirmed' 
                                            END 
                                    END
                                ),
                                original_line_items_num = :lineItemCount
                            WHERE id = :orderId
                        """;

                        Map<String, Object> confirmParams = Map.of(
                                "lineItemCount", originalLineItemsCount,
                                "orderId", orderId
                        );

                        crudService.executeNativeQuery(updateConfirmationQuery, confirmParams);


                    }
                    paymentAPiResponse.setIsPayment_Sent("1");
                    paymentAPiResponse.setResponse_Code("200");
                    paymentAPiResponse.setResponse_Value("Successful supervision " + r_comment);
                    return paymentAPiResponse;
                }else {
                    // Check if request was already processed
                    String checkProcessedQuery = """
                        SELECT apiorder_id 
                        FROM api_log 
                        WHERE apiorder_id != '0' AND rawdata = :postdata
                    """;
                    Map<String, Object> processedParams = Map.of(
                            "postdata", postdata
                    );
                    List<Object[]> processedResult = crudService.fetchWithNativeQuery(checkProcessedQuery, processedParams, 0, 1);
                    String comment1 = "";
                    if (!processedResult.isEmpty()) {
                       comment1 = "Successful Supervision";
                        paymentAPiResponse.setIsPayment_Sent("1");
                        paymentAPiResponse.setResponse_Code("200");
                        paymentAPiResponse.setResponse_Value("Successful");
                    } else if (paymentId == null || paymentId.trim().isEmpty()) {
                        comment1 = "Bad Request. Request elements could not be read";
                        paymentAPiResponse.setIsPayment_Sent("0");
                        paymentAPiResponse.setResponse_Code("400");
                        paymentAPiResponse.setResponse_Value("Bad Request. Request elements could not be read");
                    } else {
                         comment1= "Duplicate payment detected";
                        paymentAPiResponse.setIsPayment_Sent("0");
                        paymentAPiResponse.setResponse_Code("400");
                        paymentAPiResponse.setResponse_Value("Duplicate payment detected");
                    }
                    log.info("Comment {}", comment1);
                }


            }else {
                String comment2 = "Invalid URL and Expense ID";
                paymentAPiResponse.setIsPayment_Sent("0");
                paymentAPiResponse.setResponse_Code("300");
                paymentAPiResponse.setResponse_Value("Invalid URL and Expense ID");
                log.info("Comment {}", comment2);
                return paymentAPiResponse;
            }

            String insertApiLogQuery = """
                INSERT INTO api_log(apiorder_id, rawdata, sourceip, comment)
                VALUES (:apiorder_id, :rawdata, :sourceip, :comment)
            """;

            Map<String, Object> apiLogParams = new HashMap<>();
            apiLogParams.put("apiorder_id", orderId);
            apiLogParams.put("rawdata", queryString);
            apiLogParams.put("sourceip", sharedFunctions.getSourceIp(request));
            apiLogParams.put("comment", comment);

            crudService.executeNativeQuery(insertApiLogQuery, apiLogParams);

            String updateAppLogsQuery = """
                UPDATE app_logs 
                SET apiorder_id = :apiorder_id, status = 'SUCCESFUL' 
                WHERE requesttime = :requesttime AND payment_type = 'PAYMENT'
            """;

            Map<String, Object> updateAppLogsParams = new HashMap<>();
            updateAppLogsParams.put("apiorder_id", orderId);
            updateAppLogsParams.put("requesttime", requestTime);

            crudService.executeNativeQuery(updateAppLogsQuery, updateAppLogsParams);

        } catch (Exception e) {
            log.error("An error occurred on supervision API: {}", e.getMessage(), e);

            // Optional: fetch inserted order ID if present in context
            String insertedOrderId = (orderId != null)
                    ? String.valueOf(orderId)
                    : null;

            // Prepare error log
            String insertErrorQuery;
            Map<String, Object> errorParams = new HashMap<>();
            errorParams.put("sourceip", sharedFunctions.getSourceIp(request));
            errorParams.put("requesttime", requestTime);
            errorParams.put("comment", "Transaction rolled back due to: " +e.getLocalizedMessage());

            if (insertedOrderId != null) {
                insertErrorQuery = """
                    INSERT INTO api_errors (sourceip, apiorder_id, requesttime, comment)
                    VALUES (:sourceip, :apiorder_id, :requesttime, :comment)
                """;
                errorParams.put("apiorder_id", insertedOrderId);
            } else {
                    insertErrorQuery = """
                INSERT INTO api_errors (sourceip, requesttime, comment)
                VALUES (:sourceip, :requesttime, :comment)
            """;
            }

            // Log the error into the database
            crudService.executeNativeQuery(insertErrorQuery, errorParams);

            // Return error response
            paymentAPiResponse.setIsPayment_Sent("0");
            paymentAPiResponse.setResponse_Code("500");
            paymentAPiResponse.setResponse_Value("An error occurred. Please retry");

            return paymentAPiResponse; // HTTP status is still 200, you can map it to 500 via ControllerAdvice if needed
        }


        return paymentAPiResponse;
    }
    public PaymentRequestResponse getPaymentMdr(PaymentRequestDTO paymentRequestDTO, HttpServletRequest request, HttpServletResponse httpServletResponse){
        PaymentRequestResponse paymentRequestResponse = PaymentRequestResponse.builder()
                .isPayment_Sent("0")
                .Response_Code("500")
                .build();
        String requestTime = LocalDateTime.now().toString();
        Long orderId = null;
        try {
            String queryString = request.getQueryString();

            // Read raw body (like file_get_contents('php://input'))
            StringBuilder postDataBuilder = new StringBuilder();
            try (BufferedReader reader = request.getReader()) {
                String line;
                while ((line = reader.readLine()) != null) {
                    postDataBuilder.append(line);
                }
            }
            String postdata = postDataBuilder.toString().replace("'", "");

            // Determine request type
            String logComment = request.getMethod().equalsIgnoreCase("POST") ? "Inserted via POST" : "Inserted via GET";

            // Insert into app_logs (similar to mysqli_query)
            AppLog appLog = AppLog.builder()
                    .rawdata(queryString)
                    .sourceip(sharedFunctions.getSourceIp(request))
                    .paymentType("PAYMENT")
                    .requesttime(LocalDateTime.now())
                    .comment(logComment)
                    .status("INITIALIZED")
                    .build();
            appLogRepository.save(appLog);

            // Whitelist check
            String ipStatus = sharedFunctions.checkIfIpIsWhitelisted(request, postdata);
            if (!"OK".equals(ipStatus)) {
                log.warn("IP is not whitelisted. Aborting.");
                return paymentRequestResponse; // exit early like PHP `exit`
            }
            // Check for invalid line items
            boolean invalid = false;
            String comment = "";

            List<Map<String, Object>> itemsPre = paymentRequestDTO.getSupItems();


            int originalLineItemsCount = (itemsPre != null) ? itemsPre.size() : 0;
            // 1. Fetch base PMS links
            String baseLinkQuery = "SELECT value FROM PARAM WHERE parameter='PMS_API_LINK'";
            List<String> baseLinkResult = crudService.fetchWithNativeQuery(baseLinkQuery);
            String baseLinkValue = baseLinkResult != null && !baseLinkResult.isEmpty() ? baseLinkResult.get(0) : "";
            String[] baseUrls = baseLinkValue.split(",");

            // 2. Validate download URLs
            List<String> fileLinks = new ArrayList<>();
            for (Map<String, Object> item : itemsPre) {
                String downloadUrl = String.valueOf(item.get("Download_URL"));
                String expenseId = String.valueOf(item.get("Expense_Id"));

                if (!("-1".equals(expenseId) && (downloadUrl == null || downloadUrl.trim().isEmpty())) &&
                        !(sharedFunctions.startsWithAny(downloadUrl, baseUrls))) {

                    fileLinks.add("Invalid " + downloadUrl);

                    // Insert to receipt_upload_logs

                    ReceiptUploadLog receiptUploadLog = ReceiptUploadLog.builder()
                            .rawdata(postdata)
                            .sourceip(sharedFunctions.getSourceIp(request))
                            .comment("Invalid file type for " + downloadUrl)
                            .build();
                    receiptUploadLogRepository.save(receiptUploadLog);



                    // Compose early return response
                    String commentMsg = "Supervision: " + paymentRequestDTO.getPayment_id() +
                            "  Missing download url on  Item: " + item.get("Name");

                    paymentRequestResponse.setIsPayment_Sent("0");
                    paymentRequestResponse.setResponse_Code("500");
                    paymentRequestResponse.setResponse_Value(commentMsg);
                    return paymentRequestResponse;
                }
            }
            // Get payment_id, fallback if needed
            String paymentId = String.valueOf(paymentRequestDTO.getPayment_id());
            if (paymentId == null || paymentId.isEmpty()) {
                paymentId = String.valueOf(paymentRequestDTO.getPayment_id()); // or fetch from request param if needed
            }
            String notes = paymentRequestDTO.getNotes().replace("'", "''");

            // Check for duplicate
            String duplicateQuery = """
                            SELECT id FROM apiorder 
                            WHERE payment_id = :payment_id 
                            AND intrash = 'No' 
                            AND (resubmission_status IS NULL OR resubmission_status != 'In Progress')
                        """;
            Map<String, Object> params = new HashMap<>();
            params.put("payment_id", paymentId);
            String msisdnCheck1 = sharedFunctions.validatePaymentsPhoneNumber(paymentRequestDTO.getMsisdn());
            String recipient2msisdn = paymentRequestDTO.getMsisdn();
         if ("Invalid".equals(msisdnCheck1)) {
            String commentMsg = "Payment: " + paymentRequestDTO.getPayment_id() + "  Invalid phone Number: " + paymentRequestDTO.getMsisdn();
            paymentRequestResponse.setIsPayment_Sent("0");
            paymentRequestResponse.setResponse_Code("400");
            paymentRequestResponse.setResponse_Value(commentMsg);
            return paymentRequestResponse;
        } else if ("Invalid".equals(recipient2msisdn)) {
             String commentMsg = "Payment: " + paymentRequestDTO.getPayment_id() + "  Invalid phone Number: " + paymentRequestDTO.getMsisdn();
             paymentRequestResponse.setIsPayment_Sent("0");
             paymentRequestResponse.setResponse_Code("400");
             paymentRequestResponse.setResponse_Value(commentMsg);
             return paymentRequestResponse;
         }
            List<Object[]> duplicateResult = crudService.fetchWithNativeQuery(duplicateQuery, params, 0, 1);
            if (duplicateResult.isEmpty() && paymentId != null && !paymentId.isEmpty()) {


                String msisdn = paymentRequestDTO.getMsisdn();

                // Normalize MSISDN
                if ("Replace".equals(msisdnCheck1)) {
                    msisdn = msisdn.replace("25407", "2547");
                } else if ("Replace1".equals(msisdnCheck1)) {
                    msisdn = msisdn.replace("25401", "2541");
                }
                if ("Replace".equals(recipient2msisdn)) {
                    msisdn = msisdn.replace("25407", "2547");
                } else if ("Replace1".equals(recipient2msisdn)) {
                    msisdn = msisdn.replace("25401", "2541");
                }
                // 3. Get org_id from ordertype
                String orgQuery = "Select org_id from ordertype where title = 'Patient support'";
                List<Integer> orgResult = crudService.fetchWithNativeQuery(orgQuery);
                String orgId = orgResult != null && !orgResult.isEmpty() ? String.valueOf(orgResult.get(0)) : null;
                // 4. Approval Order / Level
                String approvalQuery = """
                    SELECT approvalOrder, budget_id 
                    FROM org_ordertype 
                    WHERE ordertypeId = :ordertypeId AND org_id = :org_id
                """;

                String paymentCategory = String.valueOf(paymentRequestDTO.getPayment_category());
                Map<String, Object> params4 = new HashMap<>();
                params4.put("ordertypeId", paymentCategory);
                params4.put("org_id", orgId);

                List<Object[]> approvalResult = crudService.fetchWithNativeQuery(approvalQuery, params4, 0, 1);
                String budget = "";
                String currentLevel = "";

                if (!approvalResult.isEmpty()) {
                    Object[] row = approvalResult.get(0);

                    String approvalOrder = row[0] != null ? row[0].toString() : "";
                    String[] levels = approvalOrder.split(",");

                     currentLevel = levels.length > 0 ? levels[0] : "0";  // Safe access
                     budget = row.length > 1 && row[1] != null ? row[1].toString() : paymentRequestDTO.getBudget().toString();

                    // Use currentLevel and budget below
                } else {
                    // Handle case when no result
                    log.error("No approvalOrder data found for org_id {} and ordertypeId {}", orgId, paymentCategory);
                    throw new IllegalArgumentException("Approval settings not configured for this org and order type");
                }
                String onbehalfCategory = paymentRequestDTO.getOnbehalf_category() != null ? paymentRequestDTO.getOnbehalf_category().toString() : "0";
                 recipient2msisdn = paymentRequestDTO.getRecipient2msisdn();
                Double recipient2amount = paymentRequestDTO.getRecipient2amount() != null ? paymentRequestDTO.getRecipient2amount() : 0.0;
                Integer recipient2category = paymentRequestDTO.getRecipient2_category() != null ? paymentRequestDTO.getRecipient2_category() : 0;
                onbehalfCategory = String.valueOf(paymentRequestDTO.getOnbehalf_category() != null ? paymentRequestDTO.getOnbehalf_category() : 0);
                String year = paymentRequestDTO.getYear();
                String insertQuery;
                Map<String, Object> insertParams = new HashMap<>();

                insertQuery = """
                    INSERT INTO apiorder (
                        treatment_model, initiator_id, initiator_username,
                        firstname, middlename, lastname, beneficiarytype,
                        onbehalffirstname, onbehalfmiddlename, onbehalflastname, onbehalftype,
                        recipient2firstname, recipient2middlename, recipient2lastname, beneficiary2Type,
                        recipient2credit, recipient2msisdn, ordertype, budget, credit,
                        msisdn, notes, facility, district, county, province,
                        request_src, approval_level, approval_status, sourceip, payment_id,
                        requesttime, month_of_claim, %s
                        patient_registration_number, date_treatment_started, dot_nurse_name,
                        dot_nurse_phoneno, MPESA_Attachment, local_mpesa_url, org_id
                    )
                    VALUES (
                        :treatment, 0, :dtlc,
                        :recipientFirstname, :recipientMiddlename, :recipientLastname, :recipientCategory,
                        :onbehalfFirstname, :onbehalfMiddlename, :onbehalfLastname, :onbehalfCategory,
                        :recipient2Firstname, :recipient2Middlename, :recipient2Lastname, :recipient2Category,
                        :recipient2Amount, :recipient2Msisdn, :paymentCategory, :budget, :amount,
                        :msisdn, :notes, :facility, :district, :county, :province,
                        'api', :approvalLevel, 'Pending', :sourceIp, :paymentId,
                        :requestTime, :monthOfClaim, %s
                        :patientRegNo, :dateStarted, :dotNurseName,
                        :dotNursePhone, :mpesaAttachment, :localMpesaUrl, :orgId
                    )
                """;


                boolean includeYear = (year != null && !year.isEmpty());
                String yearColumn = includeYear ? "year," : "";
                String yearParam = includeYear ? ":year," : "";
                insertParams.put("treatment", paymentRequestDTO.getTreatment());
                insertParams.put("dtlc", paymentRequestDTO.getDtlc());
                insertParams.put("recipientFirstname", paymentRequestDTO.getRecipient_firstname());
                insertParams.put("recipientMiddlename", paymentRequestDTO.getRecipient_middlename());
                insertParams.put("recipientLastname", paymentRequestDTO.getRecipient_lastname());
                insertParams.put("recipientCategory", paymentRequestDTO.getRecipient_category());

                insertParams.put("onbehalfFirstname", paymentRequestDTO.getOnbehalf_firstname());
                insertParams.put("onbehalfMiddlename", paymentRequestDTO.getOnbehalf_middlename());
                insertParams.put("onbehalfLastname", paymentRequestDTO.getOnbehalf_lastname());
                insertParams.put("onbehalfCategory", onbehalfCategory);

                insertParams.put("recipient2Firstname", paymentRequestDTO.getRecipient2_firstname());
                insertParams.put("recipient2Middlename", paymentRequestDTO.getRecipient2_middlename());
                insertParams.put("recipient2Lastname", paymentRequestDTO.getRecipient2_lastname());
                insertParams.put("recipient2Category", recipient2category);

                insertParams.put("recipient2Amount", recipient2amount);
                insertParams.put("recipient2Msisdn", recipient2msisdn);
                insertParams.put("paymentCategory", paymentRequestDTO.getPayment_category());
                insertParams.put("budget", paymentRequestDTO.getBudget() != null ? paymentRequestDTO.getBudget() : budget);
                insertParams.put("amount", paymentRequestDTO.getAmount());
                insertParams.put("msisdn", paymentRequestDTO.getMsisdn());
                insertParams.put("notes", paymentRequestDTO.getNotes());

                insertParams.put("facility", paymentRequestDTO.getFacility());
                insertParams.put("district", paymentRequestDTO.getDistrict());
                insertParams.put("county", paymentRequestDTO.getCounty());
                insertParams.put("province", paymentRequestDTO.getProvince());

                insertParams.put("approvalLevel", currentLevel); // derived earlier
                insertParams.put("sourceIp", sharedFunctions.getSourceIp(request));
                insertParams.put("paymentId", paymentRequestDTO.getPayment_id());
                insertParams.put("requestTime", requestTime); // assumed to be generated earlier
                insertParams.put("monthOfClaim", paymentRequestDTO.getMonth_of_claim());

                if (includeYear) insertParams.put("year", year);

                insertParams.put("patientRegNo", paymentRequestDTO.getPatient_registration_number());
                insertParams.put("dateStarted", paymentRequestDTO.getDate_treatment_started());
                insertParams.put("dotNurseName", paymentRequestDTO.getDot_nurse_name());
                insertParams.put("dotNursePhone", paymentRequestDTO.getDot_nurse_phoneno());
                insertParams.put("mpesaAttachment", paymentRequestDTO.getMPESA_Attachments()); // assumed you have this
                insertParams.put("localMpesaUrl", paymentRequestDTO.getLocal_url_MPESA_Attachments());      // assumed you have this
                insertParams.put("orgId", orgId);


                insertQuery = String.format(insertQuery, yearColumn, yearParam);
                crudService.executeNativeQuery(insertQuery, insertParams);
                // ✅ Retrieve the inserted apiorder (by timestamp + msisdn)
                String fetchInsertedOrder = """
                        SELECT id, msisdn, credit, driver_amount, driver_phone, recipient2credit, recipient2msisdn 
                        FROM apiorder 
                        WHERE requesttime = :requestTime AND msisdn = :msisdn 
                        ORDER BY id DESC 
                    """;

                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String formattedRequestTime = LocalDateTime.parse(requestTime).format(formatter);

                Map<String, Object> latestParams = Map.of("requestTime", formattedRequestTime, "msisdn", msisdn);
                List<Object[]> latestResult = crudService.fetchWithNativeQuery(fetchInsertedOrder, latestParams, 0, 1);
                orderId = latestResult.isEmpty() ? null : Long.parseLong(latestResult.get(0)[0].toString());

                // Continue from here with insert into `apiorder`, logging, and response
                if (orderId != null) {
                    // Build map for hash generation
                    // 🚀 1. Build transaction map and generate transaction chain
                    HashMap<String, String> trxMap = new HashMap<>();
                    trxMap.put("id", String.valueOf(orderId));
                    trxMap.put("msisdn", (String) latestResult.get(0)[1]);
                    BigDecimal creditValue = (BigDecimal) latestResult.get(0)[2];
                    trxMap.put("credit", creditValue.toPlainString()); // More accurate for financial data
                    BigDecimal driverAmount = (BigDecimal) latestResult.get(0)[3];
                    trxMap.put("driver_amount", driverAmount.toPlainString());
                    trxMap.put("driver_phone", (String) latestResult.get(0)[4]);
                    BigDecimal recipient2CreditValue = (BigDecimal) latestResult.get(0)[5];
                    trxMap.put("recipient2credit", recipient2CreditValue.toPlainString());
                    trxMap.put("recipient2msisdn", (String) latestResult.get(0)[6]);

                    String trxChain = sharedFunctions.updateChainTrx(null, trxMap, orderId.toString(), null);

                    // 🚀 2. Insert into TRX_VALUES
                    String trxInsertQuery = "INSERT INTO TRX_VALUES(ORDER_ID, T_VALUE,DATE_CREATED) VALUES(:orderId, :trxChain, :dateCreated)";
                    Map<String, Object> trxParams = Map.of(
                            "orderId", orderId,
                            "trxChain", trxChain,
                            "dateCreated", requestTime
                    );
                    crudService.executeNativeQuery(trxInsertQuery, trxParams);

                    // 🚀 3. Audit the creation
                    sharedFunctions.auditAction("CREATED", "Created new order transaction id" + orderId, sharedFunctions.getSourceIp(request), null, null, null, null);

                    // 🚀 4. Download receipts to /var/www/html/recipients
                    List<Map<String, Object>> items = paymentRequestDTO.getSupItems();

                    String homeLinkQuery = "SELECT value FROM PARAM WHERE parameter='SYSTEM_HOME_LINK'";
                    List<String> systemUrlRow = crudService.fetchWithNativeQuery(homeLinkQuery);
                    String systemUrl = systemUrlRow.isEmpty() ? "" : String.valueOf(systemUrlRow.get(0));

                    String targetDir = receiptsUploadPath;

                    for (Map<String, Object> item : items) {
                        String itemName = Optional.ofNullable((String) item.get("Name")).orElse((String) item.get("name"));
                        if ("Expensive Sheet".equalsIgnoreCase(itemName)) {
                            itemName = "Expense Sheet";
                        }

                        String downloadUrl = String.valueOf(item.get("Download_URL"));
                        String fileNameRaw = String.valueOf(item.get("File_Receipt_Name"));
                        String expenseId = String.valueOf(item.get("Expense_Id"));
                        if (fileNameRaw == null || fileNameRaw.equals("null") || fileNameRaw.isBlank()) {
                            // Extract filename from the URL if File_Receipt_Name is not provided
                            fileNameRaw = downloadUrl.substring(downloadUrl.lastIndexOf('/') + 1); // e.g. 40ab8772...jpg
                        }
                        String fileName = orderId + "_" + expenseId + "_" + fileNameRaw;

                        List<String> allowedTypes = List.of("jpg", "png", "xls", "pdf", "xlsx", "jpeg");
                        String localFilePath = targetDir + fileName;

                        String fileExtension = FilenameUtils.getExtension(fileName).toLowerCase();
                        boolean isExtensionAllowed = allowedTypes.contains(fileExtension);

                        String localUrl = "";

                        // 🚀 Attempt download
                        if (!downloadUrl.isBlank()) {
                            try (InputStream in = new URL(downloadUrl).openStream()) {
                                Files.copy(in, Paths.get(localFilePath), StandardCopyOption.REPLACE_EXISTING);

                                if (!isExtensionAllowed) {
                                    Files.deleteIfExists(Paths.get(localFilePath));

                                    // Log invalid extension
                                    String logQuery = "INSERT INTO receipt_upload_logs (apiorder_id, rawdata, sourceip, comment) VALUES(:id, :data, :ip, :comment)";
                                    Map<String, Object> logParams = Map.of(
                                            "id", orderId,
                                            "data", postdata,
                                            "ip", sharedFunctions.getSourceIp(request),
                                            "comment", "Invalid file extension for URL: " + downloadUrl
                                    );
                                    crudService.executeNativeQuery(logQuery, logParams);
                                } else {
                                    localUrl = systemUrl + "/api/v1/receipts/" + fileName;
                                }
                            } catch (Exception e) {
                                log.error("File download failed for URL: " + downloadUrl, e);

                                // Log download failure
                                String logQuery = "INSERT INTO receipt_upload_logs (apiorder_id, rawdata, sourceip, comment) VALUES(:id, :data, :ip, :comment)";
                                Map<String, Object> logParams = Map.of(
                                        "id", orderId,
                                        "data", postdata,
                                        "ip", sharedFunctions.getSourceIp(request),
                                        "comment", "File download failed: " + e.getMessage()
                                );
                                crudService.executeNativeQuery(logQuery, logParams);
                            }
                        }

                        // ✅ Always insert into supervision_details
                        Map<String, Object> insertParams1 = new HashMap<>();
                        insertParams1.put("orderId", orderId);
                        insertParams1.put("expenseId", expenseId);
                        insertParams1.put("title", itemName);
                        insertParams1.put("url", item.get("URL"));
                        insertParams1.put("localUrl", localUrl);

                        String insertDetailsQuery;
                        if (item.containsKey("Amount")) {
                            insertParams1.put("credit", item.get("Amount"));
                            insertParams1.put("originalAmount", item.get("Amount"));

                            insertDetailsQuery = """
            INSERT INTO supervision_details(apiorder_id, expense_id, title, credit, original_amount, url, local_url)
            VALUES(:orderId, :expenseId, :title, :credit, :originalAmount, :url, :localUrl)
        """;
                        } else {
                            insertDetailsQuery = """
            INSERT INTO supervision_details(apiorder_id, expense_id, title, url, local_url)
            VALUES(:orderId, :expenseId, :title, :url, :localUrl)
        """;
                        }

                        crudService.executeNativeQuery(insertDetailsQuery, insertParams1);
                    }

                    String updateMpesaConfirmationQuery = """
                                UPDATE apiorder
                                SET 
                                    Confirmed_Beneficairy = (
                                        CASE 
                                            WHEN (SELECT COUNT(*) FROM confirmed_recipient 
                                                  WHERE msisdn = apiorder.msisdn OR msisdn = CONCAT('+', apiorder.msisdn)) > 0 
                                            THEN 'Beneficiary Confirmed' 
                                            ELSE 'Beneficiary Not Confirmed' 
                                        END
                                    ),
                                    Confirmed_Driver = (
                                        CASE 
                                            WHEN (SELECT COUNT(*) FROM confirmed_recipient 
                                                  WHERE msisdn = apiorder.driver_phone OR msisdn = CONCAT('+', apiorder.driver_phone)) > 0 
                                            THEN CONCAT(
                                                'Confirmed Driver:(',
                                                (SELECT full_name FROM confirmed_recipient 
                                                 WHERE msisdn = apiorder.driver_phone OR msisdn = CONCAT('+', apiorder.driver_phone) 
                                                 LIMIT 1),
                                                '(',
                                                (SELECT msisdn FROM confirmed_recipient 
                                                 WHERE msisdn = apiorder.driver_phone OR msisdn = CONCAT('+', apiorder.driver_phone) 
                                                 LIMIT 1),
                                                '))'
                                            )
                                            ELSE 
                                                CASE 
                                                    WHEN driver_phone IS NULL OR driver_phone = '' 
                                                    THEN '' 
                                                    ELSE 'Driver Not Confirmed' 
                                                END 
                                        END
                                    ),
                                    Confirmed_DOT = (
                                        CASE 
                                            WHEN (SELECT COUNT(*) FROM confirmed_recipient 
                                                  WHERE msisdn = apiorder.recipient2msisdn OR msisdn = CONCAT('+', apiorder.recipient2msisdn)) > 0 
                                            THEN CONCAT(
                                                'Confirmed DOT:(',
                                                (SELECT full_name FROM confirmed_recipient 
                                                 WHERE msisdn = apiorder.recipient2msisdn OR msisdn = CONCAT('+', apiorder.recipient2msisdn) 
                                                 LIMIT 1),
                                                '(',
                                                (SELECT msisdn FROM confirmed_recipient 
                                                 WHERE msisdn = apiorder.recipient2msisdn OR msisdn = CONCAT('+', apiorder.recipient2msisdn) 
                                                 LIMIT 1),
                                                '))'
                                            )
                                            ELSE 
                                                CASE 
                                                    WHEN recipient2msisdn IS NULL OR recipient2msisdn = '' 
                                                    THEN '' 
                                                    ELSE 'DOT Not Confirmed' 
                                                END 
                                        END
                                    ),
                                    original_line_items_num = :originalLineItemsNum
                                WHERE id = :apiOrderId
                            """;

                    Map<String, Object> confirmationParams = new HashMap<>();
                    confirmationParams.put("originalLineItemsNum", originalLineItemsCount); // must be an int, from earlier in the logic
                    confirmationParams.put("apiOrderId", orderId); // ID of inserted apiorder

                    crudService.executeNativeQuery(updateMpesaConfirmationQuery, confirmationParams);
                    String comment1 = "Successfull MDR Patient support";
                    paymentRequestResponse.setPayment("1");
                    paymentRequestResponse.setStatus("Success");
                    paymentRequestResponse.setResponse_Code("200");
                    paymentRequestResponse.setResponse_Value(comment1);
                    log.info("Successfull MDR Patient support");
                    return paymentRequestResponse;

                }
                paymentRequestResponse.setPayment("1");
                paymentRequestResponse.setStatus("Success");
                log.info("Successfull MDR Patient support");
                return paymentRequestResponse;
            }else {
                String duplicateLogQuery = """
                    SELECT apiorder_id 
                    FROM api_log 
                    WHERE apiorder_id != '0' AND rawdata = :postdata
                """;

                Map<String, Object> logParams = new HashMap<>();
                logParams.put("postdata", postdata);

                List<Object[]> existingApiLog = crudService.fetchWithNativeQuery(duplicateLogQuery, logParams, 0, 1);


                // Match PHP logic exactly
                if (!existingApiLog.isEmpty() && existingApiLog.get(0)[0] != null && !existingApiLog.get(0)[0].toString().isEmpty()) {
                    comment = "Duplicate MDR Patient support2";
                    paymentRequestResponse.setStatus("Duplicate");
                    paymentRequestResponse.setPayment("1"); // ✅ was incorrectly "0" before
                } else if (paymentId == null || paymentId.trim().isEmpty()) {
                    comment = "Bad Request. Request elements could not be read";
                    paymentRequestResponse.setStatus("Fail");
                    paymentRequestResponse.setPayment("0");
                } else {
                    comment = "Duplicate payment detected";
                    paymentRequestResponse.setStatus("Duplicate");
                    paymentRequestResponse.setPayment("1"); // ✅ was incorrectly "0" before
                }


                // Final response
                log.info("Payment handling result: {}", comment);

            }
            Object apiOrderId = (paymentId != null && !paymentId.trim().isEmpty() && orderId != null && orderId!= null) ? orderId : 0;

            // Insert into api_log
            String insertLogQuery = """
                INSERT INTO api_log (apiorder_id, rawdata, sourceip, comment)
                VALUES (:apiorderId, :rawdata, :sourceip, :comment)
            """;
            Map<String, Object> insertLogParams = new HashMap<>();
            insertLogParams.put("apiorderId", apiOrderId);
            insertLogParams.put("rawdata", queryString);
            insertLogParams.put("sourceip", sharedFunctions.getSourceIp(request));  // implement getClientIp()
            insertLogParams.put("comment", comment);

            crudService.executeNativeQuery(insertLogQuery, insertLogParams);

            // Update app_logs
            String updateAppLogsQuery = """
                UPDATE app_logs 
                SET apiorder_id = :apiorderId, status = 'SUCCESFUL'
                WHERE requesttime = :requestTime AND payment_type = 'PAYMENT'
            """;
            Map<String, Object> updateParams = new HashMap<>();
            updateParams.put("apiorderId", apiOrderId);
            updateParams.put("requestTime", requestTime);

            crudService.executeNativeQuery(updateAppLogsQuery, updateParams);

        }catch (Exception e) {
            log.error("An error occurred on supervision API: {}", e.getMessage(), e);
            log.error("An error occurred on MDR Patient Support API: {}", e.getMessage(), e);

            // Prepare default comment
            String errorComment = "Transaction rolled back due to: " + e.getMessage();

            // Attempt to insert error details into api_errors table
            String errorInsertQuery;
            Map<String, Object> errorParams = new HashMap<>();
            errorParams.put("sourceip", request.getRemoteAddr());
            errorParams.put("requesttime", requestTime);

            if (orderId != null) {
                errorInsertQuery = """
                    INSERT INTO api_errors (sourceip, apiorder_id, requesttime, comment)
                    VALUES (:sourceip, :apiorder_id, :requesttime, :comment)
                """;
                errorParams.put("apiorder_id", orderId);
            } else {
                errorInsertQuery = """
                    INSERT INTO api_errors (sourceip, requesttime, comment)
                    VALUES (:sourceip, :requesttime, :comment)
                """;
            }

            errorParams.put("comment", errorComment);

            try {
                crudService.executeNativeQuery(errorInsertQuery, errorParams);
            } catch (Exception dbLogErr) {
                dbLogErr.printStackTrace();
                log.warn("Failed to insert into api_errors: {}", dbLogErr.getMessage());
            }

            // Prepare error response
            paymentRequestResponse.setPayment("0");
            paymentRequestResponse.setStatus("Error");
            paymentRequestResponse.setResponse_Code("500");
            paymentRequestResponse.setResponse_Value("An error occurred. Please check your request and try again.");
            e.printStackTrace();

        }

        return paymentRequestResponse;
    }

    public PaymentAPiResponse getNonSupervisionPayments(NonSupervisionPaymentRequestDTO nonSupervisionPaymentRequestDTO, HttpServletRequest request, HttpServletResponse httpServletResponse) {
        PaymentAPiResponse paymentAPiResponse = PaymentAPiResponse.builder()
                .isPayment_Sent("0")
                .Response_Value("An error occurred. Please check your request and try again")
                .build();
        String requestTime = LocalDateTime.now().toString();
        Long orderId = null;
        try {
            String queryString = request.getQueryString();

            // Read raw body (like file_get_contents('php://input'))
            StringBuilder postDataBuilder = new StringBuilder();
            try (BufferedReader reader = request.getReader()) {
                String line;
                while ((line = reader.readLine()) != null) {
                    postDataBuilder.append(line);
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            String postdata = postDataBuilder.toString().replace("'", "");

            // Determine request type
            String logComment = request.getMethod().equalsIgnoreCase("POST") ? "Inserted via POST" : "Inserted via GET";

            // Insert into app_logs (similar to mysqli_query)
            AppLog appLog = AppLog.builder()
                    .rawdata(queryString)
                    .sourceip(sharedFunctions.getSourceIp(request))
                    .paymentType("PAYMENT")
                    .requesttime(LocalDateTime.now())
                    .comment(logComment)
                    .status("INITIALIZED")
                    .build();
            appLogRepository.save(appLog);

            // Whitelist check
            String ipStatus = sharedFunctions.checkIfIpIsWhitelisted(request, postdata);
            if (!"OK".equals(ipStatus)) {
                log.warn("IP is not whitelisted. Aborting.");
                return paymentAPiResponse; // exit early like PHP `exit`
            }
            // Check for invalid line items
            boolean invalid = false;
            String comment = "";

            List<Map<String, Object>> itemsPre = nonSupervisionPaymentRequestDTO.getSupItems();


            int originalLineItemsCount = (itemsPre != null) ? itemsPre.size() : 0;
            // 1. Fetch base PMS links
            String baseLinkQuery = "SELECT value FROM PARAM WHERE parameter='NEW_PMS_API_LINKS'";
            List<String> baseLinkResult = crudService.fetchWithNativeQuery(baseLinkQuery);
            String baseLinkValue = baseLinkResult != null && !baseLinkResult.isEmpty() ? baseLinkResult.get(0) : "";
            String[] baseUrls = baseLinkValue.split(",");
            // 2. Validate download URLs
            List<String> fileLinks = new ArrayList<>();
            for (Map<String, Object> item : itemsPre) {
                String downloadUrl = String.valueOf(item.get("Download_URL"));
                String expenseId = String.valueOf(item.get("Expense_Id"));

                if (!("-1".equals(expenseId) && (downloadUrl == null || downloadUrl.trim().isEmpty())) &&
                        !(sharedFunctions.startsWithAny(downloadUrl, baseUrls))) {

                    fileLinks.add("Invalid " + downloadUrl);

                    // Insert to receipt_upload_logs

                    ReceiptUploadLog receiptUploadLog = ReceiptUploadLog.builder()
                            .rawdata(postdata)
                            .sourceip(sharedFunctions.getSourceIp(request))
                            .comment("Invalid file type for " + downloadUrl)
                            .build();
                    receiptUploadLogRepository.save(receiptUploadLog);



                    // Compose early return response
                    String commentMsg = "Supervision: " + nonSupervisionPaymentRequestDTO.getPayment_id() +
                            "  Missing download url on  Item: " + item.get("Name");

                    paymentAPiResponse.setIsPayment_Sent("0");
                    paymentAPiResponse.setResponse_Code("500");
                    paymentAPiResponse.setResponse_Value(commentMsg);
                    return paymentAPiResponse;
                }
            }
            // Get payment_id, fallback if needed
            String paymentId = String.valueOf(nonSupervisionPaymentRequestDTO.getPayment_id());
            if (paymentId == null || paymentId.isEmpty()) {
                paymentId = String.valueOf(nonSupervisionPaymentRequestDTO.getPayment_id()); // or fetch from request param if needed
            }
            String notes = nonSupervisionPaymentRequestDTO.getNotes().replace("'", "''");
            // Check for duplicate
            String duplicateQuery = """
                            SELECT id FROM apiorder 
                            WHERE payment_id = :payment_id 
                            AND intrash = 'No' 
                            AND treatment_model IN (SELECT id FROM treatment_model WHERE title = 'MDR Other Payments') 
                            AND (resubmission_status IS NULL OR resubmission_status != 'In Progress')
                        """;
            Map<String, Object> params = new HashMap<>();
            params.put("payment_id", paymentId);
            List<Object[]> duplicateResult = crudService.fetchWithNativeQuery(duplicateQuery, params, 0, 1);
            String msisdnCheck = sharedFunctions.validatePaymentsPhoneNumber(nonSupervisionPaymentRequestDTO.getMsisdn());
            if ("Invalid".equals(msisdnCheck)) {
                String comment1 = "NonSupervision: " + paymentId + " Invalid phone Number: " + nonSupervisionPaymentRequestDTO.getMsisdn();
                log.info("message ",comment1);
                paymentAPiResponse.setIsPayment_Sent("0");
                paymentAPiResponse.setResponse_Code("400");
                paymentAPiResponse.setResponse_Value(comment1);
                return paymentAPiResponse;
            }

            if (duplicateResult.isEmpty() && paymentId != null && !paymentId.isEmpty()) {

                // Normalize MSISDN
                String msisdn = nonSupervisionPaymentRequestDTO.getMsisdn();
                if ("Replace".equals(msisdnCheck)) {
                    msisdn = msisdn.replace("25407", "2547");
                } else if ("Replace1".equals(msisdnCheck)) {
                    msisdn = msisdn.replace("25401", "2541");
                }

                requestTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

                // Get ordertype ID
                String ordertypeQuery = "SELECT id FROM ordertype WHERE title = 'MDR Other Payments'";
                List<Integer> ordertypeResult = crudService.fetchWithNativeQuery(ordertypeQuery);
                String ordertype = ordertypeResult != null && !ordertypeResult.isEmpty() ? String.valueOf(ordertypeResult.get(0)) : null;

                // Get treatment_model ID
                String treatmentQuery = "SELECT id FROM treatment_model WHERE title = 'MDR Other Payments'";
                List<Integer> treatmentResult = crudService.fetchWithNativeQuery(treatmentQuery);
                String treatment_model = treatmentResult != null && !treatmentResult.isEmpty() ? String.valueOf(treatmentResult.get(0)) : null;

                // Get budget ID
                String budgetTitle = "HCW".equalsIgnoreCase(nonSupervisionPaymentRequestDTO.getPayment_due_to())
                        ? "Non Supervision Budget"
                        : "4.1.1 MDR TB patient support";

                String budgetQuery = "SELECT id FROM budget WHERE title = :title";
                Map<String, Object> budgetParams = Map.of("title", budgetTitle);
                List<Integer> budgetResult = crudService.fetchWithNativeQuery(budgetQuery, budgetParams, 0, 1);
                String budget = budgetResult != null && !budgetResult.isEmpty() ? String.valueOf(budgetResult.get(0)) : null;

                // Get org_id (for Supervision org)
                String orgQuery = "SELECT org_id FROM ordertype WHERE title = 'Supervision'";
                List<Integer> orgResult = crudService.fetchWithNativeQuery(orgQuery);
                String orgId = orgResult != null && !orgResult.isEmpty() ? String.valueOf(orgResult.get(0)) : null;

                // Get approval level
                String approvalQuery = "SELECT approvalOrder FROM org_ordertype WHERE ordertypeId = :ordertypeId AND org_id = :orgId";
                Map<String, Object> approvalParams = Map.of("ordertypeId", ordertype, "orgId", orgId);
                List<String> approvalResult = crudService.fetchWithNativeQuery(approvalQuery, approvalParams, 0, 1);
                String approvalOrder = approvalResult.get(0);
                String[] levels = approvalOrder.split(",");
                String currentLevel = levels[0];

                // Check if beneficiary exists
                String beneficiaryQuery = """
                            SELECT b.ID, firstname, middlename, lastname, c.title, p.title, d.title
                            FROM beneficiary b 
                            LEFT JOIN county c ON b.county = c.ID
                            LEFT JOIN province p ON b.province = p.ID
                            LEFT JOIN district d ON b.district = d.ID
                            WHERE b.msisdn = :msisdn
                        """;
                Map<String, Object> beneficiaryParams = Map.of("msisdn", msisdn);
                List<Object[]> beneficiaryResult = crudService.fetchWithNativeQuery(beneficiaryQuery, beneficiaryParams, 0, 1);

                if (beneficiaryResult.isEmpty()) {
                    // Insert new beneficiary
                    String insertBeneficiaryQuery = """
                                INSERT INTO beneficiary (pmsid, firstname, lastname, middlename, beneficiarytype, county, province, district, msisdn, sourceip, creationtime)
                                SELECT :pmsid, :firstname, '', '', ID, :county, :province, :district, :msisdn, :sourceip, :creationtime
                                FROM beneficiarytype WHERE title = 'dtlc'
                            """;

                    Map<String, Object> insertParams = new HashMap<>();
                    insertParams.put("pmsid", nonSupervisionPaymentRequestDTO.getSupervisor_pms_id());
                    insertParams.put("firstname", nonSupervisionPaymentRequestDTO.getSupervisor_name());
                    insertParams.put("county", nonSupervisionPaymentRequestDTO.getCounty_id());
                    insertParams.put("province", nonSupervisionPaymentRequestDTO.getProvince_id());
                    insertParams.put("district", nonSupervisionPaymentRequestDTO.getDistrict_id());
                    insertParams.put("msisdn", msisdn);
                    insertParams.put("sourceip", request.getRemoteAddr());
                    insertParams.put("creationtime", String.valueOf(System.currentTimeMillis()));

                    crudService.executeNativeQuery(insertBeneficiaryQuery, insertParams);
                }
                msisdn = nonSupervisionPaymentRequestDTO.getMsisdn();
                String beneficiaryMsisdn = nonSupervisionPaymentRequestDTO.getBeneficiary_msisdn();
                String month = nonSupervisionPaymentRequestDTO.getMonth();
                String year = nonSupervisionPaymentRequestDTO.getYear();
                paymentId = String.valueOf(nonSupervisionPaymentRequestDTO.getPayment_id());

                requestTime = LocalDateTime.now().toString();
                String clientIp = request.getRemoteAddr();

                // Step 1: Get any existing payments in the same month/year
                String paymentsQuery = """
                            SELECT id FROM apiorder 
                            WHERE month = :month AND year = :year AND msisdn = :msisdn
                        """;
                Map<String, Object> payParams = Map.of("month", month, "year", year, "msisdn", msisdn);
                List<Object[]> payRows = crudService.fetchWithNativeQuery(paymentsQuery, payParams, 0, 1);
                String paymentIDs = payRows.stream()
                        .map(row -> row[0].toString())
                        .collect(Collectors.joining(","));
                if (paymentIDs.isEmpty()) paymentIDs = "0";

                // Step 2: Handle resubmission if applicable
                String resubmissionStatus = nonSupervisionPaymentRequestDTO.getResubmission_Status() != null ? nonSupervisionPaymentRequestDTO.getResubmission_Status().trim() : "";
                String childOf = "0";

                if (!resubmissionStatus.isEmpty()) {
                    String resubQuery = """
                                SELECT id, approval_level FROM apiorder 
                                WHERE payment_id = :paymentId 
                                AND intrash = 'No' 
                                AND treatment_model IN (SELECT id FROM treatment_model WHERE title = 'Supervision Request') 
                                AND resubmission_status = 'In Progress'
                            """;
                    Map<String, Object> resubParams = Map.of("paymentId", paymentId);
                    List<Object[]> resubRows = crudService.fetchWithNativeQuery(resubQuery, resubParams, 0, 1);
                    if (!resubRows.isEmpty()) {
                        childOf = resubRows.get(0)[0].toString();
                        currentLevel = resubRows.get(0)[1].toString();

                        // Mark old as completed
                        String updateOldResub = """
                                    UPDATE apiorder SET 
                                        resubmission_status = :resubmit, 
                                        approval_status = 'Stopped', 
                                        resubmission_comments = 'Request Marked as Complete new Request created after Successful resubmission' 
                                    WHERE id = :childId
                                """;
                        Map<String, Object> updateParams = Map.of(
                                "resubmit", resubmissionStatus,
                                "childId", childOf
                        );
                        crudService.executeNativeQuery(updateOldResub, updateParams);
                    }
                }

                // Step 3: Prepare insert
                String insertQuery = """
                            INSERT INTO apiorder (
                                treatment_model, initiator_id, initiator_username, firstname, middlename, lastname, 
                                ordertype, budget, credit, msisdn, notes, county, province, district, 
                                resubmission_status, child_of, zone_id, request_src, requesttime, 
                                approval_level, approval_status, sourceip, latitude, longitude, 
                                year, month, payment_id, attached_to, org_id, payment_due_to
                            ) VALUES (
                                :treatmentModel, :initiatorId, :initiatorUsername, :firstName, :middleName, :lastName, 
                                :orderType, :budget, :credit, :msisdn, :notes, :county, :province, :district, 
                                :resubmissionStatus, :childOf, :zoneId, 'api', :requestTime, 
                                :approvalLevel, 'Pending', :sourceIp, :lat, :lon, 
                                :year, :month, :paymentId, :attachedTo, :orgId, :paymentDueTo
                            )
                        """;

                Map<String, Object> insertParams = new HashMap<>();
                insertParams.put("treatmentModel", treatment_model);
                insertParams.put("initiatorId", nonSupervisionPaymentRequestDTO.getSupervisor_pms_id());
                insertParams.put("initiatorUsername", nonSupervisionPaymentRequestDTO.getSupervisor_name());
                insertParams.put("firstName", nonSupervisionPaymentRequestDTO.getBeneficiary_firstname());
                insertParams.put("middleName", nonSupervisionPaymentRequestDTO.getBeneficiary_middlename());
                insertParams.put("lastName", nonSupervisionPaymentRequestDTO.getBeneficiary_lastname());
                insertParams.put("orderType", ordertype);
                insertParams.put("budget", budget);
                insertParams.put("credit", nonSupervisionPaymentRequestDTO.getTotal_amount());
                insertParams.put("msisdn", beneficiaryMsisdn);
                insertParams.put("notes", notes);
                insertParams.put("county", nonSupervisionPaymentRequestDTO.getCounty_id());
                insertParams.put("province", nonSupervisionPaymentRequestDTO.getProvince_id());
                insertParams.put("district", nonSupervisionPaymentRequestDTO.getDistrict_id());
                insertParams.put("resubmissionStatus", resubmissionStatus);
                insertParams.put("childOf", childOf);
                insertParams.put("zoneId", nonSupervisionPaymentRequestDTO.getZone_id() != null ? nonSupervisionPaymentRequestDTO.getZone_id() : 0);
                insertParams.put("requestTime", requestTime);
                insertParams.put("approvalLevel", currentLevel);
                insertParams.put("sourceIp", clientIp);
                insertParams.put("lat", nonSupervisionPaymentRequestDTO.getLatitude());
                insertParams.put("lon", nonSupervisionPaymentRequestDTO.getLongitude());
                insertParams.put("year", year);
                insertParams.put("month", month);
                insertParams.put("paymentId", paymentId);
                insertParams.put("attachedTo", paymentIDs);
                insertParams.put("orgId", orgId);
                insertParams.put("paymentDueTo", nonSupervisionPaymentRequestDTO.getPayment_due_to());

                crudService.executeNativeQuery(insertQuery, insertParams);

                // Step 4: Fetch last inserted payment
                String fetchLatest = """
                            SELECT id, msisdn, credit, driver_amount, driver_phone, recipient2credit, recipient2msisdn 
                            FROM apiorder 
                            WHERE requesttime = :requestTime 
                            AND (msisdn = :msisdn OR msisdn = :beneficiaryMsisdn) 
                            ORDER BY id DESC
                        """;
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String formattedRequestTime = LocalDateTime.parse(requestTime).format(formatter);

                Map<String, Object> latestParams = Map.of("requestTime", formattedRequestTime, "msisdn", msisdn, "beneficiaryMsisdn", beneficiaryMsisdn);
                List<Object[]> latestResult = crudService.fetchWithNativeQuery(fetchLatest, latestParams, 0, 1);
                orderId = latestResult.isEmpty() ? null : Long.parseLong(latestResult.get(0)[0].toString());

                // Continue from here with insert into `apiorder`, logging, and response
                if (orderId != null) {
                    // Build map for hash generation
                    // 🚀 1. Build transaction map and generate transaction chain
                    HashMap<String, String> trxMap = new HashMap<>();
                    trxMap.put("id", String.valueOf(orderId));
                    trxMap.put("msisdn", (String) latestResult.get(0)[1]);
                    BigDecimal creditValue = (BigDecimal) latestResult.get(0)[2];
                    trxMap.put("credit", creditValue.toPlainString()); // More accurate for financial data
                    BigDecimal driverAmount = (BigDecimal) latestResult.get(0)[3];
                    trxMap.put("driver_amount", driverAmount.toPlainString());
                    trxMap.put("driver_phone", (String) latestResult.get(0)[4]);
                    BigDecimal recipient2CreditValue = (BigDecimal) latestResult.get(0)[5];
                    trxMap.put("recipient2credit", recipient2CreditValue.toPlainString());
                    trxMap.put("recipient2msisdn", (String) latestResult.get(0)[6]);

                    String trxChain = sharedFunctions.updateChainTrx(null, trxMap, orderId.toString(), null);

                    // 🚀 2. Insert into TRX_VALUES
                    String trxInsertQuery = "INSERT INTO TRX_VALUES(ORDER_ID, T_VALUE,DATE_CREATED) VALUES(:orderId, :trxChain, :dateCreated)";
                    Map<String, Object> trxParams = Map.of(
                            "orderId", orderId,
                            "trxChain", trxChain,
                            "dateCreated", requestTime
                    );
                    crudService.executeNativeQuery(trxInsertQuery, trxParams);

                    // 🚀 3. Audit the creation
                    sharedFunctions.auditAction("CREATED", "Created new order transaction id" + orderId, sharedFunctions.getSourceIp(request), null, null, null, null);

                    // 🚀 4. Download receipts to /var/www/html/recipients
                    List<Map<String, Object>> items = nonSupervisionPaymentRequestDTO.getSupItems();

                    String homeLinkQuery = "SELECT value FROM PARAM WHERE parameter='SYSTEM_HOME_LINK'";
                    List<String> systemUrlRow = crudService.fetchWithNativeQuery(homeLinkQuery);
                    String systemUrl = systemUrlRow.isEmpty() ? "" : String.valueOf(systemUrlRow.get(0));

                    String targetDir = receiptsUploadPath;

                    for (Map<String, Object> item : items) {
                        String itemName = Optional.ofNullable((String) item.get("Name")).orElse((String) item.get("name"));
                        if ("Expensive Sheet".equalsIgnoreCase(itemName)) {
                            itemName = "Expense Sheet";
                        }

                        String downloadUrl = String.valueOf(item.get("Download_URL"));
                        String fileNameRaw = String.valueOf(item.get("File_Receipt_Name"));
                        String expenseId = String.valueOf(item.get("Expense_Id"));

                        if (fileNameRaw == null || fileNameRaw.equals("null") || fileNameRaw.isBlank()) {
                            // Extract filename from the URL if File_Receipt_Name is not provided
                            fileNameRaw = downloadUrl.substring(downloadUrl.lastIndexOf('/') + 1); // e.g. 40ab8772...jpg
                        }

                        // Construct full file name: e.g. 1234_567_40ab8772-xxx.jpg
                        String fileName = orderId + "_" + expenseId + "_" + fileNameRaw;

                        List<String> allowedTypes = List.of("jpg", "png", "xls", "pdf", "xlsx", "jpeg");

                        // Extract extension from the actual file name
                        String fileExtension = FilenameUtils.getExtension(fileName).toLowerCase();
                        boolean isExtensionAllowed = allowedTypes.contains(fileExtension);

                        // Set the local path for saving
                        String localFilePath = targetDir + fileName;

                        String localUrl = ""; // Default value, updated only on successful download

                        // Attempt file download
                        if (!downloadUrl.isBlank()) {
                            try (InputStream in = new URL(downloadUrl).openStream()) {
                                Files.copy(in, Paths.get(localFilePath), StandardCopyOption.REPLACE_EXISTING);

                                if (!isExtensionAllowed) {
                                    Files.deleteIfExists(Paths.get(localFilePath));

                                    // Log invalid extension
                                    String logQuery = """
                                                INSERT INTO receipt_upload_logs (apiorder_id, rawdata, sourceip, comment) 
                                                VALUES(:id, :data, :ip, :comment)
                                            """;
                                    Map<String, Object> logParams = Map.of(
                                            "id", orderId,
                                            "data", postdata,
                                            "ip", sharedFunctions.getSourceIp(request),
                                            "comment", "Invalid file extension for item URL: " + downloadUrl
                                    );
                                    crudService.executeNativeQuery(logQuery, logParams);
                                } else {
                                    localUrl = systemUrl + "/api/v1/receipts/" + fileName;
                                }

                            } catch (Exception e) {
                                log.error("File download failed for URL: " + downloadUrl, e);

                                // Log download error
                                String logQuery = """
                                            INSERT INTO receipt_upload_logs (apiorder_id, rawdata, sourceip, comment) 
                                            VALUES(:id, :data, :ip, :comment)
                                        """;
                                Map<String, Object> logParams = Map.of(
                                        "id", orderId,
                                        "data", postdata,
                                        "ip", sharedFunctions.getSourceIp(request),
                                        "comment", "Download failed: " + e.getMessage()
                                );
                                crudService.executeNativeQuery(logQuery, logParams);
                            }
                        }

                        // Insert into supervision_details regardless of file download success
                        Map<String, Object> detailParams = new HashMap<>();
                        detailParams.put("orderId", orderId);
                        detailParams.put("expenseId", expenseId);
                        detailParams.put("title", itemName);
                        detailParams.put("url", item.get("Download_URL"));
                        detailParams.put("localUrl", localUrl);

                        if (item.get("Amount") != null) {
                            detailParams.put("credit", item.get("Amount"));
                            detailParams.put("originalAmount", item.get("Amount"));
                            crudService.executeNativeQuery("""
                                        INSERT INTO supervision_details(apiorder_id, expense_id, title, credit, original_amount, url, local_url)
                                        VALUES(:orderId, :expenseId, :title, :credit, :originalAmount, :url, :localUrl)
                                    """, detailParams);
                        } else {
                            crudService.executeNativeQuery("""
                                        INSERT INTO supervision_details(apiorder_id, expense_id, title, url, local_url)
                                        VALUES(:orderId, :expenseId, :title, :url, :localUrl)
                                    """, detailParams);
                        }
                    }
                }

                paymentAPiResponse.setIsPayment_Sent("0");
                paymentAPiResponse.setResponse_Value("Sucess");
                paymentAPiResponse.setResponse_Code("200");
                    PaymentAPiResponse.builder()
                        .isPayment_Sent("0")
                        .Response_Code("200")
                        .Response_Value("Success")
                        .build();
            }else {
                    String postData = request.getQueryString();

                    // Check for duplicate log entry
                    String logQuery = """
                        SELECT apiorder_id FROM api_log 
                        WHERE apiorder_id != '0' AND rawdata = :rawdata
                    """;

                    Map<String, Object> logParams = Map.of("rawdata", postData);
                    List<Object[]> logResult = crudService.fetchWithNativeQuery(logQuery, logParams, 0, 1);

                    if (paymentId == null || paymentId.toString().isEmpty()) {
                        String comment1 = "Missing Payment ID";
                        log.info("NonSupervision Failure: {}", comment1);
                        httpServletResponse.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                        paymentAPiResponse.setIsPayment_Sent("0");
                        paymentAPiResponse.setResponse_Code("400");
                        paymentAPiResponse.setResponse_Value(comment1);
                    } else {
                        String comment1 = "Failed non Supervision - Duplicate payment detected";
                        log.info("NonSupervision Failure: {}", comment1);
                        httpServletResponse.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                        paymentAPiResponse.setIsPayment_Sent("0");
                        paymentAPiResponse.setResponse_Code("400");
                        paymentAPiResponse.setResponse_Value(comment1);
                    }
                }

            // Insert into api_log
            String insertLogQuery = """
                INSERT INTO api_log (apiorder_id, rawdata, sourceip, comment)
                VALUES (:apiorderId, :rawdata, :sourceip, :comment)
            """;
            Map<String, Object> insertLogParams = new HashMap<>();
            insertLogParams.put("apiorderId", orderId);
            insertLogParams.put("rawdata", queryString);
            insertLogParams.put("sourceip", sharedFunctions.getSourceIp(request));  // implement getClientIp()
            insertLogParams.put("comment", comment);

            crudService.executeNativeQuery(insertLogQuery, insertLogParams);

            // Update app_logs
            String updateAppLogsQuery = """
                UPDATE app_logs 
                SET apiorder_id = :apiorderId, status = 'SUCCESFUL'
                WHERE requesttime = :requestTime AND payment_type = 'NON_SUPERVISION'
            """;
            Map<String, Object> updateParams = new HashMap<>();
            updateParams.put("apiorderId", orderId);
            updateParams.put("requestTime", requestTime);

            crudService.executeNativeQuery(updateAppLogsQuery, updateParams);


        }catch (Exception e) {
                log.error("An error occurred on supervision API: {}", e.getMessage(), e);

                // Rollback the DB transaction if active

                // Log to `api_errors` table
                Map<String, Object> errorParams = new HashMap<>();
                errorParams.put("sourceip", request.getRemoteAddr());
                errorParams.put("apiorder_id", orderId != null ? orderId : 0);
                errorParams.put("requesttime", requestTime);
                errorParams.put("comment", "Transaction rolled back due to: " + e.getMessage());

                try {
                    crudService.executeNativeQuery("""
                        INSERT INTO api_errors(sourceip, apiorder_id, requesttime, comment)
                        VALUES(:sourceip, :apiorder_id, :requesttime, :comment)
                    """, errorParams);
                } catch (Exception logErr) {
                    log.error("Failed to log API error: {}", logErr.getMessage(), logErr);
                }

                // Build response
                httpServletResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                return PaymentAPiResponse.builder()
                        .isPayment_Sent("0")
                        .Response_Code("500")
                        .Response_Value("An error occurred. Please check your request and try again")
                        .build();
            }
        return paymentAPiResponse;

        }

}