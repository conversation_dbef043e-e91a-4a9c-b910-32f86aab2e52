package com.tangazoletu.tibuPortalEngine.service;

import com.tangazoletu.tibuPortalEngine.entities.Activity;
import com.tangazoletu.tibuPortalEngine.enums.FormName;
import com.tangazoletu.tibuPortalEngine.enums.Module;
import com.tangazoletu.tibuPortalEngine.repositories.ActivityAuditRepository;
import com.tangazoletu.tibuPortalEngine.security.UserDetailsImpl;
import com.tangazoletu.tibuPortalEngine.util.FormModuleMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * Enhanced audit service that includes module categorization
 * for better organization and tracking of user activities
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AuditService {

    private final ActivityAuditRepository activityRepository;
    private final FormModuleMapper formModuleMapper;
    private final CrudService crudService;

    /**
     * Enhanced audit method that includes module information based on FormName
     * 
     * @param activityType The type of activity (CREATE, UPDATE, DELETE, etc.)
     * @param description Description of the activity
     * @param sourceIp Source IP address
     * @param postData POST data as string
     * @param getData GET data as string
     * @param userId User ID (optional, will be extracted from security context if null)
     * @param orgId Organization ID
     * @param formName The FormName enum to determine the module
     */
    public void auditActionWithModule(String activityType, String description, String sourceIp, 
                                    String postData, String getData, Integer userId, Integer orgId, 
                                    FormName formName) {
        try {
            // Get user details from security context if userId is null
            if (userId == null) {
                try {
                    UserDetailsImpl userDetails = (UserDetailsImpl) SecurityContextHolder.getContext()
                            .getAuthentication()
                            .getPrincipal();
                    userId = Math.toIntExact(userDetails.getId());
                } catch (Exception e) {
                    log.warn("Could not extract user ID from security context: {}", e.getMessage());
                    userId = 0; // Default to system user
                }
            }

            // Set defaults for null values
            if (orgId == null) {
                orgId = 0;
            }
            if (postData == null) {
                postData = "";
            }
            if (getData == null) {
                getData = "";
            }

            // Get module information from FormName
            Module module = formModuleMapper.getModuleForForm(formName);
            
            // Create activity record
            Activity activity = Activity.builder()
                    .user(userId)
                    .activityType(activityType)
                    .description(description)
                    .resource(formName != null ? formName.name().toLowerCase() : "unknown")
                    .getData(getData)
                    .sourceip(sourceIp)
                    .postData(postData)
                    .creationTime((int) (Instant.now().toEpochMilli() / 1000))
                    .orgId(orgId)
                    .module(module)
                    .formName(formName != null ? formName.name() : null)
                    .build();

            // Save using repository
            activityRepository.save(activity);
            
            log.debug("Audit record created: {} - {} - Module: {}", activityType, description, module.getDbValue());

        } catch (Exception e) {
            log.error("Failed to create audit record: {}", e.getMessage(), e);
        }
    }

    /**
     * Backward compatible audit method that uses native query
     * Enhanced to include module information when FormName is provided
     */
    public void auditActionWithModuleNative(String activityType, String description, String sourceIp, 
                                          String postData, String getData, Integer userId, Integer orgId, 
                                          FormName formName) {
        try {
            // Get user details from security context if userId is null
            if (userId == null) {
                try {
                    UserDetailsImpl userDetails = (UserDetailsImpl) SecurityContextHolder.getContext()
                            .getAuthentication()
                            .getPrincipal();
                    userId = Math.toIntExact(userDetails.getId());
                } catch (Exception e) {
                    log.warn("Could not extract user ID from security context: {}", e.getMessage());
                    userId = 0;
                }
            }

            // Set defaults
            if (orgId == null) orgId = 0;
            if (postData == null) postData = "";
            if (getData == null) getData = "";

            // Get module information
            Module module = formModuleMapper.getModuleForForm(formName);
            
            int createdTime = (int) (Instant.now().toEpochMilli() / 1000);
            
            // Enhanced query with module and form_name fields
            String query = "INSERT INTO activity (user, activityType, description, resource, getData, sourceip, postData, creationTime, org_id, module, form_name) " +
                          "VALUES (:user, :activityType, :description, :resource, :getData, :sourceip, :postData, :creationTime, :orgId, :module, :formName)";
            
            Map<String, Object> params = new HashMap<>();
            params.put("user", userId);
            params.put("activityType", activityType);
            params.put("description", description);
            params.put("resource", formName != null ? formName.name().toLowerCase() : "unknown");
            params.put("getData", getData);
            params.put("sourceip", sourceIp);
            params.put("postData", postData);
            params.put("creationTime", createdTime);
            params.put("orgId", orgId);
            params.put("module", module.getDbValue());
            params.put("formName", formName != null ? formName.name() : null);

            crudService.executeNativeQuery(query, params);
            
            log.debug("Audit record created via native query: {} - {} - Module: {}", activityType, description, module.getDbValue());

        } catch (Exception e) {
            log.error("Failed to create audit record via native query: {}", e.getMessage(), e);
        }
    }

    /**
     * Simple audit method for backward compatibility (without module info)
     */
    public void auditAction(String activityType, String description, String sourceIp, 
                          String postData, String getData, Integer userId, Integer orgId) {
        auditActionWithModuleNative(activityType, description, sourceIp, postData, getData, userId, orgId, null);
    }

    /**
     * Audit method specifically for role-related activities
     */
    public void auditRoleAction(String activityType, String description, String sourceIp, 
                              String postData, String getData, Integer userId, Integer orgId) {
        auditActionWithModuleNative(activityType, description, sourceIp, postData, getData, userId, orgId, FormName.ROLE);
    }

    /**
     * Get module name for a given FormName
     */
    public String getModuleForForm(FormName formName) {
        return formModuleMapper.getModuleNameForForm(formName);
    }
}
