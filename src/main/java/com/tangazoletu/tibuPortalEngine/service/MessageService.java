package com.tangazoletu.tibuPortalEngine.service;

import com.tangazoletu.tibuPortalEngine.dto.MessageFilterRequest;
import com.tangazoletu.tibuPortalEngine.dto.MessageOutboxDTO;
import com.tangazoletu.tibuPortalEngine.dto.MessageTemplateDTO;
import com.tangazoletu.tibuPortalEngine.dto.MessageTemplateRequest;
import com.tangazoletu.tibuPortalEngine.repositories.MessageOutboxRepository;
import com.tangazoletu.tibuPortalEngine.repositories.MessageTemplateRepository;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

@Service
@Data

public class MessageService {

    private final MessageOutboxRepository messageOutboxRepository;
    private  final MessageTemplateRepository messageTemplateRepository;
    public MessageService(MessageOutboxRepository messageOutboxRepository,
                          MessageTemplateRepository messageTemplateRepository) {
        this.messageOutboxRepository = messageOutboxRepository;
        this.messageTemplateRepository = messageTemplateRepository;
    }

    public Page<MessageOutboxDTO> getFilteredMessages(MessageFilterRequest filter, Pageable pageable) {
        if (filter != null && filter.getId() != null) {
            Optional<MessageOutboxDTO> messageOpt = messageOutboxRepository.findById(filter.getId())
                    .map(MessageOutboxDTO::new);
            return messageOpt.map(dto -> new PageImpl<>(List.of(dto), pageable, 1))
                    .orElse(new PageImpl<>(Collections.emptyList(), pageable, 0));


        }

        if (filter != null && Stream.of(filter.getBatchNo(), filter.getPhoneNumber(), filter.getOrigin())
                .anyMatch(Objects::nonNull)) {
            return messageOutboxRepository
                    .findFiltered(filter.getBatchNo(), filter.getPhoneNumber(), filter.getOrigin(), pageable)
                    .map(MessageOutboxDTO::new);
        }

        return messageOutboxRepository.findAll(pageable).map(MessageOutboxDTO::new);
    }

    public Page<MessageTemplateDTO> getMessageTemplates(MessageTemplateRequest request, Pageable pageable) {
        if (request != null && request.getId() != null) {
            Optional<MessageTemplateDTO> templateOpt = messageTemplateRepository.findById(request.getId())
                    .map(MessageTemplateDTO::new);
            return templateOpt.map(dto -> new PageImpl<>(List.of(dto), pageable, 1))
                    .orElse(new PageImpl<>(Collections.emptyList(), pageable, 0));
        }

        return messageTemplateRepository.findAll(pageable).map(MessageTemplateDTO::new);
    }


}
