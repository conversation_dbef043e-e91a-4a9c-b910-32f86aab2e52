package com.tangazoletu.tibuPortalEngine.service;

import com.tangazoletu.tibuPortalEngine.dto.*;
import com.tangazoletu.tibuPortalEngine.repositories.MessageOutboxRepository;
import com.tangazoletu.tibuPortalEngine.repositories.MessageTemplateRepository;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

@Service
@Data

public class MessageService {

    private final MessageOutboxRepository messageOutboxRepository;
    private  final MessageTemplateRepository messageTemplateRepository;
    public MessageService(MessageOutboxRepository messageOutboxRepository,
                          MessageTemplateRepository messageTemplateRepository) {
        this.messageOutboxRepository = messageOutboxRepository;
        this.messageTemplateRepository = messageTemplateRepository;
    }

    public Page<MessageOutboxDTO> getFilteredMessages(MessageFilterRequest filter, Pageable pageable) {
        if (filter.getId() != null) {
            Optional<MessageOutboxDTO> messageOpt = messageOutboxRepository.findById(filter.getId())
                    .map(MessageOutboxDTO::new);
            return messageOpt.map(dto -> new PageImpl<>(List.of(dto), pageable, 1))
                    .orElse(new PageImpl<>(Collections.emptyList(), pageable, 0));


        }

        if (filter != null && Stream.of(filter.getBatchNo(), filter.getPhoneNumber(), filter.getOrigin())
                .anyMatch(Objects::nonNull)) {
            return messageOutboxRepository
                    .findFiltered(filter.getBatchNo(), filter.getPhoneNumber(), filter.getOrigin(), pageable)
                    .map(MessageOutboxDTO::new);
        }

        return messageOutboxRepository.findAll(pageable).map(MessageOutboxDTO::new);
    }
    public MessageResponse getMessages(MessageFilterRequest filter, HttpServletRequest request, HttpServletResponse response) {
        MessageResponse messageResponse = MessageResponse.builder()
                .responseCode("01")
                .responseMessage("Failed")
                .build();
        try {
            int page = filter.getPage();
            int pageSize = filter.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);
            Page<MessageOutboxDTO> pageResult = getFilteredMessages(filter, pageable);
            if (pageResult.isEmpty()) {
                messageResponse.setResponseMessage("No records found");
                messageResponse.setResponseCode("01");
            }else {
                messageResponse.setResponseMessage("Success");
                messageResponse.setResponseCode("00");
                messageResponse.setData(pageResult);
            }


        }catch (Exception ex){
            messageResponse.setResponseMessage(ex.getMessage());
            messageResponse.setResponseCode("01");
        }
        return messageResponse;
    }

    public Page<MessageTemplateDTO> getMessageTemplates(MessageTemplateRequest request, Pageable pageable) {
        if (request != null && request.getId() != null) {
            Optional<MessageTemplateDTO> templateOpt = messageTemplateRepository.findById(request.getId())
                    .map(MessageTemplateDTO::new);
            return templateOpt.map(dto -> new PageImpl<>(List.of(dto), pageable, 1))
                    .orElse(new PageImpl<>(Collections.emptyList(), pageable, 0));
        }

        return messageTemplateRepository.findAll(pageable).map(MessageTemplateDTO::new);
    }
    public MessageResponse getMessageTemplates(MessageTemplateRequest request, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        MessageResponse messageResponse = MessageResponse.builder()
                .responseCode("01")
                .responseMessage("Failed")
                .build();
        try {
            int page = request.getPage();
            int pageSize = request.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);
            Page<MessageTemplateDTO> pageResult = getMessageTemplates(request, pageable);
            if (pageResult.isEmpty()) {
                messageResponse.setResponseMessage("No records found");
                messageResponse.setResponseCode("01");
            }else {
                messageResponse.setResponseMessage("Success");
                messageResponse.setResponseCode("00");
                messageResponse.setData(pageResult);
            }
        }catch (Exception ex){
            messageResponse.setResponseMessage(ex.getMessage());
            messageResponse.setResponseCode("01");
        }
        return messageResponse;
    }


}
