package com.tangazoletu.tibuPortalEngine.service;

import com.tangazoletu.tibuPortalEngine.dto.UssdLogsRequest;
import com.tangazoletu.tibuPortalEngine.dto.UssdLogsResponse;
import com.tangazoletu.tibuPortalEngine.entities.UssdLogs;
import com.tangazoletu.tibuPortalEngine.enums.ApiResponse;
import com.tangazoletu.tibuPortalEngine.repositories.UssdLogsRepo;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;

@Service
@Slf4j
@RequiredArgsConstructor
public class UssdService {
    private final UssdLogsRepo ussdLogsRepo;

    public UssdLogsResponse getUssdLogs(UssdLogsRequest  ussdLogsRequest, HttpServletResponse response, HttpServletRequest request) {
        UssdLogsResponse ussdLogsResponse = UssdLogsResponse.builder()
                .responseMessage("Failed")
                .responseCode(ApiResponse.FAIL.getCode())
                .build();
        try {

            String keyword = ussdLogsRequest.getKeyword();
            int page = ussdLogsRequest.getPageNumber();
            int pageSize = ussdLogsRequest.getPageSize();
            Pageable pageable = PageRequest.of(page, pageSize);
            Date dateFrom = ussdLogsRequest.getDateFrom();
            Date dateTo = ussdLogsRequest.getDateTo();

            Page<UssdLogs> ussdLogs = ussdLogsRepo.findFilteredLogs(keyword, dateFrom, dateTo, pageable);
            if (ussdLogs.isEmpty()) {
                ussdLogsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                ussdLogsResponse.setResponseMessage("No records found");
                ussdLogsResponse.setPage(ussdLogs);
            }else {
                ussdLogsResponse.setResponseCode(ApiResponse.SUCCESS.getCode());
                ussdLogsResponse.setResponseMessage(ussdLogs.getTotalElements() + " records found");
                ussdLogsResponse.setPage(ussdLogs);
            }

        }catch (Exception e){
            log.error("An error occured", e.getMessage());
            e.printStackTrace();
            ussdLogsResponse.setResponseCode(ApiResponse.FAIL.getCode());
            ussdLogsResponse.setResponseMessage("Failed");

        }
        return ussdLogsResponse;
    }
}
