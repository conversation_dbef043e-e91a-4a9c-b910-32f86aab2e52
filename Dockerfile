# Use an official OpenJDK image for the build stage
FROM openjdk:21-jdk-slim AS build

# Use a slim OpenJDK image to run the application
FROM openjdk:21-jdk-slim

# Set the working directory in the container
WORKDIR /tibuportalengine

# Copy the application JAR file
COPY tibuPortalEngine.jar /tibuportalengine/tibuPortalEngine.jar

# Create a logs directory for storing application logs
RUN mkdir -p /tibuportalengine/logs

# Set permissions to ensure the application can write to the logs directory
RUN mkdir -p /var/log/tibuportalengine && chown -R 1000:1000 /var/log/tibuportalengine

# Expose the port that the application will run on
EXPOSE 9090

# Define the command to run the application with -Xmx6G option
ENTRYPOINT ["java", "-jar", "tibuPortalEngine.jar", "--spring.config.location=/config/application.yml", "--logging.file.path=/var/log/tibuportalengine"]
